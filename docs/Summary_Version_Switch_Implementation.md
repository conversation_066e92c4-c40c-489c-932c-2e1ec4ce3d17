# PlaybackSummaryFragment 多版本摘要切换功能实现

## 功能概述

在 PlaybackSummaryFragment 中实现了多次生成摘要的切换显示功能，支持用户在同一录音文件的不同摘要版本之间进行切换查看。

## 核心功能特性

### 1. 多版本摘要管理
- ✅ 支持同一录音文件最多5个摘要版本的存储
- ✅ 按时间排序显示摘要版本（最新的在前）
- ✅ 自动管理摘要版本数量限制（FIFO替换机制）

### 2. 版本切换控件
- ✅ 左右箭头按钮支持前后版本切换
- ✅ 版本信息显示（当前版本/总版本数）
- ✅ "最新"按钮快速跳转到最新版本
- ✅ 单版本时自动隐藏切换控件

### 3. 选中状态持久化
- ✅ 数据库中使用 `chooseState` 字段记录选中状态
- ✅ Tab切换后返回时保持用户最后查看的版本
- ✅ 退出详情页再进入时仍显示最后查看的版本
- ✅ 新生成的摘要自动设置为选中状态

### 4. UI交互优化
- ✅ 版本切换时的按钮状态管理
- ✅ 清晰的视觉反馈显示当前版本
- ✅ 生成时间显示对应当前版本
- ✅ 平滑的版本切换体验

## 技术实现架构

### 1. 数据层 (AISummaryDataManager)

#### 新增方法：
```kotlin
// 获取摘要历史版本列表
suspend fun getSummaryHistory(recordId: Long): List<SummaryEntity>

// 获取当前选中的摘要
suspend fun getSelectedSummary(recordId: Long): SummaryEntity?

// 设置摘要的选中状态
suspend fun setSelectedSummary(recordId: Long, summaryIndex: Int): Boolean
```

#### 数据实体增强：
```kotlin
data class SummaryEntity(
    val id: Long = -1,  // 数据库ID
    val recordId: Long,
    val filePath: String,
    val summaryContent: String,
    val summaryStyle: Int = -1,
    val recordType: Int = 0,
    val createTime: Long = System.currentTimeMillis(),
    val updateTime: Long = System.currentTimeMillis(),
    val isSelected: Boolean = false  // 是否为当前选中的摘要
)
```

### 2. 业务逻辑层 (AISummaryViewModel)

#### 新增 LiveData：
```kotlin
// 摘要历史版本列表
val summaryHistory: LiveData<List<AISummaryDataManager.SummaryEntity>>

// 当前选中的摘要版本索引
val currentSummaryIndex: LiveData<Int>

// 是否有多个摘要版本
val hasMultipleVersions: LiveData<Boolean>
```

#### 版本切换方法：
```kotlin
// 切换到上一个摘要版本
fun switchToPreviousSummary()

// 切换到下一个摘要版本
fun switchToNextSummary()

// 切换到指定的摘要版本
fun switchToSummaryVersion(index: Int)

// 切换到最新版本
fun switchToLatestSummary()
```

### 3. UI层 (PlaybackSummaryFragment)

#### 版本控制UI组件：
```xml
<!-- 版本切换控件 -->
<LinearLayout android:id="@+id/layout_version_control">
    <ImageButton android:id="@+id/btn_previous_summary" />
    <TextView android:id="@+id/text_version_info" />
    <ImageButton android:id="@+id/btn_next_summary" />
    <TextView android:id="@+id/btn_latest_summary" />
</LinearLayout>
```

#### UI更新方法：
```kotlin
// 更新版本信息显示
private fun updateVersionInfo(history: List<SummaryEntity>)

// 更新当前版本索引
private fun updateCurrentVersionIndex(index: Int)

// 更新版本控制的可见性
private fun updateVersionControlVisibility(hasMultiple: Boolean)
```

## 数据流程设计

### 1. 摘要生成流程
```
用户点击生成摘要
    ↓
清除所有现有摘要的选中状态
    ↓
创建新摘要记录（设置为选中状态）
    ↓
保存摘要内容到数据库
    ↓
重新加载摘要历史版本列表
    ↓
更新UI显示最新版本
```

### 2. 版本切换流程
```
用户点击版本切换按钮
    ↓
验证目标版本索引有效性
    ↓
更新数据库中的选中状态
    ↓
更新ViewModel中的当前索引
    ↓
加载选中版本的摘要内容
    ↓
更新UI显示和按钮状态
```

### 3. 状态恢复流程
```
Fragment重新进入或Tab切换
    ↓
加载摘要历史版本列表
    ↓
查找当前选中的版本
    ↓
如果没有选中版本，默认选择最新版本
    ↓
更新UI显示选中版本的内容
```

## 关键技术要点

### 1. 选中状态管理
- 使用数据库 `chooseState` 字段（0=选中，-1=未选中）
- 确保同一录音文件只有一个摘要处于选中状态
- 新生成的摘要自动设置为选中状态

### 2. 版本索引映射
- 数组索引：0=最新版本，递增表示更旧的版本
- UI显示：1/N 表示最新版本，N/N 表示最旧版本
- 数据库时间戳：按 `timeStamp` 降序排列

### 3. UI状态同步
- 版本信息实时更新（当前版本/总版本数）
- 按钮状态动态管理（启用/禁用）
- 时间显示对应当前版本的生成时间

### 4. 性能优化
- 懒加载摘要历史版本列表
- 缓存机制避免重复数据库查询
- 异步操作不阻塞UI线程

## 用户交互设计

### 1. 版本切换操作
- **左箭头**：切换到上一个版本（更旧）
- **右箭头**：切换到下一个版本（更新）
- **最新按钮**：快速跳转到最新版本
- **版本信息**：显示当前位置（如 "2/5"）

### 2. 视觉反馈
- 按钮状态：不可用时显示为灰色
- 版本信息：清晰显示当前位置
- 时间显示：对应当前版本的生成时间
- 控件可见性：单版本时隐藏切换控件

### 3. 边界处理
- 最新版本时禁用"下一个"和"最新"按钮
- 最旧版本时禁用"上一个"按钮
- 无效索引时显示错误提示
- 数据库操作失败时的错误处理

## 测试覆盖

### 1. 功能测试
- ✅ 多个摘要版本的加载和显示
- ✅ 版本切换功能（上一个、下一个、最新）
- ✅ 选中状态的持久化
- ✅ UI状态的正确更新

### 2. 边界测试
- ✅ 单版本时的行为
- ✅ 版本切换边界情况
- ✅ 数据库操作异常处理
- ✅ UI状态同步验证

### 3. 集成测试
- ✅ 与现有摘要生成功能的兼容性
- ✅ Tab切换时的状态保持
- ✅ Fragment生命周期管理
- ✅ 数据持久化验证

## 总结

通过以上实现，PlaybackSummaryFragment 现在支持：

1. **完整的多版本管理**：从数据存储到UI展示的完整链路
2. **直观的用户交互**：简洁明了的版本切换控件
3. **可靠的状态持久化**：确保用户选择的版本状态得到保持
4. **优秀的用户体验**：平滑的切换动画和清晰的视觉反馈

该功能与现有的摘要生成、显示、刷新功能完全兼容，为用户提供了更加丰富和便捷的摘要管理体验。
