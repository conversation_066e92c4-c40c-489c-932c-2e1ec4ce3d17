/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardUtilsTest
 Description:
 Version: 1.0
 Date: 2022/9/22
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/10/14 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary

import android.content.ContentResolver
import android.content.Context
import android.os.Build
import android.os.Bundle
import android.widget.TextView
import androidx.core.os.bundleOf
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.doAction
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.fixTextFlash
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S])
class AppCardUtilsTest {

    private var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun clear() {
        context = null
    }

    @Test
    fun isFastDoubleClick() {
        AppCardUtils.isFastDoubleClick()
        AppCardUtils.isFastDoubleClick()
    }

    @Test
    fun fixTextFlash() {
        val textView = TextView(context)
        textView.fixTextFlash("XXXX")

        val mockTextView = Mockito.mock(TextView::class.java)
        Mockito.`when`(mockTextView.width).thenReturn(1)
        textView.fixTextFlash("XXXX")
        textView.fixTextFlash("XXXX", true)
    }

    @Test
    fun doAction() {
        val context = Mockito.mock(Context::class.java)
        val mockContentResolver = Mockito.mock(ContentResolver::class.java)
        Mockito.`when`(context.contentResolver).thenReturn(mockContentResolver)

        Mockito.`when`(mockContentResolver.call(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any(Bundle::class.java)))
            .thenThrow(java.lang.IllegalArgumentException())
        Assert.assertNull(context.doAction("", ""))

        Mockito.`when`(mockContentResolver.call(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any(Bundle::class.java)))
            .thenReturn(bundleOf())
//        Assert.assertNotNull(context?.doAction("", ""))
    }
}