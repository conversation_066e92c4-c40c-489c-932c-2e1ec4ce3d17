/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ScreenUtilTest
 Description:
 Version: 1.0
 Date: 2022/9/22
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/10/14 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.views

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oplus.soundrecorder.breenocardlibrary.R
import com.oplus.soundrecorder.breenocardlibrary.bean.RecorderState
import com.oplus.soundrecorder.breenocardlibrary.bean.SaveFileState
import com.oplus.soundrecorder.breenocardlibrary.bean.SmallCardData
import org.junit.After
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S])
@Ignore
class SmallCardLayoutTest {

    private var context: Context? = null
    private var smallCardLayout: SmallCardLayout? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        smallCardLayout = SmallCardLayout(context!!)
    }

    @After
    fun clear() {
        context = null
    }

    @Test
    fun setData() {
        val data = SmallCardData(
            packageName = "CommonFlavor.getInstance().getPackageName()",
            widgetCode = "0&0&0",
            recordState = RecorderState.RECORDING,
            saveFileState = SaveFileState.START_LOADING,
            timeText = "00:00:00",
            timeTextColor = R.color.breeno_card_wave_dash_line_color,
            timeDes = "00:00:00",
            stateText = "stateText",
            showMarkNotice = false,
            markEnable = false,
            isStartServiceFormAppCar = false,
            fileName = "录音1",
            fileNameWithOutType = "",
            markSrc = R.drawable.breeno_card_record_mark,
            markBackGroundSrc = R.drawable.breeno_card_record_mark,
            recordInitSrc = R.drawable.breeno_card_record_init,
            recordPauseSrc = R.drawable.breeno_card_record_mark,
            recordResumeSrc = R.drawable.breeno_card_record_recording,
            saveFileSrc = R.drawable.breeno_card_record_save,
            saveBackGroundSrc = R.drawable.breeno_card_record_init,
            waveData = 10,
            showLoadingDialog = true,
            progressValue = 5,
            loadingTitle = "saving...",
            needRunMarkAndSaveAnimation = false
        )
        smallCardLayout?.refreshData(data)
        data.recordState = RecorderState.INIT
        smallCardLayout?.refreshData(data)
        data.recordState = RecorderState.PAUSED
        smallCardLayout?.refreshData(data)

        data.saveFileState = SaveFileState.INIT
        data.saveFileState = SaveFileState.SHOW_DIALOG
        smallCardLayout?.refreshData(data)
        data.saveFileState = SaveFileState.SUCCESS
        smallCardLayout?.refreshData(data)

        data.needRunMarkAndSaveAnimation = true
        smallCardLayout?.refreshData(data)
    }
}