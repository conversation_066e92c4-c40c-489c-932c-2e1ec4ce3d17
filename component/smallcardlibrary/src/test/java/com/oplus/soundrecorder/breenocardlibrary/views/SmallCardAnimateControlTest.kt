/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SmallCardAnimateControlTest
 * Description:
 * Version: 1.0
 * Date: 2023/12/20
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/12/20 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.views

import android.content.Context
import android.os.Build
import android.os.SystemClock
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S])
@Ignore
class SmallCardAnimateControlTest {

    private var context: Context? = null
    private var systemClockMockStatic: MockedStatic<SystemClock>? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        systemClockMockStatic = Mockito.mockStatic(SystemClock::class.java)
        systemClockMockStatic?.`when`<Long> { SystemClock.elapsedRealtime() }?.thenReturn(10000)
    }

    @After
    fun release() {
        context = null
        systemClockMockStatic?.close()
        systemClockMockStatic = null
    }

    @Test
    fun should_correct_when_showMarkAndSaveFileView() {
        val context = context ?: return
        val alphaOne = 1f
        val alphaZero = 0f
        val rootView = ConstraintLayout(context)
        val markView = View(context).apply { alpha = alphaZero }
        val saveView = View(context).apply { alpha = alphaZero }
        rootView.addView(markView)
        rootView.addView(saveView)
        val animateControl = SmallCardAnimateControl()
        animateControl.showMarkAndSaveFileView(false, rootView, markView, saveView, {})
        Assert.assertEquals(alphaOne, markView.alpha)
        Assert.assertEquals(alphaOne, saveView.alpha)

        Whitebox.setInternalState(animateControl, "doShowAnimationTime", 10000)
        markView.alpha = alphaZero
        saveView.alpha = alphaZero
        animateControl.showMarkAndSaveFileView(false, rootView, markView, saveView, {})
        Assert.assertEquals(alphaZero, markView.alpha)
        Assert.assertEquals(alphaZero, saveView.alpha)
    }

    @Test
    fun should_correct_when_checkAnimatorRunning() {
        val funName = "checkAnimatorRunning"
        val animateControl = SmallCardAnimateControl()
        var result = Whitebox.invokeMethod<Boolean>(animateControl, funName, -1L)
        Assert.assertFalse(result)

        result = Whitebox.invokeMethod(animateControl, funName, 9500L)
        Assert.assertTrue(result)

        result = Whitebox.invokeMethod(animateControl, funName, 9499L)
        Assert.assertFalse(result)
    }
}