<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/saveSuccessView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="gone"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottie_success"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:importantForAccessibility="no"
        app:flow_horizontalAlign="center"
        app:layout_constraintBottom_toTopOf="@id/tvSaveSuccess"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="@+id/tvSaveSuccess"
        android:layout_marginBottom="@dimen/small_card_save_succes_spacing_file_name"
        app:layout_constraintStart_toStartOf="@+id/tvSaveSuccess"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintWidth_percent="0.27"
        app:lottie_loop="false" />

    <TextView
        android:id="@+id/tvSaveSuccess"
        style="@style/SmallCard_Save_Success_Text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textFontWeight="400"
        android:ellipsize="end"
        app:layout_constraintBottom_toTopOf="@id/tvFileName"
        app:layout_constraintEnd_toEndOf="@+id/tvFileName"
        app:layout_constraintStart_toStartOf="@+id/tvFileName"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="保存成功" />

    <TextView
        android:id="@+id/tvFileName"
        style="@style/SmallCard_Save_Success_Text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:textFontWeight="400"
        app:layout_constraintBottom_toTopOf="@id/guideline_bottom"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="标准录音 1" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.81" />

</androidx.constraintlayout.widget.ConstraintLayout>