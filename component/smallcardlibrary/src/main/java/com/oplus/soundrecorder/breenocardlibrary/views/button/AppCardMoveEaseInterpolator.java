package com.oplus.soundrecorder.breenocardlibrary.views.button;

import android.view.animation.PathInterpolator;

public class AppCardMoveEaseInterpolator extends PathInterpolator {

    private static final float CONTROL_X_1 = 0.30f;
    private static final float CONTROL_Y_1 = 0f;
    private static final float CONTROL_X_2 = 0.10f;
    private static final float CONTROL_Y_2 = 1f;

    public AppCardMoveEaseInterpolator() {
        super(CONTROL_X_1, CONTROL_Y_1, CONTROL_X_2, CONTROL_Y_2);
    }
}
