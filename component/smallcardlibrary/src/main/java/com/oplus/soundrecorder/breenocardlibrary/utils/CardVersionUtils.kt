/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: CardUtils
 Description:
 Version: 1.0
 Date: 2023/2/13
 Author: ********(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2023/2/13 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.utils

import android.content.Context
import android.content.pm.PackageManager

internal object CardVersionUtils {
    private val RECORDER_PACKAGE = arrayOf("com.coloros.soundrecorder", "com.oneplus.soundrecorder")
    private const val META_DATA_SMALL_CARD_VERSION_CODE = "com.soundRecorder.smallCard.versionCode"
    private const val VERSION_CODE_2 = 2
    /*波形更改为内屏真实波形*/
    const val VERSION_CODE_3 = 3

    /**
     * 需要manifest正确配置package包名
     */
    @Suppress("DEPRECATION")
    @JvmStatic
    fun Context.metaDataInt(packageName: String, metaDataName: String): Int {
        if (packageName.isEmpty()) return -1
        try {
            val applicationInfo = applicationContext.packageManager.getApplicationInfo(packageName, PackageManager.GET_META_DATA)
            return applicationInfo.metaData?.getInt(metaDataName, -1) ?: -1
        } catch (_: Exception) {
        }
        return -1
    }

    @JvmStatic
    fun getRecordAppVersionCode(context: Context, packageName: String): Int {
        return context.applicationContext.metaDataInt(
            packageName,
            META_DATA_SMALL_CARD_VERSION_CODE
        )
    }
}