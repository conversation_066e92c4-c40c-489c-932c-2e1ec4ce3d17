/********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  WaveRecyclerView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2018/11/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
</desc></version></data></author> */
package com.oplus.soundrecorder.breenocardlibrary.views.wave

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.os.SystemClock
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import androidx.core.content.ContextCompat
import androidx.core.view.OneShotPreDrawListener
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.oplus.soundrecorder.breenocardlibrary.R
import com.oplus.soundrecorder.breenocardlibrary.bean.MarkDataBean
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.log
import com.oplus.soundrecorder.breenocardlibrary.views.wave.SmallCardWaveAdapter.Companion.VIEW_TYPE_WAVE
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.DEFAULT_SCROLL_X
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.DURATION_560
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.FLOAT_0_5
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.FLOAT_0_7
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.FLOAT_2
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.INT_100
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.INT_2
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.INT_4
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.INT_8
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.getOneWaveLineWidth
import com.oplus.soundrecorder.breenocardlibrary.views.wave.WaveViewUtil.getOneWaveWidth
import kotlin.math.abs

internal class SmallCardWaveRecyclerView @JvmOverloads constructor(
    ctx: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) :
    RecyclerView(ctx, attrs, defStyle),
    IWaveItemViewDelegate {

    private val linearLayoutManager = LinearLayoutManager(ctx).apply {
        orientation = LinearLayoutManager.HORIZONTAL
        reverseLayout = AppCardUtils.isRTL()
    }
    private val waveAdapter = SmallCardWaveAdapter(this)
    private val linearInterpolator = LinearInterpolator()
    private var preTime: Int = -1
    private var hasRecording = false
    private var ampsSize = 0
    private var lastAmps = emptyList<Int>()
    private var cardWaveColor = 0
    private var cardDashWaveColor = 0
    private var markLineColor = 0
    private var centerLineColor = 0
    private var doEnterAnimationTime = -1L
    private var doEndAnimationTime = -1L
    private var centerLinePaint: Paint? = null
    private var viewHeight = -1
    private var maxWaveHeight: Float

    init {
        overScrollMode = OVER_SCROLL_NEVER
        setWaveAdapter()
        OneShotPreDrawListener.add(this) {
            setSelectTime(ampsSize)
            val max = (width / context.getOneWaveWidth()).toInt() + INT_2
            recycledViewPool.setMaxRecycledViews(VIEW_TYPE_WAVE, if (max > 0) max else INT_100)
        }
        cardWaveColor = ContextCompat.getColor(context, R.color.breeno_card_wave_line_color)
        cardDashWaveColor = ContextCompat.getColor(context, R.color.breeno_card_wave_dash_line_color)
        ContextCompat.getColor(context, R.color.breeno_card_wave_center_line_default_color).apply {
            centerLineColor = this
        }
        ContextCompat.getColor(context, R.color.breeno_card_wave_mark_line_color).apply {
            markLineColor = this
        }

        centerLinePaint = Paint().apply {
            color = centerLineColor
            isAntiAlias = true
            style = Paint.Style.FILL_AND_STROKE
            strokeCap = Paint.Cap.ROUND
        }
        maxWaveHeight = resources.getDimension(R.dimen.small_card_max_wave_line_height)
    }

    fun doEnterAnimator() {
        doEnterAnimationTime = SystemClock.elapsedRealtime()
        waveAdapter.notifyDataSetChanged()
    }

    fun doEndAnimator() {
        doEndAnimationTime = SystemClock.elapsedRealtime()
        waveAdapter.notifyDataSetChanged()
    }

    fun refreshColor(cardWaveColor: Int, cardDashWaveColor: Int, markLineColor: Int) {
        this.cardWaveColor = cardWaveColor
        this.cardDashWaveColor = cardDashWaveColor
        this.markLineColor = markLineColor
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        super.onLayout(changed, l, t, r, b)
        val height = b - t
        if (!changed || viewHeight == height) {
            return
        }
        viewHeight = height
        maxWaveHeight = viewHeight * FLOAT_0_7
        "onLayout,viewHeight=$viewHeight, maxWaveHeight=$maxWaveHeight".log()
    }

    override fun createNewItemView(parent: ViewGroup): SmallCardWaveItemView {
        return LayoutInflater.from(parent.context)
            .inflate(R.layout.small_card_item_ruler, parent, false) as SmallCardWaveItemView
    }

    override fun onBindItemView(rulerView: SmallCardWaveItemView, position: Int) {
        rulerView.refreshMaxWaveHeight(maxWaveHeight)
        rulerView.refreshColor(cardWaveColor, cardDashWaveColor, markLineColor)
        rulerView.refreshData(
            hasRecording,
            ampsSize,
            lastAmps,
            doEnterAnimationTime,
            doEndAnimationTime
        )
    }

    override fun draw(c: Canvas) {
        super.draw(c)
        drawCenterLine(c)
    }

    private fun drawCenterLine(canvas: Canvas) {
        val paint = centerLinePaint ?: return
        val centerLineStartX: Float = width / FLOAT_2
        val centerLineWidth = context.getOneWaveLineWidth()
        val halfLineWidth = centerLineWidth / FLOAT_2
        canvas.drawRoundRect(
            centerLineStartX - halfLineWidth,
            0f,
            centerLineStartX + halfLineWidth,
            height.toFloat(),
            centerLineWidth,
            centerLineWidth,
            paint
        )
    }

    override fun halfWidth(): Int {
        return ((width - context.getOneWaveWidth()) * FLOAT_0_5).toInt()
    }

    /**
     * stop wave timer
     */
    fun stopRecorderMove(ampsSize: Int, lastAmps: List<Int>) {
        runCatching {
            this.hasRecording = false
            this.ampsSize = ampsSize
            this.lastAmps = lastAmps
            waveAdapter.notifyDataSetChanged()
            setSelectTime(this.ampsSize)
        }.onFailure {
            "stopRecorderMove error $it".log()
        }
    }

    /**
     * update wave
     */
    fun recorderIntervalUpdate(ampsSize: Int, lastAmps: List<Int>, hasRefreshTime70: Boolean) {
        runCatching {
            this.hasRecording = true
            this.ampsSize = ampsSize
            this.lastAmps = lastAmps
            waveAdapter.notifyDataSetChanged()
            startSmoothScroll(this.ampsSize, hasRefreshTime70)
        }.onFailure {
            "recorderIntervalUpdate error $it".log()
        }
    }

    fun setSelectTime(curTime: Int) {
        "setSelectTime".log()
        preTime = curTime
        stopScroll()
        val halfWidth = halfWidth()
        if (halfWidth > 0) {
            linearLayoutManager.scrollToPositionWithOffset(curTime, halfWidth())
        }
    }

    /**
     * v1.0，140毫秒推送一次波形数据，大概距离delta为context.getOneWaveWidth()的2倍
     * v2.0  70毫秒推送一次波形数据，大概距离delta为context.getOneWaveWidth()的1倍
     * hasRefreshTime70 录音app传送过来的，判断是否支持70毫秒刷新
     */
    private fun startSmoothScroll(curTime: Int, hasRefreshTime70: Boolean) {
        if (preTime > 0) {
            val scrolledLength = getTotalScrolledLength()
            if (scrolledLength < 0) {
                setSelectTime(curTime)
            } else {
                var delta = getDeltaScrollLength(curTime, scrolledLength)
                val rate = if (hasRefreshTime70) INT_8 else INT_4
                if (delta < context.getOneWaveWidth() * rate) {
                    delta *= rate
                    val newX = if (linearLayoutManager.reverseLayout) -delta else delta
                    smoothScrollBy(newX, 0, linearInterpolator, DURATION_560)
                } else {
                    setSelectTime(curTime)
                }
            }
        }
        preTime = curTime
    }

    /**
     * delta理论上始终大于0
     */
    private fun getDeltaScrollLength(curTime: Int, scrolledLength: Int): Int {
        val oneWaveWidth = context.getOneWaveWidth()
        val expectedLength = (curTime * oneWaveWidth).toInt()
        return expectedLength - scrolledLength
    }

    /**
     * 计算WaveRecyclerView當前已滚动的距离
     */
    @Suppress("TooGenericExceptionCaught")
    private fun getTotalScrolledLength(): Int {
        try {
            val firstVisibleItemPosition = linearLayoutManager.findFirstVisibleItemPosition()
            val firstVisibleItem = linearLayoutManager.findViewByPosition(firstVisibleItemPosition)
                ?: return DEFAULT_SCROLL_X
            val left =
                if (AppCardUtils.isRTL()) abs(firstVisibleItem.right - width) else firstVisibleItem.left
            return if (firstVisibleItemPosition == 0) {
                abs(left)
            } else {
                (abs(left) + halfWidth() + (firstVisibleItemPosition - 1) * context.getOneWaveWidth()).toInt()
            }
        } catch (e: Exception) {
            "getTotalScrolledLength error $e".log()
            return DEFAULT_SCROLL_X
        }
    }

    private fun setWaveAdapter() {
        layoutManager = linearLayoutManager
        adapter = waveAdapter
    }

    fun setMarkTimeList(markData: List<MarkDataBean?>?, currentTime: Long) {
        waveAdapter.setMarkTimeList(markData, currentTime)
    }

    fun clearMarkTimeList() {
        waveAdapter.clearMarkTimeList()
    }
}