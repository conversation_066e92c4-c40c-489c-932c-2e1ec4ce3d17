package com.recorder.move

import android.util.Xml
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.base.utils.DebugUtil
import org.xmlpull.v1.XmlSerializer
import java.io.IOException
import java.io.StringWriter

abstract class BaseXmlComposer<T> {

    private val TAG = "BaseComposer"

    protected val mSerializer: XmlSerializer by lazy {
        Xml.newSerializer()
    }
    private val mStringWriter: StringWriter by lazy {
        StringWriter()
    }


    fun startCompose(): Boolean {
        var result = false
        try {
            mSerializer.setOutput(mStringWriter)
            mSerializer.startDocument(null, false)
            mSerializer.startTag("", getTag())
            result = true
        } catch (e: IOException) {
            DebugUtil.e(TAG, "startCompose IOException error", e)
        } catch (e: IllegalArgumentException) {
            DebugUtil.e(TAG, "startCompose IllegalArgumentException error", e)
        } catch (e: IllegalStateException) {
            DebugUtil.e(TAG, "startCompose IllegalStateException error", e)
        }

        return result
    }

    fun endCompose(): Boolean {
        var result = false
        try {
            mSerializer.endTag("", getTag())
            mSerializer.endDocument()
            result = true
        } catch (e: IllegalArgumentException) {
            DebugUtil.e(TAG, "endCompose IllegalArgumentException error", e)
        } catch (e: IllegalStateException) {
            DebugUtil.e(TAG, "endCompose IllegalStateException error", e)
        } catch (e: IOException) {
            DebugUtil.e(TAG, "endCompose IOException error", e)
        }
        return result
    }

    fun addData(data: T) {
        mSerializer.startTag("", DatabaseConstant.ROOT)
        composerData(data)
        mSerializer.endTag("", DatabaseConstant.ROOT)
    }


    fun getXmlInfo(): String {
        try {
            val info = mStringWriter.toString()
            mStringWriter.close()
            return info
        } catch (e: IOException) {
            DebugUtil.e(TAG, "mStringWriter close IOException error", e)
        }
        return ""
    }

    abstract fun getXmlName(): String

    /**
     * 获取tag
     */
    abstract fun getTag(): String

    abstract fun composerData(data: T)
}