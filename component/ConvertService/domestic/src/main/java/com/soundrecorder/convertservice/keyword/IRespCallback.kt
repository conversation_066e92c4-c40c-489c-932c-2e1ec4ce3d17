/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  IRespCallback
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.keyword

/**
 * 网络响应的回调
 */
interface IRespCallback<Data> {

    /**
     * 成功的回调
     * @param data 数据
     *
     */
    fun onSuccess(data: Data)

    /**
     * 失败的回调
     * @param code 失败的code
     * @param msg 失败的原因
     */
    fun onFailed(code: Int, msg: String)
}