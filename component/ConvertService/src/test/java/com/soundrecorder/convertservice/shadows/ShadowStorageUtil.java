package com.soundrecorder.convertservice.shadows;

import android.content.Context;

import com.soundrecorder.base.utils.StorageUtil;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(StorageUtil.class)
public class ShadowStorageUtil {

    @Implementation
    public Long getAvailableSpace(Context context) {
        return 200 * 1024 * 1024L;
    }
}
