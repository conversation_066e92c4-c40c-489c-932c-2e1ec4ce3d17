package com.soundrecorder.convertservice.convert;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.convertservice.shadows.ShadowFeatureOption;
import com.soundrecorder.convertservice.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.modulerouter.playback.PlaybackAction;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class ConvertTaskThreadManagerTest {

    private static final long MEDIA_ID_0 = 0L;
    private static final long MEDIA_ID_1 = 1L;
    private static final long MEDIA_ID_2 = 2L;
    private static final long MEDIA_ID_3 = 3L;
    private static final int DEFAULT_NULL = 0;
    private static final int ALREADY_RUNNING = 1;
    private static final int OVER_LIMIT = 2;
    private static final int CAN_ADD_NEW = 3;

    private Context mContext;
    private ConvertTaskThreadManager mInstance;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = BaseApplication.getAppContext();
        mInstance = ConvertTaskThreadManager.INSTANCE;
    }

    @After
    public void tearDown() {
        mContext = null;
        mInstance.getTaskMaps().clear();
    }

    @Test
    public void should_send_broadcast_when_********************************() throws Exception {
        LocalBroadcastManager localBroadcastManager = LocalBroadcastManager.getInstance(mContext);
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(PlaybackAction.NOTIFY_CONVERT_STATUS_UPDATE);
        final boolean[] hasReceivedBroadcast = {false};
        final long[] mediaId = {-1L};
        final boolean[] isConvert = {false};
        BroadcastReceiver localReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                hasReceivedBroadcast[0] = true;
                mediaId[0] = intent.getLongExtra(PlaybackAction.KEY_NOTIFY_RECORD_ID, -1L);
                isConvert[0] = intent.getBooleanExtra(PlaybackAction.KEY_NOTIFY_CONVERT_STATUS, false);
            }
        };
        localBroadcastManager.registerReceiver(localReceiver, intentFilter);
        Whitebox.invokeMethod(mInstance, "********************************", MEDIA_ID_0, true);
        //Assert.assertTrue(hasReceivedBroadcast[0]);
        //Assert.assertEquals(MEDIA_ID_0, mediaId[0]);
        //Assert.assertTrue(isConvert[0]);
    }

    @Test
    public void should_cancel_task_when_cancelTask() throws Exception {
        boolean actualResult = Whitebox.invokeMethod(mInstance, "cancelTask", MEDIA_ID_0);
        Assert.assertFalse(actualResult);
        Whitebox.invokeMethod(mInstance, "startOrResumeTask", MEDIA_ID_0, null);
        actualResult = Whitebox.invokeMethod(mInstance, "cancelTask", MEDIA_ID_0);
        Assert.assertTrue(actualResult);
    }

    @Test
    public void should_return_true_when_checkIsTaskRunning() throws Exception {
        Assert.assertFalse(Whitebox.invokeMethod(mInstance, "checkIsTaskRunning", MEDIA_ID_0));
        Whitebox.invokeMethod(mInstance, "startOrResumeTask", MEDIA_ID_0, null);
        Assert.assertTrue(Whitebox.invokeMethod(mInstance, "checkIsTaskRunning", MEDIA_ID_0));
    }

    @Test
    public void should_return_true_when_checkNoTaskRunning() throws Exception {
        boolean actualRes = Whitebox.invokeMethod(mInstance, "checkNoTaskRunning");
        Assert.assertTrue(actualRes);
        Whitebox.invokeMethod(mInstance, "startOrResumeTask", MEDIA_ID_0, null);
        actualRes = Whitebox.invokeMethod(mInstance, "checkNoTaskRunning");
        //Assert.assertFalse(actualRes);
    }

    @Test
    public void should_return_status_when_checkCanAddNewTask() throws Exception {
        int actual = Whitebox.invokeMethod(mInstance, "checkCanAddNewTask", MEDIA_ID_0);
        Assert.assertEquals(CAN_ADD_NEW, actual);

        Whitebox.invokeMethod(mInstance, "startOrResumeTask", MEDIA_ID_0, null);
        actual = Whitebox.invokeMethod(mInstance, "checkCanAddNewTask", MEDIA_ID_0);
        Assert.assertEquals(ALREADY_RUNNING, actual);

        Whitebox.invokeMethod(mInstance, "startOrResumeTask", MEDIA_ID_1, null);
        Whitebox.invokeMethod(mInstance, "startOrResumeTask", MEDIA_ID_2, null);
        actual = Whitebox.invokeMethod(mInstance, "checkCanAddNewTask", MEDIA_ID_3);
        Assert.assertEquals(OVER_LIMIT, actual);
    }

    @Test
    public void should_not_null_when_registerCallback() throws Exception {
        Whitebox.invokeMethod(mInstance, "startOrResumeTask", MEDIA_ID_0, null);
        Assert.assertNull(mInstance.getMUICallbacks().get(MEDIA_ID_0));
        IConvertCallback convertCallback = Mockito.mock(IConvertCallback.class);
        mInstance.registerCallback(MEDIA_ID_0, convertCallback);
        Assert.assertNotNull(mInstance.getMUICallbacks().get(MEDIA_ID_0));
    }

    @Test
    public void should_null_when_unregisterCallback() throws Exception {
        IConvertCallback convertCallback = Mockito.mock(IConvertCallback.class);
        Whitebox.invokeMethod(mInstance, "startOrResumeTask", MEDIA_ID_0, convertCallback);
        //Assert.assertNotNull(mInstance.getMUICallbacks().get(MEDIA_ID_0));
        mInstance.unregisterCallback(MEDIA_ID_0, convertCallback);
        Assert.assertNull(mInstance.getMUICallbacks().get(MEDIA_ID_0));
    }
    @Test
    public void should_cancel_all_task() throws Exception {
        Whitebox.invokeMethod(mInstance, "cancelAllTask");
        mInstance.releaseAll();
        Assert.assertTrue(mInstance.getTaskMaps().isEmpty());
    }
}
