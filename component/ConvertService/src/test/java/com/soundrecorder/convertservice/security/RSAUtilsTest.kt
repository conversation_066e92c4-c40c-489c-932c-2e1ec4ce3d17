/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RSAUtilsTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/12/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.security

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.convertservice.okhttphelper.ConstantUrls
import com.soundrecorder.convertservice.shadows.ShadowFeatureOption
import com.soundrecorder.convertservice.shadows.ShadowOS12FeatureUtil
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog
import java.security.NoSuchAlgorithmException

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowLog::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class RSAUtilsTest {

    @Test
    @Ignore
    @Throws(NoSuchAlgorithmException::class)
    fun should_equals_key_when_encrypt() {
        val originStr = "hi, my name is tom"
        val public = ConstantUrls.PUBKEY
        val private = ConstantUrls.SECRET
        val encrypt = _root_ide_package_.com.soundrecorder.convertservice.security.RSAUtils.publicEncrypt(originStr, public)
        val real = _root_ide_package_.com.soundrecorder.convertservice.security.RSAUtils.privateDecrypt(encrypt, private)
        Assert.assertEquals(originStr, real)
    }
}