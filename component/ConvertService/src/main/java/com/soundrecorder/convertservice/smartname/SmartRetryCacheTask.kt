/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  SmartNameTasksCache
 * * Description: SmartNameTasksCache
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.convertservice.smartname

import com.soundrecorder.base.utils.DebugUtil
import java.util.concurrent.ConcurrentHashMap

object SmartRetryCacheTask {

    private const val TAG = "SmartRetryCacheTask"
    private const val MAX_RETRY_COUNT = 3
    private val cacheSmartRetryTasks = ConcurrentHashMap<Long, Int>()

    fun addRetryTaskToCache(mediaId: Long, retryCount: Int) {
        cacheSmartRetryTasks[mediaId] = retryCount
    }

    fun hasCacheTask(mediaId: Long): Boolean {
        DebugUtil.d(TAG, "hasCacheTask, taskSize:${cacheSmartRetryTasks.size}")
        return cacheSmartRetryTasks.containsKey(mediaId)
    }

    /**
     * 获取当前智能命名任务retryCount
     */
    fun getTaskRetryCount(mediaId: Long): Int {
        if (!cacheSmartRetryTasks.isNullOrEmpty() && cacheSmartRetryTasks.containsKey(mediaId)) {
            return cacheSmartRetryTasks.get(mediaId) ?: 0
        }
        return 0
    }

    fun clear() {
        DebugUtil.d(TAG, "clear")
        cacheSmartRetryTasks.clear()
    }

    /**
     * 获取当前mediaId是否进行重试
     * 首次进入未进行过智能命名时retryCount = 0
     * 进行了智能命名任务 retryCount+1，retryCount=3时从0再开始
     * retryCount：0,1,2,3,服务端最大支持为3，retryCount=3时后续不再变化
     */
    fun getCurrentRetryCount(mediaId: Long): Int {
        var retryCount = 0
        if (hasCacheTask(mediaId)) {
            val count = getTaskRetryCount(mediaId)
            if (count >= MAX_RETRY_COUNT) {
                retryCount = MAX_RETRY_COUNT
            } else {
                retryCount = count + 1
            }
        }
        return retryCount
    }
}