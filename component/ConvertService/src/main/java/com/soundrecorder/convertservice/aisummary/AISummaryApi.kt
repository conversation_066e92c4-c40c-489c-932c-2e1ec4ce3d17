/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  AISummaryApi
 * * Description: AI摘要API实现，参考SmartNameApi
 * * Version: 1.0
 * * Date : 2024/12/19
 * * Author: Assistant
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  Assistant    2024/12/19   1.0    build this module
 ****************************************************************/
package com.soundrecorder.convertservice.aisummary

import android.content.Context
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.common.databean.AISummaryRecordParam
import com.soundrecorder.modulerouter.aisummary.AISummaryAction
import com.soundrecorder.modulerouter.aisummary.IAISummaryCallback

@Component(AISummaryAction.COMPONENT_NAME)
object AISummaryApi {

    private const val TAG = "AISummaryApi"

    /**
     * @Action(AISummaryAction.ACTION_CHECK_SUPPORT_AI_SUMMARY)
     *     @JvmStatic
     *     fun checkSupportAISummary(context: Context, supportConvert: Boolean? = null, forceUpdate: Boolean = false): Boolean {
     *         /* 支持录音摘要限制条件 */
     *         return true
     *     }
     */

    @Action(AISummaryAction.ACTION_START_CONVERT_OR_AI_SUMMARY)
    @JvmStatic
    fun startAISummary(mediaId: Long): Boolean {
        return AISummaryTaskManager.startAISummary(mediaId, null)
    }

    @Action(AISummaryAction.ACTION_START_AI_SUMMARY)
    @JvmStatic
    fun startAISummary(mediaId: Long, jsonParams: String?): Boolean {
        val params = GsonUtil.fromJson(jsonParams, AISummaryRecordParam::class.java)
        return AISummaryTaskManager.startAISummary(mediaId, params)
    }

    @Action(AISummaryAction.ACTION_START_AI_SUMMARY_V2)
    @JvmStatic
    fun startAISummary(mediaId: Long, params: AISummaryRecordParam?): Boolean {
        return AISummaryTaskManager.startAISummary(mediaId, params)
    }

    @Action(AISummaryAction.ACTION_CHECK_IS_TASK_RUNNING)
    @JvmStatic
    fun checkIsTaskRunning(mediaId: Long): Boolean {
        return AISummaryTaskManager.checkIsTaskRunning(mediaId)
    }

    @Action(AISummaryAction.ACTION_CHECK_HAS_TASK_RUNNING)
    @JvmStatic
    fun checkHasTaskRunning(): Boolean {
        return AISummaryTaskManager.checkHasTaskRunning()
    }

    @Action(AISummaryAction.ACTION_REGISTER_AI_SUMMARY_CALLBACK)
    @JvmStatic
    fun registerCallback(mediaId: Long, callback: IAISummaryCallback) {
        AISummaryTaskManager.registerAISummaryCallback(mediaId = mediaId, callback)
    }

    @Action(AISummaryAction.ACTION_UN_REGISTER_AI_SUMMARY_CALLBACK)
    @JvmStatic
    fun unRegisterCallback(mediaId: Long, callback: IAISummaryCallback) {
        AISummaryTaskManager.unregisterAISummaryCallback(mediaId = mediaId, callback)
    }

    @Action(AISummaryAction.ACTION_CANCEL_AI_SUMMARY_TASK)
    @JvmStatic
    fun cancelAISummaryTask(mediaId: Long) {
        AISummaryTaskManager.cancelAISummary(mediaId)
    }

    @Action(AISummaryAction.ACTION_FROM_JSON)
    @JvmStatic
    fun <T> fromJson(json: String?, cls: Class<T>): T? {
        return GsonUtil.fromJson(json, cls)
    }

    @Action(AISummaryAction.ACTION_STOP_AI_SUMMARY)
    @JvmStatic
    fun stopAISummaryTask(mediaId: Long) {
        AISummaryTaskManager.stopAISummaryTask(mediaId)
    }
}
