/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryErrorHandler
 * Description: 统一的AI摘要错误处理中心，消除重复的错误处理逻辑
 * Version: 1.0
 * Date: 2025/6/4
 * Author: Assistant
 *********************************************************************************/
package com.soundrecorder.convertservice.aisummary

import android.content.Context
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.R
import com.soundrecorder.modulerouter.aisummary.AISummaryAction

/**
 * 统一的AI摘要错误处理中心
 * 消除各个组件中重复的错误处理逻辑
 */
object AISummaryErrorHandler {
    
    private const val TAG = "AISummaryErrorHandler"
    
    /**
     * 错误类型枚举
     */
    enum class ErrorType {
        NETWORK_ERROR,      // 网络错误
        CONTENT_ERROR,      // 内容错误
        SERVER_ERROR,       // 服务器错误
        TIMEOUT_ERROR,      // 超时错误
        FILE_ERROR,         // 文件错误
        UNKNOWN_ERROR       // 未知错误
    }
    
    /**
     * 错误处理结果
     */
    data class ErrorResult(
        val errorType: ErrorType,
        val userMessage: String,
        val logMessage: String,
        val shouldShowToast: Boolean = true,
        val shouldRetry: Boolean = false
    )
    
    /**
     * 处理AI摘要错误
     * @param errorCode 错误码
     * @param errorMsg 错误消息
     * @return 错误处理结果
     */
    fun handleError(errorCode: Int, errorMsg: String?): ErrorResult {
        DebugUtil.e(TAG, "handleError: errorCode=$errorCode, errorMsg=$errorMsg")
        
        return when (errorCode) {
            AISummaryAction.NETWORK_ERROR -> ErrorResult(
                errorType = ErrorType.NETWORK_ERROR,
                userMessage = "网络连接失败",
                logMessage = "Network connection failed: $errorMsg",
                shouldRetry = true
            )
            
            AISummaryAction.CONTENT_LESS_ERROR -> ErrorResult(
                errorType = ErrorType.CONTENT_ERROR,
                userMessage = getStringResource(R.string.summary_error_record_short),
                logMessage = "Content too short for summary: $errorMsg",
                shouldRetry = false
            )
            
            AISummaryAction.SERVER_ERROR,
            AISummaryAction.AIUNIT_ERROR -> ErrorResult(
                errorType = ErrorType.SERVER_ERROR,
                userMessage = "服务异常，摘要生成失败",
                logMessage = "Server error occurred: errorCode=$errorCode, msg=$errorMsg",
                shouldRetry = true
            )
            
            AISummaryAction.REQUEST_TIMEOUT -> ErrorResult(
                errorType = ErrorType.TIMEOUT_ERROR,
                userMessage = "请求超时，请重试",
                logMessage = "Request timeout: $errorMsg",
                shouldRetry = true
            )
            
            AISummaryAction.FILE_SIZE_UNSUPPORT_ZERO,
            AISummaryAction.DURATION_MIN_UNSUPPORT_ZERO -> ErrorResult(
                errorType = ErrorType.FILE_ERROR,
                userMessage = getStringResource(R.string.summary_error_record_format_not_support),
                logMessage = "File format not supported: errorCode=$errorCode, msg=$errorMsg",
                shouldRetry = false
            )
            
            AISummaryAction.FILE_SIZE_MAX_UNSUPPORT -> ErrorResult(
                errorType = ErrorType.FILE_ERROR,
                userMessage = getStringResource(R.string.toast_summary_error_size_over),
                logMessage = "File size too large: $errorMsg",
                shouldRetry = false
            )
            
            AISummaryAction.DURATION_MAX_UNSUPPORT -> ErrorResult(
                errorType = ErrorType.FILE_ERROR,
                userMessage = getStringResource(R.plurals.summary_error_record_long),
                logMessage = "File duration too long: $errorMsg",
                shouldRetry = false
            )
            
            AISummaryAction.PLUGIN_INIT_ERROR -> ErrorResult(
                errorType = ErrorType.SERVER_ERROR,
                userMessage = "插件初始化失败",
                logMessage = "Plugin initialization failed: $errorMsg",
                shouldRetry = true
            )
            
            else -> ErrorResult(
                errorType = ErrorType.UNKNOWN_ERROR,
                userMessage = errorMsg ?: "生成摘要失败，错误码：$errorCode",
                logMessage = "Unknown error: errorCode=$errorCode, msg=$errorMsg",
                shouldRetry = false
            )
        }
    }
    
    /**
     * 处理错误并显示Toast
     * @param errorCode 错误码
     * @param errorMsg 错误消息
     * @param context 上下文，为null时使用Application上下文
     * @return 错误处理结果
     */
    fun handleErrorWithToast(
        errorCode: Int, 
        errorMsg: String?, 
        context: Context? = null
    ): ErrorResult {
        val result = handleError(errorCode, errorMsg)
        
        if (result.shouldShowToast) {
            val ctx = context ?: BaseApplication.getAppContext()
            ToastManager.showShortToast(ctx, result.userMessage)
        }
        
        return result
    }
    
    /**
     * 获取用户友好的错误消息
     * @param errorCode 错误码
     * @param errorMsg 错误消息
     * @return 用户友好的错误消息
     */
    fun getUserMessage(errorCode: Int, errorMsg: String?): String {
        return handleError(errorCode, errorMsg).userMessage
    }
    
    /**
     * 检查错误是否可以重试
     * @param errorCode 错误码
     * @return 是否可以重试
     */
    fun canRetry(errorCode: Int): Boolean {
        return handleError(errorCode, null).shouldRetry
    }
    
    /**
     * 获取错误类型
     * @param errorCode 错误码
     * @return 错误类型
     */
    fun getErrorType(errorCode: Int): ErrorType {
        return handleError(errorCode, null).errorType
    }
    
    /**
     * 安全获取字符串资源
     */
    private fun getStringResource(resId: Int): String {
        return try {
            BaseApplication.getAppContext().getString(resId)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getStringResource: failed to get string resource $resId, error=${e.message}")
            "获取错误信息失败"
        }
    }
    
    /**
     * 记录错误日志
     * @param mediaId 录音ID
     * @param errorCode 错误码
     * @param errorMsg 错误消息
     * @param additionalInfo 附加信息
     */
    fun logError(
        mediaId: Long, 
        errorCode: Int, 
        errorMsg: String?, 
        additionalInfo: String? = null
    ) {
        val result = handleError(errorCode, errorMsg)
        val logMsg = buildString {
            append("AI Summary Error - ")
            append("MediaId: $mediaId, ")
            append("Type: ${result.errorType}, ")
            append("Code: $errorCode, ")
            append("Message: ${result.logMessage}")
            additionalInfo?.let { append(", Additional: $it") }
        }
        DebugUtil.e(TAG, logMsg)
    }
}
