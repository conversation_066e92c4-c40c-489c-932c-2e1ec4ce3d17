/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: IAISummaryRunnable
 * Description:
 * Version: 1.0
 * Date: 2025/5/13
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/13      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.convertservice.aisummary

import com.soundrecorder.modulerouter.aisummary.IAISummaryCallback

interface IAISummaryRunnable {

    fun registerAISummaryUiCallback(uiCallback: IAISummaryCallback?)

    fun registerProgressCallback(progressCallback: IAISummaryProgressCallback?)

    fun startAISummary()

    fun cancelAISummary()

    fun unregisterAISummaryUiCallback()

    fun release()
}