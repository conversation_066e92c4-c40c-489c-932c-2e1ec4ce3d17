/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RequestGetUploadResult
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.bean

class RequestGetUploadResult(
    val uploadId: String?,
    val key: String,
    val abortFlag: Boolean = false,
    val partETags: List<ETags>?
) {
    class ETags(val partNumber: Int, val etag: String)
}


