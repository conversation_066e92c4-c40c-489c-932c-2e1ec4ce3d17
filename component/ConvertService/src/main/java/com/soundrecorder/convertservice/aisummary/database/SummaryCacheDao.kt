/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: SummaryCacheDao
 * Description:
 * Version: 1.0
 * Date: 2025/5/12
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/12      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.convertservice.aisummary.database

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update

@Dao
interface SummaryCacheDao {

    @Insert(
        entity = SummaryCacheEntity::class,
        onConflict = OnConflictStrategy.REPLACE
    )
    fun insert(item: SummaryCacheRowContent): Long

    @Insert(
        entity = SummaryCacheEntity::class,
        onConflict = OnConflictStrategy.REPLACE
    )
    fun insert(items: Collection<SummaryCacheRowContent>): LongArray

    @Update(onConflict = OnConflictStrategy.REPLACE)
    fun update(item: SummaryCacheEntity): Int

    @Update(onConflict = OnConflictStrategy.REPLACE)
    fun update(items: Collection<SummaryCacheEntity>): Int

    @Delete
    fun delete(item: SummaryCacheEntity): Int

    @Delete
    fun delete(items: Collection<SummaryCacheEntity>): Int

    @Query("SELECT COUNT(*) FROM $SUMMARY_CACHE_DATA_TABLE_NAME")
    fun count(): Long

    @Query("SELECT * FROM $SUMMARY_CACHE_DATA_TABLE_NAME")
    fun query(): List<SummaryCacheEntity>

    @Query("SELECT * FROM $SUMMARY_CACHE_DATA_TABLE_NAME LIMIT :limit OFFSET :offset")
    fun query(limit: Long, offset: Long): List<SummaryCacheEntity>

    @Query("SELECT * FROM $SUMMARY_CACHE_DATA_TABLE_NAME WHERE $SUMMARY_CACHE_DATA_COLUMN_PATH = :filePath")
    fun queryByFilePath(filePath: String): List<SummaryCacheEntity>

    @Query("SELECT * FROM $SUMMARY_CACHE_DATA_TABLE_NAME WHERE $SUMMARY_CACHE_DATA_COLUMN_PATH IN (:filePathList)")
    fun queryByFilePathList(filePathList: Collection<String>): List<SummaryCacheEntity>

    /**
     * 按时间戳排序查询指定文件路径的摘要缓存（最新的在前）
     */
    @Query(
        "SELECT * FROM $SUMMARY_CACHE_DATA_TABLE_NAME " +
                "WHERE $SUMMARY_CACHE_DATA_COLUMN_PATH = :filePath ORDER BY $SUMMARY_CACHE_DATA_COLUMN_TIME_STAMP DESC"
    )
    fun queryByFilePathOrderByTimeDesc(filePath: String): List<SummaryCacheEntity>

    /**
     * 查询指定文件路径的摘要数量
     */
    @Query("SELECT COUNT(*) FROM $SUMMARY_CACHE_DATA_TABLE_NAME WHERE $SUMMARY_CACHE_DATA_COLUMN_PATH = :filePath")
    fun countByFilePath(filePath: String): Long

    /**
     * 删除指定文件路径的最旧的摘要记录
     */
    @Query(
        "DELETE FROM $SUMMARY_CACHE_DATA_TABLE_NAME WHERE $SUMMARY_CACHE_DATA_COLUMN_ID IN (SELECT $SUMMARY_CACHE_DATA_COLUMN_ID " +
                "FROM $SUMMARY_CACHE_DATA_TABLE_NAME WHERE $SUMMARY_CACHE_DATA_COLUMN_PATH = :filePath " +
                "ORDER BY $SUMMARY_CACHE_DATA_COLUMN_TIME_STAMP ASC LIMIT :count)"
    )
    fun deleteOldestByFilePath(filePath: String, count: Int): Int
}