/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ISmartNameRunnable
 * * Description: ISmartNameRunnable
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.convertservice.smartname

import com.soundrecorder.modulerouter.smartname.ISmartNameCallback

interface ISmartNameRunnable {

    fun registerSmartNameUiCallback(convertUiCallback: ISmartNameCallback?)
    fun registerProgressCallback(progressCallback: ISmartNameProgressCallback?)
    fun startSmartName()
    fun cancelSmartName()
    fun unregisterSmartNameUiCallback()
    fun release()
}