/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryRunnable
 * Description: 管理录音摘要Task
 * Version: 1.0
 * Date: 2025/5/14
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/14      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.convertservice.aisummary

import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.AISummaryRecordParam
import com.soundrecorder.modulerouter.aisummary.IAISummaryCallback

class AISummaryRunnable(val mediaId: Long, var params: AISummaryRecordParam?) : IAISummaryRunnable {

    companion object {
        private const val TAG = "AISummaryRunnable"
    }

    //录音摘要进程 实例化
    private var mAISummaryProcess: AISummaryProcess? = null

    //录音摘要 UI回调函数接口
    var mAISummaryUiCallback: IAISummaryCallback? = null

    //录音摘要 进程回调函数接口
    private var runnableProgressCallback: IAISummaryProgressCallback? = null

    override fun registerAISummaryUiCallback(uiCallback: IAISummaryCallback?) {
        mAISummaryUiCallback = uiCallback
    }

    override fun registerProgressCallback(progressCallback: IAISummaryProgressCallback?) {
        runnableProgressCallback = progressCallback
    }

    override fun startAISummary() {
        mAISummaryProcess = AISummaryProcess(mediaId, mAISummaryUiCallback, params)
        mAISummaryProcess?.registerNameProgressCallback(runnableProgressCallback)
        mAISummaryProcess?.initHandlerThread()
        mAISummaryProcess?.doStartAISummary()
    }

    override fun cancelAISummary() {
        doCancel()
        postCancelConvert()
    }

    /**
     * real cancel AISummary process
     */
    private fun doCancel() {
        mAISummaryProcess?.cancel()
    }

    private fun postCancelConvert() {
        DebugUtil.i(TAG, "postCancelConvert $mediaId")
        runnableProgressCallback?.postCancelAISummary(mediaId)
    }

    override fun unregisterAISummaryUiCallback() {
        mAISummaryUiCallback = null
        mAISummaryProcess?.callback = null
        mAISummaryProcess?.mProgressCallback = null
    }

    override fun release() {
        cancelAISummary()
        unregisterAISummaryUiCallback()
    }
}