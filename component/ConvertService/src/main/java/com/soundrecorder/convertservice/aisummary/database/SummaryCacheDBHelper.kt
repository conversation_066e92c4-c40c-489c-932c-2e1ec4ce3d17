/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: SummaryCacheDBHelper
 * Description:
 * Version: 1.0
 * Date: 2025/5/12
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/12      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.convertservice.aisummary.database

import android.content.Context
import androidx.annotation.VisibleForTesting
import com.soundrecorder.base.utils.DebugUtil

open class SummaryCacheDBHelper(private val context: Context) {
    companion object {
        private const val TAG = "SummaryCacheDBHelper"
        private const val MAX_COUNT: Int = 5
    }

    private val _summaryCacheDao by lazy { SummaryCacheDatabase.getInstance(context).summaryCacheDao() }

    @VisibleForTesting
    private val summaryCacheDao: SummaryCacheDao
        get() = _summaryCacheDao

    fun getAllSummaryCacheList(): Collection<SummaryCacheEntity> = kotlin.runCatching {
        summaryCacheDao.query()
    }.onFailure {
        DebugUtil.e(TAG, "getAllSummaryCacheList: ERROR! $it")
    }.getOrDefault(emptyList())

    /**
     * 添加单个摘要缓存
     * @param entity 摘要缓存行内容
     * @return Boolean 添加是否成功
     */
    fun addSummaryCache(entity: SummaryCacheRowContent): Boolean = kotlin.runCatching {
        DebugUtil.d(TAG, "addSummaryCache ${entity.filePath} ${entity.timeStamp} ${entity.chooseState} ${entity.recordType} ${entity.summaryStyle}")
        val insertId = summaryCacheDao.insert(entity)
        insertId > 0
    }.onFailure {
        DebugUtil.e(TAG, "addSummaryCache: ERROR! $it")
    }.getOrDefault(false)

    /**
     * 批量添加摘要缓存
     * @param entity 摘要缓存行内容列表
     * @return Boolean 批量添加是否成功（所有记录都成功插入）
     */
    fun addSummaryCache(entity: List<SummaryCacheRowContent>): Boolean = kotlin.runCatching {
        DebugUtil.d(TAG, "addSummaryCache batch: ${entity.size} items")
        val insertIds = summaryCacheDao.insert(entity)
        val successCount = insertIds.count { it > 0 }
        val allSuccess = successCount == entity.size
        DebugUtil.d(TAG, "addSummaryCache batch result: $successCount/${entity.size} success, allSuccess=$allSuccess")
        allSuccess
    }.onFailure {
        DebugUtil.e(TAG, "addSummaryCache batch: ERROR! $it")
    }.getOrDefault(false)

    /**
     * 通过文件路径查询摘要缓存
     */
    fun getSummaryCacheByFilePath(filePath: String): List<SummaryCacheEntity> = kotlin.runCatching {
        DebugUtil.d(TAG, "getSummaryCacheByFilePath: $filePath")
        summaryCacheDao.queryByFilePath(filePath)
    }.onFailure {
        DebugUtil.e(TAG, "getSummaryCacheByFilePath: ERROR! $it")
    }.getOrDefault(emptyList())

    /**
     * 更新摘要缓存
     */
    fun updateSummaryCache(entity: SummaryCacheEntity): Boolean = kotlin.runCatching {
        DebugUtil.d(TAG, "updateSummaryCache: ${entity.filePath}")
        val result = summaryCacheDao.update(entity)
        result > 0
    }.onFailure {
        DebugUtil.e(TAG, "updateSummaryCache: ERROR! $it")
    }.getOrDefault(false)

    /**
     * 批量更新摘要缓存
     */
    fun updateSummaryCache(entities: Collection<SummaryCacheEntity>): Boolean = kotlin.runCatching {
        DebugUtil.d(TAG, "updateSummaryCache: ${entities.size} items")
        val result = summaryCacheDao.update(entities)
        result > 0
    }.onFailure {
        DebugUtil.e(TAG, "updateSummaryCache: ERROR! $it")
    }.getOrDefault(false)

    /**
     * 删除摘要缓存
     */
    fun deleteSummaryCache(entity: SummaryCacheEntity): Boolean = kotlin.runCatching {
        DebugUtil.d(TAG, "deleteSummaryCache: ${entity.filePath}")
        val result = summaryCacheDao.delete(entity)
        result > 0
    }.onFailure {
        DebugUtil.e(TAG, "deleteSummaryCache: ERROR! $it")
    }.getOrDefault(false)

    /**
     * 批量删除摘要缓存
     */
    fun deleteSummaryCache(entities: Collection<SummaryCacheEntity>): Boolean = kotlin.runCatching {
        DebugUtil.d(TAG, "deleteSummaryCache: ${entities.size} items")
        val result = summaryCacheDao.delete(entities)
        result > 0
    }.onFailure {
        DebugUtil.e(TAG, "deleteSummaryCache: ERROR! $it")
    }.getOrDefault(false)

    /**
     * 通过文件路径删除摘要缓存
     */
    fun deleteSummaryCacheByFilePath(filePath: String): Boolean = kotlin.runCatching {
        DebugUtil.d(TAG, "deleteSummaryCacheByFilePath: $filePath")
        val entities = getSummaryCacheByFilePath(filePath)
        if (entities.isNotEmpty()) {
            val result = summaryCacheDao.delete(entities)
            result > 0
        } else {
            false
        }
    }.onFailure {
        DebugUtil.e(TAG, "deleteSummaryCacheByFilePath: ERROR! $it")
    }.getOrDefault(false)

    /**
     * 获取摘要缓存数量
     */
    fun getSummaryCacheCount(): Long = kotlin.runCatching {
        summaryCacheDao.count()
    }.onFailure {
        DebugUtil.e(TAG, "getSummaryCacheCount: ERROR! $it")
    }.getOrDefault(0L)

    /**
     * 分页获取摘要缓存
     */
    fun getSummaryCacheByPage(limit: Long, offset: Long): List<SummaryCacheEntity> = kotlin.runCatching {
        DebugUtil.d(TAG, "getSummaryCacheByPage: limit=$limit, offset=$offset")
        summaryCacheDao.query(limit, offset)
    }.onFailure {
        DebugUtil.e(TAG, "getSummaryCacheByPage: ERROR! $it")
    }.getOrDefault(emptyList())

    /**
     * 批量获取摘要缓存
     */
    fun getSummaryCacheByFilePathList(filePathList: Collection<String>): List<SummaryCacheEntity> = kotlin.runCatching {
        DebugUtil.d(TAG, "getSummaryCacheByFilePathList: ${filePathList.size} paths")
        summaryCacheDao.queryByFilePathList(filePathList)
    }.onFailure {
        DebugUtil.e(TAG, "getSummaryCacheByFilePathList: ERROR! $it")
    }.getOrDefault(emptyList())

    /**
     * 检查文件路径是否存在摘要
     */
    fun hasSummaryByFilePath(filePath: String): Boolean = kotlin.runCatching {
        val entities = getSummaryCacheByFilePath(filePath)
        entities.isNotEmpty() && entities.any { !it.summaryContent.isNullOrEmpty() }
    }.onFailure {
        DebugUtil.e(TAG, "hasSummaryByFilePath: ERROR! $it")
    }.getOrDefault(false)

    /**
     * 清理空摘要内容的记录
     */
    fun cleanupEmptySummaries(): Int = kotlin.runCatching {
        DebugUtil.d(TAG, "cleanupEmptySummaries")
        val allEntities = getAllSummaryCacheList()
        val emptyEntities = allEntities.filter { it.summaryContent.isNullOrEmpty() }

        if (emptyEntities.isNotEmpty()) {
            val deleted = summaryCacheDao.delete(emptyEntities)
            DebugUtil.d(TAG, "cleanupEmptySummaries: deleted $deleted empty summaries")
            deleted
        } else {
            0
        }
    }.onFailure {
        DebugUtil.e(TAG, "cleanupEmptySummaries: ERROR! $it")
    }.getOrDefault(0)

    /**
     * 根据时间范围清理摘要
     */
    fun cleanupSummariesByTimeRange(startTime: Long, endTime: Long): Int = kotlin.runCatching {
        DebugUtil.d(TAG, "cleanupSummariesByTimeRange: $startTime - $endTime")
        val allEntities = getAllSummaryCacheList()
        val targetEntities = allEntities.filter {
            it.timeStamp in startTime..endTime
        }

        if (targetEntities.isNotEmpty()) {
            val deleted = summaryCacheDao.delete(targetEntities)
            DebugUtil.d(TAG, "cleanupSummariesByTimeRange: deleted $deleted summaries")
            deleted
        } else {
            0
        }
    }.onFailure {
        DebugUtil.e(TAG, "cleanupSummariesByTimeRange: ERROR! $it")
    }.getOrDefault(0)

    /**
     * 获取最近的摘要
     */
    fun getRecentSummaries(limit: Int = 10): List<SummaryCacheEntity> = kotlin.runCatching {
        DebugUtil.d(TAG, "getRecentSummaries: limit=$limit")
        val allEntities = getAllSummaryCacheList()
        allEntities.filter { !it.summaryContent.isNullOrEmpty() }
            .sortedByDescending { it.timeStamp }
            .take(limit)
    }.onFailure {
        DebugUtil.e(TAG, "getRecentSummaries: ERROR! $it")
    }.getOrDefault(emptyList())

    /**
     * 根据摘要风格获取摘要
     */
    fun getSummariesByStyle(summaryStyle: Int): List<SummaryCacheEntity> = kotlin.runCatching {
        DebugUtil.d(TAG, "getSummariesByStyle: style=$summaryStyle")
        val allEntities = getAllSummaryCacheList()
        allEntities.filter {
            it.summaryStyle == summaryStyle && !it.summaryContent.isNullOrEmpty()
        }
    }.onFailure {
        DebugUtil.e(TAG, "getSummariesByStyle: ERROR! $it")
    }.getOrDefault(emptyList())

    /**
     * 按时间戳排序查询指定文件路径的摘要缓存（最新的在前）
     */
    fun getSummaryCacheByFilePathOrderByTime(filePath: String): List<SummaryCacheEntity> = kotlin.runCatching {
        DebugUtil.d(TAG, "getSummaryCacheByFilePathOrderByTime: $filePath")
        summaryCacheDao.queryByFilePathOrderByTimeDesc(filePath)
    }.onFailure {
        DebugUtil.e(TAG, "getSummaryCacheByFilePathOrderByTime: ERROR! $it")
    }.getOrDefault(emptyList())

    /**
     * 查询指定文件路径的摘要数量
     */
    fun getSummaryCacheCountByFilePath(filePath: String): Long = kotlin.runCatching {
        DebugUtil.d(TAG, "getSummaryCacheCountByFilePath: $filePath")
        summaryCacheDao.countByFilePath(filePath)
    }.onFailure {
        DebugUtil.e(TAG, "getSummaryCacheCountByFilePath: ERROR! $it")
    }.getOrDefault(0L)

    /**
     * 删除指定文件路径的最旧的摘要记录
     */
    fun deleteOldestSummariesByFilePath(filePath: String, count: Int): Int = kotlin.runCatching {
        DebugUtil.d(TAG, "deleteOldestSummariesByFilePath: $filePath, count=$count")
        val deletedCount = summaryCacheDao.deleteOldestByFilePath(filePath, count)
        DebugUtil.d(TAG, "deleteOldestSummariesByFilePath: deleted $deletedCount records")
        deletedCount
    }.onFailure {
        DebugUtil.e(TAG, "deleteOldestSummariesByFilePath: ERROR! $it")
    }.getOrDefault(0)

    /**
     * 为单个文件维护最大摘要数量限制
     * @param filePath 文件路径
     * @param maxCount 最大保留数量
     * @return 删除的记录数量
     */
    fun maintainMaxSummaryCount(filePath: String, maxCount: Int = MAX_COUNT): Int = kotlin.runCatching {
        DebugUtil.d(TAG, "maintainMaxSummaryCount: $filePath, maxCount=$maxCount")
        val currentCount = getSummaryCacheCountByFilePath(filePath)
        if (currentCount > maxCount) {
            val deleteCount = (currentCount - maxCount).toInt()
            val deletedCount = deleteOldestSummariesByFilePath(filePath, deleteCount)
            DebugUtil.d(TAG, "maintainMaxSummaryCount: currentCount=$currentCount, deleted=$deletedCount")
            deletedCount
        } else {
            DebugUtil.d(TAG, "maintainMaxSummaryCount: currentCount=$currentCount, no need to delete")
            0
        }
    }.onFailure {
        DebugUtil.e(TAG, "maintainMaxSummaryCount: ERROR! $it")
    }.getOrDefault(0)
}
