/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  SmartNameApi
 * * Description: SmartNameApi
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.convertservice.smartname

import android.content.Context
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.common.buryingpoint.SmartNameStatisticsUtil
import com.soundrecorder.common.databean.SmartNameParam
import com.soundrecorder.common.dialog.AIUnitDialog
import com.soundrecorder.convertservice.ConvertSupportManager
import com.soundrecorder.modulerouter.smartname.ISmartNameCallback
import com.soundrecorder.modulerouter.smartname.IUnifiedSummaryCallBack
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.translate.AIAsrManager

@Component(SmartNameAction.COMPONENT_NAME)
object SmartNameApi {

    private const val TAG = "SmartNameApi"

    @Action(SmartNameAction.NEW_UNIFIED_SUMMARY_MANAGER)
    @JvmStatic
    fun newUnifiedSummaryManager(): IUnifiedSummaryCallBack {
        return UnifiedSummaryManager()
    }

    /**
     * 是否支持智能标题能力
     * @param supportConvert 是否支持转文本
     * 若 supportConvert = null 则返回智能标题能力支持
     * 若 supportConvert ！=null 则返回智能标题能力+是否支持转文本；
     * 由于智能标题依赖转文本，若不支持转文本：则不支持智能标题
     *                      若支持转文本：则获取实际智能标题能力
     */
    @Action(SmartNameAction.ACTION_CHECK_SUPPORT_SMART_NAME)
    @JvmStatic
    fun checkSupportSmartName(context: Context?, supportConvert: Boolean? = null, forceUpdate: Boolean = false): Boolean {
        if (context == null) {
            return false
        }
        if (BaseUtil.isRealme()) {
            return false
        }
        if (supportConvert == false) {
            //不支持转文本，智能命名也不能支持
            DebugUtil.d(TAG, "checkSupportSmartName convert_disable")
            return false
        }
        return OS12FeatureUtil.isColorOS16OrLater() && AIAsrManager.isSupportSmartName(context, forceUpdate)
    }

    @Action(SmartNameAction.ACTION_START_CONVERT_OR_SMART_NAME)
    @JvmStatic
    fun startSmartName(mediaId: Long): Boolean {
        return SmartNameTaskManager.startSmartName(mediaId, null)
    }

    @Action(SmartNameAction.ACTION_START_SMART_NAME)
    @JvmStatic
    fun startSmartName(mediaId: Long, jsonParams: String?): Boolean {
        val params = GsonUtil.fromJson(jsonParams, SmartNameParam::class.java)
        return SmartNameTaskManager.startSmartName(mediaId, params)
    }

    @Action(SmartNameAction.ACTION_START_SMART_NAME_V2)
    @JvmStatic
    fun startSmartName(mediaId: Long, param: SmartNameParam?): Boolean {
        return SmartNameTaskManager.startSmartName(mediaId, param)
    }

    @Action(SmartNameAction.ACTION_CHECK_HAS_TASK_RUNNING)
    @JvmStatic
    fun checkHasTaskRunning(): Boolean {
        return SmartNameTaskManager.checkHasTaskRunning()
    }

    @Action(SmartNameAction.ACTION_CHECK_IS_TASK_RUNNING)
    @JvmStatic
    fun checkIsTaskRunning(mediaId: Long): Boolean {
        return SmartNameTaskManager.checkIsTaskRunning(mediaId)
    }

    @Action(SmartNameAction.ACTION_REGISTER_SMART_NAME_CALLBACK)
    @JvmStatic
    fun registerCallback(mediaId: Long, callback: ISmartNameCallback) {
        SmartNameTaskManager.registerSmartNameCallback(mediaId = mediaId, callback)
    }

    @Action(SmartNameAction.ACTION_UN_REGISTER_SMART_NAME_CALLBACK)
    @JvmStatic
    fun unRegisterCallback(mediaId: Long, callback: ISmartNameCallback) {
        SmartNameTaskManager.unregisterSmartNameCallback(mediaId = mediaId, callback)
    }

    @Action(SmartNameAction.ACTION_CANCEL_SMART_NAME_TASK)
    @JvmStatic
    fun cancelSmartNameTask(mediaId: Long) {
        SmartNameTaskManager.cancelSmartName(mediaId)
    }

    @Action(SmartNameAction.ACTION_FROM_JSON)
    @JvmStatic
    fun <T> fromJson(json: String?, cls: Class<T>): T? {
        return GsonUtil.fromJson(json, cls)
    }

    @Action(SmartNameAction.ACTION_SET_SMART_NAME_SWITCH_STATUS)
    @JvmStatic
    fun setSmartNameSwitchStatus(mContext: Context, isOpen: Boolean, needStatistics: Boolean = true) {
        val switchValue = if (isOpen) {
            AIUnitDialog.SMART_NAME_SWITCH_OPEN
        } else {
            AIUnitDialog.SMART_NAME_SWITCH_CLOSE
        }
        PrefUtil.putInt(
            mContext,
            PrefUtil.KEY_SMART_NAME_SWITCH_OPEN,
            switchValue
        )
        if (needStatistics) {
            //只有在点击开启、插件下载完成时才进行埋点，后续插件是否下载的检测不进行埋点
            SmartNameStatisticsUtil.addSmartNameSwitchStateEvent(switchValue)
        }
    }

    @Action(SmartNameAction.ACTION_IS_SMART_NAME_SWITCH_OPEN)
    @JvmStatic
    fun isSmartNameSwitchOpen(mContext: Context): Boolean {
        val switchValue = smartNameSwitchValue(mContext)
        DebugUtil.d(TAG, "isSmartNameSwitchOpen, switchValue:$switchValue")
        return OS12FeatureUtil.isColorOS16OrLater() && (switchValue == AIUnitDialog.SMART_NAME_SWITCH_OPEN)
    }

    @Action(SmartNameAction.ACTION_SMART_NAME_SWITCH_VALUE)
    @JvmStatic
    fun smartNameSwitchValue(mContext: Context): Int {
        return PrefUtil.getInt(mContext, PrefUtil.KEY_SMART_NAME_SWITCH_OPEN, AIUnitDialog.SMART_NAME_SWITCH_INVALID)
    }

    @Action(SmartNameAction.ACTION_NEED_SHOW_SMART_GUIDE_DIALOG)
    @JvmStatic
    fun needShowSmartGuideDialog(mContext: Context): Boolean {
        val needShow = !PrefUtil.getBoolean(mContext, PrefUtil.KEY_SMART_NAME_GUIDE_SHOW, false)
        if (!needShow) {
            return false
        }
        /*判断是否支持智能标题含转文本*/
        return checkSupportSmartName(mContext, ConvertSupportManager.isSupportConvert())
    }

    @Action(SmartNameAction.ACTION_CLEAR_SMART_RETRY_CACHE_TASK)
    @JvmStatic
    fun clearSmartRetryCacheTask() {
        SmartRetryCacheTask.clear()
    }

    @Action(SmartNameAction.ACTION_CHECK_TASK_RUNNING_MAX_LIMIT_SIZE)
    @JvmStatic
    fun checkTaskRunningMaxLimitSize(): Boolean {
        return SmartNameTaskManager.checkTaskRunningMaxLimitSize()
    }

    @Action(SmartNameAction.ACTION_RELEASE_ALL_TASK)
    @JvmStatic
    fun releaseAllTask() {
        SmartNameTaskManager.releaseAll()
    }
}