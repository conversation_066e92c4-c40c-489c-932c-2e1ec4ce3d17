/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: InitSummarySDKCallback
 * Description:
 * Version: 1.0
 * Date: 2025/5/27
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/27      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.convertservice.aisummary

import com.oplus.unified.summary.sdk.UnifiedSummaryKit

interface InitSummarySDKCallback {
    fun initSummaryKitSuccess(summaryKit: UnifiedSummaryKit?, isDelay: Boolean = false)
    fun initSummaryKitError(sessionId: String, errorCode: Int, errorMsg: String?)
}