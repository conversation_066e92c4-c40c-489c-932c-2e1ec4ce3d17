/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertTaskThreadManager
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.convert

import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.databean.ConvertStatus
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.SeedlingAction
import com.soundrecorder.common.databean.BeanConvertText
import com.soundrecorder.modulerouter.playback.PlaybackAction
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

object ConvertTaskThreadManager : IConvertProcess {

    const val TAG = "ConvertTaskThreadManager"
    const val LIMIT_SIZE = 3

    const val DEFAULT_NULL = 0
    const val ALREADY_RUNNING = 1
    const val OVER_LIMIT = 2
    const val CAN_ADD_NEW = 3

    var taskMaps: ConcurrentHashMap<Long, IConvertTextRunnable> = ConcurrentHashMap(LIMIT_SIZE)
    var mUICallbacks: ConcurrentHashMap<Long, CopyOnWriteArrayList<IConvertCallback>> = ConcurrentHashMap(LIMIT_SIZE)
    var jobManagerLifeCycleCallback: IJobManagerLifeCycleCallback? = null

    object RunnableProgress : IConvertTextRunnableProgress {
        override fun preStartConvert(mediaId: Long): Unit = Unit

        override fun preCancelConvert(mediaId: Long): Unit = Unit

        override fun postConvertEnd(mediaId: Long, convertAiTitle: Boolean) {
            taskMaps.remove(mediaId)
            checkFinalTaskEnd(mediaId)
            if (!convertAiTitle) {
                sendBroadcastNotifyConvertStatus(mediaId, false)
            }
        }

        override fun postCancelConvert(mediaId: Long, convertAiTitle: Boolean) {
            checkFinalTaskEnd(mediaId)
            if (convertAiTitle) {
                SmartNameAction.cancelSmartNameTask(mediaId)
            }
            sendBroadcastNotifyConvertStatus(mediaId, false)
        }
    }

    private fun sendBroadcastNotifyConvertStatus(mediaId: Long, isConvert: Boolean = true) {
        //send broadcast notify browseFile refresh list convert status
        DebugUtil.d(TAG, "sendBroadcastNotifyConvertStatus isConvert: $isConvert")
        val intent = Intent(PlaybackAction.NOTIFY_CONVERT_STATUS_UPDATE)
        intent.putExtra(PlaybackAction.KEY_NOTIFY_RECORD_ID, mediaId)
        intent.putExtra(PlaybackAction.KEY_NOTIFY_CONVERT_STATUS, isConvert)
        LocalBroadcastManager.getInstance(BaseApplication.getAppContext()).sendBroadcast(intent)
    }

    private fun startOrResumeTask(mediaId: Long, convertAbilityType: Int, convertAiTitle: Boolean): Boolean {
        val result = when (checkCanAddNewTask(mediaId)) {
            ALREADY_RUNNING -> {
                DebugUtil.i(TAG, "maps already contains mediaId: $mediaId, no need to add task again")
                true
            }
            OVER_LIMIT -> {
                DebugUtil.i(TAG, "maps limit reached : runnable $mediaId, not add to manager")
                false
            }
            else -> {
                val convertRunnable = ConvertRunnableFactory.getRunnable(mediaId, convertAbilityType, convertAiTitle)
                if (convertRunnable == null) {
                    DebugUtil.i(TAG, "maps not contains,but convert not support,type is $convertAbilityType")
                    return false
                }
                convertRunnable.registerConvertUiCallback(getConvertUiCallback())
                convertRunnable.registerProgressCallback(RunnableProgress)
                taskMaps[mediaId] = convertRunnable
                convertRunnable.startConvert()
                DebugUtil.i(TAG, "convertRunnable $mediaId start run")
                sendBroadcastNotifyConvertStatus(mediaId)
                true
            }
        }
        return result
    }

    private fun getConvertUiCallback(): IConvertCallback {
        return object : IConvertCallback {
            override fun onConvertStatusChange(
                mediaId: Long,
                convertStatus: ConvertStatus,
                errorCode: Int,
                errorMessage: String,
                convertAiTitle: Boolean
            ) {
                mUICallbacks[mediaId]?.forEach {
                    it.onConvertStatusChange(mediaId, convertStatus, errorCode, errorMessage, convertAiTitle)
                }
                SeedlingAction.onConvertStatusChange(mediaId, convertStatus.uploadStatus, convertStatus.convertStatus, errorMessage)
            }

            override fun onConvertTextReceived(
                mediaId: Long,
                convertType: Int,
                convertTextResult: BeanConvertText,
                showSwitch: Boolean,
                convertAiTitle: Boolean
            ) {
                mUICallbacks[mediaId]?.forEach {
                    it.onConvertTextReceived(mediaId, convertType, convertTextResult, showSwitch, convertAiTitle)
                }
                SeedlingAction.onConvertTextReceived(mediaId)
            }

            override fun onConvertProgressChanged(mediaId: Long, uploadProgress: Int, convertProgress: Int, serverPlanCode: Int) {
                mUICallbacks[mediaId]?.forEach {
                    it.onConvertProgressChanged(mediaId, uploadProgress, convertProgress, serverPlanCode)
                }
                SeedlingAction.onConvertProgressChanged(mediaId, uploadProgress, convertProgress, serverPlanCode)
            }

            override fun onConvertEnd(mediaId: Long) {
                mUICallbacks[mediaId]?.forEach {
                    it.onConvertEnd(mediaId)
                }
            }
        }
    }

    private fun cancelTask(mediaId: Long): Boolean {
        val result = if (!taskMaps.containsKey(mediaId)) {
            DebugUtil.i(TAG, "maps not contains mediaId: $mediaId, no need to cancel")
            false
        } else {
            taskMaps[mediaId]?.cancelWork()
            /*
             * 异常情况处理：转文本中断后，卡片转写状态不更新。
             * 复现路径：点击小卡开始转文本 -> 进入录音 -> 删除正在转写的录音 ->进入回收站，恢复录音 ->回到桌面查看卡片状态
             */
            val convertStatus = getCurrentConvertStatus(mediaId)
            if (convertStatus != null) {
                DebugUtil.i(
                    TAG,
                    "cancelTask mediaId: $mediaId, force to reset seedling card state to CONVERT_STATUS_ABORTTASK_SUC"
                )
                SeedlingAction.onConvertStatusChange(
                    mediaId,
                    convertStatus.uploadStatus,
                    ConvertStatus.CONVERT_STATUS_ABORTTASK_SUC,
                    ""
                )
            }
            true
        }
        return result
    }

    /*
     * 取消全部转文本任务
     */
    fun cancelAllTask() {
        taskMaps.forEach { (_, value) ->
            value.cancelWork()
        }
    }

    fun checkIsTaskRunning(mediaId: Long): Boolean {
        return taskMaps.containsKey(mediaId)
    }

    fun checkNoTaskRunning(): Boolean {
        DebugUtil.i(TAG, "checkNoTaskRunning = ${taskMaps.isEmpty()}")
        return taskMaps.isEmpty()
    }


    fun release(mediaId: Long) {
        DebugUtil.i(TAG, "release: $mediaId")
        taskMaps[mediaId]?.release()
    }


    fun getCurrentConvertStatus(mediaId: Long): ConvertStatus? {
        return taskMaps[mediaId]?.getCurrentConvertStatus()
    }

    fun checkFinalTaskEnd(mediaId: Long) {
        if (taskMaps.isEmpty()) {
            DebugUtil.i(TAG, "checkFinalTaskEnd: onFinalJobEnd $mediaId")
            jobManagerLifeCycleCallback?.onFinalJobEnd(mediaId)
        }
    }

    fun checkCanAddNewTask(mediaId: Long): Int {
        if (taskMaps.containsKey(mediaId)) {
            DebugUtil.i(TAG, "checkCanAddNewTask: $mediaId already running, no need to ")
            return ALREADY_RUNNING
        } else if (taskMaps.size >= LIMIT_SIZE) {
            DebugUtil.i(TAG, "maps limit reached : runnable $mediaId, can not add")
            return OVER_LIMIT
        } else {
            return CAN_ADD_NEW
        }
    }


    fun releaseAll() {
        DebugUtil.i(TAG, "release all")
        mUICallbacks.clear()
        for (mediaId in taskMaps.keys) {
            release(mediaId)
            /*异常情况处理：转文本中断后，卡片转写状态不更新。
             复现路径：点击小卡开始转文本 -> 进入录音 -> 取消转文本 -> 停留在转写界面
             -> 回到桌面-> 再点击小卡开始转文本 -> 进入录音 -> 在转写界面按返回键
             */
            val convertStatus = getCurrentConvertStatus(mediaId)
            if (convertStatus != null) {
                DebugUtil.i(
                    TAG,
                    "release all --> mediaId: $mediaId, force to reset seedling card state to CONVERT_STATUS_ABORTTASK_SUC"
                )
                SeedlingAction.onConvertStatusChange(
                    mediaId,
                    convertStatus.uploadStatus,
                    ConvertStatus.CONVERT_STATUS_ABORTTASK_SUC,
                    ""
                )
            }
        }
        jobManagerLifeCycleCallback = null
    }

    override fun startOrResumeConvert(mediaId: Long, convertAbilityType: Int, convertAiTitle: Boolean): Boolean {
        return startOrResumeTask(mediaId, convertAbilityType, convertAiTitle)
    }

    override fun cancelConvert(mediaId: Long): Boolean {
        return cancelTask(mediaId)
    }

    override fun releaseConvert(mediaId: Long) {
        release(mediaId)
    }

    override fun registerCallback(mediaId: Long, callback: IConvertCallback) {
        if (mUICallbacks[mediaId] == null) {
            mUICallbacks[mediaId] = CopyOnWriteArrayList()
        }
        mUICallbacks[mediaId]?.run {
            if (!contains(callback)) {
                add(callback)
            }
        }
        DebugUtil.i(TAG, "register ui callback $callback")
    }

    override fun unregisterCallback(mediaId: Long, callback: IConvertCallback) {
        mUICallbacks[mediaId]?.run {
            remove(callback)
            if (isEmpty()) {
                mUICallbacks.remove(mediaId)
            }
        }
        DebugUtil.i(TAG, "unregister ui callback $callback, size = ${mUICallbacks[mediaId]?.size}")
    }
}