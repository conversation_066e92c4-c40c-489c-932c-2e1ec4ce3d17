/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryRetryCacheTask
 * Description: AI摘要重试缓存任务管理
 * Version: 1.0
 * Date: 2025/1/15
 * Author: Assistant
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * Assistant                       2025/1/15      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.convertservice.aisummary

import com.soundrecorder.base.utils.DebugUtil
import java.util.concurrent.ConcurrentHashMap

/**
 * AI摘要重试缓存任务管理
 */
object AISummaryRetryCacheTask {

    private const val TAG = "AISummaryRetryCacheTask"
    private const val MAX_RETRY_COUNT = 3
    private val cacheAISummaryRetryTasks = ConcurrentHashMap<Long, Int>()

    /**
     * 添加重试任务到缓存
     * @param mediaId 媒体ID
     * @param retryCount 当前重试次数
     */
    fun addRetryTaskToCache(mediaId: Long, retryCount: Int) {
        cacheAISummaryRetryTasks[mediaId] = retryCount
        DebugUtil.d(TAG, "addRetryTaskToCache, mediaId:$mediaId, retryCount:$retryCount")
    }

    /**
     * 检查是否有缓存任务
     * @param mediaId 媒体ID
     * @return 是否存在缓存任务
     */
    fun hasCacheTask(mediaId: Long): Boolean {
        val hasTask = cacheAISummaryRetryTasks.containsKey(mediaId)
        DebugUtil.d(TAG, "hasCacheTask, mediaId:$mediaId, hasTask:$hasTask, taskSize:${cacheAISummaryRetryTasks.size}")
        return hasTask
    }

    /**
     * 获取当前AI摘要任务的重试次数
     * @param mediaId 媒体ID
     * @return 重试次数
     */
    fun getTaskRetryCount(mediaId: Long): Int {
        val retryCount = if (!cacheAISummaryRetryTasks.isNullOrEmpty() && cacheAISummaryRetryTasks.containsKey(mediaId)) {
            cacheAISummaryRetryTasks[mediaId] ?: 0
        } else {
            0
        }
        DebugUtil.d(TAG, "getTaskRetryCount, mediaId:$mediaId, retryCount:$retryCount")
        return retryCount
    }

    /**
     * 获取当前mediaId是否进行重试
     * 首次进入未进行过AI摘要时retryCount = 0
     * 进行了AI摘要任务 retryCount+1，retryCount=3时从0再开始
     * retryCount：0,1,2,3，服务端最大支持为3，retryCount=3时后续不再变化
     * @param mediaId 媒体ID
     * @return 当前重试次数
     */
    fun getCurrentRetryCount(mediaId: Long): Int {
        var retryCount = 0
        if (hasCacheTask(mediaId)) {
            val count = getTaskRetryCount(mediaId)
            if (count >= MAX_RETRY_COUNT) {
                retryCount = MAX_RETRY_COUNT
            } else {
                retryCount = count + 1
            }
        }
        DebugUtil.d(TAG, "getCurrentRetryCount, mediaId:$mediaId, currentRetryCount:$retryCount")
        return retryCount
    }

    /**
     * 清理所有重试任务缓存
     */
    fun clear() {
        val size = cacheAISummaryRetryTasks.size
        cacheAISummaryRetryTasks.clear()
        DebugUtil.d(TAG, "clear, cleared $size tasks")
    }
}
