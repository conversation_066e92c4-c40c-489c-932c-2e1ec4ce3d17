/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  NewConvertResultUtilApi
 * * Description: NewConvertResultUtilApi
 * * Version: 1.0
 * * Date : 2025/3/20
 * * Author: W9066446
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9066446    2025/3/20   1.0    build this module
 ****************************************************************/
package com.soundrecorder.convertservice.api

import android.content.Context
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.convertservice.convert.NewConvertResultUtil
import com.soundrecorder.convertservice.util.ConvertToUtils
import com.soundrecorder.modulerouter.convertService.NewConvertResultUtilAction

@Component(NewConvertResultUtilAction.COMPONENT_NAME)
object NewConvertResultUtilApi {

    @Action(NewConvertResultUtilAction.FUN_GET_CONVERT_SAVE_PATH)
    @JvmStatic
    fun getConvertSavePath(context: Context): String {
        return NewConvertResultUtil.getConvertSavePath(context)
    }

    @Action(NewConvertResultUtilAction.FUN_GET_CONVERT_FILE_NAME_WITH_POSTFIX)
    @JvmStatic
    fun getConvertFileNameWithPostFix(recordId: Long, title: String): String {
        return NewConvertResultUtil.genFileName(recordId, title)
    }

    @Action(NewConvertResultUtilAction.FUN_GET_CONVERT_FILE_PATH)
    @JvmStatic
    fun getConvertFilePath(context: Context, fileName: String): String {
        return NewConvertResultUtil.genConvertTextPath(context, fileName)
    }

    @Action(NewConvertResultUtilAction.ACTION_WRITE_CONVERT_CONTENT)
    @JvmStatic
    fun writeConvertContent(filePath: String?, dataList: List<ConvertContentItem>?) {
        ConvertToUtils.reWriteConvertFile(filePath, dataList)
    }
}