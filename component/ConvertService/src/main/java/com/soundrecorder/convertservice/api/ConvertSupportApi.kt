/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ConvertSupportApi
 * Description:
 * Version: 1.0
 * Date: 2025/3/12
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/12 1.0 create
 */

package com.soundrecorder.convertservice.api

import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.convertservice.ConvertSupportManager
import com.soundrecorder.modulerouter.convertService.ConvertSupportAction

@Component(ConvertSupportAction.COMPONENT_NAME)
object ConvertSupportApi {

    @Action(ConvertSupportAction.ACTION_IS_SUPPORT_CONVERT)
    @JvmStatic
    fun isSupportConvert(fromMainProcess: <PERSON>olean): Boolean {
        return ConvertSupportManager.isSupportConvert(fromMainProcess)
    }

    @Action(ConvertSupportAction.ACTION_SUPPORT_CONVERT_TYPE)
    @JvmStatic
    fun getConvertSupportType(fromMainProcess: Boolean): Int {
        return ConvertSupportManager.getConvertSupportType(fromMainProcess)
    }
}