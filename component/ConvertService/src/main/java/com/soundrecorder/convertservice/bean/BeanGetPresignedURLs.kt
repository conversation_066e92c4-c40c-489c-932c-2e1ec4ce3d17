/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BaseGetPresignedURLs
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.bean

import com.soundrecorder.base.utils.DebugUtil


class BeanGetPresignedURLs(
    val urlList: List<String>?,
    val needUpload: Boolean,
    val uploadId: String?
) {
    fun print() {
        DebugUtil.i(
            "BeanGetPresignedURLs",
            "uploadId:$uploadId, needUpload: $needUpload, urlList: ${urlList?.get(0)}"
        )
    }
}