/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  SmartNameTaskManager
 * * Description: SmartNameTaskManager
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.convertservice.smartname

import com.oplus.unified.summary.sdk.UnifiedSummaryKit
import com.oplus.unified.summary.sdk.callback.ISummaryInitCallback
import com.oplus.unified.summary.sdk.common.SummaryInitParam
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.SmartNameParam
import com.soundrecorder.convertservice.convert.IJobManagerLifeCycleCallback
import com.soundrecorder.modulerouter.SeedlingAction
import com.soundrecorder.modulerouter.convertService.ConvertThreadManageAction
import com.soundrecorder.modulerouter.smartname.ISmartNameCallback
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import java.util.LinkedList
import java.util.Queue
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

object SmartNameTaskManager : ISmartNameProcess {

    const val LIMIT_SIZE = 3
    const val TAG = "SmartNameTaskManager"
    const val DEFAULT_NULL = 0
    const val ALREADY_RUNNING = 1
    const val OVER_LIMIT = 2
    const val CAN_ADD_NEW = 3
    const val CONVERT_RUNNING = 4

    var taskMaps: ConcurrentHashMap<Long, ISmartNameRunnable> = ConcurrentHashMap(LIMIT_SIZE)
    var mUICallbacks: ConcurrentHashMap<Long, CopyOnWriteArrayList<ISmartNameCallback>> = ConcurrentHashMap(
        LIMIT_SIZE
    )

    var summarySessionIdList: ConcurrentHashMap<Long, String?> = ConcurrentHashMap()
    var jobManagerLifeCycleCallback: IJobManagerLifeCycleCallback? = null

    //private var mRunningTasks: Int = 0
    private var mWaitingTasks: Queue<Long> = LinkedList()

    private var mSummaryKit: UnifiedSummaryKit? = null

    override fun initUnifiedSummary(callback: InitSummaryKitCallback?) {
        if (mSummaryKit == null) {
            DebugUtil.d(TAG, "initUnifiedSummary, start")
            mSummaryKit = UnifiedSummaryKit(BaseApplication.getAppContext())
            mSummaryKit?.initKit(
                SummaryInitParam(
                    BaseApplication.getAppContext().packageName,
                    true,
                    null
                ), object : ISummaryInitCallback {
                    override fun onError(sessionId: String, errorCode: Int, errorMsg: String?) {
                        DebugUtil.d(TAG, "initUnifiedSummary, onError:$sessionId, errorCode:$errorCode, errorMsg:$errorMsg")
                        callback?.initSummaryKitError(sessionId, errorCode, errorMsg)
                    }

                    override fun onSuccess(bizTag: String?, extras: Map<String, Any>?) {
                        DebugUtil.d(TAG, "initUnifiedSummary, onSuccess:$bizTag, extras:$extras")
                        if (mSummaryKit != null) {
                            callback?.initSummaryKitSuccess(mSummaryKit, true)
                        } else {
                            callback?.initSummaryKitError("", SmartNameAction.PLUGIN_INIT_ERROR, null)
                        }
                    }
                }
            )
        } else {
            callback?.initSummaryKitSuccess(mSummaryKit)
        }
    }

    fun releaseSummaryKit() {
        DebugUtil.d(TAG, "releaseSummaryKit")
        mSummaryKit?.release(BaseApplication.getAppContext().packageName)
        mSummaryKit = null
    }

    fun addSessionId(mediaId: Long, sessionId: String) {
        if (!summarySessionIdList.containsKey(mediaId)) {
            summarySessionIdList.put(mediaId, sessionId)
        }
    }

    fun removeSessionId(mediaId: Long, sessionId: String) {
        if (summarySessionIdList.containsKey(mediaId)) {
            summarySessionIdList.remove(mediaId)
        }
    }

    fun stopSmartNameTask(mediaId: Long) {
        if (summarySessionIdList.isNullOrEmpty()) {
            return
        }
        if (summarySessionIdList.containsKey(mediaId)) {
            val sessionId = summarySessionIdList.get(mediaId)
            if (!sessionId.isNullOrEmpty() && !sessionId.equals("")) {
                DebugUtil.d(TAG, "stopSmartNameTask, sessionId:$sessionId")
                mSummaryKit?.stopTask(sessionId)
            }
        }
    }

    object RunnableProgress : ISmartNameProgressCallback {

        override fun preStartSmartName(mediaId: Long) {
        }

        override fun postSmartNameEnd(mediaId: Long) {
            DebugUtil.d(TAG, "postSmartNameEnd, mediaId:$mediaId")
            removeTask(mediaId)
            waitTaskQueuePoll()
            checkFinalTaskEnd(mediaId)
        }

        override fun postCancelSmartName(mediaId: Long) {
            removeTask(mediaId)
        }
    }

    private fun removeTask(mediaId: Long) {
        if (taskMaps.containsKey(mediaId)) {
            DebugUtil.d(TAG, "removeTask, mediaId:$mediaId")
            taskMaps.remove(mediaId)
        }
    }

    private fun waitTaskQueuePoll() {
        synchronized(SmartNameTaskManager::class) {
            if (!mWaitingTasks.isNullOrEmpty()) {
                val mediaId = mWaitingTasks.poll()
                DebugUtil.d(TAG, "waitTaskQueuePoll, waitRecord:$mediaId")
                if (mediaId != null) {
                    addOrResumeSmartNameTask(mediaId, null)
                }
            }
        }
    }

    fun checkFinalTaskEnd(mediaId: Long) {
        if (taskMaps.isEmpty()) {
            DebugUtil.i(TAG, "checkFinalTaskEnd: onFinalJobEnd $mediaId")
            releaseSummaryKit()
            jobManagerLifeCycleCallback?.onFinalJobEnd(mediaId)
            SeedlingAction.sendRecordInnerRenameEvent(mediaId)
        }
    }

    private fun getConvertUiCallback(): ISmartNameCallback {
        return object : ISmartNameCallback {

            override fun onSmartNameStart(mediaId: Long, extras: Map<String, Any>?) {
                mUICallbacks[mediaId]?.forEach {
                    it.onSmartNameStart(mediaId, extras)
                }
            }

            override fun onSmartNameStop(mediaId: Long, extras: Map<String, Any>?) {
                mUICallbacks[mediaId]?.forEach {
                    it.onSmartNameStop(mediaId, extras)
                }
            }

            override fun onSmartNameFinished(
                mediaId: Long,
                jsonResult: String,
                extras: Map<String, Any>?
            ) {
                mUICallbacks[mediaId]?.forEach {
                    it.onSmartNameFinished(mediaId, jsonResult, extras)
                }
            }

            override fun onSmartNameError(mediaId: Long, errorCode: Int, errorMsg: String?) {
                mUICallbacks[mediaId]?.forEach {
                    it.onSmartNameError(mediaId, errorCode, errorMsg)
                }
            }

            override fun onSmartNameEnd(mediaId: Long) {
                mUICallbacks[mediaId]?.forEach {
                    it.onSmartNameEnd(mediaId)
                }
            }
        }
    }

    private fun cancelTask(mediaId: Long): Boolean {
        val result = if (!taskMaps.containsKey(mediaId)) {
            DebugUtil.i(TAG, "maps not contains mediaId: $mediaId, no need to add task again")
            false
        } else {
            taskMaps[mediaId]?.cancelSmartName()
            true
        }
        return result
    }

    fun cancelAllTask() {
        taskMaps.forEach { (_, value) ->
            value.cancelSmartName()
        }
    }

    fun checkIsTaskRunning(mediaId: Long): Boolean {
        return taskMaps.containsKey(mediaId)
    }

    fun checkNoTaskRunning(): Boolean {
        DebugUtil.i(TAG, "checkNoTaskRunning = ${taskMaps.isEmpty()}")
        return taskMaps.isEmpty()
    }

    fun checkHasTaskRunning(): Boolean {
        DebugUtil.i(TAG, "checkHasTaskRunning = ${taskMaps.isNotEmpty()}")
        return taskMaps.isNotEmpty()
    }

    fun checkTaskRunning(excludeMedia: Long): Boolean {
        if (taskMaps.size > 1) {
            return true
        }
        return !taskMaps.isEmpty() && !taskMaps.containsKey(excludeMedia)
    }

    /**
     * 判断当前进行中的任务是否超过最大值3
     */
    fun checkTaskRunningMaxLimitSize(): Boolean {
        return taskMaps.size >= LIMIT_SIZE
    }

    fun release(mediaId: Long) {
        DebugUtil.i(TAG, "release: $mediaId")
        taskMaps[mediaId]?.release()
    }

    fun checkCanAddNewTask(mediaId: Long): Int {
        if (ConvertThreadManageAction.checkIsTaskRunning(mediaId)) {
            DebugUtil.d(TAG, "checkCanAddNewTask, converting:$mediaId")
            return CONVERT_RUNNING
        }
        if (taskMaps.containsKey(mediaId)) {
            DebugUtil.i(TAG, "checkCanAddNewTask: $mediaId already running, no need to ")
            return ALREADY_RUNNING
        } else if (taskMaps.size >= LIMIT_SIZE) {
            DebugUtil.i(TAG, "maps limit reached : runnable $mediaId, add waiting")
            return OVER_LIMIT
        } else {
            return CAN_ADD_NEW
        }
    }

    fun releaseAll() {
        DebugUtil.i(TAG, "release all")
        mUICallbacks.clear()
        for (mediaId in taskMaps.keys) {
            release(mediaId)
        }
        summarySessionIdList.clear()
        mWaitingTasks.clear()
        releaseSummaryKit()
    }

    override fun startSmartName(mediaId: Long,  params: SmartNameParam?): Boolean {
        return addOrResumeSmartNameTask(mediaId, params)
    }

    /**
     * 添加智能命名任务
     */
    fun addOrResumeSmartNameTask(mediaId: Long, params: SmartNameParam?): Boolean {
        val result = when (checkCanAddNewTask(mediaId)) {
            ALREADY_RUNNING -> {
                DebugUtil.i(TAG, "maps already contains mediaId: $mediaId, no need to add task again")
                true
            }
            OVER_LIMIT,
            CONVERT_RUNNING -> {
                DebugUtil.i(TAG, "maps limit reached : runnable $mediaId, add to waiting")
                addWaitingTasks(mediaId)
                false
            }
            else -> {
                val smartNameRunnable = SmartNameRunnable(mediaId, params)
                smartNameRunnable.registerSmartNameUiCallback(getConvertUiCallback())
                smartNameRunnable.registerProgressCallback(RunnableProgress)
                taskMaps[mediaId] = smartNameRunnable
                smartNameRunnable.startSmartName()
                DebugUtil.i(TAG, "smartNameRunnable $mediaId start run")
                true
            }
        }
        return result
    }

    private fun addWaitingTasks(mediaId: Long) {
        if (!checkIsTaskRunning(mediaId) && !mWaitingTasks.contains(mediaId)) {
            DebugUtil.d(TAG, "addWaitingTasks, mediaId:$mediaId")
            mWaitingTasks.add(mediaId)
        }
    }

    override fun cancelSmartName(mediaId: Long): Boolean {
        return cancelTask(mediaId)
    }

    override fun releaseSmartName(mediaId: Long) {
        release(mediaId)
    }

    override fun registerSmartNameCallback(mediaId: Long, callback: ISmartNameCallback) {
        if (mUICallbacks[mediaId] == null) {
            mUICallbacks[mediaId] = CopyOnWriteArrayList()
        }
        mUICallbacks[mediaId]?.run {
            if (!contains(callback)) {
                add(callback)
            }
        }
        DebugUtil.i(TAG, "register ui callback $callback")
    }

    override fun unregisterSmartNameCallback(mediaId: Long, callback: ISmartNameCallback) {
        mUICallbacks[mediaId]?.run {
            remove(callback)
            if (isEmpty()) {
                mUICallbacks.remove(mediaId)
            }
        }
        DebugUtil.i(TAG, "unregister ui callback $callback, size = ${mUICallbacks[mediaId]?.size}")
    }
}