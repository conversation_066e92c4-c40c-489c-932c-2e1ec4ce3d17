/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryService
 * Description:AISummaryService
 * Version: 1.0
 * Date: 2025/5/13
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/13      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.convertservice.aisummary

import android.app.Service
import android.content.Intent
import android.os.IBinder
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.AISummaryRecordParam
import com.soundrecorder.convertservice.convert.IJobManagerLifeCycleCallback
import com.soundrecorder.modulerouter.aisummary.IAISummaryCallback

class AISummaryService : IAISummaryProcess, Service() {

    //service调用链路 check
    companion object {
        const val TAG: String = "AISummaryService"
    }

    var callback: IJobManagerLifeCycleCallback? = object : IJobManagerLifeCycleCallback {
        override fun onFinalJobEnd(mediaId: Long) {
            DebugUtil.i(TAG, "onFinalJobEnd, stop Service")
            stopSelf()
        }
    }

    /**
     * 跟AISummaryTaskManager 进行关联
     */
    override fun onCreate() {
        super.onCreate()
        /* 创建AISummary服务 */
        DebugUtil.d(TAG, "onCreate")
        AISummaryTaskManager.jobManagerLifeCycleCallback = callback
    }

    override fun onBind(intent: Intent?): IBinder {
        DebugUtil.d(TAG, "onBind")
        return AISummaryServiceBinder(this)
    }

    override fun onUnbind(intent: Intent?): Boolean {
        DebugUtil.d(TAG, "onUnbind")
        if (AISummaryTaskManager.checkNoTaskRunning()) {
            DebugUtil.i(TAG, "no Task running, stop Service")
            stopSelf()
        }
        return super.onUnbind(intent)
    }

    override fun onDestroy() {
        super.onDestroy()
        DebugUtil.d(TAG, "onDestroy")
        AISummaryTaskManager.releaseAll()
    }

    override fun initUnifiedSummary(callback: InitSummarySDKCallback?) {
        return AISummaryTaskManager.initUnifiedSummary(callback)
    }

    override fun startAISummary(mediaId: Long, params: AISummaryRecordParam?): Boolean {
        return AISummaryTaskManager.startAISummary(mediaId, params)
    }

    override fun cancelAISummary(mediaId: Long): Boolean {
        return AISummaryTaskManager.cancelAISummary(mediaId)
    }

    override fun releaseAISummary(mediaId: Long) {
         return AISummaryTaskManager.releaseAISummary(mediaId)
    }

    override fun registerAISummaryCallback(mediaId: Long, callback: IAISummaryCallback) {
        return AISummaryTaskManager.registerAISummaryCallback(mediaId, callback)
    }

    override fun unregisterAISummaryCallback(mediaId: Long, callback: IAISummaryCallback) {
         return AISummaryTaskManager.unregisterAISummaryCallback(mediaId, callback)
    }
}