/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryCallbackManager
 * Description: 统一的AI摘要回调管理器，消除重复的回调注册/注销逻辑
 * Version: 1.0
 * Date: 2025/6/4
 * Author: Assistant
 *********************************************************************************/
package com.soundrecorder.convertservice.aisummary

import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.aisummary.IAISummaryCallback
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 统一的AI摘要回调管理器
 * 消除各个组件中重复的回调注册/注销逻辑
 */
object AISummaryCallbackManager {
    
    private const val TAG = "AISummaryCallbackManager"
    
    // 使用线程安全的集合存储回调
    private val callbacks = ConcurrentHashMap<Long, CopyOnWriteArrayList<IAISummaryCallback>>()
    
    /**
     * 注册回调
     * @param mediaId 录音ID
     * @param callback 回调接口
     */
    fun registerCallback(mediaId: Long, callback: IAISummaryCallback) {
        DebugUtil.d(TAG, "registerCallback: mediaId=$mediaId")
        callbacks.getOrPut(mediaId) { CopyOnWriteArrayList() }.apply {
            if (!contains(callback)) {
                add(callback)
                DebugUtil.d(TAG, "registerCallback: added callback for mediaId=$mediaId, total=${size}")
            }
        }
    }
    
    /**
     * 注销回调
     * @param mediaId 录音ID
     * @param callback 回调接口
     */
    fun unregisterCallback(mediaId: Long, callback: IAISummaryCallback) {
        DebugUtil.d(TAG, "unregisterCallback: mediaId=$mediaId")
        callbacks[mediaId]?.let { callbackList ->
            val removed = callbackList.remove(callback)
            DebugUtil.d(TAG, "unregisterCallback: removed=$removed for mediaId=$mediaId, remaining=${callbackList.size}")
            
            // 如果列表为空，移除整个条目
            if (callbackList.isEmpty()) {
                callbacks.remove(mediaId)
                DebugUtil.d(TAG, "unregisterCallback: removed empty callback list for mediaId=$mediaId")
            }
        }
    }
    
    /**
     * 注销指定录音的所有回调
     * @param mediaId 录音ID
     */
    fun unregisterAllCallbacks(mediaId: Long) {
        DebugUtil.d(TAG, "unregisterAllCallbacks: mediaId=$mediaId")
        callbacks.remove(mediaId)?.let { callbackList ->
            DebugUtil.d(TAG, "unregisterAllCallbacks: removed ${callbackList.size} callbacks for mediaId=$mediaId")
        }
    }
    
    /**
     * 清理所有回调
     */
    fun clearAllCallbacks() {
        DebugUtil.d(TAG, "clearAllCallbacks: clearing ${callbacks.size} media entries")
        callbacks.clear()
    }
    
    /**
     * 通知开始事件
     */
    fun notifyStart(mediaId: Long, extras: Map<String, Any>?) {
        notifyCallbacks(mediaId) { callback ->
            callback.onAISummaryStart(mediaId, extras)
        }
    }
    
    /**
     * 通知完成事件
     */
    fun notifyFinished(mediaId: Long, jsonResult: String, extras: Map<String, Any>?) {
        notifyCallbacks(mediaId) { callback ->
            callback.onAISummaryFinished(mediaId, jsonResult, extras)
        }
    }
    
    /**
     * 通知错误事件
     */
    fun notifyError(mediaId: Long, errorCode: Int, errorMsg: String?) {
        notifyCallbacks(mediaId) { callback ->
            callback.onAISummaryError(mediaId, errorCode, errorMsg)
        }
    }
    
    /**
     * 通知停止事件
     */
    fun notifyStop(mediaId: Long, extras: Map<String, Any>?) {
        notifyCallbacks(mediaId) { callback ->
            callback.onAISummaryStop(mediaId, extras)
        }
    }
    
    /**
     * 通知结束事件
     */
    fun notifyEnd(mediaId: Long) {
        notifyCallbacks(mediaId) { callback ->
            callback.onAISummaryEnd(mediaId)
        }
    }
    
    /**
     * 统一的回调通知方法
     */
    private fun notifyCallbacks(mediaId: Long, action: (IAISummaryCallback) -> Unit) {
        callbacks[mediaId]?.let { callbackList ->
            DebugUtil.d(TAG, "notifyCallbacks: notifying ${callbackList.size} callbacks for mediaId=$mediaId")
            callbackList.forEach { callback ->
                try {
                    action(callback)
                } catch (e: Exception) {
                    DebugUtil.e(TAG, "notifyCallbacks: error in callback for mediaId=$mediaId, error=${e.message}")
                }
            }
        }
    }
    
    /**
     * 检查是否有注册的回调
     */
    fun hasCallbacks(mediaId: Long): Boolean {
        return callbacks[mediaId]?.isNotEmpty() == true
    }
    
    /**
     * 获取回调数量（用于调试）
     */
    fun getCallbackCount(mediaId: Long): Int {
        return callbacks[mediaId]?.size ?: 0
    }
}
