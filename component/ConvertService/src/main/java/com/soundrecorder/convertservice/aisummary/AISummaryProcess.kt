/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: ProcessAISummary
 * Description:
 * Version: 1.0
 * Date: 2025/5/8
 * Author: ********
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * ********                         2025/5/8      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.convertservice.aisummary

import android.os.Handler
import android.os.HandlerThread
import android.os.Message
import android.text.TextUtils
import com.oplus.recorderlog.util.GsonUtil
import com.oplus.unified.summary.sdk.UnifiedSummaryKit
import com.oplus.unified.summary.sdk.speech.SpeechSummaryRequest
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.oplus.unified.summary.sdk.callback.ISummaryInitCallback
import com.oplus.unified.summary.sdk.common.SummaryInitParam
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.databean.AISummaryRecordParam
import com.soundrecorder.common.databean.BeanConvertText
import com.soundrecorder.common.R
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.databean.ConvertStatus
import com.soundrecorder.common.databean.SmartSummaryResult
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.convertservice.ConvertSupportManager
import com.soundrecorder.convertservice.convert.ConvertTaskThreadManager
import com.soundrecorder.convertservice.convert.IConvertCallback
import com.soundrecorder.convertservice.util.ConvertToUtils
import com.soundrecorder.modulerouter.aisummary.AISummaryAction
import com.soundrecorder.modulerouter.aisummary.IAISummaryCallback
import com.soundrecorder.modulerouter.convertService.NewConvertResultUtilAction
import java.io.File
import java.util.UUID

class AISummaryProcess(val mediaId: Long, var callback: IAISummaryCallback?, var params: AISummaryRecordParam?) {

    companion object {
        private const val TAG = "ProcessAISummary"
        private const val MSG_START_AI_SUMMARY = 1
        private const val MSG_RELEASE = 2
        private const val MSG_START_CONVERT_OR_AI = 3
        private const val MAX_RETRY_COUNT = 3
    }

    @Volatile
    var mIsAbort = false
    var mProgressCallback: IAISummaryProgressCallback? = null

    private var mHandlerThread: HandlerThread? = null
    private var mWorkHandler: Handler? = null
    private var isThreadQuit = false

    private var bizTag: String? = null
    private var mSummaryKit: UnifiedSummaryKit? = null
    private var mErrorRetryCount = 0  // 错误重试计数器

    fun initHandlerThread() {
        bizTag = BaseApplication.getAppContext().packageName + "-$mediaId"
        DebugUtil.d(TAG, "initHandlerThread, bizTag:$bizTag")
        isThreadQuit = false
        mHandlerThread = HandlerThread("AISummary-$mediaId")
        mHandlerThread?.start()
        mHandlerThread?.looper?.let {
            mWorkHandler = object : Handler(it) {
                override fun handleMessage(msg: Message) {
                    when (msg.what) {
                        MSG_START_CONVERT_OR_AI -> processStartConvertOrAISummary()
                        MSG_START_AI_SUMMARY -> processAISummary()
                        MSG_RELEASE -> release()
                    }
                }
            }
        }
    }

    private fun getBizTag(): String {
        return bizTag ?: (BaseApplication.getAppContext().packageName + "-$mediaId")
    }

    private fun release() {
        DebugUtil.d(TAG, "release: quitHandlerThread")
        releaseSummaryKit()
        mWorkHandler?.removeCallbacksAndMessages(null)
        mHandlerThread?.quitSafely()
        mHandlerThread = null
        mWorkHandler = null
        isThreadQuit = true
        params = null
    }

    private fun releaseSummaryKit() {
        mSummaryKit?.release(getBizTag())
        mSummaryKit = null
    }

    private var mConvertCallback: IConvertCallback? = object : IConvertCallback {
        override fun onConvertStatusChange(
            mediaId: Long,
            convertStatus: ConvertStatus,
            errorCode: Int,
            errorMessage: String,
            convertAiTitle: Boolean
        ) {
            DebugUtil.d(TAG, "onConvertStatusChange, mediaId:$mediaId, convertStatus：$convertStatus," +
                    " errorCode:$errorCode, errorMessage:$errorMessage")
            mWorkHandler?.post {
                handleConvertStatus(convertStatus, mediaId, errorMessage)
            }
        }

        override fun onConvertTextReceived(
            mediaId: Long,
            convertType: Int,
            convertTextResult: BeanConvertText,
            showSwitch: Boolean,
            convertAiTitle: Boolean
        ) {
            DebugUtil.i(TAG, "onConvertTextReceived, convertAiTitle:$convertAiTitle")
            mWorkHandler?.post {
                processAISummary(AISummaryRecordParam.toConvertAISummaryRecordParam(convertTextResult))
            }
        }

        override fun onConvertProgressChanged(mediaId: Long, uploadProgress: Int, convertProgress: Int, serverPlanCode: Int) {
            DebugUtil.i(
                TAG, """
                        onConvertProgressChanged mediaId = $mediaId
                        uploadProgress = $uploadProgress
                        convertProgress = $convertProgress
                         serverPlanCode = $serverPlanCode
                        """)
        }
    }

    private fun handleConvertStatus(
        convertStatus: ConvertStatus,
        mediaId: Long,
        errorMessage: String
    ) {
        when (convertStatus.convertStatus) {
            ConvertStatus.CONVERT_STATUS_NO_NETWORK,
            ConvertStatus.NETWORKERROR_EXCEPTION -> {
                processAISummaryError(mediaId, AISummaryAction.NETWORK_ERROR, errorMessage)
                return
            }

            ConvertStatus.CONVERT_STATUS_QUERY_FAIL,
            ConvertStatus.CONVERT_STATUS_ADD_TASK_FAIL,
            ConvertStatus.CONVERT_STATUS_QUERY_TASK_TIMEOUT,
            ConvertStatus.ENCRYPT_EXCEPTION,
            ConvertStatus.JSONPARSE_EXCEPTION,
            ConvertStatus.EXCEPTION -> {
                processAISummaryError(mediaId, AISummaryAction.SERVER_ERROR, errorMessage)
                return
            }
        }
    }

    fun doStartAISummary() {
        DebugUtil.i(TAG, "doStartAISummary: start")
        if (params != null) {
            mWorkHandler?.sendEmptyMessage(MSG_START_AI_SUMMARY)
        } else {
            mWorkHandler?.sendEmptyMessage(MSG_START_CONVERT_OR_AI)
        }
    }

    /**
     * 先进行转文本，再进行录音摘要
     * 目前逻辑：先转文本后，才有摘要入口，可以将与转文本耦合的逻辑剔除
     */
    private fun processStartConvertOrAISummary(): Boolean {
        val convertRecord = ConvertDbUtil.selectByRecordId(mediaId)
        val isConvertComplete = ConvertDbUtil.checkAlreadyConvertComplete(convertRecord)
        /* 转文本结束，进行录音摘要 */
        if (isConvertComplete) {
            return startConvertCompleteAISummary(convertRecord)
        } else {
            /* 这里直接传true */
            val convertSupportType = ConvertSupportManager.getConvertSupportType(true)
            if (convertSupportType == ConvertSupportManager.CONVERT_DISABLE) {
                /* 不支持转文本时，直接结束当前录音摘要任务 */
                DebugUtil.d(TAG, "processStartConvertOrAISummary: convertSupportType disable")
                processAISummaryError(mediaId, AISummaryAction.CONVERT_TEXT_UNSUPPORT)
                return false
            }

            val mediaRecord = MediaDBUtils.queryRecordById(mediaId)
            DebugUtil.i(TAG, "processStartConvertOrAISummary: start record:$mediaRecord")

            if (PermissionUtils.hasReadAudioPermission()) {
                ConvertDbUtil.updateRecordIdByMediaPath(mediaRecord?.data, mediaId)
            }

            mConvertCallback?.let {
                ConvertTaskThreadManager.registerCallback(mediaId, it)
            }

            val result = ConvertTaskThreadManager.startOrResumeConvert(
                mediaId,
                convertSupportType, true)
            DebugUtil.i(TAG, "processStartConvertOrAISummary: result = $result")
            return result
        }
    }

    private fun startConvertCompleteAISummary(convertRecord: ConvertRecord?): Boolean {
        /* 启动录音摘要 将转换的数据塞到 processRequest中 */
        val convertFileName = getConvertFileName(convertRecord)
        val convertContentItems: ArrayList<ConvertContentItem>? =
            if (TextUtils.isEmpty(convertFileName)) {
                null
            } else {
                /* 如果是OShare目录下的文件，采用对应文本解析规则 */
                if (convertRecord?.isOShareFile == true) {
                    ConvertToUtils.readOShareConvertContent(
                        convertRecord.convertTextfilePath,
                        convertRecord.serverPlanCode
                    )
                } else {
                    convertRecord?.serverPlanCode?.let {
                        ConvertToUtils.readConvertContent(
                            BaseApplication.getAppContext(), convertFileName,
                            it
                        )
                    }
                }
            }

        return processAISummary(AISummaryRecordParam.toConvertAISummaryRecordParam(convertContentItems))
    }

    private fun getConvertFileName(convertRecord: ConvertRecord?): String? {
        val convertFilePath: String? = convertRecord?.convertTextfilePath
        var convertFileName: String? = null
        if (!TextUtils.isEmpty(convertFilePath)) {
            val savePathDir = NewConvertResultUtilAction.getConvertSavePath(BaseApplication.getAppContext()) + File.separator
            convertFileName = convertFilePath?.replace(savePathDir, "")
        }
        DebugUtil.i(TAG, "getConvertFileName, convertFileName:$convertFileName convertFilePath = $convertFilePath")
        return convertFileName
    }

    fun registerNameProgressCallback(progressCallback: IAISummaryProgressCallback?) {
        mProgressCallback = progressCallback
    }

    /**
     * 录音摘要process
     */
    fun processAISummary(aiSummaryParam: AISummaryRecordParam? = null): Boolean {
        if (aiSummaryParam != null) {
            params = aiSummaryParam
        }

        mConvertCallback?.let {
            ConvertTaskThreadManager.unregisterCallback(mediaId, it)
        }

        if (params == null) {
            DebugUtil.d(TAG, "processAISummary: createSummaryRequest fail, params is null")
            processAISummaryError(mediaId, AISummaryAction.CONTENT_LESS_ERROR, "")
            return false
        }
        if (mIsAbort) {
            //do success
            return true
        }

        return  handleSummaryRequest(params)
    }


    private fun handleSummaryRequest(params: AISummaryRecordParam?): Boolean {
        var result = false
        params?.let {
            val sessionId = generateSessionId(it.sessionId)
            val inputLanguage = "zh"
            val outputLanguage = "zh"
            val retryCount = AISummaryRetryCacheTask.getCurrentRetryCount(mediaId)

            val request = SpeechSummaryRequest.createRecordSummaryRequest(
                sessionId = sessionId,
                content = it.content,
                timeout = it.timeout,
                inputLanguage = inputLanguage,
                outputLanguage = outputLanguage,
                retryCount = retryCount,
                isRetry = (retryCount > 0)
            )

            AISummaryRetryCacheTask.addRetryTaskToCache(mediaId, retryCount)
            DebugUtil.d(
                TAG,
                "createAISummaryRequestRetry, retryCount:$retryCount, request sessionId:${request.sessionId}, params:$params"
            )

            result = createSummaryKit(request)
        }

        return  result
    }

    private fun generateSessionId(sessionId: String?): String {
        return sessionId?.takeUnless { it.isBlank() } ?: UUID.randomUUID().toString()
    }

    private fun toastMessage(text: String) {
        ToastManager.showShortToast(BaseApplication.getAppContext(), text)
    }

    private fun toastMessage(resId: Int) {
        ToastManager.showShortToast(BaseApplication.getAppContext(), resId)
    }

    /**
     * 停止生成摘要
     */
    private fun stopSummary() {
        if (AISummaryTaskManager.summarySessionIdList.isNullOrEmpty()) {
            return
        }
        if (AISummaryTaskManager.summarySessionIdList.containsKey(mediaId)) {
            val sessionId = AISummaryTaskManager.summarySessionIdList.get(mediaId)
            if (!sessionId.isNullOrEmpty() && !sessionId.equals("")) {
                DebugUtil.d(TAG, "stopSummaryTask, sessionId:$sessionId")
                mSummaryKit?.stopTask(sessionId)
            }
        }
    }

    /**
     * 取消 生成摘要
     */
    fun cancel() {
        mWorkHandler?.post {
            mIsAbort = true
            stopSummary()
            mErrorRetryCount = 0
            mProgressCallback?.postCancelAISummary(mediaId)
            mWorkHandler?.sendEmptyMessage(MSG_RELEASE)
        }
    }

    private fun createSummaryKit(request: SpeechSummaryRequest, isRetry: Boolean? = false): Boolean {
        var result = false
        mProgressCallback?.preStartAISummary(mediaId)

        mSummaryKit = UnifiedSummaryKit(BaseApplication.getAppContext())
        mSummaryKit?.initKit(
            SummaryInitParam(getBizTag(), true, null), object : ISummaryInitCallback {
                override fun onError(sessionId: String, errorCode: Int, errorMsg: String?) {
                    DebugUtil.d(TAG, "initUnifiedSummary, onError:$sessionId, errorCode:$errorCode, errorMsg:$errorMsg")
                    processAISummaryError(mediaId, AISummaryAction.SERVER_ERROR)
                }

                override fun onSuccess(bizTag: String?, extras: Map<String, Any>?) {
                    DebugUtil.d(TAG, "initUnifiedSummary, onSuccess:$bizTag, extras:$extras")
                    result = startSummary(request)
                }
            }
        )
        return result
    }

    /**
     * 开始生成摘要,实现摘要kit相关接口
     */
    private fun startSummary(request: SpeechSummaryRequest): Boolean {
        var result = false
        DebugUtil.d(TAG, "startSummary, summaryKit:$mSummaryKit")
        mSummaryKit?.getSummary(request, object : IAISummarySDKCallback {

            override fun onStart(sessionId: String?, extras: Map<String, Any>?) {
                DebugUtil.d(TAG, "onStart, sessionId:$sessionId, extras:$extras")
                mWorkHandler?.post {
                    if (!sessionId.isNullOrEmpty()) {
                        AISummaryTaskManager.addSessionId(mediaId, sessionId)
                    }
                    callback?.onAISummaryStart(mediaId, extras)
                }
            }

            override fun onError(sessionId: String, errorCode: Int, errorMsg: String?) {
                DebugUtil.d(
                    TAG,
                    "onError, sessionId:$sessionId, errorCode:$errorCode, errorMsg:$errorMsg"
                )
                mWorkHandler?.post {
                    if (errorCode == AISummaryAction.PLUGIN_INIT_ERROR && mErrorRetryCount <= MAX_RETRY_COUNT) {
                        mErrorRetryCount += 1
                        DebugUtil.d(TAG, "startRetryAISummary, errorRetryCount:$mErrorRetryCount")
                        result = startRetryAISummary(request)
                    } else {
                        AISummaryTaskManager.removeSessionId(mediaId, sessionId)
                        processAISummaryError(mediaId, errorCode, errorMsg)
                        result = false
                    }
                }
            }

            /**
             * 流式数据返回
             * @param sessionId 当前请求ID
             * @param jsonResult 流式数据对象的json字符串
             * @param extras 扩展保留参数
             */
            override fun onDataAvailable(
                sessionId: String,
                jsonResult: String,
                extras: Map<String, Any>?
            ) {
                DebugUtil.d(TAG, "onDataAvailable, jsonResult:$jsonResult, extras:$extras")
            }

            /**
             * 流式数据结束回调
             * @param sessionId 当前请求ID
             * @param jsonResult 流式数据对象的json字符串
             * @param extras 扩展保留参数
             */
            override fun onFinished(
                sessionId: String,
                jsonResult: String,
                extras: Map<String, Any>?
            ) {
                DebugUtil.d(TAG, "onFinished, jsonResult:$jsonResult, extras:$extras")
                mWorkHandler?.post {
                    AISummaryTaskManager.removeSessionId(mediaId, sessionId)
                    processAISummaryFinished(mediaId, jsonResult, extras)
                    result = true
                }
            }

            override fun onStop(sessionId: String, extras: Map<String, Any>?) {
                DebugUtil.d(TAG, "onStop, sessionId:$sessionId, extras:$extras")
                if (mIsAbort) {
                    mWorkHandler?.post {
                        callback?.onAISummaryStop(mediaId, extras)
                        AISummaryTaskManager.removeSessionId(mediaId, sessionId)
                        postAISummary(mediaId)
                    }
                }
            }
        })
        callback?.onAISummaryEnd(mediaId)
        return result
    }

    /**
     * 重试AI摘要
     */
    private fun startRetryAISummary(request: SpeechSummaryRequest): Boolean {
        releaseSummaryKit()
        return createSummaryKit(request, true)
    }

    private fun processAISummaryError(mediaId: Long, errorCode: Int, errorMsg: String? = null) {
        DebugUtil.d(TAG, "processAISummaryError, mediaId:$mediaId, errorCode:$errorCode, errorMsg:$errorMsg")

        /* 错误信息操作 */
        when (errorCode) {
            AISummaryAction.PLUGIN_INIT_ERROR,
            AISummaryAction.REQUEST_TIMEOUT,
            AISummaryAction.SERVER_ERROR -> toastMessage("服务异常，生成失败")
            AISummaryAction.CONTENT_LESS_ERROR -> toastMessage(R.string.summary_error_record_short)
            AISummaryAction.NETWORK_ERROR -> toastMessage("无网络连接，生成失败")
            else -> toastMessage("服务异常，生成失败")
        }

        mConvertCallback?.let {
            ConvertTaskThreadManager.unregisterCallback(mediaId, it)
        }

        callback?.onAISummaryError(mediaId, errorCode, errorMsg)

        postAISummary(mediaId)
    }

    private fun processAISummaryFinished(mediaId: Long, jsonResult: String, extras: Map<String, Any>?) {
        val summaryResult = GsonUtil.fromJson(jsonResult, SmartSummaryResult::class.java)
        val result = summaryResult?.lastSummary
        if (summaryResult == null || result.isNullOrEmpty()) {
            DebugUtil.d(TAG, "process AISummary less content")
            processAISummaryError(mediaId, AISummaryAction.CONTENT_LESS_ERROR)
            return
        }
        callback?.onAISummaryFinished(mediaId, jsonResult, extras)
        postAISummary(mediaId)
    }

    private fun postAISummary(mediaId: Long) {
        DebugUtil.d(TAG, "postAISummary, mediaId:$mediaId")
        mProgressCallback?.postAISummary(mediaId)
        if (!isThreadQuit) {
            mWorkHandler?.sendEmptyMessage(MSG_RELEASE)
        }
    }
}