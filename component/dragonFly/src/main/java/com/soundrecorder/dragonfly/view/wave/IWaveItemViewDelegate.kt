/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  IWaveItemViewDelegate
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.dragonfly.view.wave

import android.view.ViewGroup

internal interface IWaveItemViewDelegate {
    fun createNewItemView(parent: ViewGroup): WaveItemView

    fun onBindItemView(rulerView: WaveItemView, position: Int)

    fun halfWidth(): Int
}