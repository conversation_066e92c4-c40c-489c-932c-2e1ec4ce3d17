/*
 Copyright (C), 2008-2023 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardButtonTest
 Description:
 Version: 1.0
 Date: 2023/3/31
 Author: W9013333(v-zhengt<PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2023/3/31 1.0 create
 */

package com.soundrecorder.dragonfly.view.button

import android.graphics.Canvas
import android.os.Build
import android.view.MotionEvent
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito.spy
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [])
class AppCardButtonTest {

    @Test
    fun onDrawTest() {
        val appCardButton = spy(AppCardButton(ApplicationProvider.getApplicationContext()))
        Whitebox.setInternalState(appCardButton, "mAnimType", 1)
        appCardButton.fakeDisable = true
        Whitebox.invokeMethod<Unit>(appCardButton, "onDraw", Canvas())
        Assert.assertTrue(appCardButton.fakeDisable)
        appCardButton.fakeDisable = false
        Whitebox.invokeMethod<Unit>(appCardButton, "onDraw", Canvas())
        Assert.assertFalse(appCardButton.fakeDisable)
        Whitebox.setInternalState(appCardButton, "mAnimType", 2)
        appCardButton.fakeDisable = true
        Whitebox.invokeMethod<Unit>(appCardButton, "onDraw", Canvas())
        Assert.assertTrue(appCardButton.fakeDisable)
        appCardButton.fakeDisable = false
        Whitebox.invokeMethod<Unit>(appCardButton, "onDraw", Canvas())
        Assert.assertFalse(appCardButton.fakeDisable)
    }

    @Test
    fun onTouchEventTest() {
        val appCardButton = spy(AppCardButton(ApplicationProvider.getApplicationContext()))
        appCardButton.fakeDisable = true
        appCardButton.onTouchEvent(MotionEvent.obtain(0, 0, MotionEvent.ACTION_DOWN, 0f, 0f, 0))
        appCardButton.onTouchEvent(MotionEvent.obtain(0, 0, MotionEvent.ACTION_UP, 0f, 0f, 0))
        appCardButton.fakeDisable = false
        appCardButton.onTouchEvent(MotionEvent.obtain(0, 0, MotionEvent.ACTION_DOWN, 0f, 0f, 0))
        appCardButton.onTouchEvent(MotionEvent.obtain(0, 0, MotionEvent.ACTION_UP, 0f, 0f, 0))
    }
}