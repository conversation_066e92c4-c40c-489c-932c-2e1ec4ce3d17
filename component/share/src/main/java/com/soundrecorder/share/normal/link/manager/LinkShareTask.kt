/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - LinkShareTask.kt
 ** Description: LinkShareTask.
 ** Version: 1.0
 ** Date : 2025/3/10
 ** Author: zhangmeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author> <data> <version > <desc>
 ** zhangmeng    2025/3/10    1.0    create
 ****************************************************************/
package com.soundrecorder.share.normal.link.manager

import android.provider.Settings
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.Record
import com.soundrecorder.share.normal.link.bean.ActionResult
import com.soundrecorder.share.normal.link.bean.PreSignatureResponseContent
import com.soundrecorder.share.normal.link.manager.listener.IFileUploaderListener
import com.soundrecorder.share.normal.link.manager.listener.ILinkShareTaskListener
import com.soundrecorder.share.normal.link.utils.AesUtils
import com.soundrecorder.share.normal.link.utils.AesUtils.AES_KEY_LENGTH
import com.soundrecorder.share.normal.link.utils.ConstantUtils
import com.soundrecorder.share.normal.link.utils.HttpUtils
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Response
import java.io.File
import java.io.IOException
import java.util.Locale

object LinkShareTask {
    private const val COPY_CHECK_SIGN_URL_PROGRESS_CODE = 200
    private const val COPY_CHECK_SIGN_URL_ERROR_CODE = -200
    private const val MAX_UPLOADING_AUDIO_FILE_COUNT = 3
    private const val TAG = "LinkShareTask"
    private const val RECORD = "RECORD"
    private var mFileUploader = AudioFileUploader()
    private var linkShareTaskListenerSet: MutableSet<ILinkShareTaskListener> = HashSet()
    private var mAudioFilePath: String? = null

    private val mFileUploaderListener = object : IFileUploaderListener {
        override fun onUploadProgress(progress: Int) {
            DebugUtil.d(TAG, "onUploadProgress progress=$progress")
            doLinkShareTask(mAudioFilePath, progress)
        }

        override fun onUploadComplete() {
            DebugUtil.d(TAG, "onUploadComplete")
            doLinkShareTaskComplete(mAudioFilePath, "")
        }

        override fun onUploadError(error: Int) {
            DebugUtil.e(TAG, "onUploadError error=$error")
            doLinkShareTaskError(mAudioFilePath, error)
        }
    }

    private fun doLinkShareTask(audioFilePath: String?, progress: Int) {
        linkShareTaskListenerSet.forEach {
            it.onLinkShareTask(audioFilePath, progress)
        }
    }

    private fun doLinkShareTaskComplete(audioFilePath: String?, link: String?) {
        linkShareTaskListenerSet.forEach {
            it.onLinkShareTaskComplete(audioFilePath, link)
        }
    }

    private fun doLinkShareTaskError(audioFilePath: String?, error: Int) {
        linkShareTaskListenerSet.forEach {
            it.onLinkShareTaskError(audioFilePath, error)
        }
    }

    /**
     * 开始文件上传
     *
    * */
    fun start(shareId: String, record: Record, recordId: Long) {
        mAudioFilePath = record.data
        mFileUploader.registerListener(mFileUploaderListener)
        LinkShareManager.instance.getGlobalDomainAndDoCallBack {
            getPreSignUrlAndStartUpload(shareId, record, recordId)
        }
    }

    fun registerLinkShareTaskListener(listener: ILinkShareTaskListener) {
        linkShareTaskListenerSet.add(listener)
    }

    fun unregisterLinkShareListener(listener: ILinkShareTaskListener) {
        linkShareTaskListenerSet.remove(listener)
    }

    /**
     * 请求获取预签名接口
     * 获取上传音频文件的预签名url
     *
    * */
    private fun getPreSignUrlAndStartUpload(shareId: String, record: Record, recordId: Long) {
        DebugUtil.d(TAG, "getPreSignUrlAndStartUpload")
        val domainName = LinkShareManager.instance.getDomainName()
        val baseUrl = ConstantUtils.HTTP + domainName
        val audioFilePath = record.data
        val aesKey = AesUtils.randomToken(AES_KEY_LENGTH)
        val map = mutableMapOf<String, Any>()
        map[ConstantUtils.PreSignUrlParam.FILE_NAME] = record.displayName
        map[ConstantUtils.PreSignUrlParam.FILE_SIZE] = record.fileSize
        map[ConstantUtils.PreSignUrlParam.DEVICE_ID] =
            Settings.Secure.getString(BaseApplication.getAppContext().contentResolver, Settings.Secure.ANDROID_ID)
        map[ConstantUtils.PreSignUrlParam.USER_ID] = ""
        map[ConstantUtils.PreSignUrlParam.FILE_LOCAL_ID] = recordId
        map[ConstantUtils.PreSignUrlParam.FILE_TYPE] = RECORD
        map[ConstantUtils.PreSignUrlParam.BUSINESS_TYPE] = RECORD
        map[ConstantUtils.PreSignUrlParam.BUSINESS_ID] = shareId
        map[ConstantUtils.PreSignUrlParam.REGION] = Locale.getDefault().country
        val requestBody = Gson().toJson(map)
        HttpUtils.sendAsynPostEncryptedRequest(baseUrl, ConstantUtils.GET_PRE_SIGNATURE_PATH, requestBody, aesKey, object : Callback {
            override fun onResponse(call: Call, response: Response) {
                val responseData = response.body?.string()
                val decryptedRealResponseBody = HttpUtils.getDecryptedRealResponseBody(responseData, aesKey, "getPreSignUrlAndStartUpload")
                val typeToken = object : TypeToken<ActionResult<PreSignatureResponseContent>>() {}.type
                val decryptedActionResult = Gson().fromJson<ActionResult<PreSignatureResponseContent>>(decryptedRealResponseBody, typeToken)
                if (decryptedActionResult.code != 0) {
                    DebugUtil.e(TAG, "getPreSignUrlAndStartUpload code=${decryptedActionResult.code}")
                    doLinkShareTaskError(audioFilePath, decryptedActionResult.code)
                    return
                }
                DebugUtil.i(TAG, "getPreSignUrlAndStartUpload audioFileExist=${decryptedActionResult.data?.exist}")
                if (decryptedActionResult.data?.exist == false) {
                    doLinkShareTask(audioFilePath, COPY_CHECK_SIGN_URL_PROGRESS_CODE)
                    val preSignedUrl = decryptedActionResult.data?.signUrl
                    val fileId = decryptedActionResult.data?.fileId
                    val file = File(audioFilePath)
                    if (!preSignedUrl.isNullOrEmpty() && !fileId.isNullOrEmpty() && file.exists()) {
                        mFileUploader.startUpload(file, preSignedUrl, fileId, shareId, recordId)
                    }
                }
            }

            override fun onFailure(call: Call, e: IOException) {
                DebugUtil.e(TAG, " requestPreSignedUploadLinkInterface onFailure javaClass=${e.javaClass} ${e.message}")
                doLinkShareTaskError(audioFilePath, COPY_CHECK_SIGN_URL_ERROR_CODE)
            }
        })
    }

    fun canUploadMoreAudioFiles(): Boolean {
        val currentUploadingAudioFileCount = mFileUploader.getCurrentUploadingAudioFileCount()
        return currentUploadingAudioFileCount < MAX_UPLOADING_AUDIO_FILE_COUNT
    }
}