/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - ActionResult.kt
 ** Description: ActionResult.
 ** Version: 1.0
 ** Date : 2025/3/10
 ** Author: zhangmeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author> <data> <version > <desc>
 ** zhangmeng    2025/3/10    1.0    create
 ****************************************************************/
package com.soundrecorder.share.normal.link.bean

import androidx.annotation.Keep

@Keep
data class ActionResult<T>(
    val code: Int,
    val msg: String,
    var data: T?,
    val traceId: String
)

// 请求加密的基础内容
@Keep
data class ResponseContent(
    val scene: String,
    val iv: String,             // iv向量
    val encryptContent: String  // 加密的响应体
)

@Keep
data class ZoneServerAddressResponseContent(
    val env: String,             // 开发环境
    val requestRegion: String,   // 区域
    val requestDomain: String    // 域名
)

@Keep
data class ShareRegisterResponseContent(
    val auditStatus: String,  // 审核状态
    val shareId: String,      // 分享id
    val auditId: String,      // 文本审核id
    val url: String           // 分享链接
)

@Keep
data class PreSignatureResponseContent(
    val signUrl: String,      // 签名url
    val expTimeMills: Long,   // 过期时间
    val fileId: String,       // 文件Id
    val fileStatus: String,   // 文件状态
    val exist: Boolean        // 音频文件是否存在
)