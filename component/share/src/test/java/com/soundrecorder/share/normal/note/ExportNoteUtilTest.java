/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.share.normal.note;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.net.Uri;
import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.BaseApplication;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import java.util.ArrayList;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S)
public class ExportNoteUtilTest {

    private Context mContext;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = BaseApplication.getAppContext();
    }

    @After
    public void tearDown() {
        mContext = null;
    }

    @Test
    public void should_return_true_when_grantUriPermission() throws Exception {
        ExportNoteUtil util = Mockito.mock(ExportNoteUtil.class);
        String pkgName = "com.coloros.note";
        Uri uri1 = Uri.parse("test1");
        Uri uri2 = Uri.parse("test2");
        util.grantUriPermission(mContext, pkgName, uri1);
        util.grantUriPermission(mContext, pkgName, uri2);
        Mockito.verify(util, Mockito.times(1)).grantUriPermission(mContext, pkgName, uri1);
        Mockito.verify(util, Mockito.times(1)).grantUriPermission(mContext, pkgName, uri2);
    }

    @Test
    public void should_return_true_when_revokeUriPermission() {
        ExportNoteUtil util = Mockito.mock(ExportNoteUtil.class);
        String pkgName = "com.coloros.note";
        List<Uri> list = new ArrayList<>();
        Uri uri1 = Uri.parse("test1");
        Uri uri2 = Uri.parse("test2");
        list.add(uri1);
        list.add(uri2);
        util.grantUriPermission(mContext, pkgName, uri1);
        util.grantUriPermission(mContext, pkgName, uri2);
        util.revokeUriPermission(mContext, list);
        Mockito.verify(util, Mockito.times(1)).grantUriPermission(mContext, pkgName, uri1);
        Mockito.verify(util, Mockito.times(1)).grantUriPermission(mContext, pkgName, uri2);
        Mockito.verify(util, Mockito.times(1)).revokeUriPermission(mContext, list);
        Assert.assertNotNull(list);
    }

    @Test
    public void should_return_true_when_getLocalId() throws Exception {
        Uri uri = Mockito.mock(Uri.class);
        String localId = ExportNoteUtil.INSTANCE.getLocalId(uri);
        Assert.assertEquals(localId, "");
    }

    @Test
    public void should_fail_when_insertToNote() throws Exception {
        ContentResolver mockContentResolver = Mockito.mock(ContentResolver.class);
        Context mockContext = Mockito.mock(Context.class);
        Mockito.when(mockContext.getContentResolver()).thenReturn(mockContentResolver);
        Mockito.when(mockContentResolver.insert(Mockito.any(Uri.class), Mockito.any(ContentValues.class))).thenReturn(Mockito.mock(Uri.class));

        Uri result = ExportNoteUtil.INSTANCE.insertToNote(mockContext, new ContentValues());
        Assert.assertNull(result);

        Mockito.when(mockContentResolver.insert(Mockito.any(Uri.class), Mockito.any(ContentValues.class))).thenThrow(new NullPointerException());
        result = ExportNoteUtil.INSTANCE.insertToNote(mockContext, new ContentValues());
        Assert.assertNull(result);
    }

    @Test
    public void should_success_when_insertToNote() {
        ContentResolver mockContentResolver = Mockito.mock(ContentResolver.class);
        Uri mockUri = Mockito.mock(Uri.class);
        Context mockContext = Mockito.mock(Context.class);
        Mockito.when(mockUri.getQueryParameter("result")).thenReturn("success");
        Mockito.when(mockContext.getContentResolver()).thenReturn(mockContentResolver);
        Mockito.when(mockContentResolver.insert(Mockito.any(Uri.class), Mockito.any(ContentValues.class))).thenReturn(mockUri);

        Uri result = ExportNoteUtil.INSTANCE.insertToNote(mockContext, new ContentValues());
        Assert.assertNotNull(result);
    }

    @Test
    public void should_fail_when_deleteNote() throws Exception {
        Uri mockUri = Mockito.mock(Uri.class);
        // empty
        ExportNoteUtil.INSTANCE.deleteNote(mContext, List.of(mockUri));

        Context mockContext = Mockito.mock(Context.class);
        ContentResolver mockContentResolver = Mockito.mock(ContentResolver.class);
        Mockito.when(mockContext.getContentResolver()).thenReturn(mockContentResolver);
        // exception
        Mockito.when(mockContentResolver.delete(Mockito.any(Uri.class), Mockito.anyString(), Mockito.any(String[].class))).thenThrow(new NullPointerException());
        ExportNoteUtil.INSTANCE.deleteNote(mockContext, List.of(mockUri));
    }
}
