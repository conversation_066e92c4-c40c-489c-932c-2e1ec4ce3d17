/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RealtimeConvertManager
 * Description:
 * Version: 1.0
 * Date: 2025/3/6
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2025/3/6 1.0 create
 */

package com.soundrecorder.translate.asr

import android.os.Bundle
import android.os.SystemClock
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.translate.asr.listener.IRealTimeAsrListener
import com.soundrecorder.translate.asr.realtime.AIRealTimeAsrClient
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser.Companion.STATUS_NET_CONNECTED
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentMap

class RealtimeAsrManager {
    private val logTag = "RealtimeAsrManager"
    private var asrClient: AIRealTimeAsrClient? = null
    private var listener: IRealTimeAsrListener? = null
    private val asrResultMap = LinkedHashMap<String, ConvertContentItem>()

    var asrCallback: RealtimeAsrCallback? = null
    var offsetManager: RealtimeOffsetManager? = null

    /*记录startAsr的成功结果、调用stop后，该值修改为false*/
    private val startAsrResultMap: ConcurrentMap<String, Boolean> = ConcurrentHashMap()
    /*记录initAsr结果，true：成功 false：失败*/
    private val initAsrResultMap: ConcurrentMap<String, Boolean> = ConcurrentHashMap()
    /*记录调用stopAsr，true：已经调用过了，false：还未调用*/
    private val stopAsrFlagMap: ConcurrentMap<String, Boolean> = ConcurrentHashMap()

    private var isDestroy = false

    @Volatile
    var isError = false

    init {
        runCatching {
            val startTime = SystemClock.elapsedRealtime()
            asrClient = AIRealTimeAsrClient(BaseApplication.getAppContext())
            DebugUtil.d(logTag, "new client spend ${SystemClock.elapsedRealtime() - startTime}")
        }.onFailure {
            DebugUtil.e(logTag, "init error $it")
        }
        listener = object : IRealTimeAsrListener {
            override fun onStatus(channelId: String?, bizType: String?, code: Int?, msg: String?) {
                /**流程结束，忽略状态回调*/
                if (isDestroy) {
                    DebugUtil.i(logTag, "on status return destory")
                    return
                }

                when (bizType) {
                    RealTimeAsrParser.BIZ_TYPE_START_ASK -> {
                        if (code == 0) { // startAsr 成功回调
                            channelId?.let {
                                startAsrResultMap[it] = true
                            }
                            return
                        }
                    }

                    RealTimeAsrParser.BIZ_TYPE_END_ASK -> {
                        asrCallback?.onStopAsrResult(code == 0, channelId)
                        if (code == 0) { // stopAsr 成功回调
                            return
                        }
                    }
                    RealTimeAsrParser.BIZ_TYPE_AUDIO -> { // 发送音频数据错误
                        if (code == RealTimeAsrParser.ERROR_NOT_FOUND_CHANNEL_ID && stopAsrFlagMap[channelId] == true) {
                            /*忽略发送数据，通道被关闭的场景*/
                            return
                        }
                    }

                    RealTimeAsrParser.BIZ_TYPE_SERVER_END_ASK -> { // 服务端主动断开链路标志
                        DebugUtil.w(logTag, "on status server end")
                        channelId?.let {
                            if (startAsrResultMap[it] == true) {
                                startAsrResultMap[it] = false
                            }
                        }
                    }
                }

                when (code) {
                    RealTimeAsrParser.STATUS_INIT_SUCCESS -> {
                        initAsrResultMap[channelId] = true
                        asrCallback?.onInitResult(channelId, true)
                        return
                    }

                    RealTimeAsrParser.STATUS_INIT_ERROR -> {
                        initAsrResultMap[channelId] = false
                        asrCallback?.onInitResult(channelId, false)
                        return
                    }
                    /**网络已连接*/
                    STATUS_NET_CONNECTED -> return
                    else -> {
                        if (channelId?.isNotBlank() == true && stopAsrFlagMap[channelId] == true) {
                            DebugUtil.d(logTag, "ignore error by stop")
                            return
                        }
                        isError = true
                        asrCallback?.onError(channelId, code, msg)
                    }
                }
            }

            override fun onAsrResult(channelId: String?, msgId: String?, type: String, startOffset: Int, endOffset: Int, text: String) {
                msgId?.let {
                    asrResultMap[it] = ConvertContentItem().apply {
                        val offset = offsetManager?.getAudioTimeDuration(channelId) ?: 0
                        startTime = offset + startOffset.toLong()
                        endTime = offset + endOffset.toLong()
                        textContent = text
                    }
                }
            }
        }
        listener?.let {
            asrClient?.registerAsrListener(it)
        }
    }

    fun initAsr(param: Bundle?) {
        DebugUtil.i(logTag, "init asr")
        runCatching {
            asrClient?.initAsr(param)
        }.onFailure {
            DebugUtil.e(logTag, "initAsr error $it")
        }
    }

    fun startAsr(channelId: String, param: Bundle?) {
        /*已经调用过stopAsr了,避免init回来之前用户点击了暂停*/
        if (stopAsrFlagMap[channelId] == true) {
            DebugUtil.w(logTag, "startAsr return by stop")
            return
        }
        runCatching {
            asrClient?.startAsr(channelId, param)
        }.onFailure {
            DebugUtil.e(logTag, "startAsr error $it")
        }
    }

    fun getStartAsrResult(channelId: String): Boolean {
        return startAsrResultMap[channelId] ?: false
    }

    fun stopAsr(channelId: String) {
        /*已经调用过stopAsr了*/
        if (stopAsrFlagMap[channelId] == true) {
            return
        }
        runCatching {
            asrClient?.stopAsr(channelId)
            stopAsrFlagMap[channelId] = true
            startAsrResultMap[channelId] = false
        }.onFailure {
            DebugUtil.e(logTag, "stopAsr error $it")
        }
    }

    fun processAudioData(channelId: String, data: ByteArray?) {
        runCatching {
            asrClient?.processRealTimeData(channelId, data)
        }.onFailure {
            DebugUtil.e(logTag, "processAudioData error $it")
        }
    }

    fun releaseChannel(channelId: String) {
        runCatching {
            asrClient?.releaseChannel(channelId)
        }.onFailure {
            DebugUtil.e(logTag, "release error $it")
        }
    }

    fun release() {
        isDestroy = true
        runCatching {
            asrClient?.release()
        }.onFailure {
            DebugUtil.e(logTag, "release error $it")
        }
        listener = null
        asrClient = null
    }

    fun isStopAsr(channelId: String): Boolean {
        return stopAsrFlagMap[channelId] ?: false
    }

    fun getAsrContent(): List<ConvertContentItem> {
        return asrResultMap.values.toList()
    }
}

interface RealtimeAsrCallback {

    fun onInitResult(channelId: String?, success: Boolean)
    fun onError(channelId: String?, code: Int?, msg: String?)

    fun onStopAsrResult(result: Boolean, channelId: String?)
}

