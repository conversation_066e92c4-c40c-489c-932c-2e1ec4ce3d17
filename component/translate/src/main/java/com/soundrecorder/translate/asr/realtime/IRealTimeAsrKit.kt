/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: IAiAsrClient
 * Description:
 * Version: 1.0
 * Date: 2025/3/18
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2025/3/18 1.0 create
 */

package com.soundrecorder.translate.asr.realtime

import android.os.Bundle
import com.soundrecorder.translate.asr.listener.IRealTimeAsrListener

interface IRealTimeAsrKit {

    fun registerAsrListener(listener: IRealTimeAsrListener?)
    fun unRegisterAsrListener(listener: IRealTimeAsrListener?)

    /**
     * 初始化asr
     */
    fun initAsr(params: Bundle?)

    /**
     * 开始asr通道
     */
    fun startAsr(channelId: String, extra: Bundle?)

    /**
     * 上传实时ASR数据
     */
    fun processRealTimeData(channelId: String, byteArray: ByteArray?) {}

    /**
     * 停止asr通道
     */
    fun stopAsr(channelId: String)

    fun releaseChannel(channelId: String)

    /**
     * 释放asr
     */
    fun release()
}