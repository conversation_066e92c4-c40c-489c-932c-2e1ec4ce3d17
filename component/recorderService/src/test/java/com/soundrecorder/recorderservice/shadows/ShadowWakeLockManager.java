/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ShadowOppoUsbEnvironment.java
 Description:
 Version: 1.0
 Date: 2019/8/22
 Author: <PERSON>I <PERSON>n
 -----------Revision History-----------
 <author> <date> <version> <desc>
 LI Kun 2019/8/22 1.0 create
 */

package com.soundrecorder.recorderservice.shadows;

import com.soundrecorder.common.manager.WakeLockManager;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(WakeLockManager.class)
public class ShadowWakeLockManager {

    @Implementation
    public void releaseWakeLock() {

    }
}
