/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderControllerTest
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.controller

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.recorderservice.controller.observer.ControllerObserver
import com.soundrecorder.recorderservice.controller.observer.WaveObserver
import com.soundrecorder.recorderservice.shadows.ShadowFeatureOption
import com.soundrecorder.recorderservice.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config
import com.soundrecorder.wavemark.wave.view.MaxAmplitudeSource
import org.junit.Assert
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import java.util.*

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class RecorderControllerTest {
    private var mContext: Context? = null
    private var mControllerObserver: ControllerObserver<MaxAmplitudeSource>? = null
    private var mWaveObserver: WaveObserver? = null
    private var recorderController: RecorderController? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mControllerObserver = Mockito.mock(ControllerObserver::class.java) as ControllerObserver<MaxAmplitudeSource>?
        mWaveObserver = Mockito.mock(WaveObserver::class.java)
        recorderController = RecorderController.Builder()
                .setControllerObserver(mControllerObserver!!)
                .setWaveObserver(mWaveObserver)
                .build()
                .initSample()
    }

    @After
    fun tearDown() {
        mContext = null
        mControllerObserver = null
        recorderController = null
    }

    @Test
    fun should_notNull_when_startSample() {
        recorderController?.startSample(100, 2000)
        var mWorkerTimer = Whitebox.getInternalState<Timer>(recorderController, "mWorkerTimer")
        Assert.assertNotNull(mWorkerTimer)
    }

    @Test
    fun should_null_when_startSample() {
        recorderController?.startSample(100, 2000)
        recorderController?.stopSample()
        var mWorkerTimer = Whitebox.getInternalState<Timer>(recorderController, "mWorkerTimer")
        Assert.assertNull(mWorkerTimer)
    }

  /*  @Test
    fun should_null_when_release() {
        recorderController?.saveRecordInfo()
        recorderController?.startSample(100, 2000)
        recorderController?.release()
        var mWorkerTimer = Whitebox.getInternalState<Timer>(recorderController, "mWorkerTimer")
        var WaveSampleWorker = Whitebox.getInternalState<Timer>(recorderController, "mWaveSampleWorker")
        Assert.assertNull(mWorkerTimer)
        Assert.assertNull(WaveSampleWorker)
    }*/
}