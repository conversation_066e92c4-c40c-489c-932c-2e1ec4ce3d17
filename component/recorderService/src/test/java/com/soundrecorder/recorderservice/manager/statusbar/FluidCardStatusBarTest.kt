/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SeedlingStatusBarTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/7/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.recorderservice.manager.statusbar

import android.content.Context
import android.os.Build
import android.os.Handler
import androidx.lifecycle.LiveData
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.modulerouter.SeedlingAction
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import com.soundrecorder.recorderservice.api.RecorderViewModelApi
import com.soundrecorder.recorderservice.manager.RecordStatusManager
import com.soundrecorder.recorderservice.shadows.ShadowFeatureOption
import com.soundrecorder.recorderservice.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.recorderservice.shadows.ShadowOplusCompactUtil
import org.json.JSONObject
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowOplusCompactUtil::class]
)
class FluidCardStatusBarTest {

    private var ctx: Context? = null
    private var seedlingActionStatic: MockedStatic<SeedlingAction>? = null
    private var recorderViewModelApi: MockedStatic<RecorderViewModelApi>? = null

    @Before
    fun setUp() {
        ctx = ApplicationProvider.getApplicationContext()
        seedlingActionStatic = Mockito.mockStatic(SeedlingAction::class.java)
        recorderViewModelApi = Mockito.mockStatic(RecorderViewModelApi::class.java)
    }

    @After
    fun reset() {
        ctx = null
        seedlingActionStatic?.close()
        seedlingActionStatic = null
        recorderViewModelApi?.close()
        recorderViewModelApi = null
    }

    private fun <T> any(type: Class<T>): T = Mockito.any<T>(type)

    @Test
    fun should_invoke_method_when_refreshSeedlingData() {
        Whitebox.setInternalState(FluidCardStatusBar::class.java, "showState", FluidCardStatusBar.FluidCardShowState.SHOWING)
        FluidCardStatusBar.refreshSeedlingData("test")
        seedlingActionStatic?.verify({
            SeedlingAction.refreshSeedlingData(any(JSONObject::class.java))
        }, Mockito.times(1))
    }

    @Test
    fun should_invoke_method_when_refreshMarkTimeSeedlingData() {
        Whitebox.setInternalState(FluidCardStatusBar::class.java, "showState", FluidCardStatusBar.FluidCardShowState.SHOWING)
        FluidCardStatusBar.refreshMarkTimeSeedlingData(false)
        seedlingActionStatic?.verify({
            SeedlingAction.refreshSeedlingData(any(JSONObject::class.java))
        }, Mockito.times(1))
    }

    @Test
    fun should_not_null_when_createSeedlingDataInfo() {
        Mockito.`when`(RecorderViewModelApi.getAmplitudeCurrentTime()).thenReturn(2000)
        Mockito.`when`(RecorderViewModelApi.getCurrentStatus()).thenReturn(RecordStatusManager.RECORDING)
        Mockito.`when`(RecorderViewModelApi.getLastMarkTime()).thenReturn(1000)
        Mockito.`when`(RecorderViewModelApi.isMarkEnabledFull()).thenReturn(true)
        val data = FluidCardStatusBar.createSeedlingDataInfo()
        Assert.assertEquals(2000, data.curTime)
    }

    @Test
    fun should_equals_when_getRecordEnableState() {
        RecorderViewModelAction.saveFileState = RecorderViewModelAction.SaveFileState.INIT
        Mockito.`when`(RecorderViewModelApi.isAudioModeChangePause()).thenReturn(false, true)
        var enableState = FluidCardStatusBar.getRecordEnableState()
        Assert.assertTrue(enableState)

        enableState = FluidCardStatusBar.getRecordEnableState()
        Assert.assertFalse(enableState)

        RecorderViewModelAction.saveFileState = RecorderViewModelAction.SaveFileState.START_LOADING
        enableState = FluidCardStatusBar.getRecordEnableState()
        Assert.assertFalse(enableState)
    }

    @Test
    fun should_equals_when_getSeedlingData() {
        Mockito.`when`(SeedlingAction.isSupportFluidCloud(any(Context::class.java))).thenReturn(true)
        val firstData = SeedlingDataInfo(curTime = 3000, curStatus = RecordStatusManager.RECORDING)
        val firstSeedlingData = FluidCardStatusBar.getSeedlingData(firstData)
        Assert.assertEquals(firstData, Whitebox.getInternalState(FluidCardStatusBar::class.java, "lastSeedlingDataInfo"))

        val secondData = SeedlingDataInfo(curTime = 3500, curStatus = RecordStatusManager.RECORDING)
        val secondSeedlingData = FluidCardStatusBar.getSeedlingData(secondData)
        Assert.assertEquals(firstSeedlingData.toString(), secondSeedlingData.toString())
    }

    @Test
    @Ignore
    fun should_equals_when_checkMarkTimeInfo() {
        val firstData = SeedlingDataInfo(curMarkTime = 2000, saveFileState = RecorderViewModelAction.saveFileState)
        val secondData = SeedlingDataInfo(curMarkTime = 3000, saveFileState = RecorderViewModelAction.saveFileState)
        FluidCardStatusBar.checkMarkTimeInfo(secondData, firstData)
        val handler = Whitebox.getInternalState<Handler>(FluidCardStatusBar::class.java, "mainHandler")
        val runnable = Whitebox.getInternalState<Runnable>(FluidCardStatusBar::class.java, "checkMarkTimeRunnable")
        Assert.assertTrue(handler.hasCallbacks(runnable))
    }

    @Test
    fun should_equals_when_show() {
        val ctx = this.ctx ?: return
        FluidCardStatusBar.show(ctx, true, "test")
        Assert.assertEquals(Whitebox.getInternalState(FluidCardStatusBar::class.java, "showRetryTimes"), 1)
    }

    @Test
    fun should_equals_when_dismiss() {
        val ctx = this.ctx ?: return
        FluidCardStatusBar.dismiss(ctx, true, "test")
        Assert.assertEquals(1, Whitebox.getInternalState(FluidCardStatusBar::class.java, "hideRetryTimes"))
    }

    @Test
    @Ignore
    fun should_equals_when_register() {
        FluidCardStatusBar.register()
        val screenLiveData = Whitebox.getInternalState<LiveData<Boolean>>(FluidCardStatusBar::class.java, "screenStateLiveData")
        Assert.assertTrue(screenLiveData.hasObservers())
    }

    @Test
    fun should_equals_when_unregister() {
        FluidCardStatusBar.unRegister()
        Assert.assertNull(Whitebox.getInternalState(FluidCardStatusBar::class.java, "lastSeedlingDataInfo"))
    }

    @Test
    fun should_null_when_release() {
        FluidCardStatusBar.release()
        Assert.assertEquals(FluidCardStatusBar.FluidCardShowState.DEFAULT, Whitebox.getInternalState(FluidCardStatusBar::class.java, "showState"))
    }
}