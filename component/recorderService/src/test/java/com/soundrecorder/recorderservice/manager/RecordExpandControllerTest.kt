/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AudioModeChangeManagerTest
 Description:
 Version: 1.0
 Date: 2022/10/9
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/10/9 1.0 create
 */

package com.soundrecorder.recorderservice.manager

import android.content.Context
import android.media.AudioManager
import android.net.Uri
import android.os.Build
import android.os.ParcelFileDescriptor
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.BaseUtil.isAndroidQOrLater
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.constant.RecorderConstant
import com.soundrecorder.recorderservice.manager.RecordStatusManager.HALT_ON
import com.soundrecorder.recorderservice.manager.RecordStatusManager.PAUSED
import com.soundrecorder.recorderservice.manager.RecordStatusManager.RECORDING
import com.soundrecorder.recorderservice.recorder.listener.IBizRecorder
import com.soundrecorder.recorderservice.shadows.ShadowFeatureOption
import com.soundrecorder.recorderservice.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.recorderservice.shadows.ShadowOplusUsbEnvironment
import io.mockk.every
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config
import java.io.File
import com.soundrecorder.recorderservice.R
import com.soundrecorder.recorderservice.RecordResult
import com.soundrecorder.recorderservice.manager.DiskStorageChecker.checkLastMaxTime
import com.soundrecorder.recorderservice.manager.RecordStatusManager.INIT
import org.junit.After
import java.lang.reflect.Field

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowOplusUsbEnvironment::class]
)
class RecordExpandControllerTest {

    private var mContext: Context? = null
    private var mExpandController: RecordExpandController? = null
    private var mRecordFileObserver: RecordFileObserver? = null
    private var mRecordFileWraper: RecordExpandController.RecordFileWraper? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        val config = RecordExpandController.RecordConfig()
        val otherConfig = RecordExpandController.OtherConfig()
        val muteConfig = MuteModeOperator.MuteConfig()
        mExpandController = RecordExpandController(config, otherConfig, muteConfig)
        mRecordFileObserver = RecordFileObserver()
        mRecordFileWraper = RecordExpandController.RecordFileWraper(recordConfig = config)
        mExpandController?.mRecordFileWraper = mRecordFileWraper
        Whitebox.setInternalState(mExpandController, "mRecordFileObserver", mRecordFileObserver)
    }

    @After
    fun tearDown() {
        mContext = null
        mRecordFileWraper = null
        mRecordFileObserver = null
        mExpandController = null
    }

    @Test
    fun should_notNull_when_updateRecordConfig() {
        val config = RecordExpandController.RecordConfig()
        config.format = RecorderConstant.RECORDER_AUDIO_FORMAT_MP3
        config.recordType = RecordModeConstant.RECORD_TYPE_STANDARD
        config.maxSizeLimit = 1000L
        config.maxDurationLimit = 10000
        mExpandController?.updateRecordConfig(config)
        Assert.assertNotNull(mExpandController?.config)
        Assert.assertTrue((mExpandController?.config?.maxSizeLimit ?: 0) == 1000L)
        Assert.assertTrue((mExpandController?.config?.maxDurationLimit ?: 0) == 10000)
    }

    @Test
    fun should_notNull_when_updateOtherConfig() {
        val otherConfig = RecordExpandController.OtherConfig()
        otherConfig.isFromOtherApp = false
        otherConfig.recordFromType = RecordExpandController.OtherConfig.START_FROM_NORMAL
        otherConfig.isNeedResult = false
        mExpandController?.updateOtherConfig(otherConfig)
        Assert.assertNotNull(mExpandController?.otherConfig)
        Assert.assertTrue(mExpandController?.otherConfig?.isFromSlidBar() == false)
        Assert.assertTrue(mExpandController?.otherConfig?.isFromBreno() == false)
        Assert.assertTrue(mExpandController?.otherConfig?.isFromBrenoFront() == false)
        Assert.assertTrue(mExpandController?.otherConfig?.isFromAppCard() == false)
        Assert.assertTrue(mExpandController?.otherConfig?.isFromSmallCard() == false)
        Assert.assertTrue(mExpandController?.otherConfig?.isFromMiniApp() == false)
        Assert.assertTrue(mExpandController?.otherConfig?.recordFromType == RecordExpandController.OtherConfig.START_FROM_NORMAL)
    }

    @Test
    fun should_notNull_when_setBlockDelete() {
        Assert.assertNotNull(Whitebox.getField(mExpandController?.javaClass, "mRecordFileObserver"))
        mExpandController?.setBlockDelete(true)
        Assert.assertTrue(mRecordFileObserver?.mBlockDelete == true)
    }

    @Test
    fun should_notNull_when_start() {
        val recordResult = mExpandController?.start()
        Assert.assertNotNull(recordResult)
    }

    @Test
    fun should_notNull_when_resume() {
        val recordResult1 = mExpandController?.resume()
        Assert.assertNotNull(recordResult1)
        Assert.assertTrue(recordResult1?.errorCode == 50)
        RecordStatusManager.changeRecordStatus(PAUSED)
        val recordResult2 = mExpandController?.resume()
        Assert.assertNotNull(recordResult2)
        Assert.assertTrue(recordResult2?.errorCode == 4)
    }

    @Test
    fun should_notNull_when_pause() {
        val recordResult1 = mExpandController?.pause()
        Assert.assertNotNull(recordResult1)
        Assert.assertTrue(recordResult1?.errorCode == 100)
        RecordStatusManager.changeRecordStatus(RECORDING)
        val recordResult2 = mExpandController?.pause()
        Assert.assertNotNull(recordResult2)
        Assert.assertTrue(recordResult2?.errorCode == 0)
    }

    @Test
    fun should_notNull_when_stop() {
        val recordResult1 = mExpandController?.stop()
        Assert.assertNotNull(recordResult1)
        Assert.assertTrue(recordResult1?.errorCode == 0)
        RecordStatusManager.changeRecordStatus(HALT_ON)
        val recordResult2 = mExpandController?.stop()
        Assert.assertNotNull(recordResult2)
        Assert.assertTrue(recordResult2?.errorCode == 150)
    }

    @Test
    fun should_notNull_when_startRecord() {
        checkLastMaxTime()
        Whitebox.invokeMethod<RecordExpandController>(
            mExpandController,
            "startRecord",
            false
        )
    }

    @Test
    fun should_notNull_when_doPause() {
        Whitebox.invokeMethod<RecordResult>(
            mExpandController,
            "doPause"
        )
        Assert.assertTrue(RecordStatusManager.getCurrentStatus() == PAUSED)
    }

    @Test
    fun should_notNull_when_doResume() {
        mExpandController?.mRecorder = Mockito.mock(IBizRecorder::class.java)
        mockkStatic(DiskStorageChecker::class)
        every { checkLastMaxTime() } returns 2000L
        val recordResult1: RecordResult = Whitebox.invokeMethod(
            mExpandController,
            "doResume"
        )
        Assert.assertNotNull(recordResult1)
    }

    @Test
    fun should_notNull_when_doStop() {
        RecordStatusManager.changeRecordStatus(RECORDING)
        Whitebox.invokeMethod<RecordResult>(
            mExpandController,
            "doStop"
        )
        Assert.assertTrue(RecordStatusManager.getCurrentStatus() == HALT_ON)
    }

    @Test
    fun should_notNull_when_doStart() {
        RecordStatusManager.changeRecordStatus(HALT_ON)
        mExpandController?.mRecorder = Mockito.mock(IBizRecorder::class.java)
        Whitebox.invokeMethod<RecordExpandController>(
            mExpandController,
            "doStart",
            false
        )
        mRecordFileWraper?.sampleFileUri = Mockito.mock(Uri::class.java)
        mRecordFileWraper?.mPfd = Mockito.mock(ParcelFileDescriptor::class.java)
        RecordStatusManager.changeRecordStatus(INIT)
        val recordResult1: RecordResult = Whitebox.invokeMethod(
            mExpandController,
            "doStart",
            false
        )
        Assert.assertNotNull(recordResult1)
        mExpandController?.config?.maxSizeLimit = 10000
        mExpandController?.config?.maxDurationLimit = 10000
        val recordResult2: RecordResult = Whitebox.invokeMethod(
            mExpandController,
            "doStart",
            true
        )
        Assert.assertNotNull(recordResult2)
    }

    @Test
    fun should_notNull_when_onCancelRecord() {
        mExpandController?.mRecorder = Mockito.mock(IBizRecorder::class.java)
        Assert.assertNotNull(mExpandController?.mRecorder)
        mExpandController?.onCancelRecord()
        Assert.assertTrue(RecordStatusManager.getCurrentStatus() == HALT_ON)
        Assert.assertTrue(mRecordFileObserver?.mBlockDelete == true)
        Assert.assertNull(mExpandController?.mRecorder)
    }

    @Test
    fun should_notNull_when_onFatalError() {
        mExpandController?.mRecordFileWraper?.sampleFileUri = Mockito.mock(Uri::class.java)
        mExpandController?.mRecordFileWraper?.sampleFile = File("c://files")
        mockkStatic(BaseUtil::class)
        every { isAndroidQOrLater } returns false
        mExpandController?.onFatalError(com.soundrecorder.common.R.string.error_start_fail_v2)
        every { isAndroidQOrLater } returns true
        mExpandController?.onFatalError(com.soundrecorder.common.R.string.error_start_fail_v2)
        Assert.assertFalse(mExpandController?.mRecordFileWraper?.sampleFile?.path == "c://files")
    }

    @Test
    fun should_notNull_when_deleteInvalidRecordDbFile() {
        mRecordFileWraper?.mRecordDbId = 100
        mExpandController?.deleteInvalidRecordDbFile()
    }

    @Test
    fun should_notNull_when_setMute() {
        Whitebox.invokeMethod<RecordExpandController>(mExpandController, "setMute", true)
        val mMuteModeOperator: Field? =
            Whitebox.getField(mExpandController?.javaClass, "mMuteModeOperator")
        Assert.assertNotNull(mMuteModeOperator)
    }

    @Test
    fun should_notNull_when_releaseAll() {
        mExpandController?.releaseAll()
        Assert.assertNull(mExpandController?.mRecorder)
        Assert.assertNull(mExpandController?.mRecordFileWraper)
    }

    @Test
    fun should_notNull_when_getMaxAmplitude() {
        mExpandController?.mRecorder = Mockito.mock(IBizRecorder::class.java)
        Mockito.`when`(mExpandController?.mRecorder?.getMaxAmplitude()).thenReturn(1000)
        val maxAmp = mExpandController?.maxAmplitude
        Assert.assertTrue(maxAmp == 1000)
    }

    @Test
    fun should_notNull_when_getRecorderState() {
        RecordStatusManager.changeRecordStatus(PAUSED)
        val state = mExpandController?.recorderState
        Assert.assertTrue(state == PAUSED)
    }

    @Test
    fun should_notNull_when_getTime() {
        mExpandController?.mRecorder = Mockito.mock(IBizRecorder::class.java)
        Mockito.`when`(mExpandController?.mRecorder?.getTime()).thenReturn(1000L)
        val time = mExpandController?.time
        Assert.assertTrue(time == 1000L)
    }

    @Test
    fun should_notNull_when_onFocuseChanged() {
        RecordStatusManager.changeRecordStatus(RECORDING)
        mExpandController?.onFocuseChanged(AudioManager.AUDIOFOCUS_LOSS)
        mExpandController?.onFocuseChanged(AudioManager.AUDIOFOCUS_LOSS_TRANSIENT)
        mExpandController?.onFocuseChanged(AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK)
        mExpandController?.onFocuseChanged(AudioManager.AUDIOFOCUS_GAIN)
        mExpandController?.onFocuseChanged(AudioManager.AUDIOFOCUS_GAIN_TRANSIENT)
        Assert.assertTrue(RecordStatusManager.getCurrentStatus() == PAUSED)
    }
}