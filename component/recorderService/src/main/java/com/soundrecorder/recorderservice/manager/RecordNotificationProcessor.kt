/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordNotificationProcessor
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.manager

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.RECEIVER_NOT_EXPORTED
import android.content.Intent
import androidx.lifecycle.map
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.registerReceiverCompat
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.modulerouter.notification.NotificationAction
import com.soundrecorder.modulerouter.notification.NotificationModel
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_NOTIFICATION
import com.soundrecorder.recorderservice.RecorderService
import com.soundrecorder.recorderservice.manager.RecordStatusManager.RECORDING

class RecordNotificationProcessor {

    companion object {
        const val TAG = "RecordNotificationProcessor"
    }

    private var mRecordNotificationModel: NotificationModel? = null

    private var mNotificationReceiver: BroadcastReceiver? = null

    fun updateNotification(service: RecorderService?, isFromOtherApp: Boolean) {
        //DebugUtil.i(TAG, "updateNotification in !")
        if (!NotificationAction.mHasComponent) {
            return
        }

        if (service == null) {
            return
        }

        NotificationAction.cancelNotificationByPage(NotificationUtils.NOTIFICATION_PAGE_RECORD)
        NotificationAction.showNotification(
            NotificationAction.getNotificationMode(isFromOtherApp),
            NotificationUtils.NOTIFICATION_PAGE_RECORD,
            getNotificationModel(service, isFromOtherApp),
            service
        )
        registerNotificationBroadcast(service, isFromOtherApp)
    }

    private fun getNotificationModel(service: RecorderService, mIsFromOtherApp: Boolean): NotificationModel? {
        if (mRecordNotificationModel == null) {
            mRecordNotificationModel = NotificationModel()
        }
        mRecordNotificationModel?.let {
            it.curTime = RecordStatusManager.getCurrentTimeMillisLiveData()
            it.playStatus = RecordStatusManager.getCurrentStatusLiveData().map { playStatus ->
                if (playStatus == RECORDING) {
                    NotificationModel.RECORD_STATUS_PLAYING
                } else {
                    NotificationModel.RECORD_STATUS_PAUSE
                }
            }
            it.isBtnDisabled = service.isBtnDisabled
            it.isMarkEnabled = service.markEnable
            it.markPoint = service.lastMarkTime
            it.canJumpIntent = !mIsFromOtherApp
            it.saveState = service.saveState
        }

        return mRecordNotificationModel
    }

    fun cancelNotificationWhenStop(mIsFromOtherApp: Boolean) {
        if (!NotificationAction.mHasComponent) {
            return
        }

        NotificationAction.cancelNotification(
            NotificationAction.getNotificationMode(mIsFromOtherApp), NotificationUtils.NOTIFICATION_PAGE_RECORD
        )
        unRegisterNotificationBroadcast()
    }

    private fun registerNotificationBroadcast(service: RecorderService, isFromOtherApp: Boolean) {
        if (isFromOtherApp) {
            DebugUtil.d(TAG, "isFromNotification = true, no need to register receiver.")
            return
        }

        if (mNotificationReceiver == null) {
            mNotificationReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent?) {
                    val notificationId = intent?.getIntExtra(NotificationUtils.KEY_NOTIFICATION_TYPE, -1) ?: -1
                    val currentId = NotificationAction.getNotificationIdByModeAndPage(
                        NotificationAction.getNotificationMode(isFromOtherApp), NotificationUtils.NOTIFICATION_PAGE_RECORD
                    )
                    if (notificationId != currentId) {
                        return
                    }
                    when (intent?.action) {
                        NotificationUtils.PLAY_STATUS_CHANGED_ACTION -> {
                            if (ClickUtils.isFastDoubleClick()) {
                                DebugUtil.i(TAG, "notification action is isFastDoubleClick")
                            } else {
                                val isPlaying = RecordStatusManager.getCurrentStatus() == RECORDING
                                service.playBtnClick()
                                BuryingPoint.addPlayBtnClickFromNotification(
                                        NotificationAction.isLockScreen() ?: false,
                                        isPlaying,
                                        NotificationUtils.NOTIFICATION_PAGE_RECORD)
                            }
                        }
                        NotificationUtils.MARK_CHANGED_ACTION -> {
                            BuryingPoint.addMarkBtnClickFromNotification(
                                    NotificationAction.isLockScreen() ?: false,
                                    NotificationUtils.NOTIFICATION_PAGE_RECORD)
                            val markMetadata = MarkMetaData("", "", -1, -1, -1)
                            service.addMark(true, markMetadata)
                        }

                        NotificationUtils.SAVE_CHANGED_ACTION -> {
                            RecorderViewModel.getInstance().saveRecordInfo(saveRecordFromWhere = MSG_ARG2_SAVE_RECORD_FROM_NOTIFICATION)
                            BuryingPoint.addClickSaveRecord(RecorderUserAction.VALUE_SAVE_RECORD_NOTIFICATION)
                        }
                    }
                }
            }
            BaseApplication.getAppContext().registerReceiverCompat(mNotificationReceiver,
                NotificationAction.getIntentFilter(), RECEIVER_NOT_EXPORTED)
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun unRegisterNotificationBroadcast() {
        if (mNotificationReceiver != null) {
            try {
                BaseApplication.getAppContext().unregisterReceiver(mNotificationReceiver)
            } catch (_: Exception) {
            }
            mNotificationReceiver = null
        }
    }
}