/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SimpleTimer.java
 Description:
 Version: 1.0
 Date: 2019.08.28
 Author: zhanghr
 -----------Revision History-----------
 <author> <date> <version> <desc>
 zhanghr 2009-11-20 1.0 create
 */

package com.soundrecorder.recorderservice.recorder.utils;

import android.os.SystemClock;

import com.soundrecorder.base.utils.DebugUtil;

/**
 * used for timing. support pause and resume. system time irrespective
 */
public class SimpleTimer {

    private static final String TAG = "SimpleTimer";

    private static final int STATUS_PAUSED = 1;
    private static final int STATUS_RUN = 2;

    private long mAppendTime;
    private long mSection = 0;
    private long mBeginning = 0;
    private int mStatus = STATUS_PAUSED;

    /** start timing */
    public synchronized void start() {
        mSection = 0;
        // System.currentTimeMillis();
        mBeginning = SystemClock.elapsedRealtime();
        mStatus = STATUS_RUN;
        DebugUtil.i(TAG,"start, mBegining : " + mBeginning + ", appendTime is " + mAppendTime);
    }

    /** pause timing */
    public synchronized void pause() {
        /**
         * OPPO zhanghr add for fix bug when pasue->stop happens,time cannot be
         * correct 2010-04-20
         */
        if (mBeginning != 0) {
            /** OPPO add end */
            mSection += SystemClock.elapsedRealtime() - mBeginning;
            DebugUtil.i(TAG, "pause, mBeginning is " + mBeginning + ", mSection is " + mSection + ", appendTime is " + mAppendTime);
        }
        // this is important
        mBeginning = 0;
        mStatus = STATUS_PAUSED;
    }

    /** resume timing */
    public synchronized void resume() {
        mBeginning = SystemClock.elapsedRealtime();
        mStatus = STATUS_RUN;
        DebugUtil.i(TAG, "resume, mBegining : " + mBeginning);
    }

    /** stop timing */
    public synchronized void stop() {
        pause();
    }

    /** get accumulate time */
    public synchronized long getTime() {

        if (mBeginning == 0) { // not started
            return mSection + mAppendTime;
        } else {
            return mSection + SystemClock.elapsedRealtime() - mBeginning + mAppendTime;
        }
    }

    /** when reset , it is the same as a new Object */
    public synchronized void reset() {
        mStatus = STATUS_PAUSED;
        mSection = 0;
        mBeginning = 0;
        mAppendTime = 0;
    }

    public synchronized void setAppendTime(long time) {
        mAppendTime = time;
        DebugUtil.i(TAG, "setAppendTime, mAppendTime : " + mAppendTime);
    }

    public synchronized boolean isStopped() {
        return mStatus == STATUS_PAUSED;
    }
}
