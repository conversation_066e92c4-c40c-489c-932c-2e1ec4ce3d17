/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordExpandController
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.manager

import android.content.ContentResolver
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.media.AudioManager
import android.media.MediaRecorder
import android.net.Uri
import android.os.ParcelFileDescriptor
import android.os.SystemClock
import android.provider.MediaStore
import android.text.TextUtils
import android.util.Pair
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.oppo.media.OppoRecorder
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.base.utils.MediaDataScanner
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.constant.RecorderConstant
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.manager.WakeLockManager
import com.soundrecorder.common.utils.AudioNameUtils.genSaveFileName
import com.soundrecorder.common.utils.MediaUtil.getPathFromNewUri
import com.soundrecorder.common.utils.RecordModeUtil
import com.soundrecorder.common.utils.RecordModeUtil.setRecordMode
import com.soundrecorder.common.utils.SettingsAdapter
import com.soundrecorder.common.utils.UniDirectionalRecordUtils
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.recorderservice.CURRENT_ACTION_PAUSERECORD
import com.soundrecorder.recorderservice.CURRENT_ACTION_RESUMERECORD
import com.soundrecorder.recorderservice.CURRENT_ACTION_STARTRECORD
import com.soundrecorder.recorderservice.CURRENT_ACTION_STOPRECORD
import com.soundrecorder.recorderservice.CURRENT_RESULT_FAILED
import com.soundrecorder.recorderservice.CURRENT_RESULT_SUC
import com.soundrecorder.recorderservice.ERROR_CODE_PAUSE_INVOKEERROR
import com.soundrecorder.recorderservice.ERROR_CODE_PAUSE_RECORD_ERROR
import com.soundrecorder.recorderservice.ERROR_CODE_RESUMERECORD_EXPAND_FILE_ERROR
import com.soundrecorder.recorderservice.ERROR_CODE_RESUMERECORD_FILE_DURATION_LIMIT_REACH
import com.soundrecorder.recorderservice.ERROR_CODE_RESUMERECORD_FILE_NOT_FOUND
import com.soundrecorder.recorderservice.ERROR_CODE_RESUMERECORD_INVOKEERROR
import com.soundrecorder.recorderservice.ERROR_CODE_RESUMERECORD_RECORDER_ERROR
import com.soundrecorder.recorderservice.ERROR_CODE_RESUMERECORD_RETRY_EXEED_LIMIT
import com.soundrecorder.recorderservice.ERROR_CODE_STARTRECORD_DISK_NOT_ENOUGH
import com.soundrecorder.recorderservice.ERROR_CODE_STARTRECORD_FILENOTFOUND
import com.soundrecorder.recorderservice.ERROR_CODE_STARTRECORD_MEDIADB_INSERT_ERROR
import com.soundrecorder.recorderservice.ERROR_CODE_STARTRECORD_OPLUS_RECORD_ERROR
import com.soundrecorder.recorderservice.ERROR_CODE_STOP_INVOKEERROR
import com.soundrecorder.recorderservice.ERROR_CODE_STOP_RECORDER_ERROR
import com.soundrecorder.recorderservice.RESULT_ERROR_CODE_DEFAULT
import com.soundrecorder.recorderservice.RecordResult
import com.soundrecorder.recorderservice.RecorderService
import com.soundrecorder.recorderservice.manager.RecordStatusManager.HALT_ON
import com.soundrecorder.recorderservice.manager.RecordStatusManager.INIT
import com.soundrecorder.recorderservice.manager.RecordStatusManager.PAUSED
import com.soundrecorder.recorderservice.manager.RecordStatusManager.RECORDING
import com.soundrecorder.recorderservice.recorder.RecordRecorderFactory
import com.soundrecorder.recorderservice.recorder.listener.IBizRecorder
import com.soundrecorder.recorderservice.recorder.listener.IOnErrorListener
import com.soundrecorder.recorderservice.recorder.listener.IOnInfoListener
import com.soundrecorder.wavemark.wave.view.MaxAmplitudeSource
import java.io.File
import java.io.FileDescriptor

class RecordExpandController(
    var config: RecordConfig,
    var otherConfig: OtherConfig,
    muteConfig: MuteModeOperator.MuteConfig = MuteModeOperator.MuteConfig()
) : DiskStorageChecker.StorageSpaceCallback, MuteModeOperator.AudioFocuseChangeListener,
    MaxAmplitudeSource {

    companion object {
        const val TAG = "RecordExpandController"

        const val NUM_5 = 5L
        const val NUM_50 = 50L
        const val NUM_300 = 300L
        const val NUM_100 = 100L

        const val MAX_START_TRYCOUNT = 5

        const val TIME_ONE_SECOND = 1000L

        const val MODE_RW = "rw"
    }


    private val mContext: Context = BaseApplication.getAppContext()

    private var mMuteModeOperator: MuteModeOperator = MuteModeOperator(mContext, muteConfig)

    private var mAppendOldTime = 0L

    private var mSettingAdapter: SettingsAdapter? = null

    private var mResumingFlag: Boolean = false

    private var mRecordFileObserver: RecordFileObserver? = null

    var mRecorder: IBizRecorder? = null
    var mRecordFileWraper: RecordFileWraper? = null
    var audioFocusListener: MuteModeOperator.AudioFocuseChangeListener? = null


    init {
        //初始化SettingAdapter
        mSettingAdapter = SettingsAdapter.getInstance()
        //初始化FileObserver
        mRecordFileObserver = RecordFileObserver()
        mRecordFileObserver?.mBlockDelete = false
    }


    fun updateRecordConfig(newConfig: RecordConfig) {
        DebugUtil.i(TAG, "updateRecordConfig $newConfig")
        config = newConfig
    }

    fun updateOtherConfig(newOtherConfig: OtherConfig) {
        DebugUtil.i(TAG, "updateRecordConfig $newOtherConfig")
        otherConfig = newOtherConfig
    }


    fun getMuteModeOperator(): MuteModeOperator {
        return mMuteModeOperator
    }

    fun setBlockDelete(blockDelete: Boolean) {
        DebugUtil.i(TAG, "setBlockDelete $blockDelete")
        mRecordFileObserver?.mBlockDelete = blockDelete
    }

    private val mRecordErrorListener: IOnErrorListener = object : IOnErrorListener() {
        private val NUM_100 = 100
        override fun onError(what: Int) {
            when (what) {
                NUM_100 -> {
                    DebugUtil.e(TAG, "onError MediaRecorder.MEDIA_ERROR_SERVER_DIED:")
                    if (mRecorder != null) {
                        mRecorder?.stop()
                        mRecorder?.release()
                        mRecorder = null
                    }
                    RecordStatusManager.changeRecordStatus(HALT_ON)
                    saveFile(mRecordFileWraper, false)
                    WakeLockManager.getInstance(mContext, null).releaseWakeLock()
                    //RecorderUtil.sendLocalBroadcast(mContext, Intent(RecorderService.ACTION_RECORDER_STOP_RECORDER_ABNORMAL))
                    RecordStopExceptionProcessor.dispatchStopEvent(RecordStopExceptionProcessor.STOP_EVENT_TYPE_RECORDE_ERROR_INFO)
                }
                OppoRecorder.MEDIA_RECORDER_ERROR_UNKNOWN -> DebugUtil.e(TAG, "onError MediaRecorder.MEDIA_RECORDER_ERROR_UNKNOWN:")
                else -> DebugUtil.e(TAG, "mRecordErrorListener error = $what")
            }
        }
    }

    private val mRecordInfoListener: IOnInfoListener = object : IOnInfoListener() {
        override fun onInfo(what: Int) {
            DebugUtil.d(TAG, "**onInfo what=$what")
            if (what == OppoRecorder.MEDIA_RECORDER_INFO_MAX_FILESIZE_REACHED
                || what == OppoRecorder.MEDIA_RECORDER_INFO_MAX_DURATION_REACHED
            ) {
                DebugUtil.w(TAG, "onInfo sendBroadcast( STOP_NORMAL )")
                RecordStopExceptionProcessor.dispatchStopEvent(RecordStopExceptionProcessor.STOP_EVENT_TYPE_RECORDE_OVERLIMIT)
            }
        }
    }


    fun start(): RecordResult? {
        val startResult = startRecord(false)
        DebugUtil.i(TAG, "startResult = $startResult")
        if (startResult?.isSuc() == true) {
            mRecordFileObserver?.startWatchingRecordingsFile(mRecordFileWraper?.sampleFile?.absolutePath)
            if (BaseApplication.sNeedToNormalRingMode) {
                when {
                    otherConfig.isFromSlidBar() ||
                            otherConfig.isFromBreno() ||
                            otherConfig.isFromSmallCard() ||
                            otherConfig.isFromMiniApp() ||
                            otherConfig.isFromCubeButton() ||
                            otherConfig.isFromLockScreen() -> {
                        ToastManager.showShortToast(
                            mContext,
                            com.soundrecorder.common.R.string.record_mute_tips
                        )
                    }
                }
            }
        }
        return startResult
    }

    fun resume(): RecordResult? {
        DebugUtil.v(TAG, "resume mState:" + RecordStatusManager.getCurrentStatus())
        return if (RecordStatusManager.getCurrentStatus() == PAUSED) {
            doResume()
        } else {
            RecordResult(CURRENT_ACTION_RESUMERECORD, CURRENT_RESULT_FAILED, ERROR_CODE_RESUMERECORD_INVOKEERROR)
        }
    }


    fun pause(): RecordResult {
        DebugUtil.d(TAG, "pause mState:${RecordStatusManager.getCurrentStatus()}")
        val recordResult = if (RecordStatusManager.getCurrentStatus() == RECORDING) {
            doPause()
        } else {
            RecordResult(CURRENT_ACTION_PAUSERECORD, CURRENT_RESULT_FAILED, ERROR_CODE_PAUSE_INVOKEERROR)
        }
        return recordResult
    }

    fun stop(): RecordResult {
        DebugUtil.v(TAG, "stop")

        //这个RecorderService中调用即可 stopForeground(true)
        setMute(false)
        var recordResult = RecordResult(CURRENT_ACTION_STOPRECORD, CURRENT_RESULT_SUC, RESULT_ERROR_CODE_DEFAULT)
        if (RecordStatusManager.getCurrentStatus() == HALT_ON || RecordStatusManager.getCurrentStatus() == INIT) {
            recordResult.currentResult = CURRENT_RESULT_FAILED
            recordResult.errorCode = ERROR_CODE_STOP_INVOKEERROR
            return recordResult
        }
        recordResult = doStop()
        //RecorderActivity在监听这个action，但是没有对这个action做任何处理
        //RecorderUtil.sendLocalBroadcast(mContext, Intent(RecorderService.SERVICE_FORWIDGET_STOP))
        mRecordFileObserver?.stopWatchingRecordingsFile()
        //change RecorderStatus
        //RecordStatusManager.changeRecordStatus(RecordStatusManager.HALT_ON)

        //销毁本地关于录制，暂停，继续的数据
        mAppendOldTime = 0
        //todo
        //mSampleFile = null

        return recordResult
    }





    private fun startRecord(isForResume: Boolean): RecordResult? {
        DebugUtil.d(TAG, "start mState: ${RecordStatusManager.getCurrentStatus()}, isForResume: $isForResume")
        var result: RecordResult? = if (isForResume) {
             RecordResult(CURRENT_ACTION_RESUMERECORD, CURRENT_RESULT_SUC, RESULT_ERROR_CODE_DEFAULT)
        } else {
             RecordResult(CURRENT_ACTION_STARTRECORD, CURRENT_RESULT_SUC, RESULT_ERROR_CODE_DEFAULT)
        }
        if (setLastMaxTime() == 0L) {
            DebugUtil.d(TAG, "doStart mLastMaxTime = 0")
            result?.currentResult = CURRENT_RESULT_FAILED
            result?.errorCode = ERROR_CODE_STARTRECORD_DISK_NOT_ENOUGH
            return result
        }
        try {
            Thread.sleep(NUM_50)
            DebugUtil.d(TAG, "start, sleep 50")
        } catch (e: InterruptedException) {
            e.printStackTrace()
        }
        setMute(true)
        //The sleep time here is used to avoid the background noise at the beginning of recording.
        if (!isForResume) {
            try {
                //开始录制睡300ms，老逻辑
                Thread.sleep(NUM_5)
                DebugUtil.d(TAG, "start, sleep 300")
            } catch (e: InterruptedException) {
                e.printStackTrace()
            }
        } else {
            try {
                //继续录制睡100ms，老逻辑
                Thread.sleep(NUM_100)
                DebugUtil.d(TAG, "start, sleep 100")
            } catch (e: InterruptedException) {
                e.printStackTrace()
            }
        }
        result = doStart(isForResume)
        DebugUtil.i(TAG, "startRecord result $result")
        return result
    }


    private fun doPause(): RecordResult {
        val start = SystemClock.elapsedRealtime()
        val result = RecordResult(CURRENT_ACTION_PAUSERECORD, CURRENT_RESULT_SUC, RESULT_ERROR_CODE_DEFAULT)
        try {
            setRecordingModeWhenPauseOrStop()
            mRecorder?.stopForPause()
        } catch (e: Exception) {
            DebugUtil.e(TAG, "doPause catch Exception when MediaRecorder.pause()", e)
            dealException(true)
            result.currentResult = CURRENT_RESULT_FAILED
            result.errorCode = ERROR_CODE_PAUSE_RECORD_ERROR
            return result
        }
        mAppendOldTime = getTime()
        saveFile(mRecordFileWraper, true)
        mRecordFileWraper?.resetFds()
        setMute(false)
        WakeLockManager.getInstance(mContext, null).releaseWakeLock()
        RecordStatusManager.changeRecordStatus(PAUSED)
        DebugUtil.d(TAG, "doPause consume time " + (SystemClock.elapsedRealtime() - start) + "ms, recordResult: $result")
        return result
    }


    private fun doResume(): RecordResult? {
        var result: RecordResult? = RecordResult(CURRENT_ACTION_RESUMERECORD, ERROR_CODE_RESUMERECORD_INVOKEERROR, RESULT_ERROR_CODE_DEFAULT)
        if (mResumingFlag) {
            return result
        }
        var mStartTryCount = 0
        mResumingFlag = true
        try {
            DebugUtil.i(TAG, "ExecutorManager ThreadName = " + Thread.currentThread().name)
            result = startRecord(true)
            while ((result?.isSuc() == false) && mStartTryCount < MAX_START_TRYCOUNT) {
                try {
                    Thread.sleep(NUM_300)
                    DebugUtil.v(TAG, "sleep 300 ms")
                } catch (e: InterruptedException) {
                    e.printStackTrace()
                }
                mStartTryCount++
                if (mStartTryCount > MAX_START_TRYCOUNT) {
                    result.currentResult = CURRENT_RESULT_FAILED
                    result.errorCode = ERROR_CODE_RESUMERECORD_RETRY_EXEED_LIMIT
                    return result
                }
                result = startRecord(true)
            }
            mStartTryCount = 0
            mResumingFlag = false
        } catch (e: Exception) {
            DebugUtil.e(TAG, "doResume catch Exception when MediaRecorder.resume()", e)
            dealException(true)
        } finally {
        }
        DebugUtil.v(TAG, "mStartTryCount:$mStartTryCount resumeResult:$result")
        return result
    }



    private fun doStop(): RecordResult {
        var recordResult = RecordResult(CURRENT_ACTION_STOPRECORD, CURRENT_RESULT_SUC, RESULT_ERROR_CODE_DEFAULT)
        try {
            DebugUtil.i(TAG, "doStop mState:${RecordStatusManager.getCurrentStatus()} mRecorder:$mRecorder")
            if (RecordStatusManager.getCurrentStatus() != PAUSED) {
                // 录制状态下停止，需要人为stop
                mRecorder?.stop()
            } else {
                // pause状态下，不需要调用stop，在Pause状态下已经底层已经调用了Player的stop方法
            }
            setRecordingModeWhenPauseOrStop()
            mRecorder?.release()
            mRecorder = null
            DebugUtil.i(TAG, " mRecorder is release and set null")
            saveFile(mRecordFileWraper, false)
            RecordStatusManager.changeRecordStatus(HALT_ON)
            WakeLockManager.getInstance(mContext, null).releaseWakeLock()
        } catch (e: Exception) {
            DebugUtil.e(TAG, "doStop catch Exception when MediaRecorder.stop()", e)
            dealException(true)
            recordResult.currentResult = CURRENT_RESULT_FAILED
            recordResult.errorCode = ERROR_CODE_STOP_RECORDER_ERROR
        }
        return recordResult
    }


    private fun doStart(isForResume: Boolean): RecordResult? {
        DebugUtil.d(TAG, "doStart mRecorder = $mRecorder, isForResume $isForResume")
        // 这个地方的intent使用isStart的回调代替相关功能了
        var recordResult = if (isForResume) {
            doStartWhenResume()
        } else {
            doStartWhenStart()
        }
        return recordResult
    }

    private fun doStartWhenStart(): RecordResult? {
        if (RecordStatusManager.getCurrentStatus() != INIT) {
            DebugUtil.i(TAG, "doStartWhenStart is return:CurrentStatus ${RecordStatusManager.getCurrentStatus()}")
            return null
        }
        DebugUtil.i(TAG, "doStartWhenStart")
        var result = RecordResult(CURRENT_ACTION_STARTRECORD, CURRENT_RESULT_SUC, RESULT_ERROR_CODE_DEFAULT)
        try {
            result = setRecorderConfig() //此处创建了 新的录音文件，并拿到了对应的uri
        } catch (e: Exception) {
            dealException(true)
            onFatalError(com.soundrecorder.common.R.string.error_fileopen_fail)
            DebugUtil.e(TAG, "doStartWhenStart set recorder error", e)
            return result
        }
        try {
            mRecorder?.start()
            RecordStatusManager.changeRecordStatus(RECORDING)
            WakeLockManager.getInstance(mContext, null).acquireWakeLock(config.maxDurationLimit)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "doStart no success when MediaRecorder.start():" + e.message)
            RecordStatusManager.changeRecordStatus(HALT_ON)
            dealException(true)
            onFatalError(com.soundrecorder.common.R.string.error_start_fail_v2)
            result.currentResult = CURRENT_RESULT_FAILED
            result.errorCode = ERROR_CODE_STARTRECORD_OPLUS_RECORD_ERROR
            return result
        }
        insertOriginalRecordInRecordDb(mRecordFileWraper, mRecordFileWraper?.insertMediaDbTime ?: -1)
        return result
    }


    private fun doStartWhenResume(): RecordResult {
        DebugUtil.i(TAG, "doStartWhenResume")
        var recordResult = RecordResult(CURRENT_ACTION_RESUMERECORD, CURRENT_RESULT_SUC, RESULT_ERROR_CODE_DEFAULT)
        val mAppendFilePath = mRecordFileWraper?.sampleFile?.absolutePath ?: ""
        var maxLimitDuration = -1
        try {
            DebugUtil.i(TAG, "resume mRecordFileWraper $mRecordFileWraper")
            if (config.maxDurationLimit != 0 || config.maxSizeLimit != 0L) {
                // 继续设置Info监听
                    if (mRecorder?.isPausedByResetRecorder() == true) {
                        mRecorder?.setOnInfoListener(mRecordInfoListener)
                    }

                // 如果mAppendOldTime为0，则重新获取上一次pause的时候的时间的时间
                if (mAppendOldTime == 0L && mRecorder != null) {
                    mAppendOldTime = mRecorder?.getTime() ?: 0
                    DebugUtil.i(TAG, "resume mAppendOldTime: $mAppendOldTime")
                }
                // 继续的时候设置之前重新计算之后的最大时间
                if (config.maxDurationLimit > 0) {
                    if (config.maxDurationLimit > mAppendOldTime) {
                        if (mRecorder?.isPausedByResetRecorder() == true) {
                            maxLimitDuration = (config.maxDurationLimit - mAppendOldTime).toInt()
                            DebugUtil.i(TAG, "resume newDurationLimit $maxLimitDuration")
                            mRecorder?.setMaxDuration(maxLimitDuration)
                        }
                    } else {
                        recordResult.currentResult = CURRENT_RESULT_FAILED
                        recordResult.errorCode = ERROR_CODE_RESUMERECORD_FILE_DURATION_LIMIT_REACH
                    }
                }
                // 继续的时候设置之前重新计算之后的最大大小
                if (config.maxSizeLimit > 0) {
                    val mLimit: Long = if (BaseUtil.isAndroidQOrLater) {
                        config.maxSizeLimit - FileUtils.getFileSize(mRecordFileWraper?.sampleFileUri)
                    } else {
                        config.maxSizeLimit - FileUtils.getFileSizeLong(mAppendFilePath)
                    }
                    DebugUtil.i(TAG, "resume newSizeLimit $mLimit")
                    if (mLimit > 0) {
                        if (mRecorder?.isPausedByResetRecorder() == true) {
                            mRecorder?.setMaxFileSize(mLimit)
                        }
                    }
                }
            }
            //调用ExpandFile函数，继续录制
            if (mRecorder != null) {
                if (BaseUtil.isAndroidQOrLater) {
                    try {
                        mRecordFileWraper?.initFds()
                        val fd: FileDescriptor? = mRecordFileWraper?.mFd
                        if (fd == null) {
                            DebugUtil.e(TAG, "expand file args fd error no fd find for uri: ${mRecordFileWraper?.sampleFileUri}")
                            recordResult.currentResult = CURRENT_RESULT_FAILED
                            recordResult.errorCode = ERROR_CODE_RESUMERECORD_FILE_NOT_FOUND
                            return recordResult
                        }
                        mRecorder?.expandFile(fd, 0, 0, OppoRecorder.AudioSource.MIC)
                        DebugUtil.i(TAG, "expand file success")
                    } catch (e: Throwable) {
                        DebugUtil.e(TAG, "expand file args fd error", e)
                        recordResult.currentResult = CURRENT_RESULT_FAILED
                        recordResult.errorCode = ERROR_CODE_RESUMERECORD_EXPAND_FILE_ERROR
                    }
                } else {
                    try {
                        mRecorder?.expandFile(mAppendFilePath, OppoRecorder.AudioSource.MIC)
                        DebugUtil.i(TAG, "expand file path success")
                    } catch (e: Throwable) {
                        DebugUtil.e(TAG, "expand file args path error", e)
                        recordResult.currentResult = CURRENT_RESULT_FAILED
                        recordResult.errorCode = ERROR_CODE_RESUMERECORD_EXPAND_FILE_ERROR
                    }
                }
            }
            if (!recordResult.isSuc()) {
                DebugUtil.e(TAG, "--doStart record expandFile error from expandFile")
                dealException(true)
                if (recordResult.errorCode == ERROR_CODE_RESUMERECORD_EXPAND_FILE_ERROR) {
                    ToastManager.showShortToast(mContext, com.soundrecorder.common.R.string.error_append_fail_v2)
                }
            } else {
                mRecorder?.setAppendTime(mAppendOldTime)
                //使用完成上次的时间，需要在这里将时间设置为0
                mAppendOldTime = 0
                try {
                    //暂停的时候录音通路会关闭，底层会把这个值恢复成默认，继续录制开始的时候也设置一次
                    setRecordingModeWhenStart()
                    mRecorder?.start()
                    RecordStatusManager.changeRecordStatus(RECORDING)
                    WakeLockManager.getInstance(mContext, null).acquireWakeLock(maxLimitDuration)
                } catch (e: Exception) {
                    RecordStatusManager.changeRecordStatus(HALT_ON)
                    dealException(true)
                    recordResult.currentResult = CURRENT_RESULT_FAILED
                    recordResult.errorCode = ERROR_CODE_RESUMERECORD_RECORDER_ERROR
                }
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "--doStartWhenResume record expandFile error from expandFile", e)
            dealException(true)
        }
        return recordResult
    }

    /**
     * 开始录音（含录制继续）设置录音模式
     * 若开启定向，则设置定向；否则则设置标准等模式（属于同级关系，不能重复叠加效果）
     */
    private fun setRecordingModeWhenStart() {
        if (RecorderViewModel.getInstance().isDirectRecodingOn()) {
            UniDirectionalRecordUtils.setUniDirectionalRecordingSwitch(mContext, true)
        } else {
            setRecordMode(mContext, config.recordType)
        }
    }

    /**
     * 暂停录制。设置录制模式
     * 若开启定向，则重置定向；
     * 其他：不需要处理，底层恢复标准等value
     */
    private fun setRecordingModeWhenPauseOrStop() {
        if (RecorderViewModel.getInstance().isDirectRecodingOn()) {
            UniDirectionalRecordUtils.setUniDirectionalRecordingSwitch(mContext, false)
        }
    }


    private fun setRecorderConfig(): RecordResult {
        var recordResult = RecordResult(CURRENT_ACTION_STARTRECORD, CURRENT_RESULT_SUC, RESULT_ERROR_CODE_DEFAULT)
        val format = config.format
        require(!(RecorderConstant.RECORDER_AUDIO_FORMAT_MP3 != format
                && RecorderConstant.RECORDER_AUDIO_FORMAT_WAV != format
                && RecorderConstant.RECORDER_AUDIO_FORMAT_AMR_WB != format
                && RecorderConstant.RECORDER_AUDIO_FORMAT_AMR_NB != format
                && RecorderConstant.RECORDER_AUDIO_FORMAT_AAC_ADTS != format)
        )
        // set audioManager param
        setRecordingModeWhenStart()
        if (mRecorder == null) {
            mRecorder = RecordRecorderFactory.newRecorder(format)
            mRecorder?.setOnErrorListener(mRecordErrorListener)
        }

        DebugUtil.d(TAG, "set recordConfig : $config")
        if (config.maxSizeLimit != 0L || config.maxDurationLimit != 0) {
            if (config.maxDurationLimit != 0) {
                mRecorder?.setMaxDuration(config.maxDurationLimit)
            }
            if (config.maxSizeLimit != 0L) {
                mRecorder?.setMaxFileSize(config.maxSizeLimit)
            }
            mRecorder?.setOnInfoListener(mRecordInfoListener)
        } else {
            mRecorder?.setOnInfoListener(null)
        }
        mRecordFileWraper = RecordFileWraper(recordConfig = config)
        val mSuffix = mRecordFileWraper?.recordConfig?.getSuffix()
        mRecordFileWraper?.relativePath = RecordModeUtil.getRelativePathByRecordType(config.recordType, true)
        mRecordFileWraper?.sampleFileName = genSaveFileName(config.recordType, mSuffix, true)
        val path = mSettingAdapter?.storagePhone + File.separator + mRecordFileWraper?.relativePath + mRecordFileWraper?.sampleFileName
        RecorderViewModel.getInstance().setFileBeingRecorded(path)
        try {
            mRecordFileWraper?.sampleFileUri = MediaDBUtils.genUri(genSampleContentValues(mRecordFileWraper, path)) //真正去创建文件
            DebugUtil.i(TAG, "set generate new SampleUri: ${mRecordFileWraper?.sampleFileUri}")
        } catch (e: Exception) {
            DebugUtil.e(TAG, "set error", e)
            recordResult.currentResult = CURRENT_RESULT_FAILED
            recordResult.errorCode = ERROR_CODE_STARTRECORD_MEDIADB_INSERT_ERROR
        }
        if (mRecordFileWraper?.sampleFileUri == null) {
            DebugUtil.e(TAG, "sampleUri gen failed null")
            return recordResult
        }
        mRecordFileWraper?.sampleFileUri?.let {
            val dataPath = getPathFromNewUri(BaseApplication.getAppContext(), it)
            if (dataPath != null) {
                mRecordFileWraper?.sampleFile = File(dataPath)
                FileUtils.ensureDirectory(mRecordFileWraper?.sampleFile?.parent)
                DebugUtil.d(TAG, "mRecordFileWraper?.sampleFile?.exists ${mRecordFileWraper?.sampleFile?.exists()}")
            }
        }
        if (BaseUtil.isAndroidQOrLater) {
            mRecordFileWraper?.initFds()
            val fd: FileDescriptor? = mRecordFileWraper?.mFd
            if (fd == null) {
                DebugUtil.e(TAG, "set fd not find for uri ${mRecordFileWraper?.sampleFileUri}")
                recordResult.currentResult = CURRENT_RESULT_FAILED
                recordResult.errorCode = ERROR_CODE_STARTRECORD_FILENOTFOUND
                return recordResult
            }
            mRecorder?.setOutputFile(fd)
        } else {
            mRecorder?.setOutputFile(mRecordFileWraper?.sampleFile?.getAbsolutePath())
        }
        try {
            mRecorder?.prepare()
        } catch (e: Exception) {
            onFatalError(com.soundrecorder.common.R.string.error_fileopen_fail)
            DebugUtil.e(TAG, "prepare recorder error", e)
            recordResult.currentResult = CURRENT_RESULT_FAILED
            recordResult.errorCode = ERROR_CODE_STARTRECORD_OPLUS_RECORD_ERROR
        }
        return recordResult
    }

    private fun genSampleContentValues(recordFileWraper: RecordFileWraper?, data: String?): ContentValues? {
        val current = System.currentTimeMillis()
        val values = ContentValues()
        if (recordFileWraper != null) {
            values.put(MediaStore.Audio.Media.IS_MUSIC, "0")
            values.put(MediaStore.Audio.Media.DISPLAY_NAME, recordFileWraper.sampleFileName)
            values.put(MediaStore.Audio.Media.DATE_ADDED, current / TIME_ONE_SECOND)
            values.put(MediaStore.Audio.Media.DATE_MODIFIED, current / TIME_ONE_SECOND)
            values.put(MediaStore.Audio.Media.MIME_TYPE, recordFileWraper.recordConfig.getMimeType())
            if (BaseUtil.isAndroidQOrLater) {
                values.put(MediaStore.Audio.Media.RELATIVE_PATH, recordFileWraper.relativePath)
            } else {
                values.put(MediaStore.Audio.Media.DATA, data)
            }
            //这里加上size=1的情况主要是为了解决在录制的过程中，从通话进入录音列表查看不到当前正在录制的音频的问题。
           /* values.put(MediaStore.Audio.Media.SIZE, 1)
            recordFileWraper.insertMediaDbTime = current*/
        }
        return values
    }

    fun getMediaRecorder(): MediaRecorder? {
        return mRecorder?.getMediaRecorder()
    }

    fun onCancelRecord() {
        DebugUtil.d(TAG, "onCancelError")
        mRecordFileObserver?.mBlockDelete = true
        RecordStatusManager.changeRecordStatus(HALT_ON)
        deleteInvalidMediaFile()
        if (mRecorder != null) {
            mRecorder?.release()
            mRecorder = null
        }
        WakeLockManager.getInstance(mContext, null).releaseWakeLock()
    }

    fun onFatalError(errorID: Int) {
        DebugUtil.d(TAG, "onFatalError mState:" + RecordStatusManager.getCurrentStatus())
        deleteInvalidMediaFile()
        ToastManager.showShortToast(mContext, errorID)
        DebugUtil.e(TAG, "fatal error:" + mContext.getString(errorID))
    }

    private fun deleteInvalidMediaFile() {
        DebugUtil.e(TAG, "deleteInvalidFile")
        if (BaseUtil.isAndroidQOrLater) {
            MediaDBUtils.delete(mRecordFileWraper?.sampleFileUri)
        } else {
            DebugUtil.d(TAG, "getName = " + mRecordFileWraper?.sampleFile?.getName() + " size = " + mRecordFileWraper?.sampleFile?.length())
            mRecordFileWraper?.sampleFile?.let {
                deleteFromMediaDB(it)
                val ans: Boolean = it.delete()
                DebugUtil.d(TAG, "deleteInvalidFile delete ans:$ans")
            }
        }
    }


    // Modify by xiuhua.ke, For delete file and DB have the record,when play the
    // file,it cause null point at startPlay function, 2013/09/28
    private fun deleteFromMediaDB(file: File?) {
        val path: String? = null
        if (file == null || TextUtils.isEmpty(file.absolutePath)) {
            DebugUtil.e(TAG, "deleteFromDB, path is null!")
            return
        }
        try {
            val resolver: ContentResolver = mContext.getContentResolver()
            val where = MediaStore.Audio.Media.DATA + " LIKE " + "\"" + path + "\""
            resolver.delete(MediaDBUtils.BASE_URI, where, null)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "deleteFromDB, exception:$e")
        }
    }


    fun deleteInvalidRecordDbFile() {
        DebugUtil.e(TAG, "deleteInvalidRecordDbFile")
        val recordDbId = mRecordFileWraper?.mRecordDbId ?: -1
        if (recordDbId != -1L) {
            val deleteSuc = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).deleteRecordsById(recordDbId)
            RecorderService.removeRecordIdWhenRecordComplete(recordDbId.toString(), PrefUtil.KEY_SAVE_RECORD_ID_WHEN_RECORDING)
            RecorderService.removeRecordIdWhenRecordComplete(recordDbId.toString(), PrefUtil.KEY_SAVE_RECORD_ID_FOR_ABNORMAL_EXIT)
            DebugUtil.i(TAG, "deleteInvalidRecordDbFile recordDbId: $recordDbId, deleteSuc $deleteSuc")
        }
    }



    private fun setMute(mute: Boolean) {
        mMuteModeOperator.setMute(mute)
    }

    private fun setLastMaxTime(): Long {
        DebugUtil.d(TAG, "setLastMaxTime start")
        var lastMaxTime = DiskStorageChecker.checkLastMaxTime()
        DebugUtil.i(TAG, "mLastMaxTime = $lastMaxTime")
        return lastMaxTime
    }


    private fun dealException(stopRecordForErrorFlag: Boolean) {
        DebugUtil.e(TAG, "dealException mStopRecordForErrorFlag = $stopRecordForErrorFlag")
        if (mRecorder != null) {
            mRecorder?.release()
            mRecorder = null
        }
        WakeLockManager.getInstance(mContext, null).releaseWakeLock()
        if (stopRecordForErrorFlag) {
            mAppendOldTime = 0
            RecordStatusManager.changeRecordStatus(HALT_ON)
            val intent = Intent(RecorderDataConstant.ACTION_RECORDER_STOP_RECORDER)
            LocalBroadcastManager.getInstance(mContext).sendBroadcast(intent)
            RecordStopExceptionProcessor.dispatchStopEvent(RecordStopExceptionProcessor.STOP_EVENT_TYPE_RECORDE_EXCEPTION)
        }
    }


    private fun insertOriginalRecordInRecordDb(recordWraper: RecordFileWraper?, currentTime: Long) {
        recordWraper?.let {
            val disPlayName: String? = it.sampleFileName
            val currentTime: Long = currentTime
            val mimeType: String = it.recordConfig.getMimeType()
            val mRelativePath: String? = it.relativePath
            val allPath: String? = it.sampleFile?.absolutePath
            val recordType: Int = it.recordConfig.recordType
            var record = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).qureyRecordByPath(allPath)
            if (record != null) {
                RecorderDBUtil.getInstance(BaseApplication.getAppContext())
                    .deleteRecordsById(record.id)
            }
            record = RecorderDBUtil.getInstance(BaseApplication.getAppContext())
                .insertInitRecordIntoDbForStartRecord(
                    disPlayName,
                    currentTime,
                    mimeType,
                    mRelativePath,
                    allPath,
                    recordType
                )
            if (record != null) {
                recordWraper.mRecordDbId = record.id
                recordWraper.mUUID = record.uuid
                RecorderService.saveRecordIdWhenRecording(record.id, PrefUtil.KEY_SAVE_RECORD_ID_WHEN_RECORDING)
                RecorderService.saveRecordIdWhenRecording(record.id, PrefUtil.KEY_SAVE_RECORD_ID_FOR_ABNORMAL_EXIT)
            } else {
                DebugUtil.e(TAG, "insertOriginalRecordInRecordDb error, record null")
            }
        }
    }


    private fun saveFile(recordFileWraper: RecordFileWraper?, isforpause: Boolean) {
        DebugUtil.d(TAG, "saveFile  isForPause: " + isforpause + ", recordFileWraper: $recordFileWraper")
        if (BaseUtil.isAndroidQOrLater) {
            if (recordFileWraper?.sampleFileUri == null) {
                DebugUtil.e(TAG, "mSampleUri is null")
                return
            }
        } else {
            if (recordFileWraper?.sampleFile == null || !(recordFileWraper.sampleFile?.exists()?:false)) {
                DebugUtil.e(TAG, "mSampleFile is null")
                return
            }
        }
        val file = recordFileWraper.sampleFile
        if (file != null) {
            MediaDataScanner.getInstance().mediaScan(mContext, arrayOf(file.absolutePath))
            DebugUtil.d(TAG, "saveFile ,the name is " + file.absolutePath)
        }
    }

    private fun releasePfd() {
        DebugUtil.i(TAG, "releasePfd")
        mRecordFileWraper?.resetFds()
    }


    fun releaseAll() {
        releasePfd()
        mRecorder = null
        mRecordFileWraper = null
    }


    override fun getMaxAmplitude(): Int {
        return try {
            mRecorder?.getMaxAmplitude() ?: 0
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getMaxAmplitude error $e")
            0
        }
    }

    override fun getRecorderState(): Int {
        return RecordStatusManager.getCurrentStatus()
    }

    override fun getTime(): Long {
        if (mRecorder != null) {
            mAppendOldTime = mRecorder!!.getTime()
            return mAppendOldTime
        }
        return 0
    }


    override fun onStorageNotEnough() {
        DebugUtil.i(TAG, "onStorageNotEnough")
        stop()
    }


    override fun onFocuseChanged(audioFoucesChanged: Int) {
        DebugUtil.i(TAG, "onFocuseChanged $audioFoucesChanged")
        when (audioFoucesChanged) {
            AudioManager.AUDIOFOCUS_LOSS, AudioManager.AUDIOFOCUS_LOSS_TRANSIENT, AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> {
                DebugUtil.d(TAG, "AudioFocus: received AUDIOFOCUS_LOSS")
                pause()
                setMute(false)
            }
            AudioManager.AUDIOFOCUS_GAIN -> {
                DebugUtil.d(TAG, "AudioFocus: received AUDIOFOCUS_GAIN ")
            }
            else -> DebugUtil.d(TAG, "Unknown audio focus change code")
        }
        audioFocusListener?.onFocuseChanged(audioFoucesChanged)
    }

    data class OtherConfig(var isFromOtherApp: Boolean = false, var recordFromType: Int = START_FROM_NORMAL, var isNeedResult: Boolean = false) {

        companion object {
            // 自己界面启动
            const val START_FROM_NORMAL = 0
            // 侧边栏启动录音服务
            const val START_FROM_SLIDBAR = 1
            // Breno 助手启动录音服务
            const val START_FROM_BREENO = 2
            // Breno 首页处于前台，助手启动
            const val START_FROM_BREENO_FRONT = 3
            /*
             * 蜻蜓副屏
             */
            const val START_FROM_APP_CARD = 4
            //负一屏卡片
            const val START_FROM_SMALL_CARD = 5

            /**
             * MINI APP
             */
            const val START_FROM_MINI_APP = 6

            /**
             * 魔方按键
             */
            const val START_FROM_CUBE_BUTTON = 7

            /**
             * 锁屏
             */
            const val START_FROM_LOCK_SCREEN = 8
        }


        fun isFromSlidBar(): Boolean {
            return recordFromType == START_FROM_SLIDBAR
        }

        fun isFromBreno(): Boolean {
            return recordFromType == START_FROM_BREENO
        }

        fun isFromBrenoFront(): Boolean {
            return recordFromType == START_FROM_BREENO_FRONT
        }

        fun isFromAppCard(): Boolean {
            return recordFromType == START_FROM_APP_CARD
        }

        fun isFromSmallCard(): Boolean {
            return recordFromType == START_FROM_SMALL_CARD
        }

        fun isFromMiniApp(): Boolean {
            return recordFromType == START_FROM_MINI_APP
        }

        fun isFromOther(): Boolean {
            return isFromOtherApp
        }

        fun isFromCubeButton(): Boolean {
            return recordFromType == START_FROM_CUBE_BUTTON
        }

        fun isFromLockScreen(): Boolean {
            return recordFromType == START_FROM_LOCK_SCREEN
        }
    }

    data class RecordConfig(
        var format: Int = RecorderConstant.RECORDER_AUDIO_FORMAT_MP3,
        var recordType: Int = RecordModeConstant.RECORD_TYPE_STANDARD,
        var maxSizeLimit: Long = 0L,
        var maxDurationLimit: Int = 0
    ) {

        fun getMimeType(): String {

            val result = when (format) {
                RecorderConstant.RECORDER_AUDIO_FORMAT_MP3 -> RecordConstant.MIMETYPE_MP3
                RecorderConstant.RECORDER_AUDIO_FORMAT_WAV -> RecordConstant.MIMETYPE_WAV
                RecorderConstant.RECORDER_AUDIO_FORMAT_AAC_ADTS -> RecordConstant.MIMETYPE_ACC
                RecorderConstant.RECORDER_AUDIO_FORMAT_AMR_NB -> RecordConstant.MIMETYPE_AMR
                RecorderConstant.RECORDER_AUDIO_FORMAT_AMR_WB -> RecordConstant.MIMETYPE_AMR_WB
                else -> RecordConstant.MIMETYPE_MP3
            }
            return result
        }

        fun getSuffix(): String {
            val result = when (format) {
                RecorderConstant.RECORDER_AUDIO_FORMAT_MP3 -> RecorderConstant.MP3_FILE_SUFFIX
                RecorderConstant.RECORDER_AUDIO_FORMAT_WAV -> RecorderConstant.WAV_FILE_SUFFIX
                RecorderConstant.RECORDER_AUDIO_FORMAT_AAC_ADTS -> RecorderConstant.AAC_FILE_SUFFIX
                RecorderConstant.RECORDER_AUDIO_FORMAT_AMR_NB -> RecorderConstant.AMR_FILE_SUFFIX
                RecorderConstant.RECORDER_AUDIO_FORMAT_AMR_WB -> RecorderConstant.AMR_WB_FILE_SUFFIX
                else -> RecorderConstant.MP3_FILE_SUFFIX
            }
            return result
        }
    }



    data class RecordFileWraper(
        var sampleFile: File? = null,
        var sampleFileUri: Uri? = null,
        var relativePath: String? = null,
        var sampleFileName: String? = null,
        var recordConfig: RecordConfig
    ) {

        var insertMediaDbTime = -1L
        var mPfd: ParcelFileDescriptor? = null
        var mFd: FileDescriptor? = null
        var mRecordDbId = -1L
        var mUUID: String? = null

        fun resetFds() {
            DebugUtil.i(TAG, "resetFds pfd $mPfd, fds $mFd")
            try {
                mPfd?.close()
            } catch (e: java.lang.Exception) {
                DebugUtil.e(TAG, "reset pfd error", e)
            } finally {
                mPfd = null
            }
            mFd = null
        }


        fun initFds() {
            if (sampleFileUri == null) {
                DebugUtil.e(TAG, "init fd error, sampleFileUri null")
                return
            }
            DebugUtil.i(TAG, "initFds mPfd $mPfd, mFd $mFd")
            if (mPfd == null && mFd == null) {
                val fileDescriptors: Pair<FileDescriptor, ParcelFileDescriptor> = MediaDBUtils.getFileDescriptors(sampleFileUri, MODE_RW)
                mPfd = fileDescriptors.second
                mFd = fileDescriptors.first
                DebugUtil.i(TAG, "end getPfd statSize(): ${mPfd?.statSize} , fd: $mFd")
            }
        }
    }
}