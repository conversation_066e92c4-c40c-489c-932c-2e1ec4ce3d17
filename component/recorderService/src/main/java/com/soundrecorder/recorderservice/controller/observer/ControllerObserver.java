/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ControllerObserver.java
 Description:
 Version: 1.0
 Date: 2020/1/7
 Author: liuyulong
 -----------Revision History-----------
 <author> <date> <version> <desc>
 liuyulong 2020/1/7 1.0 create
 */

package com.soundrecorder.recorderservice.controller.observer;

import android.net.Uri;

public interface ControllerObserver<T> {

    T getAmplitudeCallback();

    String getRecordFilePath();

    String getSuffix();

    String getRelativePath();

    String getMimeType();

    Uri getSampleUri();

    String getSampleFileName();

    int getRecordType();

    String getUUID();
}
