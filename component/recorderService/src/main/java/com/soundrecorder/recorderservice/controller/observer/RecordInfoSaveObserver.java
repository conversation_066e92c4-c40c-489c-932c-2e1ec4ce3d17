/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordInfoSaveObserver.java
 Description:
 Version: 1.0
 Date: 2020/1/7
 Author: liuyulong
 -----------Revision History-----------
 <author> <date> <version> <desc>
 liuyulong 2020/1/7 1.0 create
 */

package com.soundrecorder.recorderservice.controller.observer;

import android.app.RecoverableSecurityException;

public interface RecordInfoSaveObserver {

    void updateLoadingCallback(String name);

    void saveDialogCallback(String name, String fullPath, int saveRecordFromWhere);

    default void deleteUriInMediaDBWhenException(String name, RecoverableSecurityException e) {
    }
}
