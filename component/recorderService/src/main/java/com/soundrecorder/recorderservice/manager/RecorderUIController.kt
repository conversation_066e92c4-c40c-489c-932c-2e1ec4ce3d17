/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderUIController
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.manager

import android.net.Uri
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.recorderservice.CURRENT_ACTION_SAVERECORD
import com.soundrecorder.recorderservice.CURRENT_RESULT_FAILED
import com.soundrecorder.recorderservice.RecordResult
import com.soundrecorder.recorderservice.controller.RecorderController
import com.soundrecorder.recorderservice.controller.observer.ControllerObserver
import com.soundrecorder.recorderservice.controller.observer.RecordInfoSaveObserver
import com.soundrecorder.recorderservice.controller.observer.WaveObserver
import com.soundrecorder.wavemark.wave.view.MaxAmplitudeSource

class RecorderUIController(var recordExpandController: RecordExpandController?) {

    companion object {
        const val TAG = "RecorderUIController"

        private const val RECORD_SAMPLE_DELAY = 0
        private const val RECORD_SAMPLE_INTERVAL = 70
    }

    var mRecorderController: RecorderController? = null
    private var mControllerObserver: ControllerObserver<MaxAmplitudeSource>? = null
    private var mWaveObserver: WaveObserver? = null
    private var mRecordInfoSaveObserver: RecordInfoSaveObserver? = null

    var mRecorderStateListener: RecorderStateListener? = null

    fun setRecorderStateListener(listener: RecorderStateListener?) {
        mRecorderStateListener = listener
    }

    private fun initRecorderControllerObserver() {
        mControllerObserver = object : ControllerObserver<MaxAmplitudeSource> {

            override fun getAmplitudeCallback(): MaxAmplitudeSource {
                return object : MaxAmplitudeSource {
                    override fun getMaxAmplitude(): Int {
                        return recordExpandController?.maxAmplitude ?: 0
                    }

                    override fun getTime(): Long {
                        //DebugUtil.d(TAG, "getAmplitudeCallback, getMaxAmplitude: " + RecorderService.this.getTime())
                        return recordExpandController?.time ?: 0
                    }

                    override fun getRecorderState(): Int {
                        return RecordStatusManager.getCurrentStatus()
                    }
                }
            }

            override fun getRecordFilePath(): String {
                return recordExpandController?.mRecordFileWraper?.sampleFile?.absolutePath ?: ""
            }

            override fun getSuffix(): String {
                return recordExpandController?.config?.getSuffix() ?: ""
            }

            override fun getRelativePath(): String {
                return recordExpandController?.mRecordFileWraper?.relativePath ?: ""
            }

            override fun getMimeType(): String {
                return recordExpandController?.config?.getMimeType() ?: ""
            }

            override fun getSampleUri(): Uri? {
                return recordExpandController?.mRecordFileWraper?.sampleFileUri
            }

            override fun getSampleFileName(): String? {
                return recordExpandController?.mRecordFileWraper?.sampleFileName
            }

            override fun getRecordType(): Int {
                return recordExpandController?.mRecordFileWraper?.recordConfig?.recordType
                    ?: RecordModeConstant.RECORD_TYPE_STANDARD
            }

            override fun getUUID(): String? {
                return recordExpandController?.mRecordFileWraper?.mUUID
            }
        }
    }


    fun onlyInitRecordderControllerAndObserver() {
        DebugUtil.i(TAG, "onlyInitRecordderControllerAndObserver")
        if (mControllerObserver == null) {
            DebugUtil.e(
                TAG,
                "onlyInitRecordderControllerAndObserver, mControllerObserver is null, init ControllerObserver"
            )
            initRecorderControllerObserver()
        }
        if (mRecorderController == null) {
            DebugUtil.e(
                TAG,
                "onlyInitRecordderControllerAndObserver, mRecorderController is null, init mRecorderController"
            )
            mRecorderController = RecorderController.Builder()
                .setControllerObserver(mControllerObserver!!)
                .setWaveObserver(null)
                .setRecordInfoSaveObserver(null)
                .build()
                .initSample()
        }
    }


    fun registerRecorderControlObservers(
        waveObserver: WaveObserver,
        recordInfoSaveObserver: RecordInfoSaveObserver
    ) {
        DebugUtil.i(TAG, "registerRecorderControlObservers")
        if (mControllerObserver == null) {
            DebugUtil.e(TAG, "registerRecorderControlObservers, mControllerObserver is null")
            initRecorderControllerObserver()
        }
        mWaveObserver = waveObserver
        mRecordInfoSaveObserver = recordInfoSaveObserver

        /*define recorder controller to cache calculating data while recording,
         * mControllerObserver mWaveObserver mMarkObserver  mRecordInfoSaveObserver must define to observer the data
         * and choose ControllerScenes to create a controller*/
        if (mRecorderController == null) {
            DebugUtil.i(
                TAG,
                "registerRecorderControlObservers new mRecorderController with mWaveObserver and mMarkObserver"
            )
            mRecorderController = RecorderController.Builder()
                .setControllerObserver(mControllerObserver!!)
                .setWaveObserver(mWaveObserver)
                .setRecordInfoSaveObserver(mRecordInfoSaveObserver)
                .build()
                .initSample()
        } else {
            DebugUtil.i(
                TAG,
                "registerRecorderControlObservers , just update mWaveObserver, mMarkObserver"
            )
            updateRecorderControlObservers(mWaveObserver, recordInfoSaveObserver)
        }
    }

    fun updateRecorderControlObservers(
        waveObserver: WaveObserver?,
        recordInfoSaveObserver: RecordInfoSaveObserver?
    ) {
        if (mRecorderController == null || mControllerObserver == null) {
            DebugUtil.e(TAG, "updateRecorderControlObservers, mControllerObserver is null")
            return
        }
        if (waveObserver != null) {
            mRecorderController?.setWaveObserver(waveObserver)
        }
        if (recordInfoSaveObserver != null) {
            mRecorderController?.setRecordInfoSaveObserver(recordInfoSaveObserver)
        }
    }

    fun unRegisterWaveUIObserver() {
        if (mControllerObserver != null) {
            mControllerObserver = null
        }
        if (mWaveObserver != null) {
            mWaveObserver = null
        }
        if (mRecordInfoSaveObserver != null) {
            mRecordInfoSaveObserver = null
        }
    }

    fun startSample() {
        DebugUtil.d(TAG, "startSample $mRecorderController")
        if (mRecorderController != null) {
            mRecorderController?.startSample(RECORD_SAMPLE_DELAY, RECORD_SAMPLE_INTERVAL)
        }
    }

    fun stopSample() {
        DebugUtil.d(TAG, "stopSample $mRecorderController")
        if (mRecorderController != null) {
            mRecorderController?.stopSample()
        }
    }

    fun release() {
        DebugUtil.d(TAG, "release $mRecorderController")
        if (mRecorderController != null) {
            mRecorderController?.release()
            mRecorderController = null
        }
    }

    fun saveRecordInfo(
        displayName: String?,
        markDataBeans: List<MarkDataBean>?,
        saveRecordFromWhere: Int,
        trigSyncNow: Boolean = true
    ): RecordResult {
        DebugUtil.d(TAG, "saveRecordInfo $mRecorderController")
        var recordResult = RecordResult(CURRENT_ACTION_SAVERECORD)
        if (mRecorderController != null) {
            recordResult = mRecorderController?.saveRecordInfo(displayName, saveRecordFromWhere, trigSyncNow) ?: RecordResult(
                CURRENT_ACTION_SAVERECORD,
                CURRENT_RESULT_FAILED
            )
        }
        return recordResult
    }


    interface RecorderStateListener {
        fun onRecordStatusChange(state: Int)
        fun onRecordCallConnected()
        fun saveDialogCallback(name: String?, fullPath: String?, saveRecordFromWhere: Int)
    }
}