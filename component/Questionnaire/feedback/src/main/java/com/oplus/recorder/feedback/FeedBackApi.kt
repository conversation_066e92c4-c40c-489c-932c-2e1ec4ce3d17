/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/2/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.recorder.feedback

import android.content.pm.ActivityInfo
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.customer.feedback.sdk.FeedbackHelper
import com.customer.feedback.sdk.model.RequestData
import com.google.gson.Gson
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.common.databean.CollectionInfo
import com.soundrecorder.common.databean.CollectionInfo.Companion.COLLECTION_TYPE_BRAND
import com.soundrecorder.common.databean.CollectionInfo.Companion.COLLECTION_TYPE_CONTACT
import com.soundrecorder.common.databean.CollectionInfo.Companion.COLLECTION_TYPE_FEEDBACK
import com.soundrecorder.common.databean.CollectionInfo.Companion.COLLECTION_TYPE_LOG
import com.soundrecorder.common.databean.CollectionInfo.Companion.COLLECTION_TYPE_MODEL
import com.soundrecorder.common.databean.CollectionInfo.Companion.COLLECTION_TYPE_OPEN_ID
import com.soundrecorder.common.databean.CollectionInfo.Companion.COLLECTION_TYPE_OS
import com.soundrecorder.common.databean.CollectionInfo.Companion.COLLECTION_TYPE_STATISTICS
import com.soundrecorder.modulerouter.FeedBackAction
import java.util.Locale


@Component(FeedBackAction.COMPONENT_NAME)
object FeedBackApi {
    enum class AreaCode(val value: String) {
        CN("CN"), //中国
        IN("IN"), //印度
        VN("VN"), //越南
        SG("SG")//新加坡
    }
    private const val TAG = "PlayBackConvertApi"

    private var internetDialog: AlertDialog? = null

    private val defaultLifecycleObserver by lazy {
        object : DefaultLifecycleObserver {
            override fun onDestroy(owner: LifecycleOwner) {
                releasePermissionDialog()
                owner.lifecycle.removeObserver(this)
            }
        }
    }

    /**
     * 帮助与反馈信息采集回调
     */
    @Action(FeedBackAction.GET_FEED_BACK_REQUEST_DATA)
    @JvmStatic
    fun getFeedbackRequestData(callback: ((String?) -> Unit)) {
        FeedbackHelper.setRequestMadeListener(object : FeedbackHelper.RequestMadeCallback {
            override fun onRequestMade(list: List<RequestData>) {
                DebugUtil.e(TAG, "getFeedbackRequestData list:${list.size}")
                val feedbackDataList = feedbackRequestData(list)
                var json: String? = null
                if (feedbackDataList.isNotEmpty()) {
                    json = Gson().toJson(feedbackDataList)
                }
                callback.invoke(json)
            }
        })
    }

    @JvmStatic
    private fun feedbackRequestData(list: List<RequestData>): ArrayList<CollectionInfo> {
        val feedbackDataList = arrayListOf<CollectionInfo>()
        for (item in list) {
            when (item) {
                is RequestData.OpenId -> addDataOpenId(item, feedbackDataList)
                is RequestData.Brand -> addDataBrand(item, feedbackDataList)
                is RequestData.Model -> addDataModel(item, feedbackDataList)
                is RequestData.Os -> addDataOs(item, feedbackDataList)
                is RequestData.Contact -> addDataContact(item, feedbackDataList)
                is RequestData.Log -> addDataLog(item, feedbackDataList)
                is RequestData.Statistics -> addDataStatistics(item, feedbackDataList)
                is RequestData.Feedback -> addDataFeedback(item, feedbackDataList)
            }
        }
        return feedbackDataList
    }

    @JvmStatic
    private fun addDataFeedback(
        item: RequestData.Feedback,
        feedbackDataList: ArrayList<CollectionInfo>
    ) {
        val feedback: RequestData.Feedback = item
        DebugUtil.d(
            TAG,
            "feedback collect Feedback des = " + feedback.des + " contentStr = " + feedback.content
        )
        feedbackDataList.add(
            CollectionInfo(
                type = COLLECTION_TYPE_FEEDBACK,
                content = feedback.content,
                dateCreated = System.currentTimeMillis(),
                count = 1
            )
        )
    }

    @JvmStatic
    private fun addDataStatistics(
        item: RequestData.Statistics,
        feedbackDataList: ArrayList<CollectionInfo>
    ) {
        val statistics: RequestData.Statistics = item
        DebugUtil.d(
            TAG,
            "feedback collect Statistics des = " + statistics.des + " contentStr = " + statistics.content
        )
        feedbackDataList.add(
            CollectionInfo(
                type = COLLECTION_TYPE_STATISTICS,
                content = statistics.content,
                dateCreated = System.currentTimeMillis(),
                count = 1
            )
        )
    }

    @JvmStatic
    private fun addDataLog(
        item: RequestData.Log,
        feedbackDataList: ArrayList<CollectionInfo>
    ) {
        val log: RequestData.Log = item
        DebugUtil.d(TAG, "feedback collect Log des = " + log.des + " contentStr = " + log.content)
        feedbackDataList.add(
            CollectionInfo(
                type = COLLECTION_TYPE_LOG,
                content = log.content,
                dateCreated = System.currentTimeMillis(),
                count = 1
            )
        )
    }

    @JvmStatic
    private fun addDataContact(
        item: RequestData.Contact,
        feedbackDataList: ArrayList<CollectionInfo>
    ) {
        val contact: RequestData.Contact = item
        DebugUtil.d(
            TAG,
            "feedback collect Contact des = " + contact.des + " contentStr = " + contact.content
        )
        feedbackDataList.add(
            CollectionInfo(
                type = COLLECTION_TYPE_CONTACT,
                content = contact.content,
                dateCreated = System.currentTimeMillis(),
                count = 1
            )
        )
    }

    @JvmStatic
    private fun addDataOs(
        item: RequestData.Os,
        feedbackDataList: ArrayList<CollectionInfo>
    ) {
        val os: RequestData.Os = item
        DebugUtil.d(TAG, "feedback collect Os des = " + os.des + " contentStr = " + os.content)
        feedbackDataList.add(
            CollectionInfo(
                type = COLLECTION_TYPE_OS,
                content = os.content,
                dateCreated = System.currentTimeMillis(),
                count = 1
            )
        )
    }

    @JvmStatic
    private fun addDataModel(
        item: RequestData.Model,
        feedbackDataList: ArrayList<CollectionInfo>
    ) {
        val model: RequestData.Model = item
        DebugUtil.d(
            TAG,
            "feedback collect Model des = " + model.des + " contentStr = " + model.content
        )
        feedbackDataList.add(
            CollectionInfo(
                type = COLLECTION_TYPE_MODEL,
                content = model.content,
                dateCreated = System.currentTimeMillis(),
                count = 1
            )
        )
    }

    @JvmStatic
    private fun addDataBrand(
        item: RequestData.Brand,
        feedbackDataList: ArrayList<CollectionInfo>
    ) {
        val brand: RequestData.Brand = item
        DebugUtil.d(
            TAG,
            "feedback collect Brand des = " + brand.des + " contentStr = " + brand.content
        )
        feedbackDataList.add(
            CollectionInfo(
                type = COLLECTION_TYPE_BRAND,
                content = brand.content,
                dateCreated = System.currentTimeMillis(),
                count = 1
            )
        )
    }

    @JvmStatic
    private fun addDataOpenId(
        item: RequestData.OpenId,
        feedbackDataList: ArrayList<CollectionInfo>
    ) {
        val openId: RequestData.OpenId = item
        DebugUtil.d(
            TAG,
            "feedback collect OpenId des = " + openId.des + " contentStr = " + openId.content
        )
        feedbackDataList.add(
            CollectionInfo(
                type = COLLECTION_TYPE_OPEN_ID,
                content = openId.content,
                dateCreated = System.currentTimeMillis(),
                count = 1
            )
        )
    }

    @Action(FeedBackAction.LAUNCH_FEED_BACK)
    @JvmStatic
    fun launchFeedback(activity: FragmentActivity, id: String, color: Int) {
        try {
            val isFeedbackPermission = PrefUtil.getBoolean(activity, PrefUtil.KEY_RECORD_FEEDBACK_PERMISSION, false)
            activity.lifecycle.addObserver(defaultLifecycleObserver)
            if (isFeedbackPermission || FeedPermissionUtils.hasGetInternetPermission(activity)) {
                FeedPermissionUtils.setInternetPermission(activity, 1)
                FeedbackHelper.setNetworkUserAgree(true)
                openFeedBack(activity, id, color)
            } else {
                internetDialog = FeedPermissionUtils.showFeedInternetPermissionDialog(activity,
                        object : FeedPermissionUtils.FeedPermissionDialogListener {
                            override fun onClick(isOk: Boolean) {
                                FeedPermissionUtils.setInternetPermission(activity, if (isOk) 1 else 0)
                                if (isOk) {
                                    FeedbackHelper.setNetworkUserAgree(true)
                                    openFeedBack(activity, id, color)
                                }
                            }
                        })
            }
        } catch (e: Throwable) {
            DebugUtil.e(TAG, "openFeedback fail with an exception", e)
        }
    }

    @Action(FeedBackAction.ACTION_SET_THEME_COLOR)
    @JvmStatic
    fun setFeedbackThemeColor(color: Int) {
        FeedbackHelper.setThemeColor(color)
    }

    private fun openFeedBack(activity: FragmentActivity, id: String, color: Int) {
        FeedbackHelper.setThemeColor(color)
        val feedbackHelper = FeedbackHelper.getInstance(BaseApplication.getAppContext())
        //除了中国印度越南都使用新加坡的域名，后续需要加入
        when (Locale.getDefault().country) {
            AreaCode.CN.value -> FeedbackHelper.setDataSavedCountry(FeedbackHelper.FbAreaCode.CN)
            AreaCode.IN.value -> FeedbackHelper.setDataSavedCountry(FeedbackHelper.FbAreaCode.IN)
            AreaCode.VN.value -> FeedbackHelper.setDataSavedCountry(FeedbackHelper.FbAreaCode.VN)
            else -> FeedbackHelper.setDataSavedCountry(FeedbackHelper.FbAreaCode.SG)
        }
        FeedbackHelper.setId(id)
        if (FeatureOption.IS_PAD) {
            feedbackHelper?.setCommonOrientationType(ActivityInfo.SCREEN_ORIENTATION_USER)
        }
        feedbackHelper?.openFeedback(activity)
    }


    private fun releasePermissionDialog() {
        if (internetDialog?.isShowing == true) {
            internetDialog?.dismiss()
        }
        internetDialog = null
    }
}