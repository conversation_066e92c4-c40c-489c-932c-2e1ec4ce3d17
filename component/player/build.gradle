apply from: "../../common_build.gradle"

android {
    sourceSets {
        main {
            res.srcDirs += ['res']
            res.srcDirs += ['../../res-strings']
        }
    }
}
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$prop_kotlinVersion"
    implementation "androidx.appcompat:appcompat:${prop_appcompatVersion}"
    implementation "androidx.core:core-ktx:${core_ktx}"

    //依赖Ostich
    implementation "com.inno.ostitch:stitch:${stitchVersion}"
    implementation "com.inno.ostitch:stitch-annotation:${stitchAnnotationVersion}"
    kapt "com.inno.ostitch:stitch-compile:${stitchCompileVersion}"

    implementation project(':common:libbase')
    implementation project(':common:libcommon')
    implementation project(':common:modulerouter')
}