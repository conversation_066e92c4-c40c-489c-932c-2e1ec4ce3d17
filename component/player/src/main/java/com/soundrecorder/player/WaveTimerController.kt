/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  WaveTimerController
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.player

import androidx.lifecycle.MutableLiveData
import com.soundrecorder.player.status.PlayStatus
import java.lang.ref.WeakReference
import java.util.Timer
import java.util.TimerTask

class WaveTimerController(private val timerListener: TimerCallbackListener) {

    companion object {
        const val TAG = "WaveTimerController"
    }

    /**
     * Wave view move timer
     */
    private var mTimer: Timer? = null
    private var mTimerTask: WaveTimerTask? = null

    fun startTimer(forceRefresh: Boolean = false) {
        if (mTimer != null && !forceRefresh) {
            return
        }

        stopTimer()
        mTimerTask = WaveTimerTask(timerListener).also {
            mTimer = Timer()
            mTimer?.schedule(it, 0, timerListener.getTimerPeriod())
        }
    }

    fun stopTimer() {
        mTimerTask?.cancel()
        mTimerTask = null

        mTimer?.let {
            it.cancel()
            it.purge()
        }
        mTimer = null
    }

    class WaveTimerTask(timerListener: TimerCallbackListener) : TimerTask() {

        private val mTimerListener: WeakReference<TimerCallbackListener> =
            WeakReference(timerListener)

        override fun run() {
            mTimerListener.get()?.let { listener ->
                val curPos = listener.getCurrentPosition()
                when (listener.getPlayStatus().value) {
                    PlayStatus.PLAYER_STATE_PLAYING -> {
//                        DebugUtil.i(TAG, "WaveTimerTask.run: is Playing, curPos = $curPos")
                        listener.onTimerTick(curPos)
                    }
                    PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> {
//                        DebugUtil.i(TAG, "WaveTimerTask.run: is fake Playing, curPos = $curPos")
                        listener.onTimerTick(curPos + listener.getTimerPeriod())
                    }
                }
            }
        }
    }
}

interface TimerTickCallback {
    fun onTimerTick(timeTickMillis: Long)
}

interface TimerCallbackListener : TimerTickCallback {

    fun getPlayStatus(): MutableLiveData<Int>

    fun getCurrentPosition(): Long

    fun getTimerPeriod(): Long
}

