/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  IPlayController
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/11/24
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.player.base

import android.net.Uri

interface IPlayerController {

    fun getPlayUri(): Uri?

    /**
     * 设置播放playUri
     */
    fun setPlayUri(playUri: Uri?)

    /**
     * 加载音频长度
     */
    fun loadDuration(): Long

    /**
     * 准备播放
     */
    fun prepareToPlay(timeMillis: Long): Boolean

    /**
     * 开始播放，需要先prepareToPlay
     */
    fun startToPlay()

    /**
     * 直接播放
     * @param timeMillis 开始播放定位的时间进度
     * @param delay 延迟播放时间
     *
     */
    fun startPlay(timeMillis: Long, delay: Long = 0)

    /**
     * 定点播放
     * 若未播放，则定位到具体位置后播放
     * 若已播放，则定位到具体位置后继续播放
     */
    fun seekToPlay(timeMillis: Long)

    /**
     * 使用另一种stream播放
     */
    fun toggleAudioStream(timeMillis: Long)

    /**
     * 暂停播放
     */
    fun pausePlay()

    /**
     * 继续播放
     */
    fun continuePlay()

    /**
     * 释放player资源
     */
    fun releasePlay()

    /**
     * 释放其他资源
     */
    fun onRelease()

    /**
     * seek到播放点
     * @param timeMills 期望的播放点
     */
    fun seekTime(timeMills: Long)

    /**
     * 获取当前播放位置
     */
    fun getCurrentPosition(): Long

    /**
     * 修改播放速度
     */
    fun changePlayerSpeed(speed: Float, isSilence: Boolean)

    /**
     * 获取播放流类型
     * 由于切换扬声器和听筒模式时将值设置到本地sp中，此处不方便set进去，只能每次使用时获取
     */
    fun getAudioStreamType(): Int

    fun isPlaying(): Boolean
}