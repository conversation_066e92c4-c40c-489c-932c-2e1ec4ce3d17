/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  WaveTimerControllerTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.player

import android.os.Build
import androidx.lifecycle.MutableLiveData
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.player.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class WaveTimerControllerTest {

    @Test
    fun versify_value_when_startTimer() {
        val timerController = WaveTimerController(object : TimerCallbackListener {
            override fun getPlayStatus(): MutableLiveData<Int> {
                return MutableLiveData()
            }

            override fun getCurrentPosition(): Long {
                return 0
            }

            override fun onTimerTick(timeKickMillis: Long) {
                //do nothing
            }

            override fun getTimerPeriod(): Long {
                return 70
            }
        })
        Assert.assertNull(Whitebox.getInternalState(timerController, "mTimer"))

        timerController.startTimer()
        Assert.assertNotNull(Whitebox.getInternalState(timerController, "mTimer"))
    }

    @Test
    fun versify_value_when_stopTimer() {
        val timerController = WaveTimerController(object : TimerCallbackListener {
            override fun getPlayStatus(): MutableLiveData<Int> {
                return MutableLiveData()
            }

            override fun getCurrentPosition(): Long {
                return 0
            }

            override fun onTimerTick(timeKickMillis: Long) {
                //do nothing
            }

            override fun getTimerPeriod(): Long {
                return 70
            }
        })
        timerController.stopTimer()
        Assert.assertNull(Whitebox.getInternalState(timerController, "mTimer"))
    }
}