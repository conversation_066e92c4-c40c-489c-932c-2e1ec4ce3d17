<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <include layout="@layout/include_notificaiton_title_content_fast_play" />

    <FrameLayout
        android:id="@+id/play_btn_area"
        style="@style/Notification_Button_Container"
        android:layout_below="@id/timestamp_tv">
        <TextView
            android:id="@+id/play_btn"
            style="@style/Notification_Button"
            android:background="@drawable/notification_background_button"
            tools:text="@string/talkback_flag"
            android:layout_gravity="bottom"
            android:textColor="@color/notification_button_text_color" />
    </FrameLayout>

</RelativeLayout>