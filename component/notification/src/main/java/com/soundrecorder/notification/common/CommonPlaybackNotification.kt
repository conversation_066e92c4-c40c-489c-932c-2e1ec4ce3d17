/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CommonPlaybackNotification
 * * Description :  播放页锁屏标记notification
 * * Version     : 1.0
 * * Date        : 2022/07/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification.common

import android.app.Notification
import android.app.PendingIntent
import android.content.Intent
import android.widget.RemoteViews
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.common.utils.AppCardUtils.launchDisplay
import com.soundrecorder.common.utils.DisplayUtils
import com.soundrecorder.modulerouter.BrowseFileAction
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.notification.R

class CommonPlaybackNotification(groupId: Int, notificationId: Int) :
    CommonNotification(groupId, notificationId) {

    override var logTag: String
        get() = "CommonPlaybackNotification"
        set(value) {}

    override fun getOldChannelId(): String {
        return NotificationUtils.PLAYBACK_OLD_CID
    }

    override fun getChannelName(): String {
        return defaultContext.resources.getString(R.string.playback_channel_name)
    }

    override fun getChannelId(): String {
        return NotificationUtils.PLAYBACK_CID
    }

    override fun getLayoutId(): Int {
        return if (BaseUtil.isAndroidSOrLater) {
            R.layout.layout_notification_play_back
        } else {
            R.layout.layout_notification_play_back_below_12
        }
    }

    override fun getFoldLayoutId(): Int? {
        return if (BaseUtil.isAndroidSOrLater) {
            R.layout.layout_notification_play_back_fold
        } else {
            null
        }
    }

    override fun setRemoteViewData(remoteViews: RemoteViews) {
        super.setRemoteViewData(remoteViews)
        //最近标记文本信息
        setMarkContent()
        //标记按钮状态信息
        setMarkBtnStatus()
    }

    override fun getMarkButtonAction(): Notification.Action {
        return Notification.Action.Builder(
//            Icon.createWithResource(defaultContext, CommonNotificationResourceHelper.getMarkButtonResId()),
            null,
            defaultContext.getString(R.string.talkback_flag),
            getMarkButtonPendingIntent()).build()
    }

    override fun getJumpIntent(): Intent? {
        return BrowseFileAction.createBrowseFileIntent(defaultContext)?.also {
            it.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        }
    }

    override fun getOtherDisplayContentIntent(): PendingIntent? {
        return getJumpIntent()?.run {
            PendingIntent.getActivity(
                defaultContext,
                PENDING_INTENT_REQUEST_CODE,
                this,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
                DisplayUtils.mainId.launchDisplay()
            )
        }
    }
}