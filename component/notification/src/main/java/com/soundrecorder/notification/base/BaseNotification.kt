/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BaseNotification
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/07/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification.base

import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Handler
import android.os.HandlerThread
import android.os.Looper
import android.view.View
import android.widget.RemoteViews
import androidx.lifecycle.Observer
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.screenstate.ScreenStateLiveData
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.AppCardUtils.addContinueFlag
import com.soundrecorder.common.utils.AppCardUtils.launchDisplay
import com.soundrecorder.common.utils.DisplayUtils
import com.soundrecorder.modulerouter.notification.NotificationModel
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.notification.CommonNotificationModel

abstract class BaseNotification(private val groupId: Int, private val notificationId: Int) :
    INotification {

    companion object {
        const val PENDING_INTENT_REQUEST_CODE = 3

        const val REFRESH_STATE_UNINITED = -1
        const val REFRESH_STATE_ENABLED = 0
        const val REFRESH_STATE_DISABLED = 1
    }

    open var logTag: String = "BaseNotification"
    var defaultContext: Context = BaseApplication.getAppContext()
    var notificationMgr: NotificationManager = defaultContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    var notification: Notification? = null
    var service: Service? = null
    lateinit var remoteViews: RemoteViews
    var foldRemoteViews: RemoteViews? = null
    private var isShowNotificationInUI: Boolean = false

    var notificationModel: CommonNotificationModel? = null
    protected var screenStateLiveData = ScreenStateLiveData()
    protected var refreshState = REFRESH_STATE_UNINITED
    private var handler: Handler? = null
    private var ioHandler: Handler? = null

    protected val normalEventReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            DebugUtil.i(logTag, "normalEventReceiver:" + intent?.action)
            when (intent?.action) {
                Intent.ACTION_LOCALE_CHANGED -> {
                    setNotificationChannel()
                    if ((refreshState == REFRESH_STATE_ENABLED) && !isPlaying()) {
                        refreshNotification()
                    }
                }
            }
        }
    }

    protected val notificationDeleteEventReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            DebugUtil.i(logTag, "notificationDeleteEventReceiver:" + intent?.action)
            when (intent?.action) {
                NotificationUtils.NOTIFICATION_DELETE_ACTION -> {
                    cancelNotification()
                    notification = null
                }
            }
        }
    }
    protected val screenStateObserver = Observer<Boolean> {
        DebugUtil.d(logTag, "screenStateObserver: screenOn = $it, refreshState = $refreshState")
        if (it && refreshState != REFRESH_STATE_UNINITED) {
            //点亮屏幕刷新通知,disable状态可能需要刷新，所以拦截UNINIT状态下不需要刷新
            refreshNotification()
        }

        //初始化后状态可以变更
        if (refreshState != REFRESH_STATE_UNINITED) {
            refreshState = if (it) REFRESH_STATE_ENABLED else REFRESH_STATE_DISABLED
        }
    }

    init {
        setNotificationChannel()
        clearOldChannel()
    }

    private fun setNotificationChannel() {
        if (BaseUtil.isAndroidOOrLater) {
            var channel = notificationMgr.getNotificationChannel(getChannelId())
            val channelName = getChannelName()
            if (channel == null) {
                channel = NotificationChannel(
                    getChannelId(),
                    channelName,
                    NotificationManager.IMPORTANCE_HIGH
                )
            } else {
                channel.name = channelName
            }
            channel.importance = NotificationManager.IMPORTANCE_HIGH
            channel.setSound(null, null)
            channel.enableVibration(false)
            channel.enableLights(false)
            channel.setShowBadge(false)
            notificationMgr.createNotificationChannel(channel)
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun clearOldChannel() {
        try {
            notificationMgr.deleteNotificationChannel(getOldChannelId())
        } catch (e: Exception) {
            DebugUtil.e(logTag, "clear OldChannel Exception: " + DebugUtil.recordExceptionMessage(e))
        }
    }

    protected fun getNormalEventFilter(): IntentFilter {
        return IntentFilter().also {
            it.addAction(Intent.ACTION_LOCALE_CHANGED)
        }
    }

    protected fun getNotificationDeleteEventFilter(): IntentFilter {
        return IntentFilter().also {
            it.addAction(NotificationUtils.NOTIFICATION_DELETE_ACTION)
        }
    }

    protected open fun initRemoteView() {
        remoteViews = RemoteViews(defaultContext.packageName, getLayoutId())
        getFoldLayoutId()?.let {
            foldRemoteViews = RemoteViews(defaultContext.packageName, it)
        }
    }

    protected fun setRemoteViewText(id: Int, content: String, contentDescription: String? = null) {
        remoteViews.setTextViewText(id, content)
        remoteViews.setContentDescription(id, contentDescription ?: content)
    }

    protected fun setTitleOrContentText(
        id: Int,
        content: String,
        contentDescription: String? = null
    ) {
        remoteViews.setViewText(id, content, contentDescription)
        foldRemoteViews.setViewText(id, content, contentDescription)
    }

    protected fun setTitleOrContentVisible(viewId: Int, shouldShow: Boolean) {
        remoteViews.setViewVisible(viewId, shouldShow)
        foldRemoteViews.setViewVisible(viewId, shouldShow)
    }

    protected fun setViewVisibility(viewId: Int, shouldShow: Boolean) {
        remoteViews.setViewVisible(viewId, shouldShow)
    }

    fun RemoteViews?.setViewText(id: Int, content: String, contentDescription: String? = null) {
        this?.let {
            it.setTextViewText(id, content)
            it.setContentDescription(id, contentDescription ?: content)
        }
    }

    fun RemoteViews?.setViewVisible(viewId: Int, shouldShow: Boolean) {
        this?.let {
            runCatching {
                it.setViewVisibility(
                        viewId, if (shouldShow) {
                    View.VISIBLE
                } else {
                    View.GONE
                }
                )
            }.onFailure {
                DebugUtil.e(logTag, "setViewVisible error $it")
            }
        }
    }

    protected open fun getNotificationBuilder(): Notification.Builder {
        return if (BaseUtil.isAndroidOOrLater) {
            Notification.Builder(BaseApplication.getAppContext(), getChannelId())
        } else {
            Notification.Builder(BaseApplication.getAppContext())
        }
    }

    override fun showNotification(service: Service?) {
        this.service = service

        refreshState = REFRESH_STATE_UNINITED
        observeData()
        initRemoteView()
        setRemoteViewData(remoteViews)
        sendNotification(false)
        refreshState = REFRESH_STATE_ENABLED
    }

    override fun sendNotification(inThread: Boolean) {
        initNotification()
        //发送通知
        if (inThread && !isShowNotificationInUI) {
            postInIOHandler {
                showImmediately()
            }
        } else {
            showImmediately()
            isShowNotificationInUI = false
        }
    }

    protected open fun refreshNotification() {
        initRemoteView()
        setRemoteViewData(remoteViews)
        sendNotification(true)
    }

    /**
     * 初始化通知
     */
    @Suppress("DEPRECATION")
    @SuppressLint("WrongConstant")
    protected open fun initNotification() {
        val builder = getNotificationBuilder()
        val jumpIntent = getJumpIntent()?.addContinueFlag()
        if ((notificationModel?.canJumpIntent == true) && (jumpIntent != null)) {
            builder.setContentIntent(
                PendingIntent.getActivity(
                    defaultContext,
                    PENDING_INTENT_REQUEST_CODE,
                    jumpIntent,
                    PendingIntent.FLAG_IMMUTABLE,
                    DisplayUtils.mainId.launchDisplay()
                )
            )
        }
        notification = builder.setStyle(Notification.DecoratedCustomViewStyle()).apply {
            if (this@BaseNotification::remoteViews.isInitialized) {
                setCustomContentView(remoteViews)
            }
            setSmallIcon(com.soundrecorder.common.R.drawable.ic_launcher_recorder)
            val contentTitle = getContentTitle()
            if (contentTitle.first.isNotEmpty()) {
                setContentTitle(contentTitle.first)
            }
            val contentText = getContentText()
            if (contentText.first.isNotEmpty()) {
                setContentText(contentText.first)
            }
            setVisibility(Notification.VISIBILITY_PUBLIC)
            setOnlyAlertOnce(true)
        }.build()
        notification?.let {
            it.flags = it.flags or Notification.FLAG_ONGOING_EVENT or Notification.FLAG_NO_CLEAR
        }
    }

    protected open fun showImmediately() {
        if (notification != null) {
            notificationMgr.notify(getGroupId(), notification)
        }
    }

    override fun cancelNotification() {
        DebugUtil.d(logTag, "cancelNotification")
        onRelease()
        notificationMgr.cancel(getGroupId())
    }

    override fun getNotificationId(): Int {
        return notificationId
    }

    override fun getGroupId(): Int {
        return groupId
    }

    protected fun isPlaying(): Boolean =
        notificationModel?.playStatus?.value == NotificationModel.RECORD_STATUS_PLAYING

    protected fun doInMainThread(runnable: Runnable) {
        if (Looper.getMainLooper().thread.id == Thread.currentThread().id) {
            runnable.run()
        } else {
            postInMainHandler(runnable)
        }
    }

    @Synchronized
    private fun postInMainHandler(runnable: Runnable) {
        if (handler == null) {
            handler = Handler(Looper.getMainLooper())
        }
        handler?.post(runnable)
    }

    @Synchronized
    private fun postInIOHandler(runnable: Runnable) {
        if (ioHandler == null) {
            ioHandler = Handler(HandlerThread(logTag).apply { start() }.looper)
        }
        ioHandler?.post(runnable)
    }

    @Synchronized
    protected fun releaseHandler() {
        handler?.removeCallbacksAndMessages(null)
        handler = null
        ioHandler?.removeCallbacksAndMessages(null)
        ioHandler = null
    }

    fun setNotificationModelFromOrigin(notificationModel: NotificationModel?) {
        notificationModel?.let {
            this.notificationModel = CommonNotificationModel.copyFrom(it)
        }
    }

    fun isRemoteViewsInitialized(): Boolean = this::remoteViews.isInitialized

    protected open fun showNotificationInUI() {
        isShowNotificationInUI = true
    }
}