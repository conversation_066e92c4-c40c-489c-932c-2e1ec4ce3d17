/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ThirdPartyPlaybackNotification
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/07/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification.third

import android.content.Intent
import com.soundrecorder.notification.R
import com.soundrecorder.modulerouter.notification.NotificationUtils

class ThirdPartyPlaybackNotification(groupId: Int, notificationId: Int) :
    ThirdPartyNotification(groupId, notificationId) {

    override var logTag: String
        get() = "ThirdPartyPlaybackNotification"
        set(value) {}

    override fun getOldChannelId(): String {
        return NotificationUtils.PLAYBACK_OLD_CID
    }

    override fun getChannelName(): String {
        return defaultContext.resources.getString(R.string.playback_channel_name)
    }

    override fun getChannelId(): String {
        return NotificationUtils.PLAYBACK_CID
    }

    override fun getJumpIntent(): Intent? {
        return null
    }
}