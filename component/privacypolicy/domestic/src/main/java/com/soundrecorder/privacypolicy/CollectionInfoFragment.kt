/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CollectionInfoFragment.kt
 * * Description : 个人信息收集明示清单
 * * Version     : 1.0
 * * Date        : 2024/8/20
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.privacypolicy

import android.app.Activity
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.preference.Preference
import com.coui.appcompat.preference.COUIJumpPreference
import com.coui.appcompat.preference.COUIPreferenceCategory
import com.coui.appcompat.toolbar.COUIToolbar
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.view.RootViewPersistentInsetsCallback
import com.soundrecorder.common.utils.taskbar.TaskBarUtil
import com.soundrecorder.common.widget.AutoIndentPreferenceFragment
import com.soundrecorder.modulerouter.SettingAction
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant

class CollectionInfoFragment : AutoIndentPreferenceFragment(),
    Preference.OnPreferenceClickListener {

    companion object {
        private const val TAG = "CollectionInfoFragment"
        private const val KEY_FRAGMENT_TYPE_VALUE = "key_fragment_type_value"

        private const val KEY_PREF_RECORD_AUDIO_CATEGORY = "key_pref_record_audio_category"
        private const val KEY_PREF_COLLECTION_RECORD_AUDIO = "key_pref_collection_record_audio"
        private const val KEY_PREF_COLLECTION_DEVICE_DUID = "key_pref_collection_device_duid"
        private const val KEY_PREF_COLLECTION_DEVICE_BRAND = "key_pref_collection_device_brand"
        private const val KEY_PREF_COLLECTION_OS_VERSION = "key_pref_collection_os_version"
        private const val KEY_PREF_COLLECTION_DEVICE_MODEL = "key_pref_collection_device_model"
        private const val KEY_PREF_COLLECTION_CONTACT_QQ = "key_pref_collection_contact_qq"
        private const val KEY_PREF_COLLECTION_DATA_ERROR_LOG_REPORT = "key_pref_collection_data_error_log_report"
        private const val KEY_PREF_COLLECTION_DATA_BURIED = "key_pref_collection_data_buried"
        private const val KEY_PREF_COLLECTION_DATA_FEEDBACK_CONTENT = "key_pref_collection_data_feedback_content"

        fun newInstance(
            type: Int = PrivacyPolicyConstant.TYPE_PERSONAL_INFORMATION_PROTECTION_POLICY
        ): CollectionInfoFragment {
            val args = Bundle().apply {
                putInt(KEY_FRAGMENT_TYPE_VALUE, type)
            }
            val fragment = CollectionInfoFragment()
            fragment.arguments = args
            return fragment
        }
    }

    private var mRecordAudioPreferenceCategory: COUIPreferenceCategory? = null
    private var mRecordAudioPreference: COUIJumpPreference? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        initActionBar(view)
        listView?.isForceDarkAllowed = false
        addPreferencesFromResource(R.xml.preference_collection_info)

        val type = arguments?.getInt(KEY_FRAGMENT_TYPE_VALUE) ?: 0
        DebugUtil.d(TAG, "onCreateView, type:$type")
        mRecordAudioPreferenceCategory = findPreference<COUIPreferenceCategory>(KEY_PREF_RECORD_AUDIO_CATEGORY)
        if (BaseUtil.isLightOS()) {
            mRecordAudioPreferenceCategory?.isVisible = false
        } else {
            mRecordAudioPreference = findPreference<COUIJumpPreference>(KEY_PREF_COLLECTION_RECORD_AUDIO)
            mRecordAudioPreference?.setOnPreferenceClickListener(this)
        }
        findPreference<COUIJumpPreference>(KEY_PREF_COLLECTION_DEVICE_DUID)?.setOnPreferenceClickListener(this)
        findPreference<COUIJumpPreference>(KEY_PREF_COLLECTION_DEVICE_BRAND)?.setOnPreferenceClickListener(this)
        findPreference<COUIJumpPreference>(KEY_PREF_COLLECTION_OS_VERSION)?.setOnPreferenceClickListener(this)

        findPreference<COUIJumpPreference>(KEY_PREF_COLLECTION_DEVICE_MODEL)?.setOnPreferenceClickListener(this)
        findPreference<COUIJumpPreference>(KEY_PREF_COLLECTION_CONTACT_QQ)?.setOnPreferenceClickListener(this)
        findPreference<COUIJumpPreference>(KEY_PREF_COLLECTION_DATA_ERROR_LOG_REPORT)?.setOnPreferenceClickListener(this)
        findPreference<COUIJumpPreference>(KEY_PREF_COLLECTION_DATA_BURIED)?.setOnPreferenceClickListener(this)
        findPreference<COUIJumpPreference>(KEY_PREF_COLLECTION_DATA_FEEDBACK_CONTENT)?.setOnPreferenceClickListener(this)
        return view
    }

    private fun Activity.startActivity(
        title: String?,
        collectionType: Int = com.soundrecorder.common.R.string.key_pref_collection_record_audio
    ) {
        val type: Int = com.soundrecorder.common.R.string.settings_personal_collection_content_key
        SettingAction.launchCollectionInfoDetails(this, title, type, collectionType)
    }

    private fun initActionBar(view: View?) {
        if (view == null) {
            return
        }
        val activity = activity as? AppCompatActivity? ?: return
        val toolbar = view.findViewById<COUIToolbar>(R.id.toolbar)
        activity.setSupportActionBar(toolbar)
        val actionBar = activity.supportActionBar
        actionBar?.setHomeButtonEnabled(true)
        actionBar?.setDisplayHomeAsUpEnabled(true)
        actionBar?.setTitle(com.soundrecorder.common.R.string.privacy_policy_collection_of_personal_information_express_checklist)
        initiateWindowInsets(activity)
    }

    private fun initiateWindowInsets(activity: Activity) {
        WindowCompat.setDecorFitsSystemWindows(activity.window, false)
        activity.findViewById<View>(R.id.root_layout)?.let {
            val callback: RootViewPersistentInsetsCallback = object : RootViewPersistentInsetsCallback() {
                override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                    super.onApplyInsets(v, insets)
                    DebugUtil.i(TAG, "onApplyInsets")
                    TaskBarUtil.setNavigationColorOnSupportTaskBar(
                        navigationHeight = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.navigationBars()).bottom,
                        activity = activity,
                        defaultNoTaskBarColor = (activity as? BaseActivity)?.navigationBarColor()
                    )
                }
            }
            ViewCompat.setOnApplyWindowInsetsListener(it, callback)
        }
    }

    override fun onPreferenceClick(preference: Preference?): Boolean {
        if (preference == null) {
            return false
        }
        val title = preference.title.toString()
        when (preference.key) {
            KEY_PREF_COLLECTION_RECORD_AUDIO -> {
                activity?.startActivity(
                    title,
                    com.soundrecorder.common.R.string.key_pref_collection_record_audio
                )
            }

            KEY_PREF_COLLECTION_DEVICE_DUID -> {
                activity?.startActivity(
                    title,
                    com.soundrecorder.common.R.string.key_pref_collection_device_duid
                )
            }

            KEY_PREF_COLLECTION_DEVICE_BRAND -> {
                activity?.startActivity(
                    title,
                    com.soundrecorder.common.R.string.key_pref_collection_device_brand
                )
            }

            KEY_PREF_COLLECTION_OS_VERSION -> {
                activity?.startActivity(
                    title,
                    com.soundrecorder.common.R.string.key_pref_collection_os_version
                )
            }

            KEY_PREF_COLLECTION_DEVICE_MODEL -> {
                activity?.startActivity(
                    title,
                    com.soundrecorder.common.R.string.key_pref_collection_device_model
                )
            }

            KEY_PREF_COLLECTION_CONTACT_QQ -> {
                activity?.startActivity(
                    title,
                    com.soundrecorder.common.R.string.key_pref_collection_contact_qq
                )
            }

            KEY_PREF_COLLECTION_DATA_ERROR_LOG_REPORT -> {
                activity?.startActivity(
                    title,
                    com.soundrecorder.common.R.string.key_pref_collection_data_error_log_report
                )
            }

            KEY_PREF_COLLECTION_DATA_BURIED -> {
                activity?.startActivity(
                    title,
                    com.soundrecorder.common.R.string.key_pref_collection_data_error_log_report
                )
            }

            KEY_PREF_COLLECTION_DATA_FEEDBACK_CONTENT -> {
                activity?.startActivity(
                    title,
                    com.soundrecorder.common.R.string.key_pref_collection_data_feedback_content
                )
            }
        }
        return false
    }
}