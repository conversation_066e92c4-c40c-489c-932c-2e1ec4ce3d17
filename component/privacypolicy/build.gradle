apply from:"../../common_flavor_build.gradle"

android {

    buildTypes {
        debug {
            /*https://record-pp-cn.wanyol.com/index.html*/
            buildConfigField "String", "HOST", "\"`||x{2''zmkgzl%xx%kf\""
            buildConfigField "String", "PRIVACY_POLICY_URL", "\"`||x{2''zmkgzl%xx%kf&\u007Fifqgd&kge'aflmp&`|ed\""
        }
        release {
            /*https://record-pp-cn.allawntech.com/index.html*/
            buildConfigField "String", "HOST", "\"`||x{2''zmkgzl%xx%kf\""
            buildConfigField "String", "PRIVACY_POLICY_URL", "\"`||x{2''zmkgzl%xx%kf&iddi\u007Ff|mk`&kge'aflmp&`|ed\""
        }
    }
}

dependencies {
    implementation project(':common:libbase')
    implementation project(':common:libcommon')
    implementation project(':common:modulerouter')
    compileOnly("org.jetbrains.kotlinx:kotlinx-coroutines-android:$kotlinx_coroutines_android_version")
    compileOnly "androidx.transition:transition-ktx:${transition_ktx}"
    compileOnly "androidx.appcompat:appcompat:${prop_appcompatVersion}"
    implementation "androidx.core:core-ktx:${core_ktx}"
    compileOnly("androidx.viewpager2:viewpager2:${viewpager2}")
    api "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycleVersion"
    compileOnly "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycleVersion"
//    compileOnly "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycleVersion"
    // base包为必须引用的包，prop_versionName需保持一致
    implementation ("com.oplus.appcompat:core:${prop_versionName}") {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    // 以下子包应用可选使用，如有使用了如下子包的控件，则需要添加，未使用可以不引用
    implementation "com.oplus.appcompat:toolbar:${prop_versionName}"
    implementation "com.oplus.appcompat:preference:${prop_versionName}"
    implementation "com.oplus.appcompat:poplist:${prop_versionName}"
    implementation "com.oplus.appcompat:rotateview:${prop_versionName}"
    implementation "com.oplus.appcompat:statement:${prop_versionName}"
    implementation "com.oplus.appcompat:panel:${prop_versionName}"
    implementation "com.oplus.appcompat:dialog:${prop_versionName}"
    implementation "com.oplus.appcompat:snackbar:${prop_versionName}"
    implementation "com.oplus.appcompat:clickablespan:${prop_versionName}"
    implementation "com.oplus.appcompat:emptyview:${prop_versionName}"
    implementation "com.oplus.appcompat:scrollview:${prop_versionName}"
    implementation "com.oplus.appcompat:recyclerview:${prop_versionName}"
    implementation "com.oplus.appcompat:indicator:${prop_versionName}"
    implementation "com.oplus.appcompat:scroll:${prop_versionName}"

    //依赖Ostich
    implementation "com.inno.ostitch:stitch:${stitchVersion}"
    implementation "com.inno.ostitch:stitch-annotation:${stitchAnnotationVersion}"
    kapt "com.inno.ostitch:stitch-compile:${stitchCompileVersion}"
    testImplementation "com.oplus.sdk:addon:${prop_addonAdapterVersion}"
    testImplementation "com.oplus.support:api-adapter-compat:${prop_supportSdkVersion}"

    implementation "org.ccil.cowan.tagsoup:tagsoup:1.2.1"
}
