package com.photoviewer.ui

import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.core.view.updateLayoutParams
import androidx.transition.*
import com.photoviewer.PhotoViewer
import com.photoviewer.extensions.globalVisibleRect
import com.soundrecorder.common.utils.PathInterpolatorHelper

internal class TransitionImageAnimator(
    private val targetView: ImageView,
    private val transitionImageView: ImageView
) {

    companion object {
        private const val TRANSITION_DURATION_OPEN = 300L
        private const val TRANSITION_DURATION_CLOSE = 300L
    }

    internal var isAnimating = false
    private var isClosing = false
    private val transitionDuration: Long
        get() = if (isClosing) TRANSITION_DURATION_CLOSE else TRANSITION_DURATION_OPEN
    private val transitionImageContainer: FrameLayout
        get() = transitionImageView.parent as FrameLayout
    private val dismissContainer: FrameLayout
        get() = transitionImageContainer.parent as FrameLayout

    internal fun animateOpen(
        onTransitionStart: (Long) -> Unit,
        onTransitionEnd: () -> Unit
    ) {
        onTransitionStart(TRANSITION_DURATION_OPEN)
        doOpenTransition(onTransitionEnd)
    }

    internal fun animateClose(
        onTransitionStart: (Long) -> Unit,
        onTransitionEnd: () -> Unit
    ) {
        onTransitionStart(TRANSITION_DURATION_CLOSE)
        doCloseTransition(onTransitionEnd)
    }

    private fun doOpenTransition(onTransitionEnd: () -> Unit) {
        isAnimating = true
        prepareTransitionLayout()
        dismissContainer.post {
            TransitionManager.beginDelayedTransition(dismissContainer, createTransition {
                if (!isClosing) {
                    isAnimating = false
                    onTransitionEnd()
                }
            })

            transitionImageContainer.updateLayoutParams {
                width = ViewGroup.LayoutParams.MATCH_PARENT
                height = ViewGroup.LayoutParams.MATCH_PARENT
            }
            transitionImageView.updateLayoutParams<FrameLayout.LayoutParams> {
                width = ViewGroup.LayoutParams.MATCH_PARENT
                height = ViewGroup.LayoutParams.MATCH_PARENT
                leftMargin = 0
                topMargin = 0
            }
            transitionImageView.scaleType = PhotoViewer.scaleType
            transitionImageContainer.requestLayout()
        }
    }

    private fun doCloseTransition(onTransitionEnd: () -> Unit) {
        isAnimating = true
        isClosing = true
        TransitionManager.beginDelayedTransition(
            dismissContainer,
            createTransition { handleCloseTransitionEnd(onTransitionEnd) }
        )
        prepareTransitionLayout()
        transitionImageContainer.requestLayout()
    }

    private fun prepareTransitionLayout() {
        transitionImageView.scaleType = targetView.scaleType
        with(targetView.globalVisibleRect) {
            transitionImageView.updateLayoutParams<FrameLayout.LayoutParams> {
                gravity = Gravity.LEFT or Gravity.TOP
                width = targetView.width - targetView.paddingLeft - targetView.paddingRight
                height = targetView.height - targetView.paddingTop - targetView.paddingBottom
                leftMargin = left + targetView.paddingLeft
                topMargin = top + targetView.paddingTop
            }
        }
        resetRootTranslation()
    }

    private fun handleCloseTransitionEnd(onTransitionEnd: () -> Unit) {
        targetView.visibility = View.VISIBLE
        transitionImageView.post { onTransitionEnd() }
        isAnimating = false
    }

    private fun resetRootTranslation() {
        dismissContainer
            .animate()
            .translationY(0f)
            .translationX(0f)
            .scaleX(1f)
            .scaleY(1f)
            .setInterpolator(PathInterpolatorHelper.couiMoveEaseInterpolator)
            .setDuration(transitionDuration)
            .start()
    }

    private fun createTransition(onTransitionEnd: (() -> Unit)? = null): Transition {
        val listener = object : Transition.TransitionListener {
            override fun onTransitionEnd(transition: Transition) {
                onTransitionEnd?.invoke()
                transition.removeListener(this)
            }

            override fun onTransitionResume(transition: Transition) {}
            override fun onTransitionPause(transition: Transition) {}
            override fun onTransitionCancel(transition: Transition) {}
            override fun onTransitionStart(transition: Transition) {}
        }
        return TransitionSet()
            .addTransition(ChangeBounds())
            .addTransition(ChangeClipBounds())
            .addTransition(ChangeTransform())
            .addTransition(ChangeImageTransform())
            .setDuration(transitionDuration)
            .setInterpolator(PathInterpolatorHelper.couiMoveEaseInterpolator)
            .addListener(listener)
    }
}