/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SellModeApi
 Description:
 Version: 1.0
 Date: 2022/12/13
 Author: W9013333(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/12/13 1.0 create
 */

package com.soundrecorder.sellmode

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.modulerouter.sellmode.SellModeAction
import com.soundrecorder.sellmode.SellModeService.Companion.checkAndStartSellModeService

@Component(SellModeAction.COMPONENT_NAME)
@Suppress("unused")
object SellModeApi {
    /**
     * 卖场模式初始化,每次初始都会去检查是否需要预置数据，同时注册预置数据任务触发时机
     */
    @JvmStatic
    @Action(SellModeAction.CHECK_AND_START_SELL_MODE_SERVICE)
    fun checkAndStartSellModeService(context: Context) {
        context.checkAndStartSellModeService()
    }

    /**
     * 卖场模式屏幕监听
     */
    @JvmStatic
    @Action(SellModeAction.SELL_MODE_SCREEN_STATE_LISTENER)
    fun createSellModeScreenStateListener(lifecycleOwner: LifecycleOwner, screenChangedCheck: (() -> Boolean)?) {
        SellModeScreenStateLiveData(lifecycleOwner, screenChangedCheck)
    }
}