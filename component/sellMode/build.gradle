apply from: "../../common_build.gradle"

android {
    namespace 'com.soundrecorder.sellmode'
}

dependencies {
    implementation project(':common:libbase')
    implementation project(':common:RecorderLogBase')
    implementation project(path: ':common:libcommon')
    implementation project(':common:modulerouter')
    implementation "com.inno.ostitch:stitch:${stitchVersion}"
    implementation "com.inno.ostitch:stitch-annotation:${stitchAnnotationVersion}"
    kapt "com.inno.ostitch:stitch-compile:${stitchCompileVersion}"
}