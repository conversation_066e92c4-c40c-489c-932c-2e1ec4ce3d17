/******************************************************************
 * Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd
 * VENDOR_EDIT
 * File: - WaveLinearLayoutManager.java
 * Description:
 * Version: 1.0
 * Date : 2018-11-14
 * Author: <EMAIL>
 **
 * ---------------- Revision History: ---------------------------
 *      <author>        <data>        <version >        <desc>
 *    PengFei.Ma      2018-11-14          1.0         build this module
 ********************************************************************/
package com.soundrecorder.wavemark.wave.view;

import android.content.Context;
import android.graphics.PointF;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;

public class WaveLinearLayoutManager extends LinearLayoutManager {

    public WaveLinearLayoutManager(Context context) {
        super(context);
    }

    @Override
    public boolean canScrollHorizontally() {
        return true;
    }

    @Override
    public void smoothScrollToPosition(RecyclerView recyclerView, RecyclerView.State state, int position) {
        LinearSmoothScroller linearSmoothScroller = new WaveSmoothScroller(recyclerView.getContext());
        linearSmoothScroller.setTargetPosition(position);
        startSmoothScroll(linearSmoothScroller);
    }

    @Override
    public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
        try{
            super.onLayoutChildren(recycler, state);
        } catch (IndexOutOfBoundsException e) {
            e.printStackTrace();
        }
    }

    public class WaveSmoothScroller extends LinearSmoothScroller {
        WaveSmoothScroller(Context context) {
            super(context);
        }

        @Override
        public PointF computeScrollVectorForPosition(int targetPosition) {
            return WaveLinearLayoutManager.this.computeScrollVectorForPosition(targetPosition);
        }

        @Override
        public int calculateDtToFit(int viewStart, int viewEnd, int boxStart, int boxEnd, int snapPreference) {
            return boxEnd - viewEnd;
        }

        @Override
        protected int getVerticalSnapPreference() {
            return SNAP_TO_END;
        }

        @Override
        protected int getHorizontalSnapPreference() {
            return SNAP_TO_END;
        }
    }
}