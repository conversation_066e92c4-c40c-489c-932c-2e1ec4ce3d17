/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MarkApi
 * Description:
 * Version: 1.0
 * Date: 2022/10/24
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/10/24 1.0 create
 */

package com.soundrecorder.wavemark

import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.modulerouter.mark.IIPictureMarkListener
import com.soundrecorder.modulerouter.mark.IPictureMarkDelegate
import com.soundrecorder.modulerouter.mark.IPictureMarkLifeOwnerProvider
import com.soundrecorder.modulerouter.mark.MarkAction
import com.soundrecorder.wavemark.picturemark.PictureMarkDelegate

@Component(MarkAction.COMPONENT_NAME)
class MarkApi {

    @Action(MarkAction.NEW_PICTURE_MARK_DELEGATE)
    fun newPictureMarkDelegate(
        ownerProvider: IPictureMarkLifeOwnerProvider,
        isActivityRecreate: Boolean,
        listener: IIPictureMarkListener<MarkMetaData, MarkDataBean>?,
    ): IPictureMarkDelegate<MarkMetaData> {
        return PictureMarkDelegate(ownerProvider, isActivityRecreate, listener)
    }
}