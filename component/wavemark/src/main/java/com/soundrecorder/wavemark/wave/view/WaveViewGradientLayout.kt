package com.soundrecorder.wavemark.wave.view

import android.content.Context
import android.os.Build
import android.util.AttributeSet
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.soundrecorder.wavemark.R

class WaveViewGradientLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
        ConstraintLayout(context, attrs, defStyleAttr) {
    private var backgroundWhole: Int = -1
    private var gradientView: View? = null

    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.WaveViewGradientLayout)
        backgroundWhole = ta.getResourceId(R.styleable.WaveViewGradientLayout_backgroundWhole, -1)

        ta.recycle()
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        addGradientView()
    }

    private fun addGradientView() {
        gradientView?.let {
            removeView(it)
        }
        //整体背景
        if (backgroundWhole != -1) {
            val markViewHeight =
                    resources.getDimension(R.dimen.record_wave_view_mark_area_height).toInt()
            gradientView = View(context).apply {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    isForceDarkAllowed = false
                }
                setBackgroundResource(backgroundWhole)
            }
            val layoutParams = LayoutParams(0, 0).apply {
                startToStart = LayoutParams.PARENT_ID
                endToEnd = LayoutParams.PARENT_ID
                topToTop = LayoutParams.PARENT_ID
                bottomToBottom = LayoutParams.PARENT_ID
                topMargin = markViewHeight
            }
            addView(gradientView, layoutParams)
        }
    }
}