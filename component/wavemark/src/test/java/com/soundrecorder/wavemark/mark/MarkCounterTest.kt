/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  MarkCounterTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/1/24
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.wavemark.mark

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.wavemark.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class MarkCounterTest {

    @Test
    fun should_notnull_when_newInstance() {
        val instance = MarkerCounter.newInstance()
        Assert.assertNotNull(instance)

        val instance1 = MarkerCounter.newInstance()
        Assert.assertNotEquals(instance, instance1)
    }

    @Test
    fun should_equals_when_setStartIndex() {
        val instance = MarkerCounter.newInstance()
        Assert.assertEquals(0, instance.startIndex)
        instance.startIndex = 5

        val next = instance.next
        Assert.assertEquals(6, next)
        Assert.assertEquals(6, instance.startIndex)

        instance.reset()
        Assert.assertEquals(0, instance.startIndex)

        val instance1 = MarkerCounter.newInstance()
        instance1.startIndex = 6
        val next1 = instance1.next
        Assert.assertEquals(7, next1)

        Assert.assertNotEquals(instance.startIndex, instance1.startIndex)
    }
}