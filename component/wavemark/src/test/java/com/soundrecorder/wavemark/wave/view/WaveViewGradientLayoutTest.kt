/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: WaveViewGradientLayoutTest
 * Description:
 * Version: 1.0
 * Date: 2024/2/5
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/2/5 1.0 create
 */

package com.soundrecorder.wavemark.wave.view

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.wavemark.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowLog::class, ShadowFeatureOption::class])
class WaveViewGradientLayoutTest {
    var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun release() {
        context = null
    }

    @Test
    fun should_correct_when_init() {
        val context = context ?: return
        val view = WaveViewGradientLayout(context)
        Assert.assertNull(Whitebox.getInternalState(view, "gradientView"))
    }

    @Test
    fun should_correct_when_onFinishInflate() {
        val context = context ?: return
        val view = WaveViewGradientLayout(context)
        Whitebox.invokeMethod<Unit>(view, "onFinishInflate")
        Assert.assertNull(Whitebox.getInternalState(view, "gradientView"))

        Whitebox.setInternalState(view, "backgroundWhole", 0)
        Whitebox.invokeMethod<Unit>(view, "addGradientView")
        Assert.assertNotNull(Whitebox.getInternalState(view, "gradientView"))
        Whitebox.invokeMethod<Unit>(view, "addGradientView")
    }
}