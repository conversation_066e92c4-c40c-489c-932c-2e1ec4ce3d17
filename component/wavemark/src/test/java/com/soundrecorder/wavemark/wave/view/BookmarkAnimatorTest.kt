/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: BookmarkAnimatorTest
 * Description:
 * Version: 1.0
 * Date: 2024/1/30
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/1/30 1.0 create
 */

package com.soundrecorder.wavemark.wave.view

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.wavemark.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class BookmarkAnimatorTest {
    private var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun tearDown() {
        context = null
    }

    @Test
    fun should_null_when_init() {
        val itemView = WaveItemView(context)
        val markAnimator = BookmarkAnimator(itemView)
        Assert.assertNull(Whitebox.getInternalState(markAnimator, "mAnimAddFlag"))
        Assert.assertNull(Whitebox.getInternalState(markAnimator, "mAnimRemoveFlag"))
    }

    @Test
    fun should_notNull_when_getFlagPaint() {
        val itemView = WaveItemView(context)
        val markAnimator = BookmarkAnimator(itemView)
        val markData = MarkDataBean(10)
        val flagPaint = markAnimator.getFlagPaint(markData)
        Assert.assertNotNull(flagPaint)
        Assert.assertEquals(itemView.bookmarksPaint, flagPaint)
    }

    @Test
    fun should_mAnimAddFlagNotNull_when_getFlagPaint() {
        val markData = MarkDataBean(10)
        markData.correctTime = 10
        val recyclerView = Mockito.mock(WaveRecyclerView::class.java).apply {
            addMarkData = markData
        }
        Mockito.`when`(recyclerView.addMarkData).thenReturn(markData)
        val mockItemView = Mockito.mock(WaveItemView::class.java)
        Mockito.`when`(mockItemView.parent).thenReturn(recyclerView)
        Mockito.`when`(mockItemView.context).thenReturn(context)
        val markAnimator = BookmarkAnimator(mockItemView)

        markAnimator.getFlagPaint(markData)
        Assert.assertNotNull(Whitebox.getInternalState(markAnimator, "mAnimAddFlag"))
    }
}