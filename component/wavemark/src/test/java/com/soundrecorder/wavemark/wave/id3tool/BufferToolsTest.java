package com.soundrecorder.wavemark.wave.id3tool;


import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.wavemark.shadows.ShadowFeatureOption;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.annotation.Config;

import java.io.UnsupportedEncodingException;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class BufferToolsTest {

    @Test
    public void should_value_when_byteBufferToStringIgnoringEncodingIssues() {
        byte[] bytes = "11122233".getBytes();
        String str = BufferTools.byteBufferToStringIgnoringEncodingIssues(bytes, 1, 3);
        Assert.assertEquals(str, "112");
    }

    @Test
    public void should_returnNotnull_when_stringToByteBuffer() throws UnsupportedEncodingException {
        Assert.assertNotNull(BufferTools.stringToByteBuffer("11122", 1, 2));
    }

    @Test
    public void should_value_when_trimStringRight() {
        Assert.assertEquals(BufferTools.trimStringRight("19981223"), "19981223");
    }

    @Test
    public void should_value_when_padStringRight() {
        Assert.assertEquals(BufferTools.padStringRight("1998", 5, '2'), "19982");
    }

    @Test
    public void should_value_when_sizeUnsynchronisationWouldAdd() {
        System.out.println(BufferTools.sizeUnsynchronisationWouldAdd("1998".getBytes()));
        Assert.assertEquals(BufferTools.sizeUnsynchronisationWouldAdd("1998".getBytes()), 0);
    }

    @Test
    public void should_returnNotnull_when_unsynchroniseBuffer() {
        byte[] bytes = "112233".getBytes();
        byte[] bytes1 = BufferTools.unsynchroniseBuffer(bytes);
        Assert.assertNotNull(bytes1);
    }

    @Test
    public void should_returnNotnull_when_sizeSynchronisationWouldSubtract() {
        byte[] bytes = "112233".getBytes();
        int length = BufferTools.sizeSynchronisationWouldSubtract(bytes);
        Assert.assertEquals(length, 0);
    }

    @Test
    public void should_returnNotnull_when_synchroniseBuffer() {
        Assert.assertNotNull(BufferTools.synchroniseBuffer("22334".getBytes()));
    }

    @Test
    public void should_returnNotnull_when_substitute() {
        String str = BufferTools.substitute("23344556", "2", "5");
        Assert.assertEquals(str, "53344556");
    }

    @Test
    public void should_returnNotnull_when_indexOfTerminator() {
        int length = BufferTools.indexOfTerminator("23344556".getBytes(), 2, 5);
        Assert.assertEquals(length, -1);
    }
}
