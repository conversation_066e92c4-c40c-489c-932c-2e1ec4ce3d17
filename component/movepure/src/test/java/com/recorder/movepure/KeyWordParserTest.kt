/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        : KeyWordParserTest.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/7/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.recorder.movepure

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.db.KeyWordDbUtils
import com.recorder.movepure.shadows.ShadowBaseUtils
import com.recorder.movepure.shadows.ShadowFeatureOption
import com.recorder.movepure.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.databean.KeyWord
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowBaseUtils::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class KeyWordParserTest {

    private var parser: BaseXmlParser<KeyWord>? = null

    @Before
    fun setUp() {
        parser = KeyWordParser()
    }

    @Test
    fun should_return_string_when_getXmlName() {
        val xmlName = parser?.getXmlName()

        Assert.assertNotNull(xmlName)
        Assert.assertEquals("key_word.xml", xmlName)
    }

    @Test
    fun test_createData() {
        val keyWord = parser?.createData()
        Assert.assertNotNull(keyWord)
    }

    @Test
    fun test_setDataAttr() {
        parser?.let {
            val keyWord = KeyWord("", 0f)

            it.setDataAttr(keyWord, KeyWordDbUtils.RECORD_ID, "10")
            Assert.assertEquals(keyWord.recordId, 10)

            it.setDataAttr(keyWord, KeyWordDbUtils.MEDIA_PATH, "/sdcard/Music/1.mp3")
            Assert.assertEquals(keyWord.mediaPath, "/sdcard/Music/1.mp3")
            it.setDataAttr(keyWord, KeyWordDbUtils.NAME, "1.mp3")
            Assert.assertEquals(keyWord.name, "1.mp3")
            it.setDataAttr(keyWord, KeyWordDbUtils.TFIDF_VALUE, "0.5")
            Assert.assertEquals(keyWord.tfidfvalue, 0.5f)
        }
    }
}