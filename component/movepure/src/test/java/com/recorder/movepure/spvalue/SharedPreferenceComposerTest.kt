/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SharedPreferenceComposerTest
 * Description:
 * Version: 1.0
 * Date: 2023/9/5
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/9/5 1.0 create
 */

package com.recorder.movepure.spvalue

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.recorder.movepure.shadows.ShadowBaseUtils
import com.recorder.movepure.shadows.ShadowFeatureOption
import com.recorder.movepure.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowBaseUtils::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class SharedPreferenceComposerTest {
    private var composer: SharedPreferenceComposer? = null

    @Before
    fun setUp() {
        composer = SharedPreferenceComposer()
    }

    @After
    fun release() {
        composer = null
    }


    @Test
    fun should_return_String_getTag() {
        val tag = composer?.getTag()

        Assert.assertNotNull(tag)
        Assert.assertEquals("sharedPreference", tag)
    }

    @Test
    fun should_return_string_getXmlName() {
        val xmlName = composer?.getXmlName()

        Assert.assertNotNull(xmlName)
        Assert.assertEquals("sharedPreference.xml", xmlName)
    }

    @Test
    fun should_return_bool_startCompose() {
        val startResult = composer?.startCompose()
        val endResult = composer?.endCompose()
        Assert.assertEquals(true, startResult)
        Assert.assertEquals(true, endResult)
    }

    @Test
    fun should_return_string_getXmlInfo() {
        composer?.startCompose()
        composer?.addData(Unit)
        composer?.endCompose()
        val result = composer?.getXmlInfo()

        Assert.assertNotNull(result)
    }
}