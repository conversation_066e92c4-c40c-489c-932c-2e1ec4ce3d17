package com.recorder.movepure.shadows;

import com.soundrecorder.base.utils.OS12FeatureUtil;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(OS12FeatureUtil.class)
public class ShadowOS12FeatureUtil {

    @Implementation
    public static boolean isSuperSoundRecorderEpicEffective() {
        return true;
    }


    @Implementation
    public static boolean readFeatureByOplusFeature() {
        return true;
    }


    @Implementation
    public static boolean isFindX4AndNotConfidential() {
        return true;
    }

}
