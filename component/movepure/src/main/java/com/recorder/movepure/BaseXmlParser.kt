package com.recorder.movepure

import android.text.TextUtils
import com.soundrecorder.common.constant.DatabaseConstant
import org.xmlpull.v1.XmlPullParser
import org.xmlpull.v1.XmlPullParserFactory
import java.io.StringReader
import java.util.*

abstract class BaseXmlParser<T> {

    private val TAG = "BaseXmlParser"

    fun parse(text: String): HashSet<T> {
        val list = hashSetOf<T>()
        var data: T? = null
        try {
            val factory = XmlPullParserFactory.newInstance()
            val parser = factory.newPullParser()
            parser.setInput(StringReader(text))
            var eventType = parser.eventType
            var tagName = ""
            while (eventType != XmlPullParser.END_DOCUMENT) {
                when (eventType) {
                    XmlPullParser.START_DOCUMENT -> {
                    }
                    XmlPullParser.START_TAG -> {
                        tagName = parser.name
                        data = createData()
                        if (TextUtils.equals(tagName, DatabaseConstant.ROOT)) {
                            val count = parser.attributeCount
                            for (i in 0 until count) {
                                val name = parser.getAttributeName(i)
                                val value = parser.getAttributeValue(i)
                                setDataAttr(data, name, value)
                            }
                        }
                    }
                    XmlPullParser.END_TAG -> {
                        if (TextUtils.equals(parser.name, DatabaseConstant.ROOT)) {
                            data?.let {
                                list.add(it)
                            }
                        }
                    }
                    else -> {
                    }
                }

                eventType = parser.next()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return list
    }

    /**
     * 获取xml文件的名称
     */
    abstract fun getXmlName(): String

    /**
     * 创建数据对象
     */
    abstract fun createData(): T

    /**
     * 设置数据对象的属性值
     */
    abstract fun setDataAttr(data: T, name: String, value: String)
}