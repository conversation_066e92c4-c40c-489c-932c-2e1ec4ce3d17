/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SmartSharedPreferenceComposer.kt
 ** Description : Smart Name Sp Value Composer
 ** Version     : 1.0
 ** Date        : 2025/05/28
 ** Author      : W9035969(<EMAIL>)
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9035969     2025/05/28     1.0      create
 ***********************************************************************/

package com.recorder.movepure.spvalue

import com.recorder.movepure.BaseXmlComposer
import com.recorder.movepure.MoveUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.modulerouter.smartname.SmartNameAction

class SmartSharedPreferenceComposer : BaseXmlComposer<Unit>() {
    companion object {
        private const val TAG = "SmartSharedPreferenceComposer"
        const val PREF_SUFFIX = "_sync_info"
        const val SMART_PREFERENCE_TAG = "prefSharedPreference"
        const val VALUE_SP_TRUE = "true"
        const val VALUE_SP_FALSE = "false"
    }
    override fun getTag(): String {
        return SMART_PREFERENCE_TAG
    }

    override fun composerData(data: Unit) {
        if (MoveUtils.checkCanBackSmartName()) {
            DebugUtil.i(TAG, "composerData")
            val context = BaseApplication.getAppContext()

            val isGuideShow = PrefUtil.getBoolean(context, PrefUtil.KEY_SMART_NAME_GUIDE_SHOW, false)
            if (isGuideShow) {
                mSerializer.attribute("", PrefUtil.KEY_SMART_NAME_GUIDE_SHOW, VALUE_SP_TRUE)
            }

            //智能命名开关
            if (SmartNameAction.isSmartNameSwitchOpen(context)) {
                DebugUtil.d(TAG, "smartNameSwitchValue")
                mSerializer.attribute("", PrefUtil.KEY_SMART_NAME_SWITCH_OPEN,
                    SmartNameAction.smartNameSwitchValue(context).toString())
            }
        }
    }

    override fun getXmlName(): String {
        return MoveUtils.getSmartXmlName()
    }
}