package com.recorder.movepure;


import android.text.TextUtils;

import com.oplus.backup.sdk.component.plugin.AbstractPlugin;
import com.recorder.movepure.spvalue.SmartSharedPreferenceComposer;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.constant.RecordModeConstant;
import com.soundrecorder.common.utils.FunctionOption;

import java.io.File;
import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.channels.FileChannel;

public class MoveUtils {
    public static final String TAG = "MoveUtils";

    /**
     * 是否可以备份恢复智能命名相关值
     * @return
     */
    public static boolean checkCanBackSmartName() {
        return !FunctionOption.isSupportSellMode();
    }

    public static String getSmartXmlName() {
        return BaseUtil.getPackageName() + SmartSharedPreferenceComposer.PREF_SUFFIX;
    }

    /**
     * 是否可以备份恢复用户须知相关值
     */
    public static boolean checkCanBackUserNotice() {
        // 内销且非卖场模式
        return !BaseUtil.isEXP() && !FunctionOption.isSupportSellMode();
    }

    public static boolean copyOverwriteFileForBackup(AbstractPlugin plugin, File src, String destPath) {
        boolean copySuc = false;
        if ((src == null) || !src.exists() || (plugin == null)) {
            return false;
        }
        DebugUtil.i(TAG, " copyOverwriteFileForBackup: srcfile: " + src + ", destName: " + FileUtils.getDisplayNameByPath(destPath));
        //fd to avoid no write or read ExternalStorger Permission granted on SoundRecorder app to transfer files to banjia App;
        FileDescriptor fileDescriptor = plugin.getFileDescriptor(destPath);
        if (fileDescriptor == null) {
            return false;
        }
        FileInputStream srcInputStream = null;
        FileOutputStream dstOutStream = null;
        FileChannel srcChannel = null;
        FileChannel dstChannel = null;
        try {
            srcInputStream = new FileInputStream(src);
            dstOutStream = new FileOutputStream(fileDescriptor);
            srcChannel = srcInputStream.getChannel();
            dstChannel = dstOutStream.getChannel();
            dstChannel.transferFrom(srcChannel, 0, srcChannel.size());
            copySuc = true;
        } catch (FileNotFoundException e) {
            DebugUtil.e(TAG, "copy file not found error:", e);
            e.printStackTrace();
            copySuc = false;
        } catch (IOException e) {
            DebugUtil.e(TAG, "copy file io error:", e);
            copySuc = false;
        } finally {
            try {
                if (srcInputStream != null) {
                    srcInputStream.close();
                }
                if (dstOutStream != null) {
                    dstOutStream.close();
                }
                if (srcChannel != null) {
                    srcChannel.close();
                }
                if (dstChannel != null) {
                    dstChannel.close();
                }
            } catch (IOException e) {
                DebugUtil.e(TAG, "close file io error:", e);
            }
        }
        return copySuc;
    }

    public static boolean copyOverwriteFileForRestore(AbstractPlugin plugin, String srcFilePath, String destPath) {
        boolean copySuc = false;
        if (TextUtils.isEmpty(srcFilePath) || (plugin == null)) {
            return false;
        }
        DebugUtil.i(TAG, " copyOverwriteFileForRestore srcfile: " + FileUtils.getDisplayNameByPath(srcFilePath) + ", destName: " + FileUtils.getDisplayNameByPath(destPath));
        //fd to avoid no write or read ExternalStorger Permission granted on SoundRecorder app to transfer files to banjia App;
        FileDescriptor fileDescriptor = plugin.getFileDescriptor(srcFilePath);
        if (fileDescriptor == null) {
            return true;
        }
        String parentDir = getParentDirectoryFromFullPath(destPath);
        if (TextUtils.isEmpty(parentDir)) {
            return false;
        }
        FileUtils.ensureDirectory(parentDir);
        File dest = new File(destPath);
        FileInputStream srcInputStream = null;
        FileOutputStream dstOutStream = null;
        FileChannel srcChannel = null;
        FileChannel dstChannel = null;
        try {
            srcInputStream = new FileInputStream(fileDescriptor);
            dstOutStream = new FileOutputStream(dest);
            srcChannel = srcInputStream.getChannel();
            dstChannel = dstOutStream.getChannel();
            dstChannel.transferFrom(srcChannel, 0, srcChannel.size());
            copySuc = true;
        } catch (FileNotFoundException e) {
            DebugUtil.e(TAG, "copy file not found error:", e);
            e.printStackTrace();
            copySuc = false;
        } catch (IOException e) {
            DebugUtil.e(TAG, "copy file io error:", e);
            copySuc = false;
        } finally {
            try {
                if (srcInputStream != null) {
                    srcInputStream.close();
                }
                if (dstOutStream != null) {
                    dstOutStream.close();
                }
                if (srcChannel != null) {
                    srcChannel.close();
                }
                if (dstChannel != null) {
                    dstChannel.close();
                }
            } catch (IOException e) {
                DebugUtil.e(TAG, "close file io error:", e);
            }
        }
        return copySuc;
    }

    private static String getParentDirectoryFromFullPath(String fullPath) {
        String result = "";
        if (!TextUtils.isEmpty(fullPath)) {
            int index = fullPath.lastIndexOf(File.separator);
            if ((index > 0) && (index < fullPath.length())) {
                result = fullPath.substring(0, index);
            }
        }
        return result;
    }

    public static String getDataByData(String data) {
        boolean dataIsQ = data.contains(RecordModeConstant.STORAGE_RECORD_ABOVE_Q);
        if (BaseUtil.isAndroidQOrLater()) {
            if (!dataIsQ) {
                return data.replace(File.separator + Constants.RECORDINGS + File.separator, File.separator + RecordModeConstant.STORAGE_RECORD_ABOVE_Q);
            } else {
                return data;
            }
        } else {
            if (!dataIsQ) {
                return data;
            } else {
                return data.replace(File.separator + RecordModeConstant.STORAGE_RECORD_ABOVE_Q, File.separator + RecordModeConstant.STORAGE_RECORD);
            }
        }
    }
}
