/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SmartSharedPreferenceParser.kt
 ** Description : Smart Name Sp Value Parser
 ** Version     : 1.0
 ** Date        : 2025/05/28
 ** Author      : W9035969(<EMAIL>)
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9035969     2025/05/28     1.0      create
 ***********************************************************************/

package com.recorder.movepure.spvalue

import com.recorder.movepure.BaseXmlParser
import com.recorder.movepure.MoveUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.PrefUtil

class SmartSharedPreferenceParser : BaseXmlParser<Unit>() {

    companion object {
        private const val TAG = "SmartSharedPreferenceParser"
    }

    override fun getXmlName(): String {
        return MoveUtils.getSmartXmlName()
    }

    override fun createData(): Unit {
        return Unit
    }

    override fun setDataAttr(data: Unit, name: String, value: String) {
        if (MoveUtils.checkCanBackSmartName()) {
            DebugUtil.i(TAG, "setDataAttr,name=$name,value=$value")
            if (value == SmartSharedPreferenceComposer.VALUE_SP_TRUE
                || value == SmartSharedPreferenceComposer.VALUE_SP_FALSE) {
                PrefUtil.putBoolean(BaseApplication.getAppContext(), name, value.toBoolean())
            } else {
                value.toIntOrNull()?.let {
                    PrefUtil.putInt(BaseApplication.getAppContext(), name, it)
                }
            }
        }
    }
}