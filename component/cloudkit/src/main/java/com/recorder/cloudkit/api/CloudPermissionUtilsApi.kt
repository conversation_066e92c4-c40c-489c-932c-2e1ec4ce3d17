package com.recorder.cloudkit.api

import android.content.Context
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.recorder.cloudkit.utils.CloudPermissionUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.cloudkit.utils.CloudPermissionUtilsAction

@Component(CloudPermissionUtilsAction.COMPONENT_NAME)
object CloudPermissionUtilsApi {

    private const val TAG = "CloudPermissionUtilsApi"



    /**
     * 设置云服务权限开启
     */
    @Action(CloudPermissionUtilsAction.ACTION_SET_CLOUD_STATUS)
    @JvmStatic
    fun setCloudGrantedStatus(context: Context) {
        DebugUtil.i(TAG, "setCloudGrantedStatus")
        return CloudPermissionUtils.setCloudGrantedStatus(context)
    }

    /**
     * 关闭开关状态
     */
    @Action(CloudPermissionUtilsAction.ACTION_CLEAR_CLOUD_GRANTED_STATUS)
    @JvmStatic
    fun clearCloudGrantedStatus() {
        DebugUtil.i(TAG, "clearCloudGrantedStatus")
        return CloudPermissionUtils.clearCloudGrantedStatus()
    }

    /**
     * 判断云服务权限已授予
     */
    @Action(CloudPermissionUtilsAction.ACTION_IS_CLOUD_GRANTED)
    @JvmStatic
    fun isStatementCloudGranted(context: Context): Boolean {
        val isCloudGranted = CloudPermissionUtils.isStatementCloudGranted(context)
        DebugUtil.i(TAG, "isStatementCloudGranted:$isCloudGranted")
        return isCloudGranted
    }

    @Action(CloudPermissionUtilsAction.ACTION_HAS_CLOUD_REQUIRED_PERMISSION)
    @JvmStatic
    fun hasCloudRequiredPermissions(): Boolean {
        return CloudPermissionUtils.hasCloudRequirePermission()
    }

    @Action(CloudPermissionUtilsAction.ACTION_IS_NETWORK_GRANTED)
    @JvmStatic
    fun isNetWorkGranted(context: Context): Boolean {
        return CloudPermissionUtils.isNetWorkNoticeGranted(context)
    }
}