package com.recorder.cloudkit.sync.bean

import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode
import com.recorder.cloudkit.utils.CloudKitErrorExt.calFinalErrorCode
import com.recorder.cloudkit.utils.CloudKitErrorExt.logMessage

class CloudSyncResult {

    // 结果未error，是由于哪步流程error件导致；2/1-上传/下载元数据出错； 4/3-上传/下载文件出错
    var errorFrom = ERROR_FROM_DEFAULT_CHECKER
    var errorCode: Int = -1
    var cloudKitError: CloudKitError? = null

    /**
     *  1.同步开始前，元数据恢复暂停，弱网、网络抖动、网络不稳定、网络请求超时  【网络异常，同步未开始】-仅针对第一页元数据下载且不含补刀逻辑
     *  2. 同步开始前，元数据恢复异常暂停或3分钟内没有响应，或文件未开始同步   【服务异常，同步未开始】-仅针对第一页元数据下载且不含补刀逻辑
     *  3.同步开始前，查询元数据时判断本地空间小于300MB时，为保证程序的正常运行，不会拉起同步任务。  【存储空间不足，同步未开始】仅针对第一页元数据下载且不含补刀逻辑
     * 标记 inQueryStep=true，同步未开始，用于UI展示
     */
    var inQueryStep: Boolean = false

    // 标记：本次同步成功完成起始是备份还是恢复，用于同步完成成功，副标题是否判断剩余同步数据显示错误
    var enterByRecovery = false
    var batchErrorCode = 0


    fun isSuccess(): Boolean {
        return errorCode == SyncErrorCode.RESULT_SUCCESS
    }

    fun isCanTry(tryCount: Int, maxTryCount: Int): Boolean {
        return if (isCanTryCode()) tryCount < maxTryCount else false
    }

    fun errorFromMetaData() = (errorFrom == ERROR_FROM_METADATA_RECOVERY) || (errorFrom == ERROR_FROM_METADATA_BACKUP)

    /**
     * 错误码是否为可重试错误码
     */
    fun isCanTryCode(): Boolean =
        (errorCode == SyncErrorCode.RESULT_NETWORK_ERROR) || (errorCode in SyncErrorCode.CAN_TRY_SERVER_ERROR_CODE)

    override fun toString(): String {
        return "CloudKitResult{" +
                ", inQueryStep=" + inQueryStep +
                ", errorFrom=" + errorFrom +
                ", errorCode=" + errorCode +
                ", enterByRecovery=" + enterByRecovery +
                ", cloudKitError=" + cloudKitError +
                ", enterByRecovery=" + enterByRecovery +
                ", batchErrorCode=" + batchErrorCode +
                '}'
    }

    fun logMessage(): String {
        return "errorCode=$errorCode, cloudKitError=${cloudKitError?.logMessage()}"
    }

    companion object {
        const val ERROR_FROM_DEFAULT_CHECKER = 0
        const val ERROR_FROM_METADATA_RECOVERY = 1
        const val ERROR_FROM_METADATA_BACKUP = 2
        const val ERROR_FROM_FILE_RECOVERY = 3
        const val ERROR_FROM_FILE_BACKUP = 4

        fun createSuccess() = CloudSyncResult().apply { errorCode = SyncErrorCode.RESULT_SUCCESS }

        @JvmStatic
        fun createResult(errorCode: Int, errorFromIn: Int?): CloudSyncResult {
            return CloudSyncResult().apply {
                this.errorCode = errorCode
                errorFromIn?.let {
                    errorFrom = it
                }
            }
        }

        /**
         * @param isBackUp 是否备份流程
         */
        @JvmStatic
        fun createMetaDataError(kitError: CloudKitError?, isBackUp: Boolean): CloudSyncResult {
            return CloudSyncResult().apply {
                errorFrom = if (isBackUp) ERROR_FROM_METADATA_BACKUP else ERROR_FROM_METADATA_RECOVERY
                this.cloudKitError = kitError
                errorCode = kitError.calFinalErrorCode()
            }
        }

        /**
         * @param isBackUp 是否备份流程
         */
        @JvmStatic
        fun createFileError(kitError: CloudKitError?, isBackUp: Boolean): CloudSyncResult {
            return CloudSyncResult().apply {
                errorFrom = if (isBackUp) ERROR_FROM_FILE_BACKUP else ERROR_FROM_FILE_RECOVERY
                this.cloudKitError = kitError
                errorCode = kitError.calFinalErrorCode()
            }
        }
    }
}