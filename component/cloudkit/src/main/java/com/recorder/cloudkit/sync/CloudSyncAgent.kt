package com.recorder.cloudkit.sync

import android.content.Context
import android.os.Build
import androidx.annotation.WorkerThread
import com.google.gson.Gson
import com.heytap.cloudkit.libcommon.netrequest.error.CloudBizError
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.heytap.cloudkit.libsync.ext.ICloudBackupMetaData
import com.heytap.cloudkit.libsync.ext.ICloudRecoveryMetaData
import com.heytap.cloudkit.libsync.metadata.helper.CloudBackupRequestSource
import com.heytap.cloudkit.libsync.metadata.helper.CloudRecoveryRequestSource
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudBackupResponseError
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudBackupResponseRecord
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataFileInfo
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord
import com.heytap.cloudkit.libsync.service.CloudDataType
import com.recorder.cloudkit.sync.SyncDataConstants.ZONE_GROUP
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.databean.Record
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.db.RecorderDBUtil
import com.recorder.cloudkit.sync.SyncDataConstants.ZONE_RECORDER
import com.recorder.cloudkit.sync.backup.FileUpLoadUtil
import com.recorder.cloudkit.sync.backup.MetaDataUploadUtil
import com.recorder.cloudkit.sync.bean.*
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode
import com.recorder.cloudkit.sync.listener.IBackUpListener
import com.recorder.cloudkit.sync.listener.IRecoveryListener
import com.recorder.cloudkit.sync.recovery.DownloadSyncUtil
import com.recorder.cloudkit.sync.recovery.SyncDownloadBiz
import com.recorder.cloudkit.utils.CloudKitErrorExt.canContinueSyncProcess
import com.recorder.cloudkit.utils.CloudKitErrorExt.logMessage
import com.recorder.cloudkit.utils.CloudRecordDbUtil
import com.recorder.cloudkit.utils.PathUtil
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.db.GroupInfoDbUtil
import com.soundrecorder.common.db.GroupInfoManager

class CloudSyncAgent private constructor() {

    companion object {
        private const val TAG = "CloudSyncAgent"
        private const val METADATA_TYPE_TEXT = 1 //1 纯文本普通数据
        private const val METADATA_TYPE_FILE = 2 //2 文件数据

        @Volatile
        private var mCloudSyncAgent: CloudSyncAgent? = null

        @Synchronized
        @JvmStatic
        fun getInstance(): CloudSyncAgent =
            mCloudSyncAgent ?: synchronized(CloudSyncAgent::class.java) { mCloudSyncAgent ?: CloudSyncAgent().apply { mCloudSyncAgent = this } }

        @Synchronized
        @JvmStatic
        fun release() {
            DebugUtil.i(TAG, "release")
            mCloudSyncAgent = null
        }
    }

    private val mRecordModule = SyncDataConstants.MODULE_RECORDER
    private val mContext: Context
    private val mRecorderDBUtil: RecorderDBUtil
    private var mSyncStop = false
    private var mSyncStopSubErrorCode = SyncErrorCode.RESULT_CANCEL
    private val mGson: Gson

    //过滤高温限制云同步 一旦设置为true,需要执行BrowseFile onDestroy才会设置为false
    var ignoreCheckHighTemperature = false

    init {
        mContext = BaseApplication.getAppContext()
        mRecorderDBUtil = RecorderDBUtil.getInstance(mContext)
        mGson = Gson()
    }

    fun setSyncStop(isStop: Boolean, subErrorCode: Int?) {
        if (mSyncStop != isStop) {
            mSyncStop = isStop
            mSyncStopSubErrorCode = subErrorCode ?: SyncErrorCode.RESULT_CANCEL
            DebugUtil.i(TAG, "setSyncStop $mSyncStop", true)
            CloudStaticsUtil.addCloudLog(TAG, "setSyncStop,value=$isStop")
        }
    }

    fun isManualStop() = mSyncStop

    fun getStopSubErrorCode(): Int = mSyncStopSubErrorCode

    /**
     * 上传 未上传过的GroupInfo表的元数据
     */
    @WorkerThread
    fun backUpGroupInfoMetaData(requestSource: CloudBackupRequestSource, stepValue: String, backUpListener: IBackUpListener) {
        val unCommitMetaDataList: List<GroupInfo> = GroupInfoManager.getInstance(mContext).getDirtyDataForUnCommitMetadata()
        if (unCommitMetaDataList.isNullOrEmpty()) {
            DebugUtil.i(TAG, "备份step0_1: 未上传元数据data：backUpGroupInfoMetaData data.size: 0 ", true)
            backUpListener.onFinish(stepValue, CloudSyncResult.createSuccess())
            return
        }
        //上传元数据
        DebugUtil.i(TAG, "备份step0_1: 未上传元数据data：backUpGroupInfoMetaData data.size: ${unCommitMetaDataList.size}", true)
        doCommitGroupInfoMetaDataList(requestSource, stepValue, genBackUpGroupInfoMetaData(unCommitMetaDataList), backUpListener)
    }

    /**
     * 上传 已上传过但是本地有改变的GroupInfo表的元数据
     */
    @WorkerThread
    fun backUpLocalChangedGroupInfoMetaData(requestSource: CloudBackupRequestSource, stepValue: String, backUpListener: IBackUpListener) {
        val localChangedMetaDataList: List<GroupInfo> = GroupInfoManager.getInstance(mContext).getDirtyDataForChangedMetadata()
        if (localChangedMetaDataList.isNullOrEmpty()) {
            DebugUtil.i(TAG, "备份step0_2: 未上传元数据data：backUpLocalChangedGroupInfoMetaData data.size: 0 ", true)
            backUpListener.onFinish(stepValue, CloudSyncResult.createSuccess())
            return
        }
        //上传元数据
        DebugUtil.i(TAG, "备份step0_2: 未上传元数据data：backUpLocalChangedGroupInfoMetaData data.size: ${localChangedMetaDataList.size}", true)
        doCommitGroupInfoMetaDataList(requestSource, stepValue, genBackUpGroupInfoMetaData(localChangedMetaDataList), backUpListener)
    }


    /**
     * 上传元数据，
     * STEP1:针对未同步过数据：已上传文件，待上传元数据db（无globalId、有fileId）- create
     */
    @WorkerThread
    fun backUpUploadedFileMetaData(requestSource: CloudBackupRequestSource, stepValue: String, backUpListener: IBackUpListener) {
        val unCommitMetaDataList: List<Record> = mRecorderDBUtil.dirtyDataForUnCommitMetadata

        DebugUtil.i(TAG, "备份step1: 已上传文件，未上传元数据data：backUpUploadedFileMetaData data.size: ${unCommitMetaDataList.size}", true)
        if (unCommitMetaDataList.isNullOrEmpty()) {
            backUpListener.onFinish(stepValue, CloudSyncResult.createSuccess())
            return
        }

        // 继续备份 已上传文件待上传元数据
        doCommitRecorderMetaDataList(requestSource, stepValue, genBackUpMetaData(unCommitMetaDataList), backUpListener)
    }

    /**
     * 上传元数据，
     * step2:针对已同步过数据（有globalId、有fileId），本地有变更-update/delete
     */
    @WorkerThread
    fun backUpLocalChangedMetaData(requestSource: CloudBackupRequestSource, stepValue: String, backUpListener: IBackUpListener) {
        val unCommitMetaDataList: List<Record> = mRecorderDBUtil.dirtyDataForBatchUploadMegaData
        DebugUtil.i(TAG, "备份step2: 已成功上传元数据，本地有改变：backUpLocalChangedMetaData data.size: ${unCommitMetaDataList.size}", true)

        if (unCommitMetaDataList.isNullOrEmpty()) {
            backUpListener.onFinish(stepValue, CloudSyncResult.createSuccess())
            return
        }

        // 提交本地变更元数据信息
        doCommitRecorderMetaDataList(requestSource, stepValue, genBackUpMetaData(unCommitMetaDataList), backUpListener)
    }

    /**
     * 备份还未上传文件的本地数据-create
     *Step3:
     * @param backUpListener 回调
     */
    @WorkerThread
    fun backUpUnUploadFileData(requestSource: CloudBackupRequestSource, stepValue: String, backUpListener: IBackUpListener) {
        val checkerResult = checkCanSyncCondition(false)
        if (!checkerResult.success()) {
            DebugUtil.e(TAG, "备份step3,checkCondition fail ${checkerResult.message()}")
            backUpListener.onFinish(stepValue, CloudSyncResult.createResult(checkerResult.resultCode, CloudSyncResult.ERROR_FROM_FILE_BACKUP))
            return
        }

        val dirtyData = FileUpLoadUtil.getUploadFileList(BaseApplication.getAppContext())
        DebugUtil.i(TAG, "备份step3:未上传文件：backUpUnUploadFileData data.size: ${dirtyData.size}", true)
        CloudStaticsUtil.addCloudLog(TAG, "backUpUnUploadFileData, data.size= ${dirtyData.size}")
        if (dirtyData.isNullOrEmpty()) { //数据为空则不触发
            backUpListener.onFinish(stepValue, CloudSyncResult.createSuccess())
            return
        }

        // 改变db数据状态
        dirtyData.forEach {
            mRecorderDBUtil.updateSyncStateByUUid(it.record.uuid, RecordConstant.SYNC_STATUS_BACKUPING_FILE)
        }

        //step3: 批量上传文件
        TransferFileControl.uploadFileList(dirtyData, object : TransferFileControl.TransferFilesCallBack {
            override fun batchResult(successFileList: List<RecordTransferFile>?, errorFileList: List<RecordTransferFile>?) {
                DebugUtil.i(TAG,
                    "备份step3:未上传文件-上传文件结果：uploadFileResult successFileList.size: ${successFileList?.size}  errorFileList.size: ${errorFileList?.size}", true)

                backUpListener.onUploadFileFinish(errorFileList)
                CloudStaticsUtil.addCloudLog(
                    TAG,
                    "backUpUnUploadFileData,batchResult,successFileSize=${successFileList?.size},errorFileSize=${errorFileList?.size},errorInfo=${
                        errorFileList?.getOrNull(0)?.uploadResult?.logMessage()
                    }")
                // 检查失败原因是否为不可恢复，不可恢复，结束同步流程
                if (errorFileList?.getOrNull(0)?.uploadResult?.canContinueSyncProcess(mContext) == false) {
                    backUpListener.onFinish(stepValue, CloudSyncResult.createFileError(errorFileList[0].uploadResult, true))
                    return
                }

                //step4: 上传成功后，填充文件信息到元数据，并且提交
                doCommitRecorderMetaDataList(requestSource, stepValue, genBackUpMetaData(mRecorderDBUtil.dirtyDataForUnCommitMetadata),
                    object : IBackUpListener {
                        override fun onStartUploadMetaData() {
                            backUpListener.onStartUploadMetaData()
                        }

                        override fun onFinish(stepValue: String, cloudKitError: CloudSyncResult) {
                            DebugUtil.i(TAG, "备份step3:doCommitRecorderMetaDataList result $cloudKitError", true)

                            if (!cloudKitError.isSuccess()) {
                                backUpListener.onFinish(stepValue, cloudKitError)
                                return
                            }
                            // 第n批次
                            backUpUnUploadFileData(requestSource, stepValue, backUpListener)
                        }
                    })
            }

            override fun onIOResult(file: RecordTransferFile, cloudKitError: CloudKitError) {
                DebugUtil.i(TAG, "备份step3:未上传文件-上传文件单个结果：singleFile uploadFileResult : ${file.record.displayName}: $cloudKitError", true)
                if (cloudKitError.isSuccess) {
                    mRecorderDBUtil.updateFileIdByUUid(file.record.uuid, file.cloudId, file.checkPayload)
                } else {
                    if (cloudKitError.bizErrorCode == CloudBizError.NO_FIND_LOCAL_FILE.code) {
                        // 找不到文件，是否需要删除本地记录;可能被文管更改了名称
//                        mRecorderDBUtil.deleteRecordsById(file.record.id)
                        DebugUtil.e(TAG, "onIOResultFail for NO_FIND_LOCAL_FILE: ${file.record}")
                    }
                    mRecorderDBUtil.updateSyncStateByUUid(file.uuid, RecordConstant.SYNC_STATUS_BACKUP_FILE_FAILED)
                }
            }

            override fun onTransferStart() {
                backUpListener.onStartUploadFile()
            }
        })
    }

    /**
     * Step1:恢复已下载元数据，未下载文件内容
     * 单个文件下载成功-->文件复制到应用目录下
     * 批量文件下载结果-->全部成功：下个批次；若无就回调finish()
     *              -->部分成功：onDownloadFileFinish(error)
     */
    fun recoveryUnDownloadData(requestSource: CloudRecoveryRequestSource, recoverListener: IRecoveryListener) {
        val downloadList = CloudRecordDbUtil.getDownloadFileList(BaseApplication.getAppContext())
        DebugUtil.i(TAG, "恢复Step1:下载文件requestSource=$requestSource, data.size: ${downloadList.size}", true)
        if (downloadList.isNullOrEmpty()) {
            recoverListener.onFinish(CloudSyncResult.createSuccess())
            return
        }
        val checkerResult = checkCanSyncCondition(true)
        if (!checkerResult.success()) {
            DebugUtil.e(TAG, "恢复Step1,checkCondition fail requestSource=$requestSource, ${checkerResult.message()}", true)
            recoverListener.onFinish(CloudSyncResult.createResult(checkerResult.resultCode, CloudSyncResult.ERROR_FROM_FILE_RECOVERY))
            return
        }
        CloudStaticsUtil.addCloudLog(TAG, "recoveryUnDownloadData,downloadList.size=${downloadList.size}")
        downloadList.forEach {
            mRecorderDBUtil.updateDownloadStateByUUid(it.record.uuid, RecordConstant.SYNC_STATUS_RECOVERYING_FILE)
        }
        // 下载文件
        TransferFileControl.downLoadFileList(downloadList, object : TransferFileControl.TransferFilesCallBack {
            override fun batchResult(successFileList: List<RecordTransferFile>?, errorFileList: List<RecordTransferFile>?) {
                DebugUtil.i(TAG,
                    "恢复Step1:下载文件批量结果 downloadFileListResult: successFileList.size：${successFileList?.size}  errorFileList.size：${errorFileList?.size}", true)
                recoverListener.onDownloadFileFinish(successFileList, errorFileList)
                CloudStaticsUtil.addCloudLog(TAG, "recoveryUnDownloadData,batchResult,successFileSize=${successFileList?.size}," +
                        "errorSize=${errorFileList?.size},errorInfo=${errorFileList?.getOrNull(0)?.uploadResult?.logMessage()}")
                // 检查失败文件原因是否阻塞下一步流程
                if (errorFileList?.getOrNull(0)?.uploadResult?.canContinueSyncProcess(mContext) != false) {
                    // 继续下一批次
                    recoveryUnDownloadData(requestSource, recoverListener)
                } else {
                    recoverListener.onFinish(CloudSyncResult.createFileError(errorFileList.getOrNull(0)?.uploadResult, false))
                }
            }

            override fun onIOResult(file: RecordTransferFile, cloudKitError: CloudKitError) {
                DebugUtil.i(TAG,
                    "恢复Step1:下载文件单个下载结果： downloadSingleFileResult: ${cloudKitError.isSuccess}  ${file.record.displayName} - $cloudKitError", true)
                SyncDownloadBiz.process(mContext, file, cloudKitError.isSuccess)
            }

            override fun onTransferStart() {
                recoverListener.onStartDownloadFile()
            }
        })
    }

    /**
     * 云同步恢复数据时，检查是否需要清理云同步锚点
     * @param module 模块名
     * @param zone 区域名
     */
    private fun checkIfNeedClearRecoveryAnchor(module: String, zone: String) {
        val curSysVersion = SyncDataConstants.RECORD_TYPE_VERSION
        val key = "${module}_$zone"
        val cachedSysVersion = PrefUtil.getInt(BaseApplication.getAppContext(), key, 0)
        // 如果缓存的版本号不等于当前版本号，则清理云同步锚点，否则再旧版本云同步后，覆盖安装新版后，无法取到云端数据
        if (cachedSysVersion != curSysVersion) {
            //清理表sys_version
            if (zone == ZONE_RECORDER) {
                MetaDataUploadUtil.clearRecordTableSysVersion()
            } else if (zone == ZONE_GROUP) {
                MetaDataUploadUtil.clearGroupInfoTableSysVersion()
            }
            // 清理云同步锚点
            CloudSyncManager.getInstance().clearSysVersion(module, zone)
            PrefUtil.putInt(BaseApplication.getAppContext(), key, curSysVersion)
            DebugUtil.i(TAG, "checkIfNeedClearRecoveryAnchor,key=$key,cachedSysVersion=$cachedSysVersion,curSysVersion=$curSysVersion", true)
        }
    }

    /**
     * 开始录音的元数据恢复
     * 在分组信息元数据恢复完成之后调用
     */
    private fun startRecoveryRecordsMetaData(requestSource: CloudRecoveryRequestSource, recoverListener: IRecoveryListener) {
        checkIfNeedClearRecoveryAnchor(mRecordModule, ZONE_RECORDER)
        // 下载元数据
        CloudSyncManager.getInstance().startRecoveryMetaData(mRecordModule, ZONE_RECORDER, requestSource, SyncDataConstants.RECORD_TYPE_VERSION,
            object : ICloudRecoveryMetaData {
                /**
                 * 下载元数据成功
                 */
                override fun onSuccess(
                    cloudDataType: CloudDataType,
                    data: MutableList<CloudMetaDataRecord>?,
                    hasMoreData: Boolean,
                    totalCount: Long,
                    remainCount: Long,
                ) {
                    DebugUtil.i(TAG,
                        "下载元数据 downloadRecordsMetaDataFromCloud success: data.size ${data?.size}," +
                                " total=$totalCount remain=$remainCount hasMore=$hasMoreData \n ", true)
                    recoverListener.onDownloadMetaDataFinish(hasMoreData, data)
                    CloudStaticsUtil.addCloudLog(TAG, "recoveryCloudData,onSuccess," +
                            "data.size= ${data?.size}, hasMoreData=$hasMoreData,totalCount=$totalCount,remainCount=$remainCount")

                    val handleResultCode = handleDownloadMetaData(requestSource, data)
                    DebugUtil.i(TAG, "handleDownloadMetaData,code $handleResultCode")

                    if (handleResultCode == SyncErrorCode.RESULT_SUCCESS) {
                        //获取需要下载的文件，下载文件
                        downloadMetaDataFile(requestSource, data, hasMoreData, recoverListener)
                    } else {
                        DebugUtil.e(TAG, "处理元数据失败...,$handleResultCode")
                        // 处理元数据失败,这个地方只处理没有权限的情况，处理数据中的error 忽略
                        recoverListener.onFinish(CloudSyncResult.createResult(handleResultCode,
                            CloudSyncResult.ERROR_FROM_METADATA_RECOVERY))
                    }
                }

                /**
                 * 下载元数据失败
                 */
                override fun onError(cloudDataType: CloudDataType, error: CloudKitError) {
                    DebugUtil.e(TAG, "downloadMetaDataFromCloud error: $error")
                    notifyRecoveryUpdateAnchor(false)
                    recoverListener.onFinish(CloudSyncResult.createMetaDataError(error, false))
                    CloudStaticsUtil.addCloudLog(TAG, "recoveryCloudData,onError ${error.errorMsg}")
                }
            })
    }

    /**
     * Step2:从远端下载元数据、文件
     * 下载元数据成功
     *  ->通知kit完成恢复
     *  ->下载本批次文件 -->全部成功+hasMoreData ---> 下载下一页元数据
     *                -->全部成功+hasMoreData ---> 下载下一页元数据
     */
    fun recoveryCloudData(requestSource: CloudRecoveryRequestSource, recoverListener: IRecoveryListener) {
        DebugUtil.i(TAG, "恢复Step2:下载元数据 requestSource $requestSource", true)
        val checkerResult = checkCanSyncCondition(true)
        if (!checkerResult.success()) {
            DebugUtil.e(TAG, "恢复Step2:下载元数据,checkCondition fail requestSource=$requestSource, ${checkerResult.message()}")
            recoverListener.onFinish(CloudSyncResult.createResult(checkerResult.resultCode, CloudSyncResult.ERROR_FROM_METADATA_RECOVERY))
            return
        }
        recoverListener.onStartRecovery()
        //检查分组表是否因云端数据库升级清理锚点和本地global_id
        checkIfNeedClearRecoveryAnchor(mRecordModule, ZONE_GROUP)
        //先下载分组元数据
        CloudSyncManager.getInstance().startRecoveryMetaData(mRecordModule, ZONE_GROUP, requestSource, SyncDataConstants.RECORD_TYPE_VERSION,
            object : ICloudRecoveryMetaData {

                /**
                 * 下载分组元数据成功
                 */
                override fun onSuccess(
                    cloudDataType: CloudDataType,
                    data: MutableList<CloudMetaDataRecord>?,
                    hasMoreData: Boolean,
                    totalCount: Long,
                    remainCount: Long,
                ) {
                    DebugUtil.i(TAG,
                        "下载元数据 downloadGroupInfoMetaDataFromCloud success: data.size ${data?.size}, " +
                                "total=$totalCount remain=$remainCount hasMore=$hasMoreData \n ", true)
                    recoverListener.onDownloadMetaDataFinish(hasMoreData, data)
                    CloudStaticsUtil.addCloudLog(TAG, "recoveryCloudData,onSuccess," +
                            "data.size= ${data?.size}, hasMoreData=$hasMoreData,totalCount=$totalCount,remainCount=$remainCount")
                    val handleResultCode = handleDownloadGroupInfoMetaData(requestSource, data)
                    DebugUtil.i(TAG, "handleDownloadGroupInfoMetaData,code $handleResultCode")

                    if (handleResultCode == SyncErrorCode.RESULT_SUCCESS) {
                        //开始录音元数据的下载
                        startRecoveryRecordsMetaData(requestSource, recoverListener)
                    } else {
                        DebugUtil.e(TAG, "处理录音分组元数据失败...,$handleResultCode")
                        // 处理元数据失败,这个地方只处理没有权限的情况，处理数据中的error 忽略
                        recoverListener.onFinish(CloudSyncResult.createResult(handleResultCode,
                            CloudSyncResult.ERROR_FROM_METADATA_RECOVERY))
                    }
                }

                /**
                 * 下载元数据失败
                 */
                override fun onError(cloudDataType: CloudDataType, error: CloudKitError) {
                    DebugUtil.e(TAG, "downloadMetaDataFromCloud error: $error")
                    notifyGroupInfoRecoveryUpdateAnchor(false)
                    recoverListener.onFinish(CloudSyncResult.createMetaDataError(error, false))
                    CloudStaticsUtil.addCloudLog(TAG, "recoveryCloudData,onError ${error.errorMsg}")
                }
            })
    }

    /**
     * 下载下载元数据成功那批数据中的文件
     */
    private fun downloadMetaDataFile(
        requestSource: CloudRecoveryRequestSource,
        data: MutableList<CloudMetaDataRecord>?,
        hasMoreData: Boolean,
        recoverListener: IRecoveryListener,
    ) {
        val checkerResult = checkCanSyncCondition(true)
        if (!checkerResult.success()) {
            DebugUtil.e(TAG, "恢复Step2:下载文件,checkCondition fail requestSource=$requestSource, ${checkerResult.message()}")
            recoverListener.onFinish(CloudSyncResult.createResult(checkerResult.resultCode, CloudSyncResult.ERROR_FROM_FILE_RECOVERY))
            return
        }

        // 元数据中拿到需要下载文件的列表
        val downloadList = CloudRecordDbUtil.getDownloadFileList(mContext, data)
        DebugUtil.i(TAG, "下载元数据中的文件：downloadMetaDataFile ：${downloadList?.size}", true)
        CloudStaticsUtil.addCloudLog(TAG, "downloadMetaDataFile, fileSize=${downloadList.size}")
        if (downloadList.isNullOrEmpty()) {
            DebugUtil.i(TAG, " downloadMetaDataFile is null or empty.hasMoreMetaData $hasMoreData", true)
            //本批元数据没有需要下载文件，先校准一下分组信息
            GroupInfoManager.getInstance(BaseApplication.getAppContext()).verifyGroupCount()

            // 本批元数据没有需要下载文件，则接着下载下个批次需要下载的文件
            if (hasMoreData) {
                recoveryCloudData(requestSource, recoverListener)
            } else {
                recoverListener.onFinish(CloudSyncResult.createSuccess())
            }
            return
        }

        //下载文件
        downloadList.forEach {
            mRecorderDBUtil.updateDownloadStateByUUid(it.uuid, RecordConstant.SYNC_STATUS_RECOVERYING_FILE)
        }
        TransferFileControl.downLoadFileList(downloadList, object : TransferFileControl.TransferFilesCallBack {
            // 批次下载完成
            override fun batchResult(successFileList: List<RecordTransferFile>?, errorFileList: List<RecordTransferFile>?) {
                DebugUtil.i(TAG, "恢复Step2:下载文件批量结果 downloadFileListResult: success.size：${successFileList?.size}  error.size：${errorFileList?.size}", true)
                recoverListener.onDownloadFileFinish(successFileList, errorFileList)
                CloudStaticsUtil.addCloudLog(
                    TAG,
                    "downloadMetaDataFile,batchResult,successSize=${successFileList?.size},errorSize=${errorFileList?.size},errorInfo:${
                        errorFileList?.getOrNull(0)?.uploadResult?.logMessage()
                    }")
                // 检查该错误是否不会阻断同步流程，是：不阻断，接着走下一步流程； 否：阻断，同步流程结束
                if (errorFileList?.getOrNull(0)?.uploadResult?.canContinueSyncProcess(mContext) != false) {
                    if (hasMoreData) {
                        //文件全部下载成功，还有元数据需下载
                        recoveryCloudData(requestSource, recoverListener)
                    } else {
                        // 云端数据全部恢复完成
                        recoverListener.onFinish(CloudSyncResult.createSuccess())
                    }
                } else {
                    recoverListener.onFinish(CloudSyncResult.createFileError(errorFileList.getOrNull(0)?.uploadResult, false))
                }
            }

            // 单个下载结果
            override fun onIOResult(file: RecordTransferFile, cloudKitError: CloudKitError) {
                DebugUtil.i(TAG, "恢复Step2:下载单个文件结果：downloadSingleFileResult: ${cloudKitError.isSuccess} ${file.record.displayName}-\n $cloudKitError", true)
                SyncDownloadBiz.process(mContext, file, cloudKitError.isSuccess)
            }

            override fun onTransferStart() {
                recoverListener.onStartDownloadFile()
            }
        })
    }

    private fun handleDownloadGroupInfoMetaData(requestSource: CloudRecoveryRequestSource, data: List<CloudMetaDataRecord>?): Int {
        if (mSyncStop) {
            notifyGroupInfoRecoveryUpdateAnchor(false)
            return mSyncStopSubErrorCode
        }
        kotlin.runCatching {
            // 写入db
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                val handleResult = DownloadSyncUtil.onServerDataSuccessForGroupInfoRecover(mContext, requestSource, data)
                notifyGroupInfoRecoveryUpdateAnchor(handleResult)
                return if (handleResult) SyncErrorCode.RESULT_SUCCESS else mSyncStopSubErrorCode
            } else {
                notifyGroupInfoRecoveryUpdateAnchor(false)
                DebugUtil.e(TAG, "handleDownloadGroupInfoMetaData error: below Q")
                return SyncErrorCode.RESULT_FAIL
            }
        }.onFailure {
            DebugUtil.e(TAG, "handleDownloadGroupInfoMetaData error: ${it.message}")
            notifyGroupInfoRecoveryUpdateAnchor(false)
            if (it is SecurityException) {
                return SyncErrorCode.RESULT_PERMISSION_DENIED
            }
            return SyncErrorCode.RESULT_FAIL
        }
        return SyncErrorCode.RESULT_SUCCESS
    }


    private fun handleDownloadMetaData(requestSource: CloudRecoveryRequestSource, data: List<CloudMetaDataRecord>?): Int {
        if (mSyncStop) {
            notifyRecoveryUpdateAnchor(false)
            return mSyncStopSubErrorCode
        }
        try {
            // 写入db
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                val handleResult = DownloadSyncUtil.onServerDataSuccessForRecover(mContext, requestSource, data)
                notifyRecoveryUpdateAnchor(handleResult)
                return if (handleResult) SyncErrorCode.RESULT_SUCCESS else mSyncStopSubErrorCode
            } else {
                notifyRecoveryUpdateAnchor(false)
                DebugUtil.e(TAG, "handleDownloadMetaData error: below Q")
                return SyncErrorCode.RESULT_FAIL
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "onServerDataSuccessForRecover error: $e")
            notifyRecoveryUpdateAnchor(false)
            if (e is SecurityException) {
                return SyncErrorCode.RESULT_PERMISSION_DENIED
            }
            return SyncErrorCode.RESULT_FAIL
        }
        return SyncErrorCode.RESULT_SUCCESS
    }

    /**
     * 通知SDK更新锚点
     * 只要调用了startRecoveryMetaData，再回调中不管什么情况都需要调用completeRecoveryMetaData通知SDK，否则SDK内部恢复流程一直存在，不会被清理掉
     */
    private fun notifyRecoveryUpdateAnchor(success: Boolean) {
        if (success) {
            CloudSyncManager.getInstance().completeRecoveryMetaData(mRecordModule, ZONE_RECORDER, mRecorderDBUtil.localCloudDataCount.toLong())
        } else {
            // metaDataTotalCount 只有成功（isSuccess=true）才会使用
            CloudSyncManager.getInstance().completeRecoveryMetaData(mRecordModule, ZONE_RECORDER, false, CloudDataType.PRIVATE, 0)
        }
    }

    /**
     * groupInfo 同步
     * 通知SDK更新锚点
     * 只要调用了startRecoveryMetaData，再回调中不管什么情况都需要调用completeRecoveryMetaData通知SDK，否则SDK内部恢复流程一直存在，不会被清理掉
     */
    private fun notifyGroupInfoRecoveryUpdateAnchor(success: Boolean) {
        if (success) {
            val context = BaseApplication.getAppContext()
            CloudSyncManager.getInstance().completeRecoveryMetaData(mRecordModule, ZONE_GROUP,
                GroupInfoManager.getInstance(context).getLocalGroupInfoCloudDataCount().toLong())
        } else {
            // metaDataTotalCount 只有成功（isSuccess=true）才会使用
            CloudSyncManager.getInstance().completeRecoveryMetaData(mRecordModule, ZONE_GROUP, false, CloudDataType.PRIVATE, 0)
        }
    }

    private fun doCommitGroupInfoMetaDataList(
        requestSource: CloudBackupRequestSource,
        stepValue: String,
        cloudMetaDataList: MutableList<CloudMetaDataRecord>,
        backUpListener: IBackUpListener,
        runCount: Int = 0,
    ) {
        // check 条件
        val checkerResult = checkCanSyncCondition(false)
        if (!checkerResult.success()) {
            DebugUtil.e(TAG, "doCommitGroupInfoMetaDataList,checkCondition fail ${checkerResult.message()}")
            backUpListener.onFinish(stepValue, CloudSyncResult.createResult(checkerResult.resultCode, CloudSyncResult.ERROR_FROM_METADATA_BACKUP))
            return
        }
        CloudStaticsUtil.addCloudLog(TAG, "doCommitGroupInfoMetaDataList, data.size= ${cloudMetaDataList.size}")
        if (cloudMetaDataList.isNullOrEmpty()) {
            backUpListener.onFinish(stepValue, CloudSyncResult.createSuccess())
            return
        }

        DebugUtil.i(TAG, "doCommitGroupInfoMetaDataList data.size: ${cloudMetaDataList.size}", true)
        backUpListener.onStartUploadMetaData()

        CloudSyncManager.getInstance().startBackupMetaData(mRecordModule, ZONE_GROUP, requestSource, SyncDataConstants.RECORD_TYPE_VERSION,
            cloudMetaDataList, object : ICloudBackupMetaData {
                override fun onSuccess(
                    cloudDataType: CloudDataType,
                    successData: List<CloudBackupResponseRecord>,
                    errorData: List<CloudBackupResponseError>,
                ) {
                    DebugUtil.e(TAG, "doCommitGroupInfoMetaDataList onSuccess: " + successData.size
                            + ", errorData: " + errorData)
                    backUpListener.onUploadMetaDataFinish(successData, errorData)
                    CloudStaticsUtil.addCloudLog(TAG, "doCommitGroupInfoMetaDataList,onSuccess," +
                            "successSize=${successData.size},errorSize=${errorData.size}")
                    MetaDataUploadUtil.onServerAddedOrUpdateGroupInfoDbForBackup(mContext, successData)
                    if (errorData.isEmpty()) {
                        backUpListener.onFinish(stepValue, CloudSyncResult.createSuccess())
                    } else {
                        DebugUtil.e(TAG, "doCommitGroupInfoMetaDataList errorData ${errorData.size}")
                        filterBackUpRealErrorList(errorData, ZONE_GROUP).apply {
                            if ((this.first.isNotEmpty()) || (runCount > 0)) {
                                backUpListener.onFinish(stepValue, CloudSyncResult.createMetaDataError(this.first.getOrNull(0)?.cloudKitError, true))
                            } else {
                                doCommitGroupInfoMetaDataList(requestSource, stepValue, this.second.toMutableList(), backUpListener, (runCount + 1))
                            }
                        }
                    }
                }

                override fun onError(cloudDataType: CloudDataType, error: CloudKitError) {
                    DebugUtil.e(TAG, "doCommitGroupInfoMetaDataList onError ${error.errorMsg} \r\n ${cloudMetaDataList.size}")
                    backUpListener.onFinish(stepValue, CloudSyncResult.createMetaDataError(error, true))
                    CloudStaticsUtil.addCloudLog(TAG, "doCommitGroupInfoMetaDataList,onError ${error.errorMsg}")
                }
            })
    }

    /**
     * 提交录音表元数据
     * @param requestSource
     * @param stepValue
     * @param cloudMetaDataList 需备份元数据
     * @param backUpListener
     */
    private fun doCommitRecorderMetaDataList(
        requestSource: CloudBackupRequestSource,
        stepValue: String,
        cloudMetaDataList: MutableList<CloudMetaDataRecord>,
        backUpListener: IBackUpListener,
        runCount: Int = 0,
    ) {
        // check 条件
        val checkerResult = checkCanSyncCondition(false)
        if (!checkerResult.success()) {
            DebugUtil.e(TAG, "doCommitRecorderMetaDataList,checkCondition fail ${checkerResult.message()}")
            backUpListener.onFinish(stepValue, CloudSyncResult.createResult(checkerResult.resultCode, CloudSyncResult.ERROR_FROM_METADATA_BACKUP))
            return
        }
        CloudStaticsUtil.addCloudLog(TAG, "doCommitRecorderMetaDataList, data.size= ${cloudMetaDataList.size}")
        if (cloudMetaDataList.isNullOrEmpty()) {
            backUpListener.onFinish(stepValue, CloudSyncResult.createSuccess())
            return
        }

        DebugUtil.i(TAG, "doCommitRecorderMetaDataList data.size: ${cloudMetaDataList.size}", true)
        backUpListener.onStartUploadMetaData()

        CloudSyncManager.getInstance().startBackupMetaData(mRecordModule, ZONE_RECORDER, requestSource, SyncDataConstants.RECORD_TYPE_VERSION,
            cloudMetaDataList, object : ICloudBackupMetaData {
                override fun onSuccess(
                    cloudDataType: CloudDataType,
                    successData: List<CloudBackupResponseRecord>,
                    errorData: List<CloudBackupResponseError>,
                ) {
                    DebugUtil.e(TAG, "doCommitRecorderMetaDataList onSuccess " + successData.size + " errorData " + errorData)
                    backUpListener.onUploadMetaDataFinish(successData, errorData)
                    CloudStaticsUtil.addCloudLog(TAG, "doCommitRecorderMetaDataList," +
                            "onSuccess,successSize=${successData.size},errorSize=${errorData.size}")
                    if (successData.getOrNull(0)?.operatorType == SyncDataConstants.OPERATOR_TYPE_RESUME) {
                        MetaDataUploadUtil.onRecordResumeOperateForBackUp(mContext, successData, cloudMetaDataList)
                    } else {
                        MetaDataUploadUtil.onServerAddedOrUpdateForBackup(mContext, successData)
                    }
                    if (errorData.isEmpty()) {
                        backUpListener.onFinish(stepValue, CloudSyncResult.createSuccess())
                    } else {
                        filterBackUpRealErrorList(errorData, ZONE_RECORDER).apply {
                            if ((this.first.isNotEmpty()) || (runCount > 0)) {
                                backUpListener.onFinish(stepValue, CloudSyncResult.createMetaDataError(this.first.getOrNull(0)?.cloudKitError, true))
                            } else {
                                doCommitRecorderMetaDataList(requestSource, stepValue, this.second.toMutableList(), backUpListener, (runCount + 1))
                            }
                        }
                    }
                }

                override fun onError(cloudDataType: CloudDataType, error: CloudKitError) {
                    DebugUtil.e(TAG, "doCommitRecorderMetaDataList onError ${error.errorMsg} \r\n ${cloudMetaDataList.size}")
                    backUpListener.onFinish(stepValue, CloudSyncResult.createMetaDataError(error, true))
                    CloudStaticsUtil.addCloudLog(TAG, "doCommitRecorderMetaDataList,onError ${error.errorMsg}")
                }
            })
    }

    private fun genBackUpGroupInfoMetaData(dataList: List<GroupInfo>): MutableList<CloudMetaDataRecord> {
        val cloudBackupRequestList: MutableList<CloudMetaDataRecord> = ArrayList()
        var cloudMetaDataRecord: CloudMetaDataRecord

        for (groupInfo in dataList) { //循环构建sdk 待提交的 元数据对象
            val operator = getLocalRecorderOperatorType(groupInfo.getRawRecordLocalState())
            if (operator.isNullOrBlank()) {
                DebugUtil.e(TAG, "genBackUpGroupInfoMetaData is null, $groupInfo")
                continue
            }
            //构造 sdk的元数据对象
            cloudMetaDataRecord = CloudMetaDataRecord().apply {
                operatorType = operator
                DebugUtil.e(TAG, "genBackUpGroupInfoMetaData operator = $operator, $groupInfo")
                if (operator != SyncDataConstants.OPERATOR_TYPE_CREATE) {
                    sysVersion = groupInfo.sysVersion
                }
                sysRecordId = if (groupInfo.mGroupGlobalId.isNullOrEmpty()) {
                    groupInfo.mUuId
                } else {
                    groupInfo.mGroupGlobalId
                }
                /* 云服务开发建议全部使用file类型*/
                sysDataType = METADATA_TYPE_TEXT
                //业务方在开放平台定义的自己内容结构的 类型值(用于校验 fields的格式合法性)
                sysRecordType = SyncDataConstants.META_DATA_TYPE_GROUP

                //业务方元数据内容的协议版本号
                sysProtocolVersion = VERSION_DEFAULT
                //业务方的元数据内容
                CloudGroupInfoField.toMetaData(groupInfo).apply {
                    fields = mGson.toJson(this)
                    sysUniqueId = CloudSyncManager.getMD5(groupUuid + groupName)
                }
            }
            if (cloudMetaDataRecord.sysRecordId.isNullOrEmpty() || cloudMetaDataRecord.sysUniqueId.isNullOrEmpty()) {
                DebugUtil.e(TAG, "genBackUpGroupInfoMetaData param illegal $cloudMetaDataRecord", true)
            } else {
                DebugUtil.i(TAG, "genBackUpGroupInfoMetaData metaDataRecord $cloudMetaDataRecord", true)
                cloudBackupRequestList.add(cloudMetaDataRecord)
            }
        }
        return cloudBackupRequestList
    }

    private fun genBackUpMetaData(dataList: List<Record>): MutableList<CloudMetaDataRecord> {
        val cloudBackupRequestList: MutableList<CloudMetaDataRecord> = ArrayList()
        var metaDataRecord: CloudMetaDataRecord
        var fileInfoList: MutableList<CloudMetaDataFileInfo>

        for (record in dataList) { //循环构建sdk 待提交的 元数据对象
            val operator = getLocalRecorderOperatorType(record.rawRecordLocalState)
            if (operator.isNullOrBlank()) {
                DebugUtil.e(TAG, "genBackUpMetaData  is null, isRecycle=${record.isRecycle},  $record")
                if (record.isRecycle) {
                    //本地未同步云端，且进入回收站的录音文件，不参与云同步, 二期需求再做云同步
                    continue
                }
                if (record.mFileSize <= 0 && record.dateCreated <= 0) {
                    // 删除数据库中临时脏数据
                    DebugUtil.i(TAG, "delete db record with size and data is 0: $record", true)
                    mRecorderDBUtil.deleteRecordsById(record.id)
                }
                continue
            }
            //构造 sdk的元数据对象
            metaDataRecord = CloudMetaDataRecord().apply {
                operatorType = operator
                // 对于新数据，globalId同uuid相同，对于老版本云端数据，globalId是服务端生成，与本地UUID不同
                // record.globalId = null,说明是create类型，新数据：globalId同uuid相同
                sysRecordId = if (record.globalId.isNullOrEmpty()) {
                    record.uuid
                } else {
                    record.globalId
                }
                // 数据类型非创建时，传入sysVersion,标记数据类型为TEXT
                if (operator != SyncDataConstants.OPERATOR_TYPE_CREATE) {
                    sysVersion = record.sysVersion
                    // 云服务开发建议全部使用file类型
                    sysDataType = METADATA_TYPE_FILE
                } else {
                    // 无globalId,新数据
                    sysDataType = METADATA_TYPE_FILE
                    fileInfoList = arrayListOf()
                    fileInfoList.add(CloudMetaDataFileInfo(record.fileId, record.checkPayload))
                    fileInfos = fileInfoList
                }
                //业务方在开放平台定义的自己内容结构的 类型值(用于校验 fields的格式合法性)
                sysRecordType = SyncDataConstants.META_DATA_TYPE_RECORD

                //业务方元数据内容的协议版本号
                sysProtocolVersion = VERSION_DEFAULT
                //业务方的元数据内容
                CloudRecordField.toMetaData(record).apply {
                    fields = mGson.toJson(this)
                    sysUniqueId = CloudSyncManager.getMD5(this.relativePath + this.fileMD5)
                }
            }
            if (metaDataRecord.sysRecordId.isNullOrEmpty() || metaDataRecord.sysUniqueId.isNullOrEmpty()) {
                DebugUtil.e(TAG, "genBackUpMetaData param illegal $metaDataRecord", true)
            } else {
                DebugUtil.i(TAG, "genBackUpMetaData metaDataRecord $metaDataRecord", true)
                cloudBackupRequestList.add(metaDataRecord)
            }
        }
        return cloudBackupRequestList
    }

    /**
     * sdk定义的 操作取值（create、replace、deleteAndReplace、recycleAndReplace、resumeAndReplace）
     */
    private fun getLocalRecorderOperatorType(localState: Int): String? {
        when (localState) {
            // 文件被加密->从云端彻底删除
            RecordConstant.RECORD_BACKUP_STATE_SYNCDELETED -> return SyncDataConstants.OPERATOR_TYPE_DELETE
            // 新增文件 ->新增元数据
            RecordConstant.RECORD_BACKUP_STATE_ADDED -> return SyncDataConstants.OPERATOR_TYPE_CREATE
            // 本地文件标记信息改动 -> 云数据更新
            RecordConstant.RECORD_BACKUP_STATE_MODIFIED -> return SyncDataConstants.OPERATOR_TYPE_REPLACE
            // 删除已同步过元数据文件 -> 云端移动元数据到回收站
            RecordConstant.RECORD_BACKUP_STATE_DELETED -> return SyncDataConstants.OPERATOR_TYPE_RECYCLE
            RecordConstant.RECORD_BACKUP_STATE_DEFAULT -> return ""
        }
        return null
    }

    /**
     * 检查备份上传文件是否过期（对于新数据，上传了文件，但是没有上传元数据情况）
     * @param errorDataList
     * @param zone 云端zone： ZONE_RECORDER | ZONE_GROUP
     */
    private fun filterBackUpRealErrorList(
        errorDataList: List<CloudBackupResponseError>,
        zone: String
    ): Pair<List<CloudBackupResponseError>, List<CloudMetaDataRecord>> {
        val realErrorList: MutableList<CloudBackupResponseError> = ArrayList()
        val resumeOperateList: MutableList<CloudMetaDataRecord> = ArrayList()
        for (errorData in errorDataList) {
            DebugUtil.e(TAG, "backup metaData fail ${errorData.cloudKitError.subServerErrorCode}, data: $errorData")
            CloudStaticsUtil.addCloudLog(TAG, "filterBackUpRealErrorList, errorInfo=${errorData.cloudKitError.logMessage()}")
            // 对于errorData，需处理以下情况：
            // 1. errorCode = 1103, RECORDTYPE_NOTMATCH, recordtype不匹配
            // 2. errorCode = 1104, SYS_VERSION_ISNULL, sysVersion为空(deleteAndReplace/replace/recycleAndReplace/resumeAndReplace时会出现)
            // 3. errorCode = 1107, OPERATOR_TYPE_ISNULL, operator_type is null
            // 4. errorCode = 1108, FILE_UPLOAD_EXPIRED, 文件上传过期，即文件上传很久后才上传对应的元数据，此时云端文件会被删除，需重新上传文件（该过期时间由后台控制）
            // 5. errorCode = 1109, FILE_UPLOAD_NOTMATCH, 文件上传数据不匹配，上传元数据时云端解密fileCheckPayload字段拿到的ocloudId和上传的ocloudId不匹配
            // 6. errorCode = 1110, DATA_TYPE_NOTMATCH, datatype不匹配
            // 7. errorCode = 1111, FIELDS_NULL, fields信息为空
            // 8. errorCode = 1114, RECORD_FIELDS_FORMAT_ERROR, fields格式不合法
            // 9. errorCode = 1200, EXISTS, 尝试创建资源已存在(create时sysRecordId与云端数据重复)
            // 10. errorCode = 1201, NOT_FOUND, 上传数据的sysRecordId在云端不存在(deleteAndReplace/replace/recycleAndReplace/resumeAndReplace)
            // 11. errorCode = 1202, NEED_FETCH, 上传的sysVersion小于云端的数据, 需要先fetch(deleteAndReplace/replace/recycleAndReplace/resumeAndReplace)
            // 1203	UNIQUEID _ISNULL	uniqueId为空	单个	不可重试
            //   1204	UNIQUEID_EXISTS	uniqueId 已存在	单个	不可重试
            // 4、11属于需要处理的逻辑分支，其他属于需要处理的bug
            when (errorData.cloudKitError.subServerErrorCode) {
                // 1108：文件上传过期，需要清除io已经返回的ocloud信息;1109 云端解密fileCheckPayload字段拿到的ocloudId和上传的ocloudId不匹配
                SyncErrorCode.ERROR_RECORD_FILE_UPLOAD_EXPIRED, SyncErrorCode.ERROR_FILE_UPLOAD_NOTMATCH -> {
                    if (zone == ZONE_RECORDER) {
                        mRecorderDBUtil.clearExpieredFileIdByUUid(errorData.sysRecordId)
                    }
                }
                // 1200：云端存在相同sysRecordId，本地修改uuid
                SyncErrorCode.ERROR_SAME_SYS_RECORD_ID -> {
                    if (zone == ZONE_RECORDER) {
                        mRecorderDBUtil.resetUuidByUUid(errorData.sysRecordId)
                    }
                }
                SyncErrorCode.BACK_UP_ERROR_UNIQUEID_IS_NULL -> {
                    // 上传unionid 为空
                }
                SyncErrorCode.BACK_UP_ERROR_UNIQUEID_EXIST -> {
                    if (zone == ZONE_RECORDER) {
                        // 新增元数据(目前只有create会),相同名称+md5 已经再云端已经存在了
                        handleUnionIdExitErrorForRecords(errorData)?.let {
                            resumeOperateList.add(it)
                        }
                    } else if (zone == ZONE_GROUP) {
                        handleUnionIdExitErrorForGroupInfo(errorData)?.let {
                            resumeOperateList.add(it)
                        }
                    }
                }
                SyncErrorCode.ERROR_SYS_RECORD_ID_NOT_FOUND -> {
                    if (zone == ZONE_RECORDER) {
                        // 更新云端元数据，但是该数据(globalId)再云端并不存在
                        mRecorderDBUtil.getRecordByGlobalId(errorData.sysRecordId)?.let {
                            mRecorderDBUtil.clearCloudColumnByUUid(true, it.uuid)
                        }
                    } else if (zone == ZONE_GROUP) {
                        GroupInfoDbUtil.getGroupInfoByUuid(mContext, errorData.sysRecordId)?.let {
                            GroupInfoDbUtil.clearCloudColumnByUUid(mContext, it.mUuId, true)
                        }
                    }
                }
                SyncErrorCode.RECORD_FIELDS_FORMAT_ERROR,
                SyncErrorCode.ERROR_FIELDS_NULL,
                SyncErrorCode.ERROR_DATA_TYPE_NOTMATCH,
                SyncErrorCode.OPERATOR_TYPE_ISNULL,
                SyncErrorCode.SYS_VERSION_ISNULL,
                SyncErrorCode.RECORDTYPE_NOTMATCH,
                -> DebugUtil.i(TAG, "backup error bug data $errorData", true)
                else -> realErrorList.add(errorData)
            }
        }
        DebugUtil.i(TAG, "filterBackUpRealErrorList, realErrorList.size ${realErrorList.size}, resumeOperateList ${resumeOperateList.size}")
        return Pair(realErrorList, resumeOperateList)
    }

    @Suppress("TooGenericExceptionCaught")
    private fun handleUnionIdExitErrorForGroupInfo(cloudResponse: CloudBackupResponseError): CloudMetaDataRecord? {
        try {
            // 云端已存在元数据信息
            val cloudMetaData = mGson.fromJson(cloudResponse.sysRecordInfo, CloudSysRecordInfo::class.java)
            val cloudField = mGson.fromJson(cloudResponse.customRecordInfo, CloudGroupInfoField::class.java)
            // db groupInfo 信息
            val dbGroupInfoSameUuid = GroupInfoDbUtil.getGroupInfoByUuid(mContext, cloudResponse.sysRecordId)
            DebugUtil.i(TAG, "handleUnionIdExitErrorForGroupInfo, cloud data state is" +
                    " ${cloudMetaData?.sysStatus}, dbGroupInfoSameUuid $dbGroupInfoSameUuid", true)
            CloudStaticsUtil.addCloudLog(
                TAG,
                "handleUnionIdExitErrorForGroupInfo,dbGroupName=${dbGroupInfoSameUuid?.mGroupName},cloudStatus=${cloudMetaData?.sysStatus}")
            when (cloudMetaData?.sysStatus) {
                // 云端数据属于正常状态：--》本地数据需要重命名
                SyncDataConstants.SYS_STATUS_NORMAL -> {
                    dbGroupInfoSameUuid?.apply {
                        val cloudInDbGroupInfo = GroupInfoDbUtil.getGroupInfoByGlobalId(mContext, cloudMetaData.sysRecordId)
                        // 本地数据库 已经存在云端那条数据，若markData相同，或报错这条数据markData为null，则删除掉本地重复记录
                        if (cloudInDbGroupInfo != null) {
                            GroupInfoDbUtil.deleteByUUId(mContext, cloudInDbGroupInfo.mUuId)
                            DebugUtil.i(TAG, "handleUnionIdExitErrorForGroupInfo cloudInDbGroupInfo is Not null", true)
                        } else {
                            DebugUtil.i(TAG, "handleUnionIdExitErrorForGroupInfo cloudInDbGroupInfo is null", true)
                        }
                    }
                }
                // 云端数据在回收站
                SyncDataConstants.SYS_STATUS_RECYCLE -> DebugUtil.i(TAG, "handleUnionIdExitErrorForGroupInfo sys_status_recycle", true)
                // 云端数据被彻底删除
                SyncDataConstants.SYS_STATUS_DELETED -> return null
                // 返回信息为null
                else -> return null
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "")
        }
        return null
    }
    private fun handleUnionIdExitErrorForRecords(cloudResponse: CloudBackupResponseError): CloudMetaDataRecord? {
        try {
            // 云端已存在元数据信息
            val cloudMetaData = mGson.fromJson(cloudResponse.sysRecordInfo, CloudSysRecordInfo::class.java)
            val cloudField = mGson.fromJson(cloudResponse.customRecordInfo, CloudRecordField::class.java)
            // db record 信息
            val dbRecordSameUuid = mRecorderDBUtil.qureyRecordByUUid(cloudResponse.sysRecordId)
            DebugUtil.i(TAG, "handleUnionIdExitError, cloud data state is ${cloudMetaData?.sysStatus}, dbRecordSameUuid $dbRecordSameUuid", true)
            CloudStaticsUtil.addCloudLog(
                TAG,
                "handleUnionIdExitError,dbRecordName=${dbRecordSameUuid?.displayName},cloudStatus=${cloudMetaData?.sysStatus}")
            when (cloudMetaData?.sysStatus) {
                // 云端数据属于正常状态：--》本地数据需要重命名
                SyncDataConstants.SYS_STATUS_NORMAL -> {
                    dbRecordSameUuid?.apply {
                        val cloudInDbRecord = mRecorderDBUtil.getRecordByGlobalId(cloudMetaData.sysRecordId)
                        // 本地数据库 已经存在云端那条数据，若markData相同，或报错这条数据markData为null，则删除掉本地重复记录
                        if (cloudInDbRecord != null && ((this.markData == null) || (this.isSameBusinessContent(cloudInDbRecord)))) {
                            DebugUtil.i(TAG, "error record is repeat data, delete the repeat one")
                            mRecorderDBUtil.deleteRecordsById(dbRecordSameUuid.id)
                            return null
                        }
                        val uri = MediaDBUtils.getMediaUriForRecord(dbRecordSameUuid)
                        if (uri != null) {
                            val newName = PathUtil.getNewNameForSyncRecordConflict(dbRecordSameUuid)
                            // 重命名本地文件
                            val renameCount = MediaDBUtils.rename(uri, newName)
                            DebugUtil.i(TAG, "handleUnionIdExitError renameCount $renameCount", true)
                            if (renameCount > 0) {
                                mRecorderDBUtil.updateDisplayNameByRecordId(id.toString(), newName, recordType, true)
                            }
                        } else {
                            DebugUtil.i(TAG, "handleUnionIdExitError uri is null", true)
                        }
                    }
                }
                // 云端数据在回收站
                SyncDataConstants.SYS_STATUS_RECYCLE -> {
                    if (dbRecordSameUuid == null) {
                        DebugUtil.e(TAG, "handleUnionIdExitError dbRecord is null ")
                        return null
                    }
                    if (cloudField == null) {
                        DebugUtil.e(TAG, "handleUnionIdExitError cloudMeta is recycle,but cloudFild is null ")
                        return null
                    }
                    return handleResumeOperateData(dbRecordSameUuid, cloudField, cloudMetaData)
                }
                // 云端数据被彻底删除
                SyncDataConstants.SYS_STATUS_DELETED -> return null
                // 返回信息为null
                else -> return null
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "")
        }

        return null
    }

    /**
     * 1024, 上传数据再回收站存在
     */
    private fun handleResumeOperateData(dbRecord: Record, cloudField: CloudRecordField, cloudMetaData: CloudSysRecordInfo): CloudMetaDataRecord? {
        // 需要resume成功了，才能更新本地数据
        try {
            DebugUtil.i(TAG, "handleResumeOperateData,localRecord $dbRecord")
            // uuid 与云端不一致，需要更新本地与云端一致，备份成功后更新本地db
            if ((cloudField.itemId.isNullOrBlank().not()) && (dbRecord.uuid != cloudField.itemId)) {
                DebugUtil.i(TAG, "local uuid ${dbRecord.uuid}, cloud uuid ${cloudField.itemId}")
                mRecorderDBUtil.resetUuidByUUid(dbRecord.uuid, cloudField.itemId)
                dbRecord.uuid = cloudField.itemId
            }
            val result = CloudMetaDataRecord().apply {
                operatorType = SyncDataConstants.OPERATOR_TYPE_RESUME
                // 对于新数据，globalId同uuid相同，对于老版本云端数据，globalId是服务端生成，与本地UUID不同
                // record.globalId = null,说明是create类型，新数据：globalId同uuid相同
                sysRecordId = cloudMetaData.sysRecordId
                sysVersion = cloudMetaData.sysVersion
                sysDataType = cloudMetaData.sysDataType
                //业务方在开放平台定义的自己内容结构的 类型值(用于校验 fields的格式合法性)
                sysRecordType = cloudMetaData.sysRecordType

                //业务方元数据内容的协议版本号
                sysProtocolVersion = VERSION_DEFAULT
                //业务方的元数据内容
                CloudRecordField.toMetaData(dbRecord).apply {
                    fields = mGson.toJson(this)
                    sysUniqueId = CloudSyncManager.getMD5(this.relativePath + this.fileMD5)
                }
            }
            DebugUtil.i(TAG, "handleResumeOperateData $result", true)
            return result
        } catch (e: Exception) {
            DebugUtil.e(TAG, "1024Error $e")
        }
        return null
    }

    /**
     * 调用业务逻辑前校验下是否满足条件
     * 账号登录状态、同步开关、权限、本地存储空间、快稳省条件
     * @param checkLocalStorage 是否check本地存储空间大于300M，目前下载元数据or下载文件需要check
     */
    private fun checkCanSyncCondition(checkLocalStorage: Boolean): SyncCheckResult {
        var result: SyncCheckResult
        if (mSyncStop) {
            result = SyncCheckResult(mSyncStopSubErrorCode)
        } else {
            result = RecordSyncChecker.checkCloudSwitchState(mContext, false)
            if (result.success()) {
                result = RecordSyncChecker.checkSyncPreconditionsMet(mContext, checkLocalStorage)
            }
        }
        if (!result.success()) {
            CloudStaticsUtil.addCloudLog(TAG, "checkCanSyncCondition, mSyncStop=$mSyncStop, result=${result.message()}")
        }
        return result
    }
}