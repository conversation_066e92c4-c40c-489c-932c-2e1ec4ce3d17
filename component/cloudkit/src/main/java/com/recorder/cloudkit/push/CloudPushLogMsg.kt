package com.recorder.cloudkit.push

import com.soundrecorder.modulerouter.xlog.RecordLogXConfig
import java.io.Serializable

class CloudPushLogMsg : Serializable {
    companion object {
        private const val serialVersionUID = -2105056228339530007L
    }

    var action: String? = null
    var content: RecordLogXConfig? = null

    override fun toString(): String {
        return "CloudPushLogMsg(action=$action, content=$content)"
    }
}