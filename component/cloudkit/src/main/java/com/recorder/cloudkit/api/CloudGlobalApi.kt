/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: CloudGlobalApi
 * Description:
 * Version: 1.0
 * Date: 2024/11/29
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/11/29 1.0 create
 */

package com.recorder.cloudkit.api

import android.app.Activity
import androidx.appcompat.app.AlertDialog
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.recorder.cloudkit.global.CloudGlobalStateManager
import com.soundrecorder.modulerouter.cloudkit.CloudGlobalAction
import com.soundrecorder.modulerouter.cloudkit.ICloudGlobalStateCallBack

@Component(CloudGlobalAction.COMPONENT_NAME)
object CloudGlobalApi {
    /**
     * 若未获取一体化配置信息则去获取一次：
     * 初始化时机：
     * 1.应用启动
     * 2.账号登录成功
     * 3.录音后台切前台--无登录成功广播，所以通过onResume
     */
    @Action(CloudGlobalAction.ACTION_INIT_GLOBAL_STATE)
    @JvmStatic
    fun initCloudGlobalState(callback: ICloudGlobalStateCallBack? = null) {
        if (!CloudGlobalStateManager.isInitSuccess()) {
            getCloudGlobalState(false, callback)
        }
    }

    @Action(CloudGlobalAction.ACTION_JUDGE_OCLOUD_GLOBAL_STATE)
    @JvmStatic
    fun getCloudGlobalState(showErrorTip: Boolean, callback: ICloudGlobalStateCallBack?) {
        CloudGlobalStateManager.judgeOCloudGlobalState(showErrorTip, callback)
    }

    @Action(CloudGlobalAction.ACTION_REG_GLOBAL_STATE_CALLBACK)
    @JvmStatic
    fun registerGlobalStateCallback(callback: ICloudGlobalStateCallBack) {
        CloudGlobalStateManager.registerStateChangeCallBack(callback)
    }

    @Action(CloudGlobalAction.ACTION_UNREG_GLOBAL_STATE_CALLBACK)
    @JvmStatic
    fun unregisterGlobalStateCallback(callback: ICloudGlobalStateCallBack) {
        CloudGlobalStateManager.unRegisterStateChangeCallBack(callback)
    }

    @Action(CloudGlobalAction.ACTION_SHOW_GLOBAL_DISABLE_DIALOG)
    @JvmStatic
    fun showGlobalDisableDialog(activity: Activity?, state: String?, buttonListener: (() -> Unit)?): AlertDialog? {
        return CloudGlobalStateManager.showErrorDialog(activity, state, buttonListener)
    }

    @Action(CloudGlobalAction.ACTION_SHOW_GLOBAL_LOADING_DIALOG)
    @JvmStatic
    fun showGlobalLoadingDialog(activity: Activity?): AlertDialog? {
        return CloudGlobalStateManager.showLoadingDialog(activity)
    }
}