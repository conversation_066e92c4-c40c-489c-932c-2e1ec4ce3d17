/***********************************************************
 * Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File: FileUpLoadUtil
 * Description:
 * Version: 1.0
 * Date : 2019-07-11
 * Author: huang<PERSON>wang
 *
 * v1.0, 2019-3-12, huang<PERSON><PERSON>, create
 */
package com.recorder.cloudkit.sync.backup

import android.content.Context
import android.net.Uri
import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.soundrecorder.common.databean.Record
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.base.utils.MD5Utils
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.db.RecorderDBUtil
import com.recorder.cloudkit.sync.SyncDataConstants
import com.recorder.cloudkit.sync.bean.RecordTransferFile
import com.soundrecorder.common.db.MediaDBUtils

object FileUpLoadUtil {
    private const val TAG = "FileUpLoadUtil"

    // 备份每批次支持文件最大大小、总数量
    private const val MAX_FILE_SIZE = 50 * 1024 * 1024L
    private const val MAX_FILE_COUNT = 100
    private const val MB = (1024 * 1024).toFloat()
    private const val PRIORITY_STEP1 = 70
    private const val PRIORITY_STEP2 = 60
    private const val PRIORITY_STEP3 = 50
    private const val PRIORITY_STEP4 = 40
    private const val PRIORITY_STEP5 = 30
    private const val PRIORITY_STEP6 = 20

    // CloudKit 4M为临界点，4M下小文件上传，大于4M为大文件断点上传
    private const val FILE_SIZE_STEP1 = 4
    private const val FILE_SIZE_STEP2 = 20
    private const val FILE_SIZE_STEP3 = 50
    private const val FILE_SIZE_STEP4 = 100
    private const val FILE_SIZE_STEP5 = 500

    @JvmStatic
    fun getUploadFileList(
        context: Context,
        maxsize: Long = MAX_FILE_SIZE,
        maxNumber: Int = MAX_FILE_COUNT,
    ): List<RecordTransferFile> {
        val upLoadFileList = ArrayList<RecordTransferFile>()
        var totalSize: Long = 0
        var fileCount = 0
        var mPriority = -1
        try {
            var cloudTransferFile: RecordTransferFile
            val recordList: List<Record> = RecorderDBUtil.getInstance(context).dirtyDataForBatchUploadFile
            DebugUtil.d(TAG, "getDirtyDataForBatchUploadFile size:" + recordList.size, true)
            CloudStaticsUtil.addCloudLog(TAG, "getUploadFileList, size=${recordList.size}")
            if (recordList.isNullOrEmpty().not()) {
                for (record in recordList) {
                    val size = record.fileSize
                    val uri: Uri? = MediaDBUtils.getMediaUriForRecord(record)
                    DebugUtil.i(TAG, "getMediaUriForRecord  uri : $uri, record : $record", true)
                    if (uri == null) {
                        continue
                    }
                    val canStart = record.canStartSync()
                    val canUpload = record.fileExist()
                    if (size <= 0 || !canStart || !canUpload) {
                        DebugUtil.v(TAG, "getUploadFileList-size:" + size
                                + ", canStart:" + canStart + ", needUpload:" + canUpload, true)
                        continue
                    }
                    // 本地数据库的md5可能是null，cloudkit上传文件强制需要md5
                    record.checkMd5()
                    var realMD5 = record.mD5
                    val prority = getPriorityBySize(size)
                    val realFileSize: Long = FileUtils.getFileSize(uri)
                    DebugUtil.i(TAG,
                        " getUploadFileList name: " + FileUtils.getDisplayNameByPath(record.data)
                            .toString() + ", md5 from record: " + realMD5.toString() + ", realFileSize: " + realFileSize.toString() + ", size from record: " + size,
                        true)
                    if (realFileSize != size) {
                        realMD5 = MD5Utils.getMD5(uri)
                        DebugUtil.i(TAG, "getUploadFileList updateFileSizeOrMd5: realMD5: $realMD5", true)
                        record.mD5 = realMD5
                        record.fileSize = realFileSize
                        RecorderDBUtil.getInstance(context).updateRealFileSizeOrMd5(record, realFileSize, realMD5)
                    }
                    val ioFile =
                        CloudIOFile.createUploadFile(SyncDataConstants.MODULE_RECORDER, SyncDataConstants.ZONE_RECORDER, uri.toString(), realMD5)
                    ioFile.recordId = if (record.globalId.isNullOrEmpty()) {
                        record.uuid
                    } else {
                        record.globalId
                    }
                    if (mPriority == -1) {
                        mPriority = prority
                    }
                    totalSize += realFileSize
                    fileCount++
                    if (totalSize < maxsize && fileCount < maxNumber && prority == mPriority) {
                        cloudTransferFile = RecordTransferFile(record, ioFile)
                        upLoadFileList.add(cloudTransferFile)
                    } else if (fileCount <= 1) {
                        cloudTransferFile = RecordTransferFile(record, ioFile)
                        upLoadFileList.add(cloudTransferFile)
                    } else {
                        return upLoadFileList
                    }
                }
                return upLoadFileList
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getUploadFileList-e:$e")
            e.printStackTrace()
        }
        return upLoadFileList
    }


    /**
     * 1. size divide priority，0-4MB，4-20MB，20-50MB，50-100MB，100MB-500MB，500MB 。
     * 2. priority same the order will changed by the time 。
     */
    @JvmStatic
    fun getPriorityBySize(size: Long): Int {
        val sizeMb = size / MB
        var priority = 0
        if (sizeMb > 0 && sizeMb < FILE_SIZE_STEP1) {
            priority = PRIORITY_STEP1
        } else if (sizeMb >= FILE_SIZE_STEP1 && sizeMb < FILE_SIZE_STEP2) {
            priority = PRIORITY_STEP2
        } else if (sizeMb >= FILE_SIZE_STEP2 && sizeMb < FILE_SIZE_STEP3) {
            priority = PRIORITY_STEP3
        } else if (sizeMb >= FILE_SIZE_STEP3 && sizeMb < FILE_SIZE_STEP4) {
            priority = PRIORITY_STEP4
        } else if (sizeMb >= FILE_SIZE_STEP4 && sizeMb < FILE_SIZE_STEP5) {
            priority = PRIORITY_STEP5
        } else if (sizeMb >= FILE_SIZE_STEP5) {
            priority = PRIORITY_STEP6
        }
        return priority
    }
}