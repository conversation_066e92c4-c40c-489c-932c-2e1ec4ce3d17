package com.recorder.cloudkit.push;

import android.content.Context;

import com.google.gson.Gson;
import com.heytap.cloudkit.libcommon.log.CloudKitLogUtil;
import com.heytap.cloudkit.libsync.ext.CloudSyncManager;
import com.heytap.cloudkit.libsync.push.CloudPushMessage;
import com.heytap.msp.push.mode.DataMessage;
import com.heytap.msp.push.service.DataMessageCallbackService;
import com.oplus.recorderlog.log.RecorderLogger;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil;
import com.soundrecorder.modulerouter.xlog.RecordLogXConfig;
import com.recorder.cloudkit.SyncTriggerManager;
import com.recorder.cloudkit.tipstatus.TipStatusManager;

/**
 * 云同步push message
 * （兼容Q版本，继承DataMessageCallbackService）
 */
public class CloudPushService extends DataMessageCallbackService {
    private static final String TAG = "CloudPushService";

    @Override
    public void processMessage(Context context, DataMessage dataMessage) {
        super.processMessage(context, dataMessage);
        boolean netWorkGranted = com.recorder.cloudkit.utils.CloudPermissionUtils.isNetWorkNoticeGranted(context);
        DebugUtil.e(TAG, "processMessage " + dataMessage.toString() + ", cloudSwitch: "
                + TipStatusManager.getSyncSwitch() + ", netWorkGranted = " + netWorkGranted);
        if (!netWorkGranted) {
            return;
        }
        String content = dataMessage.getContent();

        //识别CloudKit日志打捞的push
        if (content.contains("\"action\":\"" + CloudPushMessage.ACTION_LOG_UPLOAD)) {
            DebugUtil.d(TAG, "processPushMessage content：" + content);
            handleUploadCloudKitLogMessage(context, content);
            return;
        }
        // 云服务 push
        boolean isCloudKitMessage = CloudSyncManager.isCloudPushMessage(content);
        if (isCloudKitMessage) {
            CloudPushMessage message = CloudSyncManager.parsePushMessage(content);
            if (null == message) {
                return;
            }
            // 如果云服务开关关闭状态下，收到云服务相关的push消息，不处理云同步业务的相关消息
            if (!TipStatusManager.isCloudOn()) {
                DebugUtil.i(TAG, "cloudSwitch state close, no process cloud push msg");
                return;
            }
            DebugUtil.i(TAG, "message type == " + message.getHandleType(), true);
            switch (message.getHandleType()) {
                case SYNC:
                    handleSyncMessage(context);
                    break;
                case FULL_SYNC:
                    handleFullSyncMessage(context);
                    break;
                default:
                    DebugUtil.e(TAG, "un handle message type " + message.getHandleType());
                    break;
            }
        }
    }

    /**
     * 云端数据有改动
     *
     * @param context
     */
    private void handleSyncMessage(Context context) {
        //  停止正在同步的流程，可能正在下载内容，这时候可能收到的是删除云端数据的操作
        SyncTriggerManager.getInstance(context).trigStopSyncForErrorCode(0);
        SyncTriggerManager.getInstance(context).trigRecoveryNow(SyncTriggerManager.RECOVERY_FROM_PUSH);
        CloudStaticsUtil.addCloudLog(TAG, "receivePushMessage,handleSyncMessage");
    }

    /**
     * 业务上传totalCount同云端totalCount不一致，需触发一次云端全量
     *
     * @param context
     */
    private void handleFullSyncMessage(Context context) {
        SyncTriggerManager.getInstance(context).trigStopSyncForClearAnchor();
        SyncTriggerManager.getInstance(context).trigRecoveryNow(SyncTriggerManager.RECOVERY_FROM_FULL_PUSH);
        CloudStaticsUtil.addCloudLog(TAG, "receivePushMessage,handleFullSyncMessage");
    }

    /**
     * 识别日志打捞的push ，并且调用sdk主动上报接口CloudKitLogUtil.pushReport(context)，由于公司的日志打捞平台push打捞设计限制是能报到 cloudkit sdk空间，没法报到业务方的日志空间（业务方实现）
     *
     * @param context
     * @param messageContent
     */
    private void handleUploadCloudKitLogMessage(Context context, String messageContent) {
        CloudPushLogMsg content = null;
        try {
            Gson gson = new Gson();
            content = gson.fromJson(messageContent, CloudPushLogMsg.class);
        } catch (Exception e) {
            DebugUtil.e(TAG, "handleUploadCloudKitLogMessage gson parce exception", e);
        }
        //获取当前主module的packagename
        String packageName = context.getPackageName();
        if (content != null) {
            RecordLogXConfig recordLogXConfig = content.getContent();
            if (recordLogXConfig != null) {
                String tracePkg = recordLogXConfig.getTracePkg();
                if (CloudKitLogUtil.getCloudKitLogPkg().equals(tracePkg)) {
                    SyncTriggerManager.getInstance(context.getApplicationContext()).scheduleWorkerJob(() -> CloudKitLogUtil.pushReport(BaseApplication.getAppContext()), 0);
                } else if (packageName.equalsIgnoreCase(tracePkg)) {
                    SyncTriggerManager.getInstance(context.getApplicationContext()).scheduleWorkerJobWithoutCheckCloud(() -> RecorderLogger.INSTANCE.processPushLog(BaseApplication.getAppContext(), recordLogXConfig), 0);
                }
            } else {
                DebugUtil.w(TAG, "handleUploadCloudKitLogMessage recordLogXConfig is null");
            }
        } else {
            DebugUtil.w(TAG, "handleUploadCloudKitLogMessage content is null");
        }
    }
}
