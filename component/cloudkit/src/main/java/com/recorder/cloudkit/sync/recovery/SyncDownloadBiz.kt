package com.recorder.cloudkit.sync.recovery

import android.content.ContentResolver
import android.content.ContentValues
import android.content.Context
import android.media.MediaScannerConnection
import android.net.Uri
import android.provider.MediaStore
import android.text.TextUtils
import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.recorder.cloudkit.sync.RecordSyncChecker
import com.recorder.cloudkit.sync.bean.RecordTransferFile
import com.recorder.cloudkit.utils.PathUtil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.base.utils.MediaDataScanner
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.constant.RecordConstant.MIMETYPE_AMR_WB
import com.soundrecorder.common.constant.RecordConstant.MIMETYPE_RAW
import com.soundrecorder.common.constant.RecordConstant.POSTFIX_POINT_AMR
import com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_RECOVERY_FILE_FAILED
import com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_RECOVERY_FILE_JUMP
import com.soundrecorder.common.constant.RecordConstant.SYNC_STATUS_RECOVERY_FILE_SUC
import com.soundrecorder.common.db.CloudSyncRecorderDbUtil
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.utils.FileDealUtil
import com.soundrecorder.common.utils.RecordFileChangeNotify
import com.soundrecorder.common.utils.RecordModeUtil
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import java.io.File

object SyncDownloadBiz {

    const val TAG = "SyncDownloadBiz"
    private val mFileChangeNotify = RecordFileChangeNotify()

    fun process(context: Context, recordCloud: RecordTransferFile, success: Boolean): Boolean {
        DebugUtil.i(TAG, "process: $recordCloud, success $success", true)
        if (success && RecordSyncChecker.checkPermission(context).success().not()) {
            return doNotFileAccess(context, recordCloud = recordCloud.cloudIOFile)
        }
        if (success && recordCloud.cloudIOFile.cacheUri.isNullOrBlank().not()) {
            return doSuccess(context, recordCloud, true)
        }
        return doFail(context, recordCloud)
    }

    fun doNotFileAccess(context: Context, recordCloud: CloudIOFile): Boolean {
        DebugUtil.i(TAG, "doNotFileAccess recordCloud: ${FileUtils.getDisplayNameByPath(recordCloud.filePath)}", true)
        val recorderDBUtil = RecorderDBUtil.getInstance(context)
        if (TextUtils.isEmpty(recordCloud.cloudId)) {
            DebugUtil.i(TAG, "processDownloadResultNoFileAccess: fileId is empty", true)
            return false
        }
        val recordList = recorderDBUtil.getRecordByFileIdForDownlaodResult(recordCloud.cloudId)
        if (recordList != null && recordList.isNotEmpty()) {
            val recordDb = recordList[0]
            if (recordDb != null) {
                recorderDBUtil.updateDownloadStateByUUid(recordDb.uuid, SYNC_STATUS_RECOVERY_FILE_JUMP)
                return true
            }
        } else {
            DebugUtil.i(TAG, "processDownloadResultNoFileAccess: find no matched record : " + recordCloud.cloudId, true)
        }
        return false
    }

    private fun doSuccess(context: Context, recordCloud: RecordTransferFile, scanMedia: Boolean): Boolean {
        DebugUtil.i(TAG, "doSuccess recordCloud: ${FileUtils.getDisplayNameByPath(recordCloud.cloudIOFile.filePath)}, size ${recordCloud.record.fileSize}, md5: ${recordCloud.record.mD5}, true")
        val recorderDBUtil = RecorderDBUtil.getInstance(context)
        if (TextUtils.isEmpty(recordCloud.cloudId)) {
            DebugUtil.e(TAG, "processDownloadResultSuc: fileId is empty")
            return false
        }
        if (TextUtils.isEmpty(recordCloud.cloudIOFile.cacheUri)) {
            doFail(context, recordCloud)
            return false
        }
        try {
            val records = recorderDBUtil.getRecordByFileIdForDownlaodResult(recordCloud.cloudId)
            CloudStaticsUtil.addCloudLog(TAG, "doSuccess,name=${recordCloud.record.displayName},recordsInDbSize=${records.size}")
            DebugUtil.i(TAG, "getRecordByFileIdForDownloadResult: " + records?.size, true)
            if ((records != null) && records.isNotEmpty()) {
                for (recordDB in records) {
                    if (recordDB != null) {
                        DebugUtil.i(TAG, "record in db name: ${recordDB.displayName}, size: ${recordDB.fileSize}, md5 is ${recordDB.mD5}", true)
                        // db状态已经更新为下载文件成功了，不用重复处理了，再媒体库扫描不及时，checkFileExist会失败
                        if (recordDB.syncDownlodStatus == SYNC_STATUS_RECOVERY_FILE_SUC) {
                            DebugUtil.i(TAG, "record in db syncDownloadStatus has changed, no need to copy from cloud ", true)
                            continue
                        }
                        if (recordDB.fileExistAndCheckSize() && recordDB.mD5.equals(recordCloud.cloudIOFile.md5, ignoreCase = true)) {
                            DebugUtil.i(TAG, "record in db already has file, no need to copy from cloud ", true)
                            recorderDBUtil.updateDownloadStateByUUid(recordDB.uuid, SYNC_STATUS_RECOVERY_FILE_SUC)
                            continue
                        }
                        //android P is not supported sync because cloud services update with system version
                        //android Q+
                        //1..check file exists
                        //2.rename or not
                        //3.insert media db
                        //4.copy file
                        //5.update sync state
                        //6.check rename update recorder db
                        //7.modify record file last modify time
                        //8.scan media db
                        //9.delete cloud cache file
                        var displayName = recordDB.displayName
                        val isFileExist = FileUtils.isFileExist(recordDB.relativePath, displayName)
                        var fileBeingRecorded = ""
                        if (RecorderViewModelAction.getFileBeingRecorded() != null) {
                            fileBeingRecorded = RecorderViewModelAction.getFileBeingRecorded() as String
                        }
                        if (isFileExist || (PathUtil.getFilePath(recordDB.relativePath, displayName) == fileBeingRecorded)) {
                            // 在相同命名但又是不一样的录音文件进行云同步时，重命名修改，会在默认命名上加录音的创建时间，空格隔开

                            displayName = PathUtil.getNewNameForSyncRecordConflict(recordDB)
                            DebugUtil.i(TAG, "recordDB displayName : ${recordDB.displayName} ----> new displayName : $displayName", true)
                        }
                        DebugUtil.i(TAG, "${recordDB.concatRelativePath} isFileExist : $isFileExist", true)
                        val destValues = getContentValues(recordDB.relativePath,
                            displayName,
                            recordDB.mimeType,
                            recordDB.dateModied)
                        //判断一下文件名称后缀和mimetype是否匹配，如果不匹配，重置一下data字段，避免因文件名称和元数据displayName不一致产生新的记录
                        val isFileExtMatched = FileDealUtil.isDisplayNameExtMatchWithMimeType(displayName, recordDB.mMimeType)
                        if (!isFileExtMatched) {
                            val destFilePath = PathUtil.getFilePath(recordDB.relativePath, displayName)
                            destValues.put(MediaStore.Audio.Media.DATA, destFilePath)
                            DebugUtil.i(TAG,  "mimeType not match the file ext, force to set path = $destFilePath", true)
                        }
                        val destMediaUri = MediaDBUtils.genUri(destValues)
                            ?: continue
                        val copyResult =
                            FileUtils.copyFile(context, Uri.parse(recordCloud.cloudIOFile.cacheUri), destMediaUri)
                        DebugUtil.i(TAG,  "destMediaUri : $destMediaUri" + ", srcUri: " + recordCloud.cloudIOFile.cacheUri + "copyResult : $copyResult", true)
                        val inputSyncState =
                            if (copyResult) SYNC_STATUS_RECOVERY_FILE_SUC else SYNC_STATUS_RECOVERY_FILE_FAILED
                        recorderDBUtil.updateDownloadStateByUUid(recordDB.uuid, inputSyncState)
                        CloudStaticsUtil.addCloudLog(TAG, "doSuccess,copyResult=$copyResult, displayName=$displayName")
                        if (copyResult) {
                            val destFilePath = PathUtil.getFilePath(recordDB.relativePath, displayName)
                            if (isFileExist) {
                                CloudSyncRecorderDbUtil.updateDisplayNameForDownloadRename(recordDB.id,
                                    destFilePath,
                                    true)
                            }
                            val modifySuc = FileUtils.modifyFileLastModifyTimeByShell(destFilePath, recordDB.dateModied)
                            DebugUtil.i(TAG, "modifyFileLastModifyTime to ${recordDB.dateModied} : $modifySuc", true)
                            if (scanMedia) {
                                MediaDataScanner.getInstance().mediaScanWithCallback(context,
                                    arrayOf(destFilePath),
                                    MediaScannerConnection.OnScanCompletedListener { _: String?, _: Uri? ->
                                        if (mFileChangeNotify != null) {
                                            val type = RecordModeUtil.getRecordTypeForMediaRecord(recordDB)
                                            mFileChangeNotify.setFileChanged(type)
                                            mFileChangeNotify.notifyBySendBroadcast(BaseApplication.getAppContext())
                                        }
                                    })
                            }
                        }
                    } else {
                        DebugUtil.i(TAG, "msg in db is null", true)
                    }
                }
                deleteCloudCacheFile(context, recordCloud)
                return true
            } else {
                DebugUtil.i(TAG, "processDownloadResult: find no matched record : ${recordCloud.cloudId}", true)
                return false
            }
        } catch (e: Throwable) {
            DebugUtil.e(TAG, "doSuccess failed", e)
            return false
        }
    }

    private fun getContentValues(
        relativePath: String,
        displayName: String,
        mimeType: String,
        modified: Long,
    ): ContentValues {
        val value = ContentValues()
        value.put(MediaStore.Audio.Media.RELATIVE_PATH, relativePath)
        value.put(MediaStore.Audio.Media.DISPLAY_NAME, displayName)
        value.put(MediaStore.Audio.Media.MIME_TYPE, mimeType)
        if (!TextUtils.isEmpty(displayName) && !TextUtils.isEmpty(mimeType)) {
            val lastIndex = displayName.lastIndexOf(".")
            if ((lastIndex > 0) && (displayName.substring(lastIndex) == POSTFIX_POINT_AMR) && MIMETYPE_AMR_WB == mimeType) {
                value.put(MediaStore.Audio.Media.MIME_TYPE, MIMETYPE_RAW)
            }
        }
        value.put(MediaStore.Audio.Media.DATE_MODIFIED, modified)
        return value
    }

    /**
     * 删除云端下载下来的file
     */
    private fun deleteCloudCacheFile(context: Context, recordCloud: RecordTransferFile) {
        /*file:/storage/emulated/0/Android/data/com.coloros.soundrecorder/cache/cloudkit/file/recorderTest/38c9ef65c3440382c881189fe5bd9fed*/
        recordCloud.cacheUri?.let {
            runCatching {
                var deleted = false
                if (it.startsWith(ContentResolver.SCHEME_FILE, true)) {
                    Uri.parse(recordCloud.cloudIOFile.cacheUri)?.path?.let {
                        val file = File(it)
                        deleted = file.deleteRecursively()
                    }
                } else if (it.startsWith(ContentResolver.SCHEME_CONTENT, true)) {
                    var deleteCount = -1
                    try {
                        deleteCount = context.contentResolver.delete(
                            Uri.parse(recordCloud.cacheUri), null, null)
                    } catch (e: SecurityException) {
                        DebugUtil.e(TAG, "deleteCloudCacheFile error.", e)
                    }
                    deleted = deleteCount > 0
                }
                DebugUtil.i(TAG, "deleted uri: ${recordCloud.cacheUri}, deleted suc : $deleted", true)
            }
        }
    }

    private fun doFail(context: Context, recordCloud: RecordTransferFile): Boolean {
        DebugUtil.i(TAG, "doFail recordCloud: ${FileUtils.getDisplayNameByPath(recordCloud.cloudIOFile.filePath)}", true)
        val recorderDBUtil = RecorderDBUtil.getInstance(context)
        if (TextUtils.isEmpty(recordCloud.cloudId)) {
            DebugUtil.i(TAG, "processDownloadResultFailed: fileId is empty", true)
            return false
        }
        val recordList = recorderDBUtil.getRecordByFileIdForDownlaodResult(recordCloud.cloudId)
        if (recordList != null && recordList.isNotEmpty()) {
            val recordDb = recordList[0]
            if (recordDb != null) {
                recorderDBUtil.updateDownloadStateByUUid(recordDb.uuid, SYNC_STATUS_RECOVERY_FILE_FAILED)
                return true
            }
        } else {
            DebugUtil.i(TAG, "processDownloadResultFailed: find no matched record : " + recordCloud.cloudId, true)
        }
        return false
    }
}