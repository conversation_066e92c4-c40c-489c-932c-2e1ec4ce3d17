package com.recorder.cloudkit.account

import android.content.Context
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.os.Build
import android.text.TextUtils
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.cloudswitch.config.CloudKitSwitchConfig
import com.heytap.usercenter.accountsdk.AccountAgent
import com.heytap.usercenter.accountsdk.model.AccountEntity
import com.recorder.cloudkit.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.base.utils.PrefUtil
import com.recorder.cloudkit.shadows.ShadowFeatureOption
import com.recorder.cloudkit.sync.CloudSynStateHelper
import com.recorder.cloudkit.tipstatus.TipStatusManager
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.anyBoolean
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.mockito.invocation.InvocationOnMock
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config


/*
File:AccountCheckHelperTest
Description:
Version:
Date:2022/6/21
Author:W9013204

------------------Revision History------------------
<author> <date> <version> <desc>
*/

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class, ShadowCOUIVersionUtil::class])
class AccountCheckHelperTest {
    private val userId = "*********"
    private var mContext: Context? = null
    private var mTextUtilsMockedStatic: MockedStatic<TextUtils>? = null


    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mTextUtilsMockedStatic = Mockito.mockStatic(TextUtils::class.java)
        mTextUtilsMockedStatic?.`when`<Any> { TextUtils.isEmpty(ArgumentMatchers.any()) }?.thenAnswer { invocation: InvocationOnMock ->
            val a = invocation.getArgument<CharSequence>(0)
            if (a == null || a.length == 0) {
                return@thenAnswer true
            }
            false
        }
    }

    @After
    fun release() {
        mTextUtilsMockedStatic?.close()
        mTextUtilsMockedStatic = null
        mContext = null
    }

    @Test
    fun verify_value_when_checkAccount() {
        val accountEntity = AccountEntity()
        accountEntity.accountName = "aeaa"
        accountEntity.authToken = "dasdasdas"
        accountEntity.ssoid = "*********"
        accountEntity.deviceId = "dasdasdasdaf"

        Whitebox.setInternalState(Build.VERSION::class.java, "SDK_INT", Build.VERSION_CODES.Q)
        val tipStatusManagerMock = Mockito.mockStatic(TipStatusManager::class.java)
        tipStatusManagerMock.`when`<Boolean> { TipStatusManager.isCloudOn() }.thenReturn(true)
        val accountEntityMock = Mockito.mockStatic(AccountAgent::class.java)
        accountEntityMock.`when`<AccountEntity> { AccountAgent.getAccountEntity(Mockito.any(Context::class.java), Mockito.anyString()) }
            .thenReturn(accountEntity)

        //模拟lastLoginId不为空串且 lastLoginId != 新id
        AccountPref.setAccountUId(mContext, userId)
        AccountCheckHelper.getInstance().checkAccount(mContext!!)

        //模拟其他情况
        //true false false
        AccountPref.setAccountUId(mContext, "")
        accountEntity.ssoid = "*********"
        PrefUtil.putBoolean(mContext, "account_tag", true)
        AccountCheckHelper.getInstance().checkAccount(mContext!!)

        //true false true
        AccountPref.setAccountUId(mContext, "")
        accountEntity.ssoid = "*********"
        PrefUtil.putBoolean(mContext, "account_tag", false)
        AccountCheckHelper.getInstance().checkAccount(mContext!!)

        //true true false
        AccountPref.setAccountUId(mContext, "")
        accountEntity.ssoid = ""
        PrefUtil.putBoolean(mContext, "account_tag", true)
        AccountCheckHelper.getInstance().checkAccount(mContext!!)

        try {
            //true true true
            AccountPref.setAccountUId(mContext, "")
            accountEntity.ssoid = ""
            PrefUtil.putBoolean(mContext, "account_tag", false)
            AccountCheckHelper.getInstance().checkAccount(mContext!!)

            //模拟执行 handleRecordUpgrade 方法
            Whitebox.setInternalState(Build.VERSION::class.java, "SDK_INT", Build.VERSION_CODES.S)
            AccountCheckHelper.getInstance().checkAccount(mContext!!)

        } catch (e: Exception) {

        }
        val cloudStatusHelperMockStatic = Mockito.mockStatic(CloudSynStateHelper::class.java)
        cloudStatusHelperMockStatic.`when`<CloudKitError> { CloudSynStateHelper.setSyncSwitch(anyInt(), anyBoolean()) }
            .thenReturn(CloudKitError.createSuccess())
        //false true true
        AccountPref.setAccountUId(mContext, userId)
        accountEntity.ssoid = ""
        PrefUtil.putBoolean(mContext, "account_tag", false)
        AccountCheckHelper.getInstance().checkAccount(mContext!!)

        //false true false
        AccountPref.setAccountUId(mContext, userId)
        accountEntity.ssoid = ""
        PrefUtil.putBoolean(mContext, "account_tag", true)
        AccountCheckHelper.getInstance().checkAccount(mContext!!)

        //false false true
        AccountPref.setAccountUId(mContext, userId)
        accountEntity.ssoid = userId
        PrefUtil.putBoolean(mContext, "account_tag", false)
        AccountCheckHelper.getInstance().checkAccount(mContext!!)

        //false false false
        AccountPref.setAccountUId(mContext, userId)
        accountEntity.ssoid = userId
        PrefUtil.putBoolean(mContext, "account_tag", true)
        AccountCheckHelper.getInstance().checkAccount(mContext!!)

        accountEntityMock.reset()
        accountEntityMock.close()
        tipStatusManagerMock.reset()
        tipStatusManagerMock.close()
        cloudStatusHelperMockStatic.close()
    }

    @Test
    fun verify_value_when_handleRecordUpgrade() {
        val mockStatic = Mockito.mockStatic(CloudKitSwitchConfig::class.java)
        mockStatic.`when`<String> { CloudKitSwitchConfig.getSyncSwitchName() }.thenReturn("cloud_sync_switch_$userId")
        Whitebox.invokeMethod<Void>(AccountCheckHelper.getInstance(), "handleRecordUpgrade", mContext!!)
        Assert.assertTrue(AccountCheckHelper.sShowLoginPage)

        val packageInfo = PackageInfo().apply {
            longVersionCode = AccountCheckHelper.ACCOUNT_MIN_VERSION_CODE.toLong() + 1
        }
        val context = Mockito.mock(Context::class.java)
        val packageManager = Mockito.mock(PackageManager::class.java)

        Mockito.`when`(context.packageManager).thenReturn(packageManager)
//        Mockito.`when`(packageManager.getPackageInfo(Mockito.anyString(), Mockito.anyInt())).thenReturn(packageInfo)
        Mockito.`when`(packageManager.getPackageInfo(AccountCheckHelper.ACCOUNT_PKG_OPPO_R, PackageManager.GET_META_DATA)).thenReturn(packageInfo)

        Whitebox.invokeMethod<Void>(AccountCheckHelper.getInstance(), "handleRecordUpgrade", context)
        Assert.assertTrue(AccountCheckHelper.sShowLoginPage)

        Mockito.`when`(packageManager.getPackageInfo(AccountCheckHelper.ACCOUNT_PKG_OPPO_S, PackageManager.GET_META_DATA)).thenReturn(packageInfo)
        Assert.assertTrue(AccountCheckHelper.sShowLoginPage)


        mockStatic.reset()
        mockStatic.close()
    }
}