package com.recorder.cloudkit

import android.content.Context
import android.os.Build
import android.os.Handler
import android.os.HandlerThread
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.heytap.cloudkit.libsync.metadata.helper.CloudRecoveryRequestSource
import com.recorder.cloudkit.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.base.BaseApplication
import com.recorder.cloudkit.shadows.ShadowFeatureOption
import com.recorder.cloudkit.sync.CloudSynStateHelper
import org.junit.*
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class, ShadowCOUIVersionUtil::class])
class SyncTriggerManagerTest {
    companion object {
        // 仅备份
        const val BACKUP = 2

        // 恢复流程 + 备份
        const val RECOVERY = 1
    }


    private var mContext: Context? = null
    private var mMockApplication: MockedStatic<BaseApplication>? = null
    private var mMockCloudHelper: MockedStatic<CloudSynStateHelper>? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mMockApplication = Mockito.mockStatic(BaseApplication::class.java)
        mMockApplication!!.`when`<Any> { BaseApplication.getAppContext() }?.thenReturn(mContext)
        mMockCloudHelper = Mockito.mockStatic(CloudSynStateHelper::class.java)
        mMockCloudHelper!!.`when`<Boolean> { CloudSynStateHelper.isRegionCloudSupport() }.thenReturn(true)
    }

    @After
    fun release() {
        mMockApplication?.close()
        mMockApplication = null
        mMockCloudHelper?.close()
        mMockCloudHelper = null
        mContext = null
    }


    @Test
    fun should_when_init_release() {
        val triggerManager = SyncTriggerManager.getInstance(mContext!!)
        val sInstance = Whitebox.getInternalState<SyncTriggerManager>(SyncTriggerManager::class.java, "sInstance")
        Assert.assertNotNull(sInstance)

        val mHandlerThread = Whitebox.getInternalState<HandlerThread>(triggerManager, "mHandlerThread")
        Assert.assertNotNull(mHandlerThread)

        val mHandler = Whitebox.getInternalState<Handler>(triggerManager, "mHandler")
        Assert.assertNotNull(mHandler)
        var hasBindLittleCore = Whitebox.getInternalState<Boolean>(triggerManager, "hasBindLittleCore")
        Assert.assertTrue(hasBindLittleCore)


        SyncTriggerManager.release()
        try {
            Thread.sleep(500)
        } catch (_: InterruptedException) {
        }

        val handlerThread = Whitebox.getInternalState<HandlerThread>(triggerManager, "mHandlerThread")
        Assert.assertNotNull(handlerThread)

        val handler = Whitebox.getInternalState<Handler>(triggerManager, "mHandler")
        Assert.assertNull(handler)

        hasBindLittleCore = Whitebox.getInternalState<Boolean>(triggerManager, "hasBindLittleCore")
        Assert.assertFalse(hasBindLittleCore)
    }

    @Test
    fun should_when_trigBackupNow() {
        val triggerManager = SyncTriggerManager.getInstance(mContext!!)

        triggerManager.trigBackupNow()
        val mHandler = Whitebox.getInternalState<Handler>(triggerManager, "mHandler")
        Assert.assertFalse(mHandler.hasMessages(BACKUP))
    }

    @Test
    fun should_when_trigRecoveryNow() {
        val triggerManager = SyncTriggerManager.getInstance(mContext!!)

        triggerManager.trigRecoveryNow()
        val mHandler = Whitebox.getInternalState<Handler>(triggerManager, "mHandler")
        Assert.assertFalse(mHandler.hasMessages(RECOVERY))
    }

    @Test
    fun should_when_trigStopSyncForLoginOut() {
        val triggerManager = SyncTriggerManager.getInstance(mContext!!)

        triggerManager.trigStopSyncForLoginOut()
        triggerManager.trigStopSyncForLoginOut(true)
        val mHandler = Whitebox.getInternalState<Handler>(triggerManager, "mHandler")
    }

    @Test
    fun should_when_trigStopSyncForClearAnchor() {
        val triggerManager = SyncTriggerManager.getInstance(mContext!!)

        triggerManager.trigStopSyncForClearAnchor()
        val mHandler = Whitebox.getInternalState<Handler>(triggerManager, "mHandler")
    }

    @Test
    fun should_when_trigStopSyncForNothing() {
        val triggerManager = SyncTriggerManager.getInstance(mContext!!)

        triggerManager.trigStopSyncForErrorCode(null)
        val mHandler = Whitebox.getInternalState<Handler>(triggerManager, "mHandler")
    }

    @Test
    fun should_when_convertToRecoveryRequestSource() {
        val triggerManager = SyncTriggerManager.getInstance(mContext!!)

        triggerManager.trigStopSyncForErrorCode(null)
        var requestSource = Whitebox.invokeMethod<CloudRecoveryRequestSource>(triggerManager, "convertToRecoveryRequestSource",
            SyncTriggerManager.RECOVERY_FROM_START_APP)
        Assert.assertEquals(CloudRecoveryRequestSource.START_APP, requestSource)

        requestSource = Whitebox.invokeMethod<CloudRecoveryRequestSource>(triggerManager, "convertToRecoveryRequestSource",
            SyncTriggerManager.RECOVERY_FROM_MANUAL)
        Assert.assertEquals(CloudRecoveryRequestSource.MANUAL, requestSource)

        requestSource = Whitebox.invokeMethod<CloudRecoveryRequestSource>(triggerManager, "convertToRecoveryRequestSource",
            SyncTriggerManager.RECOVERY_FROM_PUSH)
        Assert.assertEquals(CloudRecoveryRequestSource.PUSH, requestSource)


        requestSource = Whitebox.invokeMethod<CloudRecoveryRequestSource>(triggerManager, "convertToRecoveryRequestSource",
            SyncTriggerManager.RECOVERY_FROM_FETCH)
        Assert.assertEquals(CloudRecoveryRequestSource.NEED_FETCH, requestSource)

        requestSource = Whitebox.invokeMethod<CloudRecoveryRequestSource>(triggerManager, "convertToRecoveryRequestSource",
            999)
        Assert.assertEquals(CloudRecoveryRequestSource.OTHERS, requestSource)
    }
}