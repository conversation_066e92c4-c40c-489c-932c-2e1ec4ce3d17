package com.recorder.cloudkit;


import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;


import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.annotation.Config;


import com.heytap.cloudkit.libsync.metadata.helper.CloudRecoveryRequestSource;
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord;
import com.recorder.cloudkit.shadows.ShadowFeatureOption;
import com.recorder.cloudkit.sync.recovery.DownloadSyncUtil;

import java.util.ArrayList;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class DownloadSyncUtilTest {
    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @After
    public void tearDown() {
        mContext = null;
    }

    @Ignore
    @Test
    public void should_returnNotnull_when_onServerAddOrUpdateForRecover() throws Exception {
        List<CloudMetaDataRecord> metaDataRecordList = new ArrayList<>();
        CloudMetaDataRecord cloudMetaDataRecord = new CloudMetaDataRecord();
        cloudMetaDataRecord.setSysRecordId("12");
        cloudMetaDataRecord.setFields("{“itemId”:\"12\"}");
        metaDataRecordList.add(cloudMetaDataRecord);

        boolean success = DownloadSyncUtil.onServerDataSuccessForRecover(mContext, CloudRecoveryRequestSource.MANUAL, metaDataRecordList);
        Assert.assertTrue(success);
    }

}
