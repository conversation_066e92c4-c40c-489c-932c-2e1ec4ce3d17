package com.recorder.cloudkit.account

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.heytap.usercenter.accountsdk.AccountAgent
import com.heytap.usercenter.accountsdk.http.AccountNameTask
import com.heytap.usercenter.accountsdk.model.AccountEntity
import com.heytap.usercenter.accountsdk.model.SignInAccount
import com.recorder.cloudkit.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.base.BaseApplication
import com.recorder.cloudkit.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.any
import org.mockito.ArgumentMatchers.anyString
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

/*
File:AccountManagerTest
Description:
Version:
Date:2022/6/28
Author:W9013204

------------------Revision History------------------
<author> <date> <version> <desc>
*/
@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class, ShadowCOUIVersionUtil::class])
class AccountManagerTest {

    private val userId = "*********"
    private var mContext: Context? = null
    private var mMockApplication: MockedStatic<BaseApplication>? = null
    private var mMockAccountAgent: MockedStatic<AccountAgent>? = null
    private var mCenterReceiver: AccountBroadcastReceiver? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mMockAccountAgent = Mockito.mockStatic(AccountAgent::class.java)
        mMockApplication = Mockito.mockStatic(BaseApplication::class.java)
        mMockApplication!!.`when`<Any> { BaseApplication.getAppContext() }?.thenReturn(mContext)
        mCenterReceiver = AccountBroadcastReceiver()
    }

    @After
    fun release() {
        mMockApplication?.close()
        mMockAccountAgent?.close()
        mMockApplication = null
        mContext = null
        mMockAccountAgent = null
    }

    @Test
    fun verify_value_when_getSignInAccountFromCache() {
        //getSignInAccountFromCache
        mMockAccountAgent!!.`when`<AccountEntity> { AccountAgent.getAccountEntity(any(), anyString()) }.thenReturn(null)
        Assert.assertNull(AccountManager.sAccountManager.getSignInAccountFromCache(mContext!!))
        Assert.assertEquals("", AccountManager.sAccountManager.getLoginIdFromCache(mContext!!))
    }

    @Test
    fun verify_value_when_getSignInAccount() {
        mMockAccountAgent!!.`when`<Unit> { AccountAgent.getSignInAccount(any(), anyString(), any()) }.thenAnswer {
            (it.arguments[2] as AccountNameTask.onReqAccountCallback<SignInAccount>).onReqFinish(SignInAccount().apply { isLogin = false })
            return@thenAnswer null
        }

        mMockAccountAgent!!.`when`<Unit> { AccountAgent.reqSignInAccount(any(), anyString(), any()) }.thenAnswer {
            (it.arguments[2] as AccountNameTask.onReqAccountCallback<SignInAccount>).onReqFinish(SignInAccount().apply { isLogin = false })
            return@thenAnswer null
        }

        //getSignInAccountFromCache
        val callback = object : IAccountInfoCallback {
            override fun onComplete(account: AccountBean) {
                Assert.assertNotNull(account)
                Assert.assertFalse(account.isLogin)
            }
        }
        AccountManager.sAccountManager.getSignInAccount(mContext!!, callback)
        AccountManager.sAccountManager.reqSignInAccount(mContext!!, callback)
    }

    @Test
    fun verify_value_when_isLogin() {
        mMockAccountAgent!!.`when`<Unit> { AccountAgent.getSignInAccount(any(), anyString(), any()) }.thenAnswer {
            (it.arguments[2] as AccountNameTask.onReqAccountCallback<SignInAccount>).onReqFinish(SignInAccount().apply { isLogin = false })
            return@thenAnswer null
        }

        val isLogin = AccountManager.sAccountManager.isLogin(mContext!!)
        Assert.assertFalse(isLogin)
    }

    @Test
    fun verify_value_when_isLoginFromCache() {
        mMockAccountAgent!!.`when`<AccountEntity> { AccountAgent.getAccountEntity(any(), anyString()) }
            .thenReturn(AccountEntity().apply { ssoid = "1111" })

        val isLogin = AccountManager.sAccountManager.isLoginFromCache(mContext!!)
        Assert.assertTrue(isLogin)

        AccountManager.sAccountManager.saveUserId(mContext!!, "123456")
        Assert.assertFalse(AccountManager.sAccountManager.checkUserIdChanged(mContext!!, "123456"))
        Assert.assertTrue(AccountManager.sAccountManager.checkUserIdChanged(mContext!!, "1234567"))
    }
}