package com.recorder.cloudkit.util;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;

import android.content.Context;
import android.os.Build;
import android.text.TextUtils;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord;
import com.recorder.cloudkit.utils.CloudRecordDbUtil;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.db.RecorderDBUtil;
import com.recorder.cloudkit.shadows.ShadowFeatureOption;
import com.recorder.cloudkit.sync.bean.RecordTransferFile;
import com.soundrecorder.common.db.CloudSyncRecorderDbUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class CloudSyncRecorderDbUtilTest {
    private Context mContext;
    private MockedStatic<RecorderDBUtil> mDbUtilMockedStatic;
    private RecorderDBUtil mRecorderDBUtilMock;
    private MockedStatic<FileUtils> mFileUtilsMockedStatic;
    private MockedStatic<BaseApplication> mApplicationMockedStatic;
    private MockedStatic<TextUtils> mTextUtilsMockedStatic;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mRecorderDBUtilMock = mock(RecorderDBUtil.class);
        mDbUtilMockedStatic = Mockito.mockStatic(RecorderDBUtil.class);
        mDbUtilMockedStatic.when(() -> RecorderDBUtil.getInstance(any())).thenReturn(mRecorderDBUtilMock);
        mFileUtilsMockedStatic = Mockito.mockStatic(FileUtils.class);
        mApplicationMockedStatic = Mockito.mockStatic(BaseApplication.class);
        mApplicationMockedStatic.when(() -> BaseApplication.getAppContext()).thenReturn(mContext);
        mTextUtilsMockedStatic = mockStatic(TextUtils.class);
        mTextUtilsMockedStatic.when(() -> TextUtils.isEmpty(any())).thenAnswer(invocation -> {
            CharSequence a = invocation.getArgument(0);
            if (a == null || a.length() == 0) {
                return true;
            }
            return false;
        });
    }

    @After
    public void release() {
        mFileUtilsMockedStatic.close();
        mDbUtilMockedStatic.close();
        mApplicationMockedStatic.close();
        mTextUtilsMockedStatic.close();
        mDbUtilMockedStatic = null;
        mFileUtilsMockedStatic = null;
        mApplicationMockedStatic = null;
        mTextUtilsMockedStatic = null;
        mRecorderDBUtilMock = null;
        mContext = null;
    }

    @Test
    public void should_when_getDownloadFileList() {
        Record record = new Record();
        record.setFileSize(12344);
        record.setFileId("111");
        record.setUuid("11");
        record.setGlobalId("11");
        record.setRelativePath("Music/Recordings/Standard Recordings");
        record.setDisplayName("标准 1.mp3");
        Record record1 = new Record();
        record1.setFileSize(6999);
        record1.setFileId("2222");
        record1.setUuid("22");
        record1.setGlobalId("22");
        record1.setRelativePath("Music/Recordings/Standard Recordings");
        record1.setDisplayName("标准.mp3");

        Record recordRepeatFile = new Record();
        record1.setFileSize(6999);
        record1.setFileId("2222");
        record1.setUuid("33");
        record1.setGlobalId("11");
        record1.setRelativePath("Music/Recordings/Standard Recordings");
        record1.setDisplayName("标准1.mp3");

        mDbUtilMockedStatic.when(() -> RecorderDBUtil.getInstance(mContext).getDataForBatchDownloadFile()).thenReturn(Arrays.asList(record, record1, recordRepeatFile));
        mFileUtilsMockedStatic.when(() -> FileUtils.getRealFileSize(anyString(), anyString())).thenReturn(12344L);

        List<RecordTransferFile> resultList = CloudRecordDbUtil.getDownloadFileList(mContext);
        Assert.assertNotNull(resultList);
        Assert.assertEquals(1, resultList.size());
    }

    @Test
    public void should_return_empty_when_getDownloadFileList() {
        List<RecordTransferFile> resultList = CloudRecordDbUtil.getDownloadFileList(mContext, null);
        Assert.assertTrue(resultList.isEmpty());

    }

    @Test
    public void should_return_notEmpty_when_getDownloadFileList() {
        Record record = new Record();
        record.setFileSize(12344);
        record.setFileId("111");
        record.setUuid("1111");
        record.setGlobalId("1111");
        record.setRelativePath("Music/Recordings/Standard Recordings");
        record.setDisplayName("标准 1.mp3");
        Record record1 = new Record();
        record1.setFileSize(6999);
        record1.setFileId("2222");
        record1.setUuid("2222");
        record1.setGlobalId("2222");
        record1.setRelativePath("Music/Recordings/Standard Recordings");
        record1.setDisplayName("标准.mp3");
        mDbUtilMockedStatic.when(() -> RecorderDBUtil.getInstance(mContext).getDataForBatchDownloadFile(anyList())).thenReturn(Arrays.asList(record, record1));
        mFileUtilsMockedStatic.when(() -> FileUtils.getRealFileSize(anyString(), anyString())).thenReturn(12344L);

        List<CloudMetaDataRecord> metaDataRecordList = new ArrayList<>();
        CloudMetaDataRecord metaDataRecord = new CloudMetaDataRecord();
        metaDataRecord.setSysRecordId("1111");
        metaDataRecordList.add(metaDataRecord);
        metaDataRecord = new CloudMetaDataRecord();
        metaDataRecord.setSysRecordId("2222");
        metaDataRecordList.add(metaDataRecord);

        List<RecordTransferFile> resultList = CloudRecordDbUtil.getDownloadFileList(mContext, metaDataRecordList);
        Assert.assertFalse(resultList.isEmpty());
    }

    @Test
    public void should_return_true_when_insertCloudMetadataForLocallyExistsFile() {
        Record record = new Record();
        record.setFileSize(12344);
        record.setFileId("111");
        record.setUuid("1111");
        record.setGlobalId("1111");
        record.setRelativePath("Music/Recordings/Standard Recordings");
        record.setDisplayName("标准 1.mp3");
        record.setMarkData(new byte[1]);
        boolean insert = CloudSyncRecorderDbUtil.insertCloudMetadataForLocallyExistsFile(record);
        Assert.assertTrue(insert);

        insert = CloudSyncRecorderDbUtil.insertCloudMetadataForLocallyExistsFile(null);
        Assert.assertFalse(insert);
    }

    @Test
    public void should_return_false_when_processEncryptAudioFile() {
        boolean process = CloudSyncRecorderDbUtil.processEncryptAudioFile("", "", "");
        Assert.assertFalse(process);

        process = CloudSyncRecorderDbUtil.processEncryptAudioFile("标准 1.mp3", "Music/Recordings/Standard Recordings", "md5");
        Assert.assertFalse(process);

        List<Record> recordList = new ArrayList<>();
        Record record = new Record();
        record.setGlobalId("1111");
        recordList.add(record);
        record = new Record();
        record.setId(-1);
        recordList.add(record);
        mDbUtilMockedStatic.when(() -> RecorderDBUtil.getRecordData(any(), any(), anyString(), any(), anyString())).thenReturn(recordList);
        process = CloudSyncRecorderDbUtil.processEncryptAudioFile("标准 1.mp3", "Music/Recordings/Standard Recordings", "md5");
        Assert.assertFalse(process);
    }

    @Test
    public void should_when_updateDisplayName() {
        String relativePath = "Music/Recordings/Standard Recordings/";

        CloudSyncRecorderDbUtil.updateDisplayName("标准 1.mp3", "标准录音 1.mp3", relativePath, true);
        Mockito.verify(mRecorderDBUtilMock, times(1)).updateDisplayName(anyString(), anyString(), anyString(), anyBoolean());

        boolean success = CloudSyncRecorderDbUtil.updateDisplayName(relativePath + "标准 1.mp3", relativePath + "标准录音 1.mp3", true, true);
        Assert.assertTrue(success);
    }

    @Test
    public void should_when_updateDisplayNameForDownloadRename() {
        String relativePath = "Music/Recordings/Standard Recordings/";
        boolean success = CloudSyncRecorderDbUtil.updateDisplayNameForDownloadRename(1, relativePath + "标准 1.mp3", true);
        Assert.assertTrue(success);
    }

    @Test
    public void should_when_updateDisplayNameForRecoveryRename() {
        Record record = new Record();
        record.setMarkData(new byte[1024]);
        boolean success = CloudSyncRecorderDbUtil.updateDisplayNameForRecoveryRename(true, "1111", record);
        Assert.assertTrue(success);

        record.setGlobalId("11111");
        success = CloudSyncRecorderDbUtil.updateDisplayNameForRecoveryRename(true, "1111", record);
        Assert.assertTrue(success);
    }


    @Test
    public void should_when_updateGlobalIdAndFileIdForDelete() {
        boolean success = CloudSyncRecorderDbUtil.updateGlobalIdAndFileIdForDelete(1, "1111", "999");
        Assert.assertTrue(success);
    }

    @Test
    public void should_when_deleteRecordByPath() throws Exception {
        String path = "Music/Recordings/Standard Recordings/标准.mp3";
        boolean success = CloudSyncRecorderDbUtil.deleteRecordByPath(path, false);
        Assert.assertFalse(success);

        success = CloudSyncRecorderDbUtil.deleteRecordByPath(path, true);
        Assert.assertFalse(success);

        Record record = new Record();
        Record recordHasGlobalId = new Record();
        recordHasGlobalId.setGlobalId("8888");
        mDbUtilMockedStatic.when(() -> RecorderDBUtil.getInstance(mContext).qureyRecordByPath(anyString())).thenReturn(record, recordHasGlobalId);
        success = CloudSyncRecorderDbUtil.deleteRecordByPath(path, false);
        Assert.assertFalse(success);

        success = CloudSyncRecorderDbUtil.deleteRecordByPath(path, false);
        Assert.assertFalse(success);
    }
}
