package com.recorder.cloudkit

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

import android.app.AlarmManager
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.recorder.cloudkit.shadows.ShadowFeatureOption
import org.junit.Assert
import org.robolectric.Shadows.shadowOf
import org.robolectric.shadows.ShadowAlarmManager


@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class SyncRetryHelperTest {
    private val TYPE_RECOVERY = 1
    private val TYPE_CLEAR_COUNT = 2
    private var mContext: Context? = null
    var shadowAlarmManager: ShadowAlarmManager? = null
    var alarmManager: AlarmManager? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        alarmManager = mContext?.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        shadowAlarmManager = shadowOf(alarmManager)
    }

    @After
    fun release() {
        mContext = null
        shadowAlarmManager = null
        alarmManager = null
    }

    @Test
    fun should_sendAlarmSuccess_when_startRetryDelayAlarm() {
        Assert.assertNull(shadowAlarmManager?.nextScheduledAlarm)

        SyncRetryHelper.startRetryDelayAlarm(mContext!!, 1000, TYPE_RECOVERY)
        val repeatingAlarm = shadowAlarmManager!!.nextScheduledAlarm
        Assert.assertNotNull(repeatingAlarm)
    }

    @Test
    fun should_deleteAlarmSuccess_when_clearSyncAlarm() {
        SyncRetryHelper.startRetryDelayAlarm(mContext!!, 1000, TYPE_RECOVERY)

        SyncRetryHelper.clearSyncAlarm(mContext!!, TYPE_RECOVERY)
        val repeatingAlarm = shadowAlarmManager!!.nextScheduledAlarm
        Assert.assertNull(repeatingAlarm)
    }

    @Test
    fun should_sendAlarmSuccess_when_startClearFailCountDelayAlarm() {
        Assert.assertNull(shadowAlarmManager?.nextScheduledAlarm)

        SyncRetryHelper.startClearFailCountDelayAlarm(mContext!!)
        val repeatingAlarm = shadowAlarmManager!!.nextScheduledAlarm
        Assert.assertNotNull(repeatingAlarm)
    }

    @Test
    fun should_deleteAlarmSuccess_when_clearClearFailedCountAlarm() {
        SyncRetryHelper.startClearFailCountDelayAlarm(mContext!!)

        SyncRetryHelper.clearClearFailedCountAlarm(mContext!!)
        val repeatingAlarm = shadowAlarmManager!!.nextScheduledAlarm
        Assert.assertNull(repeatingAlarm)
    }


}