/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RecordSummaryServiceClient
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.client

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import com.oplus.summary.IRecordSummaryListener
import com.oplus.summary.IRecordSummaryServiceBinder
import com.oplus.summary.ISummaryBinderPool
import com.oplus.summary.ISummaryBinderPoolCallback
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.summary.RecordSummaryManager.PACKAGE_NAME_ASSISTANT
import com.soundrecorder.summary.data.SummaryStateResult

class RecordSummaryServiceClient {

    companion object {
        /*0-摘要不可用 （不同意用户须知，取消排队，取消登录）*/
        const val SUMMARY_STATE_ENABLE = 0
        /*7-准备进行摘要，摘要前置条件满足后回调*/
        const val SUMMARY_STATE_PREPARED = 7
        /*1-摘要开始*/
        const val SUMMARY_STATE_START = 1
        /*8-便签创建成功 noteId*/
        const val SUMMARY_STATE_GEN_NOTE_ID = 8
        /*2-已经有摘要进行中 （包括asr，摘要流程）*/
        const val SUMMARY_STATE_IN_PROGRESS = 2
        /*3-剩余次数（第一次点击查询或者重试次数回调）  count -1 0,1*/
        const val SUMMARY_STATE_AVAILABLE_TIMES = 3
        /*4-便签拷贝文件成功  noteId*/
        const val SUMMARY_STATE_NOTES_COPY_END = 4
        /*5-asr错误 or 摘要成功or失败 noteId  asrError summaryError isOver14kSize isContentSafe
        * 可能调用多次，有重试逻辑*/
        const val SUMMARY_STATE_SUMMARY_END = 5
        /*6-摘要完全结束，流体云卡片消失（字幕主动停止，比如互斥逻辑,流程结束，悬浮窗权限）*/
        const val SUMMARY_STATE_END = 6
        private const val TAG = "RecordSummaryServiceClient"
        private const val RECORD_SUMMARY_SERVICE_ACTION = "action.oplus.accessibilityassistant.service.summary"
        private const val SUMMARY_SERVICE_BIND_CODE = "record"
    }

    var serviceConnListener: ServiceConnectListener? = null
    var summaryStateListener: ISummaryStateChangeListener? = null
    private var summaryServiceBinder: IRecordSummaryServiceBinder? = null
    private var summaryBinderPool: ISummaryBinderPool? = null
    private val serviceConnection = object : ServiceConnection {

        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            DebugUtil.i(TAG, "onServiceConnected")
            kotlin.runCatching {
                summaryBinderPool = ISummaryBinderPool.Stub.asInterface(service)
                summaryServiceBinder = IRecordSummaryServiceBinder.Stub.asInterface(queryServiceBinder())
                registerSummaryListener()
                serviceConnListener?.onConnectSuccess()
            }.onFailure {
                DebugUtil.e(TAG, "onServiceConnected error= $it")
                serviceConnListener?.onConnectFailed(it.message)
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            DebugUtil.w(TAG, "onServiceDisconnected: name = $name")
            onServiceDisconnect(name)
        }

        override fun onBindingDied(name: ComponentName?) {
            DebugUtil.w(TAG, "onBindingDied: name = $name")
            onServiceDisconnect(name)
        }
    }
    private val summaryListener = object : IRecordSummaryListener.Stub() {
        /**
         *
         * 公共参数  callId mediaId md5
         *
         * 0-摘要不可用，处于等待摘要 （便签卸载，用户须知，未排队，未登录）
         * 1-摘要开始
         * 2-摘要进行中状态 （包括asr，摘要流程）
         * 3-剩余次数（第一次点击查询或者重试次数回调）  count -1 0,1,2,3,4,5
         * 4-摘要文件拷贝成功 便签拷贝文件成功  noteId
         * 5-摘要成功 noteId  asrError summaryError
         * 6-摘要结束（字幕主动停止，比如互斥逻辑,流程结束，悬浮窗权限）
         */
        override fun onRecordSummaryState(state: Int, type: Int, extra: Bundle?) {
            DebugUtil.d(TAG, "onRecordSummaryState,state=$state,type=$type}")
            summaryStateListener?.onSummaryStateChanged(state, type, SummaryStateResult.fromBundle(state, extra))
        }
    }

    fun startSummaryService(context: Context) {
        DebugUtil.w(TAG, "startSummaryService")
        val intent = Intent(RECORD_SUMMARY_SERVICE_ACTION).also {
            it.setPackage(PACKAGE_NAME_ASSISTANT)
        }
        kotlin.runCatching {
            context.bindService(
                intent, serviceConnection, Context.BIND_AUTO_CREATE or Context.BIND_IMPORTANT)
        }.onFailure {
            DebugUtil.e(TAG, "startSummaryService, bind service failed: $it")
        }
        val hasServiceBind = summaryServiceBinder?.asBinder()?.isBinderAlive == true
        if (hasServiceBind) {
            DebugUtil.d(TAG, "startSummaryService, hasServiceBind")
            serviceConnListener?.onConnectSuccess()
        }
    }

    fun stopSummaryService(context: Context) {
        DebugUtil.w(TAG, "stopSummaryService")
        kotlin.runCatching {
            unRegisterSummaryListener()
            summaryServiceBinder = null
            summaryBinderPool = null
            context.unbindService(serviceConnection)
        }.onFailure {
            DebugUtil.e(TAG, "startSummaryService, bind service failed: $it")
        }
        serviceConnListener = null
        summaryStateListener = null
    }

    fun performSummary(bundle: Bundle) {
        kotlin.runCatching {
            summaryServiceBinder?.startRecordSummary(bundle)
        }.onFailure {
            DebugUtil.e(TAG, "performSummary failed: $it")
        }
    }

    fun registerSummaryListener() {
        kotlin.runCatching {
            summaryServiceBinder?.registerRecordSummaryListener(summaryListener)
        }.onFailure {
            DebugUtil.e(TAG, "registerSummaryListener failed: $it")
        }
    }

    fun unRegisterSummaryListener() {
        kotlin.runCatching {
            summaryServiceBinder?.unRegisterRecordSummaryListener(summaryListener)
        }.onFailure {
            DebugUtil.e(TAG, "unRegisterSummaryListener failed: $it")
        }
    }

    fun querySummaryStatus(): Bundle? {
        return kotlin.runCatching {
            summaryServiceBinder?.querySummaryStatus()
        }.onFailure {
            DebugUtil.e(TAG, "querySummaryStatus failed: $it")
        }.getOrNull()
    }

    private fun onServiceDisconnect(name: ComponentName?) {
        serviceConnListener?.onDisconnected(name)
        summaryServiceBinder = null
        summaryBinderPool = null
        summaryStateListener = null
        serviceConnListener = null
    }

    private fun queryServiceBinder(): IBinder? {
        return kotlin.runCatching {
            summaryBinderPool?.queryBinder(BaseUtil.getPackageName(), SUMMARY_SERVICE_BIND_CODE, object : ISummaryBinderPoolCallback.Stub() {
                override fun callback() {
                    DebugUtil.d(TAG, "queryServiceBinder, callback")
                }
            })
        }.onFailure {
            DebugUtil.e(TAG, "queryServiceBinder failed: $it")
        }.getOrNull()
    }
}