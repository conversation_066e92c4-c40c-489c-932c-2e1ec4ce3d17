/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SummaryLrcFileUtil
 * Description:
 * Version: 1.0
 * Date: 2024/2/27
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/2/27 1.0 create
 */

package com.soundrecorder.summary.util

import android.content.Context
import android.net.Uri
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.db.NoteDbUtils.SummaryType.SUMMARY_TYPE_CALL_RECORD
import com.soundrecorder.common.db.NoteDbUtils.SummaryType.SUMMARY_TYPE_COMMON_RECORD
import com.soundrecorder.common.utils.FileDealUtil
import com.soundrecorder.common.utils.RecordModeUtil.recordType
import com.soundrecorder.summary.data.FlagInfo
import com.soundrecorder.summary.data.LrcFileBean
import com.soundrecorder.summary.data.LrcFileResult
import com.soundrecorder.summary.data.PicInfo
import com.soundrecorder.summary.data.Sentence
import java.io.BufferedWriter
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStreamWriter
import java.nio.charset.StandardCharsets
import java.util.concurrent.CopyOnWriteArrayList

object SummaryLrcFileUtil {
    private const val TAG = "SummaryLrcFileUtil"
    private const val LRC_PATH = "lrc"

    /**
     * 生成音频文件生成摘要所需LRC文件
     * @param record 对应音频文件信息（id需要是媒体库ID）
     * @param sentenceList 对应音频文件的转文本内容,不含标记信息
     * @param markList 对应音频文件的标记列表
     * @return string: lrc文件路径
     */
    @JvmStatic
    fun createLrcFile(record: Record, sentenceList: List<Sentence>?, markList: List<MarkDataBean>?): LrcFileResult {
        val result = LrcFileResult().apply {
            summaryType = calRecordSummaryType(record.relativePath)
        }
        genLrcFileJsonContent(result.summaryType, sentenceList, markList)?.apply {
            val fileName = genLrcFileName(record.id, record.displayName)
            val lrcFileUri = writeLrcJsonContent(fileName, GsonUtil.toJson(this))
            result.lrcFileName = fileName
            result.lrcFileUri = lrcFileUri
            result.picMarkUriList = picUriList
            DebugUtil.i(TAG, "createLrcFile lrcPath =$lrcFileUri,picSize=${picUriList?.size}")
        }
        return result
    }

    /**
     * 将LRC内容写入对应文件
     * @param filename lrc文件名称
     * @param beanConvertText json内容
     * @return lrc文件uri字符串
     */
    @JvmStatic
    fun writeLrcJsonContent(filename: String, beanConvertText: String?): Uri? {
        if (beanConvertText.isNullOrEmpty()) {
            DebugUtil.w(TAG, "writeLrcJsonContent beanConvertText.isNullOrEmpty")
            return null
        }
        val startTime = System.currentTimeMillis()
        val file = checkOrCreateFile(getLrcSaveDirPath(BaseApplication.getAppContext()), filename)
        if (file == null) {
            DebugUtil.w(TAG, "writeLrcJsonContent file create fail")
            return null
        }
        DebugUtil.d(TAG, "writeLrcJsonContent: $file,beanConvertText=${beanConvertText.length}")
        runCatching {
            val fileOutputStream = FileOutputStream(file)
            fileOutputStream.use {
                OutputStreamWriter(fileOutputStream, StandardCharsets.UTF_8).use {
                    BufferedWriter(it).use { writer ->
                        writer.write(beanConvertText)
                        writer.flush()
                    }
                }
            }
        }.onFailure {
            DebugUtil.e(TAG, "writeLrcJsonContent error ", it)
        }
        DebugUtil.d(TAG, "writeLrcJsonContent: time: ${(System.currentTimeMillis() - startTime)}")
        return FileDealUtil.getUriForFile(file)
    }

    /**
     * 根据转文本内容、标记内容
     * 生成lrc文件内容json格式
     * @param summaryType 摘要type 1-普通录音 2-通话录音
     * @param sentenceList 转文本内容
     * @param markList 标记信息，包含文本、图片标记
     * @return fist: lrc文件内容，含转文本、标记信息,json字符串
     *  second：图片标记中图片uriList
     */
    @JvmStatic
    fun genLrcFileJsonContent(summaryType: Int, sentenceList: List<Sentence>?, markList: List<MarkDataBean>?): LrcFileBean? {
        if (sentenceList.isNullOrEmpty() && markList.isNullOrEmpty()) {
            DebugUtil.d(TAG, "genLrcFileJsonContent convert and mark is empty")
            return null
        }
        val startTime = System.currentTimeMillis()
        val lrcBean = LrcFileBean().apply {
            type = summaryType
            s = 0
        }
        var copyTextMarkList: CopyOnWriteArrayList<FlagInfo>? = null
        var copyPicMarkList: CopyOnWriteArrayList<PicInfo>? = null
        val picUriList = mutableListOf<Uri>()

        splitOriginMarkList(markList)?.run {
            lrcBean.textFlagList = first
            lrcBean.picFlagList = second
            if (first.isNotEmpty()) {
                copyTextMarkList = CopyOnWriteArrayList<FlagInfo>()
                copyTextMarkList?.addAll(first)
            }
            if (second.isNotEmpty()) {
                copyPicMarkList = CopyOnWriteArrayList<PicInfo>()
                copyPicMarkList?.addAll(second)
            }
            picUriList.addAll(third)
        }
        sentenceList?.run {
            forEach {
                getSentenceMarkList(it.sTime, it.eTime, copyTextMarkList, copyPicMarkList).run {
                    it.textMark = first
                    it.picMark = second
                }
            }
            lrcBean.convertSentenceList = this as ArrayList<Sentence>
        }
        lrcBean.picUriList = picUriList
        DebugUtil.d(TAG, "genLrcFileJsonContent, spend time = ${System.currentTimeMillis() - startTime}")
        return lrcBean
    }

    @JvmStatic
    fun calRecordSummaryType(relativePath: String): Int {
        return if (RecordModeConstant.RECORD_TYPE_CALL == relativePath.recordType()) {
            SUMMARY_TYPE_CALL_RECORD
        } else {
            SUMMARY_TYPE_COMMON_RECORD
        }
    }

    /**
     * 将原始标记拆分成文件内容需要的文本、图片标记
     * @param markList 原始文本图片标记
     * @return first:文本标记 second：图片标记 third: 图片uri
     */
    @JvmStatic
    private fun splitOriginMarkList(markList: List<MarkDataBean>?): Triple<ArrayList<FlagInfo>, ArrayList<PicInfo>, ArrayList<Uri>>? {
        markList ?: return null
        val textMarkList = arrayListOf<FlagInfo>()
        val picMarkList = arrayListOf<PicInfo>()
        val picUriList = arrayListOf<Uri>()
        var textMark: FlagInfo
        var picMark: PicInfo
        markList.forEach { markBean ->
            if (markBean.isPictureType()) {
                FileDealUtil.getUriForFile(FileUtils.getAppFile(markBean.pictureFilePath, false))?.let {
                    picMark = PicInfo(
                        markBean.timeInMills, markBean.getRealMarkText(), it.toString())
                    picMarkList.add(picMark)
                    picUriList.add(it)
                }
            } else {
                textMark = FlagInfo(markBean.timeInMills, markBean.getRealMarkText())
                textMarkList.add(textMark)
            }
        }
        return Triple(textMarkList, picMarkList, picUriList)
    }

    /**
     * 获取转文本item内对应的图片、文本标记
     * @param startTime 文本内容对应开始时间
     * @param endTime  文本内容结束时间
     * @param textList 文本标记列表
     * @param picList 图片标记列表
     * @return first：对应起始时间内文本标记列表 second：对应起始时间的图片标记列表
     */
    @JvmStatic
    private fun getSentenceMarkList(
        startTime: Long,
        endTime: Long,
        textList: CopyOnWriteArrayList<FlagInfo>?,
        picList: CopyOnWriteArrayList<PicInfo>?
    ): Pair<ArrayList<FlagInfo>, ArrayList<PicInfo>> {
        val textMarkList = arrayListOf<FlagInfo>()
        val picMarkList = arrayListOf<PicInfo>()

        var textMark: FlagInfo
        var picMark: PicInfo

        val textIterator: Iterator<FlagInfo>? = textList?.iterator()
        val picIterator: Iterator<PicInfo>? = picList?.iterator()

        while (textIterator?.hasNext() == true) {
            textMark = textIterator.next()
            if (textMark.s in startTime..endTime) {
                textMarkList.add(textMark)
                textList.remove(textMark)
            }
        }

        while (picIterator?.hasNext() == true) {
            picMark = picIterator.next()
            if (picMark.s in startTime..endTime) {
                picMarkList.add(picMark)
                picList.remove(picMark)
            }
        }
        return Pair(textMarkList, picMarkList)
    }


    @JvmStatic
    private fun checkOrCreateFile(dirPath: String, filename: String): File? {
        runCatching {
            val file = File(dirPath, filename)
            if (!file.exists()) {
                file.parentFile?.let {
                    if (!it.exists()) {
                        val dirMakeSuc = it.mkdir()
                        DebugUtil.i(TAG, "parentDir : $it, mkdir suc: $dirMakeSuc")
                        if (!dirMakeSuc) {
                            DebugUtil.w(TAG, "checkOrCreateFile:$it make file path failed!")
                            return null
                        }
                    }
                }
                if (!file.createNewFile()) {
                    return null
                }
            }
            return file
        }.onFailure {
            DebugUtil.e(TAG, "checkOrCreateFile error $it")
        }
        return null
    }

    /**
     * lrc文件保存路径
     */
    @JvmStatic
    fun getLrcSaveDirPath(context: Context): String {
        return context.filesDir.toString() + File.separator + LRC_PATH
    }

    /**
     * 生成lrc文件名称
     * @param id 媒体库ID
     * @param displayName 文件名称
     */
    @JvmStatic
    fun genLrcFileName(id: Long, displayName: String): String {
        return "lrc_${id}_${displayName.title()}.txt"
    }
}