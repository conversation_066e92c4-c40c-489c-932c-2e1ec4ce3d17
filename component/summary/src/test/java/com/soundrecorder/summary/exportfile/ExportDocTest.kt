/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ExportDocTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/04/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.exportfile

import android.content.Context
import com.oplus.aiunit.common.client.NoteExportClient
import com.oplus.aiunit.common.result.NoteExportResult
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.summary.exportfile.ExportDoc.AIUnitState
import com.soundrecorder.summary.exportfile.ExportDoc.EXPORT_FAILURE
import com.soundrecorder.summary.exportfile.ExportDoc.EXPORT_FAILURE_UNAVAILABLE_NEED_DOWNLOAD
import com.soundrecorder.summary.exportfile.ExportDoc.EXPORT_SUCCESS
import com.soundrecorder.summary.exportfile.ExportDoc.splitByKeywords
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Assert
import org.junit.Test

class ExportDocTest {

    @Test
    fun `should return true if need download`() {
        //given
        mockkStatic(ExportDoc::class)
        val context = mockk<Context>()
        every { ExportDoc.getDetectDataState(context) } returns AIUnitState.STATE_AVAILABLE_NEED_DOWNLOAD
        //when
        val result = ExportDoc.isSupportExport(context)

        //then
        Assert.assertTrue(result)
        unmockkStatic(ExportDoc::class)
    }

    @Test
    fun `should return true if detectDataState is STATE_AVAILABLE`() {
        //given
        mockkStatic(ExportDoc::class)
        val context = mockk<Context>()
        every { ExportDoc.getDetectDataState(context) } returns AIUnitState.STATE_AVAILABLE
        //when
        val result = ExportDoc.isSupportExport(context)

        //then
        Assert.assertTrue(result)
        unmockkStatic(ExportDoc::class)
    }

    @Test
    fun `should return false if detectDataState is other`() {
        //given
        mockkStatic(ExportDoc::class)
        val context = mockk<Context>()
        every { ExportDoc.getDetectDataState(context) } returns AIUnitState.STATE_UNAVAILABLE
        //when
        val result = ExportDoc.isSupportExport(context)

        //then
        Assert.assertFalse(result)
        unmockkStatic(ExportDoc::class)
    }

    @Test
    fun `should return right when test splitByKeywords`() {
        //given
        val inputString = "我的号码是150****9721，我叫某某某，我是一个软件工程师"
        val keywords = listOf("某某某", "150****9721", "软件工程师")

        val expect = mutableListOf(
            Content("我的号码是", ContentType.Normal),
            Content("150****9721", ContentType.Link),
            Content("，我叫", ContentType.Normal),
            Content("某某某", ContentType.Link),
            Content("，我是一个", ContentType.Normal),
            Content("软件工程师", ContentType.Link),
        )

        //when
        val result = inputString.splitByKeywords(keywords)

        assert(result == expect)
    }

    @Test
    fun `should return EXPORT_FAILURE_UNAVAILABLE_NEED_DOWNLOAD when state is need download`() {
        //given
        mockkStatic(ExportDoc::class)
        mockkStatic(DebugUtil::class)
        val context = mockk<Context>()
        every { DebugUtil.d(any(), any()) } just runs
        every { ExportDoc.getDetectDataState(context) } returns AIUnitState.STATE_AVAILABLE_NEED_DOWNLOAD
        val xmlPath = ""
        val docPath = ""
        every { ExportDoc.startForAIDownload(context) } just runs
        //when
        val result = ExportDoc.export(context, xmlPath, docPath)
        //then
        verify { ExportDoc.startForAIDownload(context) }
        Assert.assertEquals(result, EXPORT_FAILURE_UNAVAILABLE_NEED_DOWNLOAD)
        unmockkStatic(ExportDoc::class)
        unmockkStatic(DebugUtil::class)
    }

    @Test
    fun `should return EXPORT_FAILURE when state is state unavailable`() {
        //given
        mockkStatic(ExportDoc::class)
        mockkStatic(DebugUtil::class)
        val context = mockk<Context>()
        every { DebugUtil.d(any(), any()) } just runs
        every { ExportDoc.getDetectDataState(context) } returns AIUnitState.STATE_UNAVAILABLE
        val xmlPath = ""
        val docPath = ""
        //when
        val result = ExportDoc.export(context, xmlPath, docPath)
        //then
        Assert.assertEquals(result, EXPORT_FAILURE)
        unmockkStatic(ExportDoc::class)
        unmockkStatic(DebugUtil::class)
    }

    @Test
    fun `should return EXPORT_FAILURE when convert failed`() {
        //given
        mockkStatic(ExportDoc::class)
        mockkStatic(DebugUtil::class)
        mockkConstructor(NoteExportClient::class)
        val context = mockk<Context>()
        every { DebugUtil.d(any(), any()) } just runs
        every { ExportDoc.getDetectDataState(context) } returns AIUnitState.STATE_AVAILABLE
        val xmlPath = ""
        val docPath = ""
        val noteExportResult = mockk<NoteExportResult>()
        every { noteExportResult.resultCode } returns -1
        every { anyConstructed<NoteExportClient>().process(any(), any()) } returns noteExportResult

        //when
        val result = ExportDoc.export(context, xmlPath, docPath)
        //then
        Assert.assertEquals(result, EXPORT_FAILURE)
        unmockkStatic(ExportDoc::class)
        unmockkStatic(DebugUtil::class)
    }

    @Test
    fun `should return EXPORT_SUCCESS when convert success`() {
        //given
        mockkStatic(ExportDoc::class)
        mockkStatic(DebugUtil::class)
        mockkConstructor(NoteExportClient::class)
        val context = mockk<Context>()
        every { DebugUtil.d(any(), any()) } just runs
        every { ExportDoc.getDetectDataState(context) } returns AIUnitState.STATE_AVAILABLE
        val xmlPath = ""
        val docPath = ""
        val noteExportResult = mockk<NoteExportResult>()
        every { noteExportResult.resultCode } returns 0
        every { anyConstructed<NoteExportClient>().process(any(), any()) } returns noteExportResult

        //when
        val result = ExportDoc.export(context, xmlPath, docPath)
        //then
        Assert.assertEquals(result, EXPORT_SUCCESS)
        unmockkStatic(ExportDoc::class)
        unmockkStatic(DebugUtil::class)
    }
}