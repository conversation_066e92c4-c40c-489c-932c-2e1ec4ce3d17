/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: DurationWarnActivityTest
 * Description:
 * Version: 1.0
 * Date: 2024/3/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/3/18 1.0 create
 */

package com.soundrecorder.summary.ui

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.summary.shadow.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class DurationWarnActivityTest {
    private var context: Context? = null
    private var activityController: ActivityController<DurationWarnActivity>? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        activityController = Robolectric.buildActivity(DurationWarnActivity::class.java)
    }

    @After
    fun tearDown() {
        context = null
        activityController = null
    }

    @Test
    fun should_dialogNull_when_onCreate() {
        var activity = activityController?.create()?.start()?.get()
        Assert.assertTrue(activity?.dialog != null)
        activity = activityController?.destroy()?.get()
        Assert.assertTrue(activity?.dialog == null)
    }

    @Test
    fun should_dialogNotNull_when_onCreate() {
        val activity = activityController?.create()?.start()?.get()
        Assert.assertTrue(activity?.dialog != null)
    }
}