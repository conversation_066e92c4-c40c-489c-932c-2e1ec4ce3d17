import ajax from './ajax'

// 在实际开发中，需将 baseUrl 替换为真实的请求地址前缀
const baseUrl = 'https://api.exampel.com/'

/**
 * 上报埋点数据
 * @param reportData 需要上报的埋点数据对象
 */
export function reportDataToClouds(reportData) {
  // 实际开发中，需替换为真实地址
  const url = `${baseUrl}example/report`

  // ajax.post(url, reportData)
}

/**
 * 获取2x2卡片数据
 * @return {Promise} 返回promise对象
 */
export function getSmallCardData(data) {
  // 实际开发中，需替换为真实地址
  const url = `${baseUrl}example/small/data`

  // 实际开发会发送真实的请求
  // return ajax.get(url, data)
  // 此处使用 mock 数据进行演示
  return new Promise((resolve, reject) => {
    // 使用 setTimeout 模拟网络请求
    setTimeout(() => {
      // 模拟请求成功
      let title, desc
      switch (data.lang) {
        case 'zh-CN':
          title = '卡片模板'
          desc = '这是一个官方的 2x2 卡片模板示例！！！'
          break
        case 'zh-HK':
          title = '卡片範本'
          desc = '這是一個官方的 2x2 卡片範本示例！！！'
          break
        case 'en-US':
          title = 'Card Template'
          desc = 'This is an official 2x2 card template example!!!'
          break
      }
      resolve({
        code: 200,
        data: {
          imgUrl: './common/images/cover.png',
          title,
          desc
        }
      })
      // 模拟请求失败
      // reject({
      //   code: 500,
      //   msg: 'request fail!!!'
      // })
    }, 300)
  })
}

