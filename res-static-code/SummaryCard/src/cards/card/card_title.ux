<import name="widget-title" src="./components/title/index.ux"></import>

<template>
  <widget-title is-dark="{{ isDark }}" ></widget-title>
</template>

<script>
/**
 * @file 空白模板 - 标题模块
 */
export default {
  props: {
    isDark: {
      type: Boolean,
      default: false,
    },
    hasRight: {
      type: Boolean,
      default: true,
    },
  },

  onRightClick() {},
}
</script>

<style lang="less">
.logo {
  width: 100%;
  height: 100%;
  background-image: url('./images/logo.png');
  background-size: contain;
  background-repeat: no-repeat;
}
</style>
