<import name="skeleton-item" src="../skeleton-item/index.ux"></import>

<template>
  <div class="skeleton-screen">
    <skeleton-item
      width="50%"
      height="{{ 22 }}"
      is-dark="{{ isDark }}"
    ></skeleton-item>
    <skeleton-item
      width="100%"
      height="{{ 30 }}"
      margin-top="{{ 6 }}"
      is-dark="{{ isDark }}"
    ></skeleton-item>
  </div>
</template>

<script>
export default {
  props: {
    isDark: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss">
@import '../../common/css/common.scss';

.skeleton-screen {
  flex-direction: column;
  justify-content: flex-end;
  width: 100%;
  height: 100%;
  padding: 16 * $size-factor;
}
</style>