<template>
  <div class="widgetui-error">
    <div class="img">
      <image src="../../images/error-state.png"></image>
    </div>
    <div class="null"></div>
    <div class="text-content">
      <text class="text {{ bgColor }}">{{
        type === 'text'
          ? $t('message.generated.convert_text_error')
          : $t('message.generated.summarize_generate_error')
      }}</text> 
    </div>
    <div class="description">
      <text class="text {{ bgColor }}">{{ description }}</text>
    </div>
  </div>
</template>
 
<script>
/** 
 * @file 缺省页组件
 */
// const widgetType = {
//   small: 'small',
//   medium: 'medium',
//   large: 'large',
// }

export default {
  props: {
    isDark: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '', // 	录音过短，不支持摘要生成
    },
    titleColor: {
      type: String,
      default: '',
    },
    description: {
      type: String,
      default: '',
    },
    descriptionColor: {
      type: String,
      default: '',
    },
  },

  computed: {
    titleStyle() {
      if (this.titleColor) {
        return {
          color: this.titleColor,
        }
      }

      return {
        color: this.isDark
          ? 'rgba(255, 255, 255, 0.85)'
          : 'rgba(0, 0, 0, 0.85)',
      }
    },

    descriptionTextStyle() {
      if (this.descriptionColor) {
        return {
          color: this.descriptionColor,
        }
      }

      return {
        color: this.isDark ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
      }
    },

    imageStyle() {
      let width = 120,
        height = 90

      if (this.type === widgetType.large) {
        width = 168
        height = 120
      }

      return {
        marginTop: this.type === widgetType.large ? '0' : '20px',
        width: `${width}px`,
        height: `${height}px`,
      }
    },
    bgColor() {
      if (this.isDark) {
        return 'dark-bg'
      }
      return 'light-bg'
    },
  },
}
</script>

<style lang="less">
.widgetui-error {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  height: 100%;
  padding-top: 23dp;
  justify-content: center;
  align-items: flex-start;
  .img {
    width: 100%;
    height: 40dp;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    image {
      width: 40dp;
      height: 40dp;
    }
  }
  .null{
    width: 100%;
    height: 8dp;
  }
  .text-content {
    height: 20dp;
    width: 100%;
    align-self: center;
    text-align: center;
    flex-wrap: wrap;
    display: flex;
    justify-content: center;
    align-items: center;
    .text {
      font-family: OPPO Sans 4 SC;
      font-size: 14px;
      font-weight: 600;
      line-height: 20dp;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
    }
    .dark-bg {
      color: #ffffff;
    }
    .light-bg {
      color: #000000;
    }
  }

  .description {
    margin-top: 2dp;
    height: 32dp;
    width: 100%;
    flex-wrap: wrap;
    display: flex;
    justify-content: space-around;
    align-content: center;
    .text {
      font-size: 12dp;
      /* padding-top: 16px; */
      line-height: 16dp;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      lines: 2;
      text-overflow: ellipsis;
      padding:  0dp 10dp;
    }
    .dark-bg {
      color: #ffffff;
      opacity: 0.54;
    }
    .light-bg {
      color: rgba(0, 0, 0, 0.54);
    }
  }
}
</style>
