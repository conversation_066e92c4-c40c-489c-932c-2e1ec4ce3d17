package com.soundrecorder.base.splitwindow

import android.graphics.Rect
import android.view.View
import com.soundrecorder.base.utils.DebugUtil

abstract class WindowLayoutChangeListener : View.OnLayoutChangeListener {

    private val TAG = "WindowLayoutChangeListener"

    private var current: Rect = Rect()
    private var old: Rect = Rect()

    override fun onLayoutChange(
        v: View?,
        left: Int,
        top: Int,
        right: Int,
        bottom: Int,
        oldLeft: Int,
        oldTop: Int,
        oldRight: Int,
        oldBottom: Int
    ) {
        current.set(left, top, right, bottom)
        old.set(oldLeft, oldTop, oldRight, oldBottom)
        if (current == old) {
            return
        }
        DebugUtil.d(TAG, "onLayoutChange new:$current old:$old")
        onLayoutChange(v, current, old)
    }

    /**
     * 判断Rect的上下左右是否都为0
     */
    fun isZero(rect: Rect): Boolean {
        val zero = Rect()
        return rect == zero
    }

    abstract fun onLayoutChange(v: View?, rect: Rect, oldRect: Rect)
}