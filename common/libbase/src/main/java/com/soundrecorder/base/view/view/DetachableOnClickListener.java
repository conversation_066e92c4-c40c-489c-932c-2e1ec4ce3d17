/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.view;

import android.app.Dialog;
import android.content.DialogInterface;
import android.view.ViewTreeObserver;

public final class DetachableOnClickListener implements DialogInterface.OnClickListener {

    private DialogInterface.OnClickListener mDelegateOrNull;

    private DetachableOnClickListener(DialogInterface.OnClickListener delegate) {
        this.mDelegateOrNull = delegate;
    }

    public static DetachableOnClickListener wrap(DialogInterface.OnClickListener delegate) {
        return new DetachableOnClickListener(delegate);
    }

    public void clearOnDetach(Dialog dialog) {
        dialog.getWindow()
                .getDecorView()
                .getViewTreeObserver()
                .addOnWindowAttachListener(new ViewTreeObserver.OnWindowAttachListener() {
                    @Override
                    public void onWindowAttached() {
                    }

                    @Override
                    public void onWindowDetached() {
                        mDelegateOrNull = null;
                    }
                });
    }


    @Override
    public void onClick(DialogInterface dialogInterface, int i) {
        if (mDelegateOrNull != null) {
            mDelegateOrNull.onClick(dialogInterface, i);
        }
    }
}