/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.view;

import android.media.AudioManager;

public class DetachAudioFocusChangeListener implements AudioManager.OnAudioFocusChangeListener {

    private AudioManager.OnAudioFocusChangeListener mDelegateOrNull;

    private DetachAudioFocusChangeListener(AudioManager.OnAudioFocusChangeListener delegate) {
        this.mDelegateOrNull = delegate;
    }

    public static DetachAudioFocusChangeListener wrap(AudioManager.OnAudioFocusChangeListener delegate) {
        return new DetachAudioFocusChangeListener(delegate);
    }

    public void clearOnDetach() {
        mDelegateOrNull = null;
    }

    @Override
    public void onAudioFocusChange(int i) {
        if (mDelegateOrNull != null) {
            mDelegateOrNull.onAudioFocusChange(i);
        }
    }
}
