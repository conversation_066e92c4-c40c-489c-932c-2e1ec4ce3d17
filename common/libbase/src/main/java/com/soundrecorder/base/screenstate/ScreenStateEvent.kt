package com.soundrecorder.base.screenstate

import android.content.Context
import android.hardware.display.DisplayManager
import android.view.Display
import android.hardware.display.DisplayManager.DisplayListener
import com.soundrecorder.base.utils.DebugUtil

/**
 * 锁屏改为监听的方式 for bug 2533039
 */
class ScreenStateEvent(private val mContext: Context) {
    private var mIsScreenOn = false
    private var mScreenFirstState = true
    private var mScreenLastState: Boolean = mIsScreenOn
    private var mDisplayManager: DisplayManager? = null
    private var mIScreenStateListener: IScreenStateListener? = null
    private fun updateScreenState() {
        // Note that we don't listen to Intent.SCREEN_ON and Intent.SCREEN_OFF because they are no
        // longer adequate for monitoring the screen state since they are not sent in cases where
        // the screen is turned off transiently such as due to the proximity sensor.
        val displays = mDisplayManager?.displays
        if (displays != null) {
            for (display in displays) {
                // Anything other than STATE_ON is treated as screen off, such as STATE_DOZE,
                // STATE_DOZE_SUSPEND, etc...
                if (display.state == Display.STATE_ON) {
                    DebugUtil.d(TAG, "Screen " + display.state + " on")
                    mIsScreenOn = true
                    return
                }
            }
            DebugUtil.d(TAG, "Screens all off")
            mIsScreenOn = false
            return
        }
        DebugUtil.d(TAG, "No displays found")
        mIsScreenOn = false
    }

    private val mDisplayListener: DisplayListener = object : DisplayListener {
        override fun onDisplayAdded(displayId: Int) {}
        override fun onDisplayRemoved(displayId: Int) {}
        override fun onDisplayChanged(displayId: Int) {
            updateScreenState()
            if (mScreenFirstState || mIsScreenOn != mScreenLastState) {
                mScreenFirstState = false
                mScreenLastState = mIsScreenOn
                if (mIsScreenOn) {
                    mIScreenStateListener?.onScreenOn()
                } else {
                    mIScreenStateListener?.onScreenOff()
                }
            }
        }
    }

    fun registerDisplay(mIScreenStateListener: IScreenStateListener?) {
        DebugUtil.d(TAG, "registerDisplay")
        this.mIScreenStateListener = mIScreenStateListener
        mDisplayManager?.registerDisplayListener(mDisplayListener, null)
    }

    fun unregisterDisplay() {
        DebugUtil.d(TAG, "unregisterDisplay")
        if (mDisplayManager != null) {
            mDisplayManager?.unregisterDisplayListener(mDisplayListener)
            mDisplayManager = null
            mIScreenStateListener = null
        }
    }

    companion object {
        private const val TAG = "ScreenStateEvent"
    }

    init {
        try {
            mDisplayManager = mContext.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
            updateScreenState()
        } catch (tr: Throwable) {
            DebugUtil.e(TAG, "DisplayManager init error", tr)
        }
        mScreenLastState = mIsScreenOn
    }

    fun getScreenOn(): Boolean {
        return mIsScreenOn
    }
}