/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: UserChangeManager
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/8/29 1.0 create
 */

package com.soundrecorder.base.userchange

import androidx.lifecycle.Observer

object UserChangeManager {

    private val userChangeLiveData: UserChaneLiveData = UserChaneLiveData()

    @JvmStatic
    fun initObserveForever(observer: Observer<Boolean>) {
        userChangeLiveData.observeForever(observer)
    }

    @JvmStatic
    fun removeObserver(observer: Observer<Boolean>) {
        userChangeLiveData.removeObserver(observer)
    }
}