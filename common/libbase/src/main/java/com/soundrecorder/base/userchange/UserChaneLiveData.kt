/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: UserChaneLiveData
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/8/29 1.0 create
 */

package com.soundrecorder.base.userchange

import android.content.Context.RECEIVER_NOT_EXPORTED
import android.content.Intent
import android.content.IntentFilter
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.CustomMutableLiveData
import com.soundrecorder.base.utils.DebugUtil

class UserChaneLiveData : CustomMutableLiveData<Boolean>(), UserChangeReceiver.UserChangeListener {

    companion object {
        private const val TAG = "UserChaneLiveData"
    }

    private var mUserChangeReceiver: UserChangeReceiver? = null

    @Suppress("TooGenericExceptionCaught")
    private fun registerUserChangerReceiver() {
        try {
            if (mUserChangeReceiver == null) {
                mUserChangeReceiver = UserChangeReceiver()
            }
            mUserChangeReceiver?.setUserChangeListener(this)
            BaseApplication.getApplication().registerReceiver(
                mUserChangeReceiver,
                IntentFilter(Intent.ACTION_USER_BACKGROUND),
                RECEIVER_NOT_EXPORTED
            )
        } catch (e: Exception) {
            DebugUtil.d(TAG, e.message)
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun unregisterUserChangerReceiver() {
        try {
            if (mUserChangeReceiver != null) {
                BaseApplication.getApplication().unregisterReceiver(mUserChangeReceiver)
                mUserChangeReceiver?.setUserChangeListener(null)
                mUserChangeReceiver = null
            }
        } catch (e: Exception) {
            DebugUtil.d(TAG, e.message)
        }
    }

    override fun doUserChange() {
        DebugUtil.d(TAG, "UserChange")
        setValue(true)
    }

    override fun onActive() {
        unregisterUserChangerReceiver()
        registerUserChangerReceiver()
    }

    override fun onInactive() {
        unregisterUserChangerReceiver()
    }
}