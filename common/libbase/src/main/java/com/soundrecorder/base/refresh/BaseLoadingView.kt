package com.soundrecorder.base.refresh

import android.content.Context
import android.util.AttributeSet
import android.view.ViewGroup
import android.widget.FrameLayout

/**
 * Pull down the refreshed base class, inheriting the class can implement different styles of refresh headers, but the basic refresh process is unchanged, are:
 * Pulldown - release - load (hover) - end of the rebound
 * Refresh the header, there are 4 states:
 * a, pull-down refresh
 * b, release immediate refresh
 * c, refreshing
 * d, refresh completed
 */
abstract class BaseLoadingView(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    FrameLayout(context, attrs, defStyleAttr) {
    companion object {
        const val HEADER_DRAG = 0x001 //Pull down to refresh
        const val HEADER_RELEASE = 0x002 //Release immediate refresh
        const val HEADER_REFRESHING = 0x003 //Refreshing
        const val HEADER_COMPLETED = 0x004 //Refresh complete
    }

    protected var mCanTranslation = true

    var mDragDistanceThreshold = 0

    /**
     * Constantly callback BounceLayout pull distance
     */
    abstract fun handleDrag(dragY: Float)

    /**
     * Drop-down header to determine if refresh loading is possible
     */
    abstract fun doRefresh(): Boolean
    abstract val isRefreshing: Boolean

    /**
     * Set parent layout
     */
    abstract fun setParent(parent: ViewGroup?)

    /**
     * Lift your finger to check if you need to refresh
     */
    abstract fun checkRefresh(): Boolean

    /**
     * Refresh completed
     */
    abstract fun refreshCompleted()

    /**
     * Get loadingView's height
     */
    abstract val loadingViewHeight: Int

    /**
     * Automatic loading
     */
    abstract fun autoRefresh()

    /**
     * Loose hand refresh
     */
    abstract fun releaseToRefresh()

    fun setCanTranslation(mCanTranslation: Boolean) {
        this.mCanTranslation = mCanTranslation
    }

    abstract fun setRefreshEnable(enable: Boolean)
}