package com.soundrecorder.base.utils;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;

import java.util.List;

public class OplusCompactUtil {

    private static final String TAG = "OplusCompactUtil";
    private static final int OPLUS_OS_11_2 = 21;

    public static int getOsVersion() {
      return AddonAdapterCompatUtil.getOplusOSVERSION();
    }

    public static boolean isOver11dot3() {
        return AddonAdapterCompatUtil.getOplusOSVERSION() > OPLUS_OS_11_2;
    }

    public static Intent getActionForIntent(Intent intent, String actionBefore, String actionAfter) {
        if (intent == null) {
            return null;
        }
        if (isOver11dot3()) {
            intent.setAction(actionAfter);
        } else {
            intent.setAction(actionBefore);
        }
        return intent;
    }

    public static String checkSupportActionActivityPackageName(Context context, String action) {
        String mSupportPackageName = null;
        try {
            Intent intent = new Intent(action);
            PackageManager pm = context.getPackageManager();
            if (pm != null) {
                List<ResolveInfo> resolveInfos = pm.queryIntentActivities(intent, PackageManager.GET_RESOLVED_FILTER);
                if (resolveInfos.size() != 0) {
                    ActivityInfo activityInfo = resolveInfos.get(0).activityInfo;
                    if (activityInfo != null) {
                        mSupportPackageName = activityInfo.packageName;
                        return mSupportPackageName;
                    }
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "checkSupportActionActivityPackageName", e);
        }
        return mSupportPackageName;
    }
}
