/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: StorageUtilTest
 * Description:
 * Version: 1.0
 * Date: 2023/10/19
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/10/19 1.0 create
 */

package com.soundrecorder.base.utils

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog
import java.io.File

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowLog::class, ShadowFeatureOption::class])
class StorageUtilTest {
    var context: Context? = null

    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
    }

    @Test
    fun should_correct_when_getAvailableSpace() {
        val context = context ?: return
        val mockAddonUtil = Mockito.mockStatic(AddonAdapterCompatUtil::class.java)
        mockAddonUtil.`when`<File> { AddonAdapterCompatUtil.getInternalSdDirectory(context) }
            .thenReturn(null, File(""))
        mockAddonUtil.`when`<Boolean> { AddonAdapterCompatUtil.isInternalSdMounted(context) }
            .thenReturn(false, true)

        val space = StorageUtil.getAvailableSpace(context)
        Assert.assertEquals(-1, space)

        StorageUtil.getAvailableSpace(context)

        mockAddonUtil.close()
    }
}