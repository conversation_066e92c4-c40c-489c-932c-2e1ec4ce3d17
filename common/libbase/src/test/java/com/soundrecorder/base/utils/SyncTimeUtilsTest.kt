/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SyncTimeUtilsTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/12/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowLog::class])
class SyncTimeUtilsTest {

    @Test
    fun should_equals_when_getTime() {
        val curTime = 1000L
        var newTime = SyncTimeUtils.getTime(1, curTime)
        Assert.assertEquals(SyncTimeUtils.TIME_5_MIN + 1000, newTime)

        newTime = SyncTimeUtils.getTime(2, curTime)
        Assert.assertEquals(SyncTimeUtils.TIME_30_MIN + 1000, newTime)

        newTime = SyncTimeUtils.getTime(3, curTime)
        Assert.assertEquals(SyncTimeUtils.TIME_1_HOUR + 1000, newTime)

        newTime = SyncTimeUtils.getTime(4, curTime)
        Assert.assertEquals(SyncTimeUtils.TIME_3_HOUR + 1000, newTime)

        newTime = SyncTimeUtils.getTime(5, curTime)
        Assert.assertEquals(SyncTimeUtils.TIME_6_HOUR + 1000, newTime)

        newTime = SyncTimeUtils.getTime(6, curTime)
        Assert.assertEquals(SyncTimeUtils.TIME_6_HOUR + 1000, newTime)
    }

    @Test
    fun should_equals_when_getRetryDelayTime() {
        var newTime = SyncTimeUtils.getRetryDelayTime(1)
        Assert.assertTrue(newTime in 300000..360000)

        newTime = SyncTimeUtils.getRetryDelayTime(2)
        Assert.assertTrue(newTime in 1800000..1860000)

        newTime = SyncTimeUtils.getRetryDelayTime(3)
        Assert.assertTrue(newTime in 3600000..3660000)

        newTime = SyncTimeUtils.getRetryDelayTime(4)
        Assert.assertTrue(newTime in 10800000..10860000)

        newTime = SyncTimeUtils.getRetryDelayTime(5)
        Assert.assertTrue(newTime in 21600000..21660000)

        newTime = SyncTimeUtils.getRetryDelayTime(8)
        Assert.assertTrue(newTime in 21600000..21660000)
    }
}