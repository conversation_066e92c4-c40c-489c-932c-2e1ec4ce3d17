/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  UrlEncodeUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowFeatureOption::class]
)
class UrlEncodeUtilText {

    @Test
    fun should_empty_when_encode() {
        val urlEncodeStr = UrlEncodeUtil.encode(null)
        Assert.assertTrue(urlEncodeStr.isEmpty())
    }

    @Test
    fun should_not_empty_when_encode() {
        var content = "标准录音 1.mp3"
        var urlEncodeStr = UrlEncodeUtil.encode(content)
        Assert.assertTrue(urlEncodeStr.isNotEmpty())

        content = "标准录音&1.mp3"
        urlEncodeStr = UrlEncodeUtil.encode(content)
        Assert.assertTrue(urlEncodeStr.isNotEmpty())

        content = "123.mp3"
        urlEncodeStr = UrlEncodeUtil.encode(content)
        Assert.assertTrue(urlEncodeStr.isNotEmpty())

        content = "make_file.mp3"
        urlEncodeStr = UrlEncodeUtil.encode(content)
        Assert.assertTrue(urlEncodeStr.isNotEmpty())

        content = "!@#$%^&*().mp3"
        urlEncodeStr = UrlEncodeUtil.encode(content)
        Assert.assertTrue(urlEncodeStr.isNotEmpty())
    }

    @Test
    fun should_empty_when_decode() {
        val urlDecodeStr = UrlEncodeUtil.decode(null)
        Assert.assertTrue(urlDecodeStr.isEmpty())
    }

    @Test
    fun should_not_empty_when_decode() {
        var content = "标准录音 1.mp3"
        var urlEncodeStr = UrlEncodeUtil.encode(content)
        var urlDecodeStr = UrlEncodeUtil.decode(urlEncodeStr)
        Assert.assertEquals(content, urlDecodeStr)

        content = "标准录音&1.mp3"
        urlEncodeStr = UrlEncodeUtil.encode(content)
        urlDecodeStr = UrlEncodeUtil.decode(urlEncodeStr)
        Assert.assertEquals(content, urlDecodeStr)

        content = "123.mp3"
        urlEncodeStr = UrlEncodeUtil.encode(content)
        urlDecodeStr = UrlEncodeUtil.decode(urlEncodeStr)
        Assert.assertEquals(content, urlDecodeStr)

        content = "make_file.mp3"
        urlEncodeStr = UrlEncodeUtil.encode(content)
        urlDecodeStr = UrlEncodeUtil.decode(urlEncodeStr)
        Assert.assertEquals(content, urlDecodeStr)

        content = "!@#$%^&*().mp3"
        urlEncodeStr = UrlEncodeUtil.encode(content)
        urlDecodeStr = UrlEncodeUtil.decode(urlEncodeStr)
        Assert.assertEquals(content, urlDecodeStr)
    }
}