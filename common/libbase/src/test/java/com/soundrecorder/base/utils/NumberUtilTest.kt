/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  NumerUtilTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class NumberUtilTest {

    companion object {
        private const val FLOAT_1_12 = 1.12f
        private const val FLOAT_1_15 = 1.15f
        private const val FLOAT_DELTA_1 = 0.1f
        private const val FLOAT_DELTA_3 = 0.001f

        private const val TEST = "test"
        private const val EMPTY_STRING = ""
        private const val STRING_ONE_TWO_THREE = "123"
        private const val INT_ONE_TWO_THREE = 123
        private const val LONG_STRING = "12345678910"
        private const val LONG_NUM = 12345678910L
        private const val LONG_ONE = 1L
        private const val INT_ONE = 1
    }

    @Test
    fun should_correct_when_floatIsEqual_with_differenceLessThanDelta() {
        var res: Boolean = NumberUtil.floatEqual(FLOAT_1_12, FLOAT_1_15, FLOAT_DELTA_1)
        Assert.assertTrue(res)
        res = NumberUtil.floatEqual(FLOAT_1_12, FLOAT_1_15, FLOAT_DELTA_3)
        Assert.assertFalse(res)
    }

    @Test
    fun should_returnTrue_when_floatEqual() {
        Assert.assertFalse(NumberUtil.floatEqual(0f, 1f))
        Assert.assertFalse(NumberUtil.floatEqual(0f, 0.0000002f))
        Assert.assertTrue(NumberUtil.floatEqual(0f, 0.00000001f))
    }

    @Test
    fun should_returnTrue_when_getFloatString() {
        Assert.assertTrue(NumberUtil.getFloatString(FLOAT_DELTA_3) == FLOAT_DELTA_3.toString())
        Assert.assertTrue(NumberUtil.getFloatString(1.0f) == "1")
    }

    @Test
    fun should_returnTrue_when_parseStr2Int() {
        Assert.assertTrue(NumberUtil.parseStr2Int("s", 0) == 0)
        Assert.assertTrue(NumberUtil.parseStr2Int(FLOAT_DELTA_1.toString(), 0) == 0)
    }

    @Test
    fun should_correct_when_parseInt_different_inputs() {
        val result: Int? = NumberUtil.parseInt(STRING_ONE_TWO_THREE, null)
        Assert.assertEquals(INT_ONE_TWO_THREE.toLong(), result?.toLong())
        var result1: Int? = NumberUtil.parseInt(null, null)
        var result2: Int? = NumberUtil.parseInt(EMPTY_STRING, null)
        var result3: Int? = NumberUtil.parseInt(TEST, null)
        Assert.assertNull(result1)
        Assert.assertNull(result2)
        Assert.assertNull(result3)
        result1 = NumberUtil.parseInt(null, INT_ONE)
        result2 = NumberUtil.parseInt(EMPTY_STRING, INT_ONE)
        result3 = NumberUtil.parseInt(STRING_ONE_TWO_THREE, INT_ONE)
        Assert.assertEquals(INT_ONE.toLong(), result1?.toLong())
        Assert.assertEquals(INT_ONE.toLong(), result2?.toLong())
        Assert.assertEquals(INT_ONE_TWO_THREE.toLong(), result3?.toLong())
    }

    @Test
    fun should_correct_when_parseLong_different_inputs() {
        val result: Long? = NumberUtil.parseLong(LONG_STRING, null)
        var result1: Long? = NumberUtil.parseLong(null, null)
        var result2: Long? = NumberUtil.parseLong(EMPTY_STRING, null)
        var result3: Long? = NumberUtil.parseLong(TEST, null)
        Assert.assertEquals(LONG_NUM, result)
        Assert.assertNull(result1)
        Assert.assertNull(result2)
        Assert.assertNull(result3)
        result1 = NumberUtil.parseLong(null, LONG_ONE)
        result2 = NumberUtil.parseLong(EMPTY_STRING, LONG_ONE)
        result3 = NumberUtil.parseLong(LONG_STRING, LONG_ONE)
        Assert.assertEquals(LONG_ONE, result1)
        Assert.assertEquals(LONG_ONE, result2)
        Assert.assertEquals(LONG_NUM, result3)
    }
}