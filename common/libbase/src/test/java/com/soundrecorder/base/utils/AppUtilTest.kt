/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  AppUtilTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils

import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.content.pm.ResolveInfo
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.text.TextUtils
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.shadows.ShadowFeatureOption
import com.soundrecorder.base.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.ArgumentMatchers.anyString
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.mockito.invocation.InvocationOnMock
import org.robolectric.Shadows
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowLog::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class AppUtilTest {
    private var mTextUtilsMockedStatic: MockedStatic<TextUtils>? = null
    private var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        mTextUtilsMockedStatic = Mockito.mockStatic(TextUtils::class.java)
        mTextUtilsMockedStatic?.`when`<Any> { TextUtils.isEmpty(ArgumentMatchers.any()) }?.thenAnswer { invocation: InvocationOnMock ->
            val a = invocation.getArgument<CharSequence>(0)
            if (a == null || a.isEmpty()) {
                return@thenAnswer true
            }
            false
        }
    }

    @After
    fun release() {
        mTextUtilsMockedStatic?.close()
        mTextUtilsMockedStatic = null
        context = null
    }

    @Test
    fun should_corrent_when_metaDataEquals() {
        val isflag = AppUtil.metaDataEquals("test", "test")
        Assert.assertFalse(isflag)
        Assert.assertFalse(AppUtil.metaDataEquals(null, "test"))

        Assert.assertFalse(AppUtil.isActionSupport(null, "test"))
        Assert.assertFalse(AppUtil.isActionSupport("test", ""))
        Assert.assertFalse(AppUtil.isActionSupport("test", "test"))

        val packageManager = BaseApplication.getAppContext().packageManager
        val shadowPackageManager = Shadows.shadowOf(packageManager)
        shadowPackageManager.addPackage(PackageInfo().apply {
            packageName = "test"
            versionName = "1.0"
        })
        shadowPackageManager.addResolveInfoForIntentNoDefaults(Intent("test"), ResolveInfo())
        Assert.assertTrue(AppUtil.isActionSupport("test", "test"))
        Assert.assertFalse(AppUtil.metaDataEquals("test", "test"))
    }

    @Test
    fun should_correct_when_getAppVersion() {
        Assert.assertEquals("", AppUtil.getAppVersion("test"))
        Assert.assertFalse(AppUtil.isAppInstalled(""))
        Assert.assertFalse(AppUtil.isAppInstalled("test"))

        val mockContext = Mockito.mock(Context::class.java)
        val mockApplication = Mockito.mockStatic(BaseApplication::class.java)
        mockApplication!!.`when`<Any> { BaseApplication.getAppContext() }?.thenReturn(mockContext)

        val mockPkgInfo = Mockito.spy(PackageInfo::class.java)
        val mockPkgManager = Mockito.mock(PackageManager::class.java)
        mockPkgInfo.packageName = "test"
        mockPkgInfo.versionName = "1"
        mockPkgInfo.applicationInfo = ApplicationInfo().apply {
            packageName = "test"
            metaData = Bundle().apply {
                putString("versionCommit", "aaa")
                putString("versionDate", "09_10")
            }
        }
        Mockito.`when`(mockPkgManager.getPackageInfo(anyString(), anyInt())).thenReturn(mockPkgInfo)
        Mockito.`when`(mockContext.packageName).thenReturn("test")
        Mockito.`when`(mockContext.packageManager).thenReturn(mockPkgManager)

        Assert.assertEquals("1", AppUtil.getAppVersion())
        Assert.assertEquals("test", AppUtil.getAppName())
        Assert.assertTrue(AppUtil.isAppInstalled("test"))
        // 需要textutils。contact方法
        Assert.assertEquals(/*"1_aaa_9010"*/null, AppUtil.getAppVersionName())

        mockApplication.close()
    }

    @Test
    fun should_when_metaDataInt() {
        var result = AppUtil.metaDataInt("", "key")
        Assert.assertEquals(-1, result)

        val mockContext = Mockito.mock(Context::class.java)
        val mockApplication = Mockito.mockStatic(BaseApplication::class.java)
        mockApplication!!.`when`<Any> { BaseApplication.getAppContext() }?.thenReturn(mockContext)
        val mockPkgInfo = Mockito.spy(ApplicationInfo::class.java)
        val mockPkgManager = Mockito.mock(PackageManager::class.java)
        mockPkgInfo.packageName = "test"
        val applicationInfo = ApplicationInfo().apply {
            packageName = "test"
            metaData = Bundle().apply {
                putInt("key", 1)
            }
        }
        Mockito.`when`(mockPkgManager.getApplicationInfo(anyString(), anyInt()))
            .thenReturn(applicationInfo)
        Mockito.`when`(mockContext.packageName).thenReturn("test")
        Mockito.`when`(mockContext.packageManager).thenReturn(mockPkgManager)

        result = AppUtil.metaDataInt("test", "key")
        Assert.assertEquals(1, result)

        applicationInfo.metaData = null
        result = AppUtil.metaDataInt("test", "key")
        Assert.assertEquals(-1, result)

        mockApplication.close()
    }

    @Test
    fun should_correct_when_isBreathModeNotContainApp() {
        val context = context ?: return
        Settings.Secure.putInt(context.contentResolver, AppUtil.IS_BREATH_MODE, 0)
        Assert.assertFalse(AppUtil.isBreathModeNotContainApp(context, ""))

        Settings.Secure.putInt(context.contentResolver, AppUtil.IS_BREATH_MODE, 1)
        Settings.Secure.putString(context.contentResolver, AppUtil.BREATH_MODE_WHITE_LIST, "com")
        Assert.assertTrue(AppUtil.isBreathModeNotContainApp(context, "1"))
        Assert.assertFalse(AppUtil.isBreathModeNotContainApp(context, "com"))
    }
}