package com.soundrecorder.base.refresh;

import android.content.Context;
import android.os.Build;
import android.view.ViewGroup;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.shadows.ShadowFeatureOption;
import com.soundrecorder.base.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.base.shadows.ShadowOplusUsbEnvironment;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOplusUsbEnvironment.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class DefaultHeaderTest {

    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @After
    public void tearDown() {
        mContext = null;
    }

    @Test
    public void should_returnTrue_when_doRefresh() {
        DefaultHeader header = new DefaultHeader(mContext, null, 0);
        Whitebox.setInternalState(header, "mStatus", BaseLoadingView.HEADER_REFRESHING);
        int state = Whitebox.getInternalState(header, "mStatus");
        Assert.assertEquals(BaseLoadingView.HEADER_REFRESHING, state);
        Assert.assertFalse(header.doRefresh());
    }

    @Test
    public void should_returnTrue_when_setParent() {
        DefaultHeader header = new DefaultHeader(mContext, null, 0);
        ViewGroup viewGroup = new ViewGroup(mContext) {
            @Override
            protected void onLayout(boolean changed, int l, int t, int r, int b) {

            }
        };
        header.setParent(viewGroup);
        Assert.assertNotNull(viewGroup.getChildAt(0));
    }

    @Test
    public void should_returnTrue_when_checkRefresh() {
        DefaultHeader header = new DefaultHeader(mContext, null, 0);
        Assert.assertFalse(header.checkRefresh());
    }

    @Test
    public void should_returnTrue_when_refreshCompleted() {
        DefaultHeader header = new DefaultHeader(mContext, null, 0);
        header.refreshCompleted();
        int state = Whitebox.getInternalState(header, "mStatus");
        Assert.assertEquals(BaseLoadingView.HEADER_COMPLETED, state);
    }

    @Test
    public void should_returnTrue_when_autoRefresh() {
        DefaultHeader header = new DefaultHeader(mContext, null, 0);
        header.autoRefresh();
        int state = Whitebox.getInternalState(header, "mStatus");
        Assert.assertEquals(BaseLoadingView.HEADER_REFRESHING, state);
    }

    @Test
    public void should_returnTrue_when_isRefreshing() {
        DefaultHeader header = new DefaultHeader(mContext, null, 0);
        Whitebox.setInternalState(header, "mStatus", BaseLoadingView.HEADER_REFRESHING);
        Assert.assertTrue(header.isRefreshing());
    }
}
