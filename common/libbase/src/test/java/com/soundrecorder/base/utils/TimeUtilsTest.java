package com.soundrecorder.base.utils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mockStatic;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.shadows.ShadowFeatureOption;
import com.soundrecorder.base.R;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;

import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class TimeUtilsTest {
    private static final int SS = 1000;
    private static final int MI = SS * 60;
    private static final int HH = MI * 60;
    private static final int DD = HH * 24;
    private static final int INT_ZERO = 0;
    private static final int INT_TEN = 10;
    private static final int INT_MINUS_TEN = -10;

    private static final String EMPTY_STRING = "";
    private static final String TEN_SECONDS = "00:00:10";
    private static final String ONE_HOUR_FIVE_MINUTES = "1:05:30";
    private static final String EIGHT_HOURS_THIRTY_MINUTES = "8:30:30";
    private static final String ONE_DAY_SEVEN_HOURS = "1:07:30:30";

    private static final String TWO_DAYS_EIGHT_HOURS = "2:08:30:30.00";
    private static final String FOURTEEN_HOURS_THIRTY_MINUTES = "14:30:30.00";
    private static final String TWO_HOURS_THIRTY_MINUTES = "2:30:30.00";
    private static final String THIRTY_MINUTES = "30:30.00";
    private static final String THIRTY_SECONDS = "00:30.00";

    private static final String THIRTY_SECONDS_TWO = "00:30";

    private Context mContext;
    private MockedStatic<BaseApplication> mMockBaseApplication;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mMockBaseApplication = mockStatic(BaseApplication.class);
        mMockBaseApplication.when(() -> BaseApplication.getAppContext()).thenReturn(mContext);
    }

    @After
    public void tearDown() {
        if (mMockBaseApplication != null) {
            mMockBaseApplication.close();
            mMockBaseApplication = null;
        }
        mContext = null;
    }

    @Test
    public void should_returnFormatTime_when_getSecTime_with_timeFormat() {
        int secTime1 = TimeUtils.getSecTime(EMPTY_STRING);
        int secTime2 = TimeUtils.getSecTime(TEN_SECONDS);
        int secTime3 = TimeUtils.getSecTime(ONE_HOUR_FIVE_MINUTES);
        int secTime4 = TimeUtils.getSecTime(FOURTEEN_HOURS_THIRTY_MINUTES);
        int secTime5 = TimeUtils.getSecTime(THIRTY_SECONDS_TWO);
        int expectTime2 = 10 * SS;
        int expectTime3 = 1 * HH + 5 * MI + 30 * SS;
        int expectTime4 = 14 * HH + 30 * MI + 30 * SS;
        int expectTime5 = 30 * SS;
        assertEquals(INT_ZERO, secTime1);
        assertEquals(expectTime2, secTime2);
        assertEquals(expectTime3, secTime3);
        assertEquals(expectTime4, secTime4);
        assertEquals(expectTime5, secTime5);
    }

    @Test
    public void should_returnFormatString_when_getformatTimeExclusiveMill_with_time() {
        long dayTime1 = 0 * DD + 8 * HH + 30 * MI + 30 * SS;
        long dayTime2 = 1 * DD + 7 * HH + 30 * MI + 30 * SS;
        String strTime1 = TimeUtils.getFormatTimeExclusiveMill(dayTime1);
        String strTime2 = TimeUtils.getFormatTimeExclusiveMill(dayTime2);
        assertEquals(EIGHT_HOURS_THIRTY_MINUTES, strTime1);
        assertEquals(ONE_DAY_SEVEN_HOURS, strTime2);

        String result = TimeUtils.getFormatTimeExclusiveMill(1, true);
        assertEquals("00:01", result);
    }

    @Test
    public void should_correct_when_getDisplayTimeInSec_different_input() {
        String result = TimeUtils.getDisplayTimeInSec(INT_MINUS_TEN);
        assertEquals(EMPTY_STRING, result);
        result = TimeUtils.getDisplayTimeInSec(INT_TEN);
        assertEquals(TEN_SECONDS, result);
    }

    @Test
    public void should_returnFormatString_when_getformatTimeByMillisecond_with_ms() {
        long longTime1 = 2 * DD + 8 * HH + 30 * MI + 30 * SS;
        long longTime2 = 14 * HH + 30 * MI + 30 * SS;
        long longTime3 = 2 * HH + 30 * MI + 30 * SS;
        long longTime4 = 30 * MI + 30 * SS;
        long longTime5 = 30 * SS;
        String result1 = TimeUtils.getFormatTimeByMillisecond(longTime1);
        assertEquals(TWO_DAYS_EIGHT_HOURS, result1);
        String result2 = TimeUtils.getFormatTimeByMillisecond(longTime2);
        assertEquals(FOURTEEN_HOURS_THIRTY_MINUTES, result2);
        String result3 = TimeUtils.getFormatTimeByMillisecond(longTime3);
        assertEquals(TWO_HOURS_THIRTY_MINUTES, result3);
        String result4 = TimeUtils.getFormatTimeByMillisecond(longTime4);
        assertEquals(THIRTY_MINUTES, result4);
        String result5 = TimeUtils.getFormatTimeByMillisecond(longTime5);
        assertEquals(THIRTY_SECONDS, result5);
    }

    @Test
    public void should_returnBoolean_when_compareTimeCrossBreakpoint_with_inputTime() {
        long dayTimeA = 2 * DD + 8 * HH + 30 * MI + 30 * SS;
        long dayTimeB = 0 * DD + 8 * HH + 30 * MI + 30 * SS;
        long dayTimeC = 1 * DD + 8 * HH + 30 * MI + 30 * SS;
        boolean b1 = TimeUtils.compareTimeCrossBreakpoint(dayTimeA, dayTimeB);
        boolean b2 = TimeUtils.compareTimeCrossBreakpoint(dayTimeA, dayTimeC);
        assertTrue(b1);
        assertFalse(b2);
    }

    @Test
    public void should_returnNotnull_when_getDateString() {
        assertNotNull(TimeUtils.getDateString(100L));
    }

    @Test
    public void should_returnEmptyString_when_getDurationHint_different_context() {
        long longTime = 2 * HH + 30 * MI + 30 * SS;
        String hint = TimeUtils.getDurationHint(null, longTime);
        assertEquals(EMPTY_STRING, hint);
        hint = TimeUtils.getDurationHint(mContext, longTime);
        String res = mContext.getString(R.string.duration_hint_with_hour, 2, 30, 30);
        assertEquals(res, hint);
    }

    @Test
    public void should_returnFormateString_when_getformatContentDiscriptionTimeByMillisecond_whit_context() {
        long longTime1 = DD + 2 * HH + 30 * MI + 30 * SS;
        long longTime2 = 2 * HH + 30 * MI + 30 * SS;
        long longTime3 = 30 * MI + 30 * SS;
        long longTime4 = 30 * SS;
        String time = TimeUtils.getFormatContentDescriptionTimeByMillisecond(null, longTime1);
        assertEquals(EMPTY_STRING, time);
        String time1 = TimeUtils.getFormatContentDescriptionTimeByMillisecond(mContext, longTime1);
        String res1 = mContext.getString(R.string.talkback_day_hour_min_seceod, 1, 2, 30, 30);
        assertEquals(res1, time1);
        String time2 = TimeUtils.getFormatContentDescriptionTimeByMillisecond(mContext, longTime2);
        String res2 = mContext.getString(R.string.duration_hint_with_hour, 2, 30, 30);
        assertEquals(res2, time2);
        String time3 = TimeUtils.getFormatContentDescriptionTimeByMillisecond(mContext, longTime3);
        String res3 = mContext.getString(R.string.duration_hint_with_min, 30, 30);
        assertEquals(res3, time3);
        String time4 = TimeUtils.getFormatContentDescriptionTimeByMillisecond(mContext, longTime4);
        String res4 = mContext.getString(R.string.duration_hint_with_sec, 30);
        assertEquals(res4, time4);
    }
}
