/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ExtTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/6/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.ext

import android.app.Activity
import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.shadows.ShadowFeatureOption
import com.soundrecorder.base.shadows.ShadowOS12FeatureUtil
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.annotation.Config
import java.io.File

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class ExtTest {

    @Test
    fun should_return_false_when_restoreRingMode() {
        val activity = Robolectric.buildActivity(Activity::class.java).get()
        BaseApplication.sNeedToNormalRingMode = true
        activity.finish()
        activity.restoreRingMode()
        Assert.assertFalse(BaseApplication.sNeedToNormalRingMode)
    }

    @Test
    fun should_return_false_when_splitTrimEnd() {
        var str = ""
        var result = str.splitTrimEnd(",")
        Assert.assertNull(result)

        str = "123,345 "
        result = str.splitTrimEnd(",")
        Assert.assertEquals("345", result?.get(1))
    }

    @Test
    fun should_return_false_when_splitOddTrimEnd() {
        var str = ""
        var result = str.splitOddTrimEnd()
        Assert.assertNull(result)

        str = "123 345 "
        result = str.splitOddTrimEnd()
        Assert.assertEquals(4, result?.size)
    }

    @Test
    fun should_return_false_when_title() {
        var str = "12"
        var result = str.title()
        Assert.assertEquals(str, result)

        str = "storage${File.separator}0${File.separator}path${File.separator}name.mp3"
        result = str.title()
        Assert.assertEquals("name", result)
    }

    @Test
    fun should_return_false_when_suffix() {
        var str = "12"
        var result = str.suffix()
        Assert.assertEquals("", result)

        str = "storage${File.separator}0${File.separator}path${File.separator}name.mp3"
        result = str.suffix()
        Assert.assertEquals(".mp3", result)
    }

    @Test
    fun should_equals_when_currentInMsFormatTimeExclusive() {
        //30s
        val curTime = 90 * 1000L
        val result = curTime.currentInMsFormatTimeExclusive(100000)
        Assert.assertEquals("01:30", result)
    }

    @Test
    fun should_equals_when_currentInMsFormatTimeTalkBack() {
        //30s
        val curTime = 30 * 1000L
        val result = curTime.currentInMsFormatTimeTalkBack(ApplicationProvider.getApplicationContext<Context>(), 60000)
        Assert.assertEquals("30 s", result)
    }

    @Test
    fun should_equals_when_displayName() {
        val path = "${File.separator}1.mp3"
        Assert.assertEquals("1.mp3", path.displayName())
        Assert.assertEquals("", "1.mp3${File.separator}".displayName())
    }
}