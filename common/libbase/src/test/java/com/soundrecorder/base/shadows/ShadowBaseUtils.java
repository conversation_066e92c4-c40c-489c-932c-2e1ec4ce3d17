/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : ShadowRecorderUtil.java
 * Version Number : 1.0
 * Description    :
 * Author         : LI Kun
 * Date           : 2019/8/20
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019/8/20, LI Kun, create
 ************************************************************/

package com.soundrecorder.base.shadows;

import android.content.Context;

import com.soundrecorder.base.utils.BaseUtil;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;


@Implements(BaseUtil.class)
public class ShadowBaseUtils {

    private static final String TEST_PHONE_DIR = "test/phoneDir";
    private static final String TEST_SDCARD_DIR = "test/sdcardDir";


    @Implementation
    public static boolean isAndroidROrLater() {
        //for test, Android R noe sdk_int is 29 now;
        return false;
    }

    @Implementation
    public static boolean isSupportMultiRecordMode(Context context) {
        if (null != context) {
            return true;
        }
        return false;
    }

    @Implementation
    public static String getPhoneStorageDir(Context context) {
        if (null != context) {
            return TEST_PHONE_DIR;
        }
        return null;
    }

    @Implementation
    public static String getSDCardStorageDir(Context context) {
        if (null != context) {
            return TEST_SDCARD_DIR;
        }
        return null;
    }

    @Implementation
    public static boolean isAndroidQOrLater() {
        return true;
    }
}
