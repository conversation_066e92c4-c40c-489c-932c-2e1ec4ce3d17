package com.soundrecorder.base.utils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;

import android.content.Context;
import android.os.Build;
import android.text.TextUtils;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.shadows.ShadowFeatureOption;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.stubbing.Answer;
import org.robolectric.annotation.Config;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class FileUtilsTest {
    private static final String TEST_RELATIVE_PATH_1 = "Music/Recordings/Standard Recordings/";
    private static final String TEST_DISPLAY_NAME_1 = "test 1.mp3";
    private static final String TEST_PATH = "testPath";
    private static final String TEST_PATH_TXT = "testPath/test.txt";
    private MockedStatic<TextUtils> mMockStaticTextUtils;
    private MockedStatic<BaseApplication> mMockBaseApplication;
    private Context mContext;

    @Before
    public void setUp() {
        mMockStaticTextUtils = Mockito.mockStatic(TextUtils.class);
        mMockStaticTextUtils.when(() -> TextUtils.isEmpty(anyString())).thenAnswer((Answer<Boolean>) invocation -> {
            CharSequence a = invocation.getArgument(0);
            return a == null || a.length() == 0;
        });
        mContext = ApplicationProvider.getApplicationContext();
        mMockBaseApplication = Mockito.mockStatic(BaseApplication.class);
        mMockBaseApplication.when(BaseApplication::getAppContext).thenReturn(mContext);
    }

    @After
    public void release() {
        if (mMockStaticTextUtils != null) {
            mMockStaticTextUtils.close();
            mMockStaticTextUtils = null;
        }
        if (mMockBaseApplication != null) {
            mMockBaseApplication.close();
            mMockBaseApplication = null;
        }
        mContext = null;
    }

    @Test
    public void should_return_with_underscore_when_getNewDisplayName() {
        MockedStatic<FileUtils> mMockStaticFileUtil = Mockito.mockStatic(FileUtils.class);
        mMockStaticFileUtil.when(() -> FileUtils.isFileExist(anyString(), anyString())).thenReturn(false, true, true, false);
        mMockStaticFileUtil.when(() -> FileUtils.getNewDisplayName(anyString(), anyString())).thenCallRealMethod();
        mMockStaticFileUtil.when(() -> FileUtils.getNewDisplayName(anyString(), anyString(), anyString())).thenCallRealMethod();
        mMockStaticFileUtil.when(() -> FileUtils.calRequireLengthTitle(anyString(), anyString(), anyString())).thenCallRealMethod();

        String newName = FileUtils.getNewDisplayName(TEST_RELATIVE_PATH_1, TEST_DISPLAY_NAME_1);
        Assert.assertEquals("test 1_1.mp3", newName);

        newName = FileUtils.getNewDisplayName(TEST_RELATIVE_PATH_1, TEST_DISPLAY_NAME_1);
        Assert.assertEquals("test 1_3.mp3", newName);

        mMockStaticFileUtil.close();
    }

    @Test
    public void should_return_with_timeMill_when_getNewDisplayName() {
        MockedStatic<FileUtils> mMockStaticFileUtil = Mockito.mockStatic(FileUtils.class);
        mMockStaticFileUtil.when(() -> FileUtils.isFileExist(anyString(), anyString())).thenReturn(false, true, true, false);
        mMockStaticFileUtil.when(() -> FileUtils.getNewDisplayName(anyString(), anyString())).thenCallRealMethod();
        mMockStaticFileUtil.when(() -> FileUtils.getNewDisplayName(anyString(), anyString(), anyString())).thenCallRealMethod();
        mMockStaticFileUtil.when(() -> FileUtils.calRequireLengthTitle(anyString(), anyString(), anyString())).thenCallRealMethod();

        String newName = FileUtils.getNewDisplayName(TEST_RELATIVE_PATH_1, TEST_DISPLAY_NAME_1, "2022");
        Assert.assertEquals("test 1_2022.mp3", newName);

        newName = FileUtils.getNewDisplayName(TEST_RELATIVE_PATH_1, TEST_DISPLAY_NAME_1, "2022");
        Assert.assertEquals("test 1_2022_2.mp3", newName);

        mMockStaticFileUtil.close();
    }

    @Test
    public void should_return_when_calRequireLengthTitle() {
        MockedStatic<FileUtils> mMockStaticFileUtil = Mockito.mockStatic(FileUtils.class);
        mMockStaticFileUtil.when(() -> FileUtils.isFileExist(anyString(), anyString())).thenReturn(false, true, true, false);
        mMockStaticFileUtil.when(() -> FileUtils.getNewDisplayName(anyString(), anyString())).thenCallRealMethod();
        mMockStaticFileUtil.when(() -> FileUtils.getNewDisplayName(anyString(), anyString(), anyString())).thenCallRealMethod();
        mMockStaticFileUtil.when(() -> FileUtils.calRequireLengthTitle(anyString(), anyString(), anyString())).thenCallRealMethod();

        String newTitle = FileUtils.calRequireLengthTitle("title", "suffix", "_");
        Assert.assertEquals("title_suffix", newTitle);

        String overLengthTitle = "title222236589636999999998888789666666662222211111";
        newTitle = FileUtils.calRequireLengthTitle(overLengthTitle, "suffix", "_");
        Assert.assertEquals("title22223658963699999999888878966666666222_suffix", newTitle);
        Assert.assertTrue(newTitle.length() <= 50);

        mMockStaticFileUtil.close();
    }

    @Test
    public void should_when_getDisplayNameByPath() {
        String path = "recordings" + File.separator + "标准1.mp3";
        String displayName = FileUtils.getDisplayNameByPath(path);
        Assert.assertEquals("标准1.mp3", displayName);
    }

    @Test
    public void should_when_getFileNameFromFullPath() {
        String path = "music/recordings" + File.separator + "标准1.mp3";
        String displayName = FileUtils.getFileNameFromFullPath(path);
        Assert.assertEquals("标准1.mp3", displayName);
    }

    @Test
    public void should_size_when_getFileSizeLong() {
        Assert.assertEquals(-1, FileUtils.getFileSizeLong(""));
        Assert.assertEquals(FileUtils.getFileSizeLong("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3"), -1);
        ArrayList<String> stringList = new ArrayList<>();
        stringList.add("111");
        stringList.add("222");
        assertEquals(SearchUtil.binSearch(stringList, 1L), -1);
    }

    @Test
    public void should_returnTrue_when_getSizeDescription() {
        String ngSizeString = FileUtils.getSizeDescription(-523);
        assertTrue("0B".equalsIgnoreCase(ngSizeString));
        String bSizeString = FileUtils.getSizeDescription(513);
        assertTrue("513B".equalsIgnoreCase(bSizeString));
        String kSizeString = FileUtils.getSizeDescription(1025);
        assertTrue("1KB".equalsIgnoreCase(kSizeString));
        String mSizeString = FileUtils.getSizeDescription(2 * 1024 * 1024);
        assertTrue("2MB".equalsIgnoreCase(mSizeString));
        String gSizeString = FileUtils.getSizeDescription(1 * 1024 * 1024 * 1024);
        assertTrue("1GB".equalsIgnoreCase(gSizeString));
    }

    @Test
    public void should_makeFolder_when_ensureDirectory_different_input() {
        FileUtils.ensureDirectory(TEST_PATH);
        File folder = new File(TEST_PATH);
        assertTrue(folder.exists());
        assertTrue(folder.isDirectory());
        File file = new File(TEST_PATH_TXT);
        try {
            file.createNewFile();
        } catch (IOException ignored) {
        }
        assertTrue(file.isFile());
        Assert.assertEquals(file.length(), FileUtils.getFileSizeLong(TEST_PATH_TXT));
        FileUtils.ensureDirectory(TEST_PATH_TXT);
        assertTrue(file.isDirectory());
        FileUtils.deleteDirOrFile(mContext, folder);
        folder.delete();
    }

    @Test
    public void should_getFileSizeLong() {
        FileUtils.getFileSizeLong("");
        FileUtils.ensureDirectory(TEST_PATH);
        File folder = new File(TEST_PATH);
        FileUtils.getFileSizeLong(folder.getPath());
    }

    @Test
    public void should_core2Full() {
        FileUtils.core2Full(TEST_DISPLAY_NAME_1, TEST_PATH);
        FileUtils.core2Full(TEST_DISPLAY_NAME_1, "aaa");
    }

    @Test
    public void should_core2FullWithEdit() {
        FileUtils.core2FullWithEdit(TEST_DISPLAY_NAME_1, TEST_PATH);
    }

    @Test
    public void should_delete() {
        File folder = new File(TEST_PATH);
        FileUtils.delete(mContext, null);
        FileUtils.delete(mContext, folder);
    }

    @Test
    public void should_deleteDirOrFile() {
        File folder = new File(TEST_PATH);
        try {
            folder.createNewFile();
        } catch (IOException ignored) {
        }
        FileUtils.deleteDirOrFile(mContext, null);
        FileUtils.deleteDirOrFile(mContext, folder);
        File folder1 = new File(TEST_PATH + "/text1");
        File folder2 = new File(TEST_PATH + "/text2");
        try {
            folder1.createNewFile();
            folder2.createNewFile();
        } catch (IOException ignored) {
        }
        FileUtils.deleteDirOrFile(mContext, folder);
    }
}
