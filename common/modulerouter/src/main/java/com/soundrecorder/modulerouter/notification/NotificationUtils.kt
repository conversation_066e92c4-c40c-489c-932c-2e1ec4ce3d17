/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  NotificationUtils
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter.notification

object NotificationUtils {
    const val TAG = "NotificationUtils"

    //普通录音发送的录制通知ID
    const val NOTIFICATION_COMMON_RECORDE_ID = 1024
    //三方APP或者不满足Feature开关发送的三方录制通知ID
    const val NOTIFICATION_THIRDPARTY_RECORDE_ID = 1025
    //普通录音发送的播放通知ID
    const val NOTIFICATION_PLAY_ID = 1026

    const val KEY_NOTIFICATION_TYPE = "notification_type"

    const val MARK_CHANGED_ACTION = "oplus.intent.action.MARK_CHANGED_ACTION"
    const val PLAY_STATUS_CHANGED_ACTION = "oplus.intent.action.PLAY_STATUS_CHANGED_ACTION"
    const val SAVE_CHANGED_ACTION = "oplus.intent.action.SAVE_CHANGED_ACTION"
    const val NOTIFICATION_DELETE_ACTION = "oplus.intent.action.NOTIFICATION_DELETED"

    const val PLAYBACK_CID = "Playback_new_Channel_id"
    const val BROWSFILE_CID = "BrowseFile_new_Channel_id"
    const val RECORDERSERVICE_CID = "RecorderService_new_Channel_id"

    const val PLAYBACK_OLD_CID = "Playback_Channel_id"
    const val BROWSFILE_OLD_CID = "BrowseFile_Channel_id"
    const val EDITRECORD_OLD_CID = "EditRecord_Channel_id"
    const val RECORDERSERVICE_OLD_CID = "RecorderService_channel_id"
    const val NEED_CHECK_BANNER_NOTIFICATION_CLICK = "Need_Check_Click"

    //正常入口
    const val NOTIFICATION_MODE_COMMON = 0
    //三方入口
    const val NOTIFICATION_MODE_THIRD = 1

    //快捷播放
    const val NOTIFICATION_PAGE_FAST_PLAY = 0
    //播放
    const val NOTIFICATION_PAGE_PLAY_BACK = 1
    //裁切
    const val NOTIFICATION_PAGE_EDIT_RECORD = 2
    //录制
    const val NOTIFICATION_PAGE_RECORD = 3
}