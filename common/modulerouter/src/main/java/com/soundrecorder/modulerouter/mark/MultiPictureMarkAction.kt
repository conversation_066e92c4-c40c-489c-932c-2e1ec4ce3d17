/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MultiPictureMarkAction
 * Description:
 * Version: 1.0
 * Date: 2022/10/24
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/10/24 1.0 create
 */

package com.soundrecorder.modulerouter.mark

import androidx.appcompat.app.AppCompatActivity
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest
import com.soundrecorder.modulerouter.photoviewer.PhotoViewerData

object MultiPictureMarkAction {
    const val COMPONENT_NAME = "MultiPictureMarkAction"
    const val START_POP_VIEW_LOADING_ACTIVITY = "startPopViewLoadingActivity"
    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    fun <T, R : PhotoViewerData> startPopViewLoadingActivity(
        activity: AppCompatActivity,
        result: ArrayList<R>,
        doMultiPictureMark: (ArrayList<T>) -> Int,
        doCompleteMultiPictureMark: () -> Unit,
        requestCode: Int,
    ) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, START_POP_VIEW_LOADING_ACTIVITY)
                .param(activity, result, doMultiPictureMark, doCompleteMultiPictureMark, requestCode)
                .build()
            OStitch.execute<Unit>(apiRequest)
        }
    }
}