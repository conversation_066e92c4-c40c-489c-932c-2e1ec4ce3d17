/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: IPictureMarkDelegate
 * Description:
 * Version: 1.0
 * Date: 2022/10/21
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2022/10/21 1.0 create
 */

package com.soundrecorder.modulerouter.mark

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel

interface IPictureMarkDelegate<T> {
    companion object {
        /*相机*/
        const val SOURCE_CAMERA = 0X1

        /*相册*/
        const val SOURCE_ALBUM = 0X2

        /*后台智能图片标记*/
        const val SOURCE_MULTI_CAMERA = 0X3
    }

    /**
     *需activity在对应生命周期onSaveInstanceState调用
     */
    fun onSaveInstanceState(outState: Bundle)

    /**
     *需activity在对应生命周期onRestoreInstanceState调用
     */
    fun onRestoreInstanceState(savedInstanceState: Bundle)

    /**
     *需activity在对应生命周期onConfigurationChanged调用
     */
    fun onConfigurationChanged(newConfig: Configuration)

    /**
     *需activity在对应生命周期onNewIntent调用
     */
    fun onNewIntent(intent: Intent?)

    /**
     *需activity在对应生命周期startActivityForResult调用
     */
    fun setRequestCodeX(requestCode: Int)

    fun launchTakePhotoSingle(sourceType: Int, timeMill: Long): Boolean

    fun postShowSelectPictureDialog(anchor: View?)

    fun handleSelectPictureMark(position: Int)

    fun dismissPictureMark()

    fun getRequestCodeX(): Int

    fun setIsAddPictureMarking(isAddPictureMarking: Boolean)

    fun isAddPictureMarking(): MutableLiveData<Boolean>

    fun checkNeedAddMark(): Boolean

    fun checkAddMarkMoreThanMax(): Boolean

    fun checkAddMarkDuplicated(mill: Long): Boolean

    fun getViewModel(): ViewModel

    fun isDialogShowing(): Boolean
}

interface IIPictureMarkListener<T, R> {
    /**添加单张图片标记(相册、相机、智能图片标记)*/
    fun doPictureMark(pictureMarkMetaData: T) {}

    /**添加多张图片标记(智能图片标记)*/
    fun doMultiPictureMark(pictureList: ArrayList<T>): Int = -1

    /**
     * 开始图片标记操作后
     * @param operateCancel 是否取消操作 true：取消拍照/相册/智能
     * @param fromSource 来源 ：相机/相册/智能图片
     * */
    fun doSingleOrMultiPictureMarkEnd(operateCancel: Boolean, fromSource: Int) {}

    /**当前音频播放位置*/
    fun getPlayerCurrentTimeMillis(): Long = -1

    /**已有标记列表*/
    fun getMarkList(): List<R>? = null

    /**音频总时长*/
    fun getDuration(): Long = -1

    /**
     * 恢复播放状态
     * @param cachePlayerState 之前存起来的播放状态
     * @param hasNeedSpeakOff
     * @return
     * */
    fun restorePlayerStatePlaying(cachePlayerState: Int, hasNeedSpeakOff: Boolean = true): Int = -1

    /**音频播放状态*/
    fun hasPlayerStatePlaying(): Int = -1

    /**相机/相册 选择弹窗 显示前调用*/
    fun beforeShowSelectPictureDialog() {}

    /**
     * 释放图片标记(图片缓存)
     * isFinishing 表示Activity是否正常finish，而非重建finish
     */
    fun releaseMarks(isFinishing: Boolean) {}

    /**
     * @return 支持图片标记方式-相机、相册、智能图片标记
     * @see SOURCE_CAMERA
     * @see SOURCE_ALBUM
     * @see SOURCE_MULTI_CAMERA
     */
    fun supportPictureMarkSource(): List<Int> = mutableListOf()

    fun onActivityLaunched(sourceType: Int)
}

interface IPictureMarkLifeOwnerProvider {
    fun provideLifeCycleOwner(): LifecycleOwner

    fun provideActivity(): AppCompatActivity

    fun isFinishing(): Boolean
}