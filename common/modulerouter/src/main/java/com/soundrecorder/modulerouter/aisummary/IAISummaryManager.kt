/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: IAISummaryManager
 * Description:IAISummaryManager
 * Version: 1.0
 * Date: 2025/5/14
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/14      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.modulerouter.aisummary

import android.app.Activity
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleObserver

/**
 * AI摘要管理器接口
 * 参考SmartName的实现，为AI摘要功能提供统一的管理接口
 */
interface IAISummaryManager : LifecycleObserver {

    /**
     * 注册AI摘要功能
     * @param viewModelStoreOwner Fragment实例
     */
    fun registerForAISummary(viewModelStoreOwner: Fragment)

    /**
     * AI摘要点击处理入口
     * @param activity 当前Activity
     * @param selectedMediaIdList 选中的录音ID列表
     */
    fun startAISummaryClickHandle(activity: Activity?, selectedMediaIdList: MutableList<Long>?)

    /**
     * 检查AI摘要插件是否下载
     * @param activity 当前Activity
     * @param callback 下载完成回调
     */
    fun checkPluginsDownload(activity: Activity?, callback: ((Boolean) -> Unit)? = null)

    /**
     * 开始或恢复AI摘要任务
     * @param activity 当前Activity
     * @param selectedMediaIdList 选中的录音ID列表
     */
    fun startOrResumeAISummary(activity: Activity?, selectedMediaIdList: MutableList<Long>?)

    /**
     * 权限确认后的处理
     * @param activity 当前Activity
     */
    fun doClickPermissionAISummaryOK(activity: Activity?)

    /**
     * 释放资源
     */
    fun release()

    /**
     * 释放所有资源
     */
    fun releaseAll()
}
