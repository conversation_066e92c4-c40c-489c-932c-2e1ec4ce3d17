package com.soundrecorder.modulerouter.xlog

import java.io.Serializable

class RecordLogXConfig : Serializable {
    companion object {
        private const val serialVersionUID = 1331261391588055973L
    }

    private var traceId: String? = null
    private var imei: String? = null
    private var openId: String? = null
    private var registrationId: String? = null
    private var beginTime: String? = null
    private var endTime: String? = null
    private var force: String? = null
    private var tracePkg: String? = null

    fun getTraceId(): String? {
        return traceId
    }

    fun setTraceId(traceId: String?) {
        this.traceId = traceId
    }

    fun getImei(): String? {
        return imei
    }

    fun setImei(imei: String?) {
        this.imei = imei
    }

    fun getOpenId(): String? {
        return openId
    }

    fun setOpenId(openId: String?) {
        this.openId = openId
    }

    fun getRegistrationId(): String? {
        return registrationId
    }

    fun setRegistrationId(registrationId: String?) {
        this.registrationId = registrationId
    }

    fun getBeginTime(): String? {
        return beginTime
    }

    fun setBeginTime(beginTime: String?) {
        this.beginTime = beginTime
    }

    fun getEndTime(): String? {
        return endTime
    }

    fun setEndTime(endTime: String?) {
        this.endTime = endTime
    }

    fun getForce(): String? {
        return force
    }

    fun setForce(force: String?) {
        this.force = force
    }

    fun getTracePkg(): String? {
        return tracePkg
    }

    fun setTracePkg(tracePkg: String?) {
        this.tracePkg = tracePkg
    }

    override fun toString(): String {
        return "RecordLogXConfig(traceId=$traceId, imei=$imei, openId=$openId, registrationId=$registrationId, " +
                "beginTime=$beginTime, endTime=$endTime, force=$force, tracePkg=$tracePkg)"
    }
}