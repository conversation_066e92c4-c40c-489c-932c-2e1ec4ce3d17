/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  EditRecordAction
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter

import android.content.Context
import android.content.Intent
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object EditRecordAction {
    const val COMPONENT_NAME = "EditRecord"
    const val CREATE_EDIT_RECORD_INTENT = "createEditRecordIntent"
    const val IS_EDIT_RECORD_ACTIVITY = "isEditRecordActivity"
    const val GET_EDIT_RECORD_ACTIVITY_CLASS = "getEditRecordActivityClass"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun createEditRecordIntent(context: Context): Intent? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, CREATE_EDIT_RECORD_INTENT)
                .param(context).build()
            OStitch.execute<Intent>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun isEditRecordActivity(context: Context): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, IS_EDIT_RECORD_ACTIVITY)
                .param(context).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun getEditRecordActivityClass(): Class<*>? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, GET_EDIT_RECORD_ACTIVITY_CLASS).build()
            OStitch.execute<Class<*>>(apiRequest).result
        } else null
    }
}