package com.soundrecorder.modulerouter.cloudkit.tipstatus

/**
 * 云同步供app UI 层错误码使用，同SyncErrorCode中定义值一致
 */
object CloudErrorCode {
    /*云同步开关关闭*/
    const val RESULT_SWITCH_CLOSE = 44103

    /*账号token 鉴权失败， 业务方需要调用账号sdk 重新刷新token*/
    const val RESULT_AUTH_ERROR: Int = 403

    /*客户端本地检查请求时是否登陆，未登录返回该错误， 业务方需要跳转账号登陆界面*/
    const val RESULT_NOT_LOGIN: Int = 100003 //客户端本地检查请求时是否登陆，未登录返回该错误， 业务方需要跳转账号登陆界面
}