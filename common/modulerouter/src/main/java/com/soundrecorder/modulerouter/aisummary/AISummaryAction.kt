/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryAction
 * Description:AISummaryAction
 * Version: 1.0
 * Date: 2025/5/8
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/8      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.modulerouter.aisummary

import android.content.Context
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest
import com.soundrecorder.modulerouter.smartname.SmartNameAction

object AISummaryAction {
    const val COMPONENT_NAME = "AISummaryAction"

    //aiunit相关错误
    const val AIUNIT_ERROR = -1

    //文件超大
    const val FILE_SIZE_MAX_UNSUPPORT = 10001

    //文件不支持
    const val FILE_SIZE_UNSUPPORT_ZERO = 10002

    //时间过短
    const val DURATION_MIN_UNSUPPORT_ZERO = 10003

    //时间过长
    const val DURATION_MAX_UNSUPPORT = 10004

    //不支持转文本
    const val CONVERT_TEXT_UNSUPPORT = 10005

    //插件未初始化或者已被销毁需要重新初始化
    const val PLUGIN_INIT_ERROR = 10008

    //端侧数据解析错误
    const val DATA_PARSE_ERROR = 10009

    //请求超时
    const val REQUEST_TIMEOUT = 10010

    //内容较少，无法生成摘要
    const val CONTENT_LESS_ERROR = 10011

    //网络不可达
    const val NETWORK_ERROR = 10012

    //server exception
    const val SERVER_ERROR = 10013

    const val ACTION_CHECK_SUPPORT_AI_SUMMARY = "checkSupportAISummary"
    const val ACTION_START_CONVERT_OR_AI_SUMMARY = "startConvertOrAISummary"
    const val ACTION_START_AI_SUMMARY = "startAISummary"
    const val ACTION_START_AI_SUMMARY_V2 = "startAISummaryByBean"
    const val ACTION_CHECK_IS_TASK_RUNNING = "checkIsTaskRunning"
    const val ACTION_CHECK_HAS_TASK_RUNNING = "checkHasTaskRunning"
    const val ACTION_REGISTER_AI_SUMMARY_CALLBACK = "registerAISummaryCallback"
    const val ACTION_UN_REGISTER_AI_SUMMARY_CALLBACK = "unRegisterAISummaryCallback"
    const val ACTION_CANCEL_AI_SUMMARY_TASK = "cancelAISummaryTask"
    const val ACTION_FROM_JSON = "fromJson"
    const val ACTION_STOP_AI_SUMMARY = "stopAISummary"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    /**
     * 检查是否支持录音摘要
     */
    @JvmStatic
    fun checkSupportAISummary(context: Context, supportConvert: Boolean?, forceUpdate: Boolean = false): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_CHECK_SUPPORT_AI_SUMMARY)
                .param(context, supportConvert, forceUpdate)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    /**
     * 启动录音摘要
     */
    @JvmStatic
    fun startAISummary(mediaId: Long): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_START_CONVERT_OR_AI_SUMMARY)
                .param(mediaId)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun startAISummary(mediaId: Long, jsonParams: String?): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_START_AI_SUMMARY)
                .param(mediaId, jsonParams)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun stopAISummaryTask(mediaId: Long) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_STOP_AI_SUMMARY)
                .param(mediaId)
                .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun <T> startAISummaryByBean(mediaId: Long, aiSummaryParams: T?): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_START_AI_SUMMARY_V2)
                .param(mediaId, aiSummaryParams)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    /**
     * 检查是否有任务在进行
     */
    @JvmStatic
    fun checkIsTaskRunning(mediaId: Long): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_CHECK_IS_TASK_RUNNING)
                .param(mediaId)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun checkHasTaskRunning(mediaId: Long): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_CHECK_HAS_TASK_RUNNING)
                .param(mediaId)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    /**
     * 注册录音摘要回调函数
     */
    @JvmStatic
    fun registerAISummaryCallback(mediaId: Long, calllback: IAISummaryCallback) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_REGISTER_AI_SUMMARY_CALLBACK)
                .param(mediaId, calllback)
                .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun unRegisterAISummaryCallback(mediaId: Long, calllback: IAISummaryCallback) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_UN_REGISTER_AI_SUMMARY_CALLBACK)
                .param(mediaId, calllback)
                .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    /**
     * 取消生成摘要任务
     */
    @JvmStatic
    fun cancelAISummaryTask(mediaId: Long) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_CANCEL_AI_SUMMARY_TASK)
                .param(mediaId)
                .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    inline fun <reified T> fromJson(json: String?, cls: Class<T>): T? {
        if (SmartNameAction.mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_FROM_JSON)
                .param(json, cls)
                .build()
            return OStitch.execute<T>(apiRequest).result
        }
        return null
    }
}