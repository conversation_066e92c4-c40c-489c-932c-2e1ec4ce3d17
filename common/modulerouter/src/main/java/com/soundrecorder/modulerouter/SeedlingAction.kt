/*
 Copyright (C), 2008-2023 OPLUS Mobile Comm Corp., Ltd.
 File: SeedlingAction
 Description:
 Version: 1.0
 Date: 2023/3/22
 Author: ********(v-zhengt<PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2023/3/22 1.0 create
 */

package com.soundrecorder.modulerouter

import android.content.Context
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest
import org.json.JSONObject

object SeedlingAction {
    const val COMPONENT_NAME = "SeedlingAction"
    const val SHOW_SEEDLING_STATUS_BAR = "showSeedlingStatusBar"
    const val HIDE_SEEDLING_STATUS_BAR = "hideSeedlingStatusBar"
    const val INIT_SUPPORT_FLUID_CARD_CALLBACK = "initSupportFluidCardCallback"
    const val REGISTER_SEEDLING_RESULT_CALLBACK = "registerResultCallBack"
    const val UNREGISTER_SEEDLING_RESULT_CALLBACK = "unRegisterResultCallBack"
    const val REFRESH_SEEDLING_DATA = "refreshSeedlingData"
    const val FORCE_REFRESH_SEEDLING_DATA = "forceRefreshSeedlingData"
    const val GET_CARD_SERVICE_ID = "getCardServiceId"

    /* 是否在支持泛在胶囊 */
    const val IS_SUPPORT_SYSTEM_SEND_INTENT = "isSupportSystemSendIntent"

    /* 是否在支持流体云 */
    const val IS_SUPPORT_FLUID_CLOUD = "isSupportFluidCloud"
    const val INIT_SEEDING_API = "init"
    const val RELEASE_SEEDING_API = "release"
    const val UPDATE_CARD_DATA = "updateCardData"
    const val DO_ACTION_IN_THREAD = "do_action_in_process"
    const val GET_STATUS_BAR_SUPPORT_TYPE = "getStatusBarSupportType"
    const val SEND_RECORD_FILE_DELETE = "sendRecordFileDelete"
    const val SEND_RECORD_FILE_ADD = "sendRecordFileAdd"
    const val SEND_RECORD_FILE_INNER_RENAME = "sendRecordFileInnerRename"
    const val SEND_RECORD_FILE_RECOVER = "sendRecordFileRecover"
    const val ON_GEN_SUMMARY_START = "onGenSummaryStart"
    const val ON_GEN_SUMMARY_END = "onGenSummaryEnd"
    const val ON_CONVERT_STATUS_CHANGE = "onConvertStatusChange"
    const val ON_CONVERT_TEXT_RECEIVED = "onConvertTextReceived"
    const val ON_CONVERT_PROGRESS_CHANGED = "onConvertProgressChanged"

    /*
    * 13.1老胶囊
    */
    const val STATUS_BAR_SUPPORT_OLD = 0

    /*
    * 13.2余光交互
    */
    const val STATUS_BAR_SUPPORT_SEEDLING_CARD = 1

    /*
    * 14.0流体云
    */
    const val STATUS_BAR_SUPPORT_FLUID_CARD = 2

    val hasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun showSeedlingStatusBar(
        originData: JSONObject? = null,
        callback: ((Boolean) -> Unit)? = null
    ) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, SHOW_SEEDLING_STATUS_BAR)
                .param(originData, callback).build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun hideSeedlingStatusBar(
        forceDismiss: Boolean = false,
        callback: ((Boolean) -> Unit)? = null
    ) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, HIDE_SEEDLING_STATUS_BAR)
                .param(forceDismiss, callback).build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun initSupportFluidCardCallback(context: Context, callback: (Boolean?, Boolean) -> Unit) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, INIT_SUPPORT_FLUID_CARD_CALLBACK)
                .param(context, callback).build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun registerResultCallBack() {
        if (hasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, REGISTER_SEEDLING_RESULT_CALLBACK).build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun unRegisterResultCallBack() {
        if (hasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, UNREGISTER_SEEDLING_RESULT_CALLBACK).build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun refreshSeedlingData(jsonData: JSONObject?) {
        if (hasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, REFRESH_SEEDLING_DATA).param(jsonData).build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun forceRefreshSeedlingData(jsonData: JSONObject?) {
        if (hasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, FORCE_REFRESH_SEEDLING_DATA).param(jsonData).build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun getCardServiceId(): String? {
        if (hasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, GET_CARD_SERVICE_ID).build()
            return OStitch.execute<String>(apiRequest).result
        }
        return null
    }

    @JvmStatic
    fun isSupportSystemSendIntent(context: Context): Boolean {
        if (hasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, IS_SUPPORT_SYSTEM_SEND_INTENT).param(context)
                    .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun isSupportFluidCloud(context: Context): Boolean {
        if (hasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, IS_SUPPORT_FLUID_CLOUD).param(context).build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun getStatusBarSupportType(context: Context, callback: ((Int) -> Unit)) {
        if (hasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, GET_STATUS_BAR_SUPPORT_TYPE)
                    .param(context, callback).build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun init() {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, INIT_SEEDING_API)
                .build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun doActionInThread(callback: (() -> Unit)) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, DO_ACTION_IN_THREAD)
                .param(callback).build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun release() {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, RELEASE_SEEDING_API)
                .build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun sendRecordDeleteEvent() {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, SEND_RECORD_FILE_DELETE)
                .build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun sendRecordAddEvent() {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, SEND_RECORD_FILE_ADD)
                .build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    /**
     * 录音内部重命名逻辑
     */
    @JvmStatic
    fun sendRecordInnerRenameEvent(mediaId: Long) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, SEND_RECORD_FILE_INNER_RENAME)
                .param(mediaId)
                .build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun sendRecordRecoverEvent() {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, SEND_RECORD_FILE_RECOVER)
                .build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun onConvertProgressChanged(
        mediaId: Long,
        uploadProgress: Int,
        convertProgress: Int,
        serverPlanCode: Int
    ) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ON_CONVERT_PROGRESS_CHANGED)
                .param(mediaId, uploadProgress, convertProgress, serverPlanCode).build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun onConvertStatusChange(
        mediaId: Long,
        uploadStatus: Int,
        convertStatus: Int,
        errorMessage: String
    ) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ON_CONVERT_STATUS_CHANGE)
                .param(mediaId, uploadStatus, convertStatus, errorMessage).build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun onConvertTextReceived(mediaId: Long) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ON_CONVERT_TEXT_RECEIVED)
                .param(mediaId).build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun onSummaryStart(mediaId: Long?) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ON_GEN_SUMMARY_START)
                .param(mediaId).build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun onSummaryProgressEnd(
        mediaId: Long?,
        noteId: String?,
        recordUUID: String?,
        asrErrorCode: Int?,
        summaryErrorCode: Int?
    ) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ON_GEN_SUMMARY_END)
                .param(mediaId, noteId, recordUUID, asrErrorCode, summaryErrorCode).build()
            OStitch.execute<Unit>(apiRequest)
        }
    }

    @JvmStatic
    fun updateCardData(card: Any, jsonData: JSONObject, updateAll: Boolean) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, UPDATE_CARD_DATA)
                .param(card, jsonData, updateAll).build()
            OStitch.execute<Unit>(apiRequest)
        }
    }
}