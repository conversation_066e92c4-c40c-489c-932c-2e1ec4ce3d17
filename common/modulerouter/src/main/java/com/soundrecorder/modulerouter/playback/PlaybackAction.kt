/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PlaybackAction
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter.playback

import android.content.Context
import android.content.Intent
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest
import com.soundrecorder.modulerouter.smartname.ISmartNameManager
import java.util.ArrayList

object PlaybackAction {
    const val NOTIFY_CONVERT_STATUS_UPDATE = "oplus.multimedia.soundrecorder.convertStatusUpdate"
    const val KEY_NOTIFY_RECORD_ID = "key_convert_record_id"
    const val KEY_NOTIFY_CONVERT_STATUS = "key_convert_status"

    const val NOTIFY_SMART_NAME_STATUS_UPDATE = "oplus.multimedia.soundrecorder.smartNameStatusUpdate"
    const val KEY_NOTIFY_SMART_NAME_STATUS = "key_notify_smart_name_status"
    const val KEY_NOTIFY_SMART_NAME_NAME_TEXT = "key_notify_smart_name_name_text"
    const val KEY_NOTIFY_SMART_NAME_CONVERT_COMPLETE = "key_notify_smart_name_convert_complete"

    const val COMPONENT_NAME = "Playback"
    const val CREATE_PLAYBACK_INTENT = "createPlaybackIntent"
    const val IS_PLAYBACK_ACTIVITY = "isPlaybackActivity"
    const val CANCEL_ALL_CONVERT_TASK = "cancelAllConvertTask"
    const val STOP_CONVERT_SERVICE = "stopConvertService"
    const val CLEAR_MUTE_CACHE = "clearMuteCache"
    const val GET_PLAYBACK_ACTIVITY_CLASS = "getPlaybackActivityClass"
    const val START_MUTE_DETECT_IF_NECESSARY = "startMuteDetectIfNecessary"
    const val UPDATE_CONVERT_CONFIG = "updateConvertConfig"
    const val IS_SUPPORT_WPS_EXPORT = "isSupportWpsExport"
    const val READ_CONVERT_CONTENT = "readConvertContent"
    const val READ_OPPO_SHARE_CONVERT_CONTENT = "readOShareConvertContent"

    const val INIT_SMART_NAME_CONVERT_MANAGER = "initSmartNameConvertManager"
    const val PARSE_CALL_NAME = "parseCallName"
    const val RELEASE_MP3_FILE = "releaseMp3File"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun createPlaybackIntent(context: Context): Intent? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, CREATE_PLAYBACK_INTENT)
                .param(context).build()
            OStitch.execute<Intent>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun isPlaybackActivity(context: Context): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, IS_PLAYBACK_ACTIVITY)
                .param(context).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun cancelAllConvertTask() {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, CANCEL_ALL_CONVERT_TASK).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun stopConvertService(context: Context) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, STOP_CONVERT_SERVICE).param(context).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun clearMuteCache(filePath: String) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, CLEAR_MUTE_CACHE).param(filePath).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun getPlaybackActivityClass(): Class<*>? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(
                COMPONENT_NAME,
                GET_PLAYBACK_ACTIVITY_CLASS
            ).build()
            OStitch.execute<Class<*>>(apiRequest).result
        } else null
    }

    @JvmStatic
    fun startMuteDetectIfNecessary(fullPath: String?) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, START_MUTE_DETECT_IF_NECESSARY).param(fullPath)
                    .build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    /**
     * 更新云控配置
     */
    @JvmStatic
    fun updateConvertConfig() {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, UPDATE_CONVERT_CONFIG).param().build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    /**
     * 是否支持分享到WPS
     */
    @JvmStatic
    fun isSupportWpsExport(): Boolean? {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, IS_SUPPORT_WPS_EXPORT).build()
            return OStitch.execute<Boolean>(apiRequest).result
        }
        return false
    }

    /**
     * 根据转写文件名获取转写内容详情
     * @param filename 转写文件名
     */
    @JvmStatic
    fun readConvertContent(appContext: Context?, filename: String?, serverPlanCode: Int): ArrayList<Any>? {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, READ_CONVERT_CONTENT)
                    .param(appContext, filename, serverPlanCode).build()
            return OStitch.execute<ArrayList<Any>>(apiRequest).result
        }
        return null
    }

    /**
     * 根据转写文件路径获取OPPO Share路径下的转文本文件内容详情
     * @param filePath 转写文件路径
     */
    @JvmStatic
    fun readOShareConvertContent(filePath: String?, serverPlanCode: Int?): ArrayList<Any>? {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, READ_OPPO_SHARE_CONVERT_CONTENT)
                    .param(filePath, serverPlanCode).build()
            return OStitch.execute<ArrayList<Any>>(apiRequest).result
        }
        return null
    }

    @JvmStatic
    fun getSmartNameManager(): ISmartNameManager? {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, INIT_SMART_NAME_CONVERT_MANAGER)
                    .build()
            return OStitch.execute<ISmartNameManager>(apiRequest).result
        }
        return null
    }

    @JvmStatic
    fun parseCallName(mContext: Context, path: String? = null, mediaId: Long = -1L, mimeType: String): String? {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, PARSE_CALL_NAME)
                    .param(mContext, path, mediaId, mimeType)
                    .build()
            return OStitch.execute<String>(apiRequest).result
        }
        return null
    }

    @JvmStatic
    fun releaseMp3File() {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, RELEASE_MP3_FILE)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }
}