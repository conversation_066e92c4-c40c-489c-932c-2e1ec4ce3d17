/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: IAISummaryCallback
 * Description:提供录音摘要的启动、停止、结束、重试、错误信息返回等接口，并返回相关数据
 * Version: 1.0
 * Date: 2025/5/14
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/14      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.modulerouter.aisummary

interface IAISummaryCallback {

    fun onAISummaryStart(mediaId: Long, extras: Map<String, Any>?) {}

    fun onAISummaryStop(mediaId: Long, extras: Map<String, Any>?) {}

    fun onAISummaryFinished(mediaId: Long, jsonResult: String, extras: Map<String, Any>?){}

    fun onAISummaryError(mediaId: Long, errorCode: Int, errorMsg: String? = null){}

    fun onAISummaryEnd(mediaId: Long) {}
}