/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  UnifiedSummaryManager
 * * Description: UnifiedSummaryManager
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.modulerouter.smartname

import android.app.Activity
import android.content.Context

interface IUnifiedSummaryCallBack {
    fun showSmartNameGuideDialog(activity: Activity, callback: IPluginDownloadCallback?)

    fun showAiUnitPluginsDialog(
        context: Context,
        callback: IPluginDownloadCallback? = null,
        isOpenSwitch: Boolean = false
    )

    fun releaseAllDialog()
}

interface IPluginDownloadCallback {
    fun onDownLoadResult(result: Boolean)
}