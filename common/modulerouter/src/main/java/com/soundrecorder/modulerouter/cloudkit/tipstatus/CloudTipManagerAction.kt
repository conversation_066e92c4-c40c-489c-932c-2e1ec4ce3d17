package com.soundrecorder.modulerouter.cloudkit.tipstatus

import android.content.Context
import androidx.lifecycle.LiveData
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object CloudTipManagerAction {
    const val COMPONENT_NAME = "CloudTipManagerAction"

    /*初始化云同步相关初始值，如：登录状态、开关状态*/
    const val ACTION_INIT_CLOUD_STATE = "init_cloud_state"

    /*注册云同步开关变化listener*/
    const val ACTION_REGISTER_CLOUD_SWITCH_LISTENER = "register_cloud_switch_listener"

    /*反注册云同步开关变化listener*/
    const val ACTION_UNREGISTER_CLOUD_SWITCH_LISTENER = "unregister_cloud_switch_listener"

    /*获取云同步状态 LiveData<ITipStatus>*/
    const val ACTION_GET_CLOUD_SYNC_STATUS = "get_cloud_sync_status"

    /*获取云同步结果状态码*/
    const val ACTION_GET_CLOUD_SYNC_RESULT = "get_cloud_sync_result"

    /*云同步开关是否开启*/
    const val ACTION_GET_CLOUD_SWITCH_STATE_ON = "get_cloud_switch_state_on"

    /*启动录音云同步开关页面*/
    const val ACTION_LAUNCH_CLOUD_SETTING_ACTIVITY = "launch_cloud_setting_activity"

    /*录音开关页面className*/
    const val ACTION_GET_RECORD_SETTING_ACTIVITY_NAME = "get_record_setting_activity_name"

    /*录音云同步权限check页面*/
    const val ACTION_CLOUD_PERMISSION_ACTIVITY_NAME = "get_cloud_permission_activity_name"

    const val ACTION_CHECK_SYNC_ABNORMAL_STOP = "check_sync_abnormal_stop"
    /*是否需要显示云同步卡片引导用户开启云同步*/
    const val ACTION_IS_NEED_SHOW_CLOUD_GUIDE = "is_need_show_guide"
    const val ACTION_IS_REFRESH_UI_OF_SYNC = "isRefreshUIOfSync"
    const val ACTION_IS_SYNCING = "action_isSyncing"
    const val ACTION_CHECK_NEED_SYNC_FULL_RECOVERY = "checkNeedSyncFullRecovery"

    const val ACTION_CLOUD_IS_LOGIN_FROM_CACHE = "ACTION_CLOUD_IS_LOGIN_FROM_CACHE"
    /*云同步未登录，调用账号登录*/
    const val ACTION_CLOUD_ACCOUNT_REQ_LOGIN = "ACTION_CLOUD_ACCOUNT_REQ_LOGIN"
    const val ACTION_CLOUD_RELEASE_ACCOUNT_REQ_CALLBACK = "ACTION_CLOUD_RELEASE_ACCOUNT_REQ_CALLBACK"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    /**
     * 初始化云同步相关初始值，如：登录状态、开关状态
     */
    @JvmStatic
    fun initCloudState() {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_INIT_CLOUD_STATE)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    /**
     * 注册云同步开关变化listener
     */
    @JvmStatic
    fun registerCloudSwitchChangeListener(listener: ICloudSwitchChangeListener?) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_REGISTER_CLOUD_SWITCH_LISTENER)
                    .param(listener)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    /**
     * 反注册云同步开关变化listener
     */
    @JvmStatic
    fun unregisterCloudSwitchChangeListener(listener: ICloudSwitchChangeListener?) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_UNREGISTER_CLOUD_SWITCH_LISTENER)
                    .param(listener)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    /**
     * 获取云同步状态liveData
     */
    @JvmStatic
    fun getCloudStatusLiveData(): LiveData<ITipStatus>? {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_GET_CLOUD_SYNC_STATUS)
                    .build()
            return OStitch.execute<LiveData<ITipStatus>>(apiRequest).result
        }
        return null
    }

    /**
     * 获取云同步结果错误码
     */
    @JvmStatic
    fun getCloudSyncResultCode(): Int {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_GET_CLOUD_SYNC_RESULT)
                    .build()
            return OStitch.execute<Int>(apiRequest).result ?: -1
        }
        return -1
    }

    /**
     * 获取云同步开关状态
     */
    @JvmStatic
    fun isCloudSwitchOn(): Boolean {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_GET_CLOUD_SWITCH_STATE_ON)
                    .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }


    /**
     * 打开云同步开关设置页面
     */
    @JvmStatic
    fun launchCloudSettingPage(context: Context?) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_LAUNCH_CLOUD_SETTING_ACTIVITY)
                    .param(context)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    /**
     * 获取录音新增开关页面className
     */
    @JvmStatic
    fun getRecordCloudSettingActivityName(): String {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_GET_RECORD_SETTING_ACTIVITY_NAME)
                    .build()
            return OStitch.execute<String>(apiRequest).result ?: ""
        }
        return ""
    }

    @JvmStatic
    fun getCloudPermissionActivityName(): String {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_CLOUD_PERMISSION_ACTIVITY_NAME)
                    .build()
            return OStitch.execute<String>(apiRequest).result ?: ""
        }
        return ""
    }

    /**
     * check同步错误码是否异常code
     */
    @JvmStatic
    fun checkSyncAbnormalStop(context: Context): Boolean {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_CHECK_SYNC_ABNORMAL_STOP)
                    .param(context)
                    .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    /**
     * 是否需要显示云同步卡片引导用户开启云同步
     */
    @JvmStatic
    fun isNeedShowCloudGuide(): Boolean {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_IS_NEED_SHOW_CLOUD_GUIDE)
                    .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun isRefreshUIOfSync(): Boolean {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_IS_REFRESH_UI_OF_SYNC)
                    .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun isSyncing(): Boolean {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_IS_SYNCING)
                    .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun checkNeedSyncFullRecovery(): Boolean {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_CHECK_NEED_SYNC_FULL_RECOVERY)
                    .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun isLoginFromCache(): Boolean {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_CLOUD_IS_LOGIN_FROM_CACHE)
                    .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun cloudAccountReqLogin(context: Context, callback: ((Boolean) -> Unit)? = null) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_CLOUD_ACCOUNT_REQ_LOGIN)
                    .param(context, callback)
                    .build()
            OStitch.execute<Boolean>(apiRequest).result
        }
    }

    @JvmStatic
    fun releaseAccountReqCallback() {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, ACTION_CLOUD_RELEASE_ACCOUNT_REQ_CALLBACK)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }
}