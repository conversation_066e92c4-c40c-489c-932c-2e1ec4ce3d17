/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PrivacyPolicyConstant
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter.privacyPolicy

object PrivacyPolicyConstant {
    const val FRAGMENT_CLASS_NAME_TYPE = "fragment_class_name_type"
    const val FRAGMENT_CLASS_NAME_TITLE = "fragment_class_name_title"
    const val FRAGMENT_CLASS_COLLECTION_TYPE = "fragment_class_collection_type"

    /*默认类型，根据全量、轻量对应显示 用户须知弹窗、轻量的用户须知弹窗*/
    const val TYPE_USER_NOTICE_DEFAULT = 0
    /*用户须知弹窗*/
    const val TYPE_USER_NOTICE = 1
    /*基本功能弹窗*/
    const val TYPE_USER_NOTICE_BASIC = 2
    /*仍可以使用基本功能弹窗*/
    const val TYPE_USER_NOTICE_BASIC_STILL = 3

    /*轻量OS, 支持ck才会有隐私政策超链接*/
    const val TYPE_USER_NOTICE_LIGHT_OS = 4
    const val TYPE_PERMISSION_CONVERT = 5
    const val TYPE_PERMISSION_CONVERT_SEARCH = 6
    const val TYPE_PERMISSION_CONVERT_WITHDRAWN = 7
    const val TYPE_PERMISSION_USER_NOTICE_UPDATE = 8
    const val TYPE_PERMISSION_USER_NOTICE_FUNCTION = 9
    const val TYPE_PERMISSION_CLOUD = 10
    /**
     * 通过点击卡片获取转文本权限
     */
    const val TYPE_PERMISSION_CONVERT_FROM_CARD = 20

    const val TYPE_PERSONAL_INFORMATION_PROTECTION_POLICY = 1
    const val TYPE_THIRD_PARTY_INFORMATION_SHARING_CHECKLIST = 2
    const val TYPE_COLLECTION_OF_PERSONAL_INFORMATION_EXPRESS_CHECKLIST = 3

    /**
     * 协议、功能弹窗弹窗等来源页面
     * 1:首页
     * 2:详情页
     * 3:通话录音更多
     */
    const val PAGE_FROM_BROWSE = 1
    const val PAGE_FROM_PLAYBACK = 2
    const val PAGE_FROM_CALL_RECORD = 3
}
