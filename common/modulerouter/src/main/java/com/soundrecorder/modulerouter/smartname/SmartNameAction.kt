/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  SmartNameAction
 * * Description: SmartNameAction
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.modulerouter.smartname

import android.content.Context
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object SmartNameAction {

    const val COMPONENT_NAME = "SmartNameAction"
    //aiunit相关错误
    const val AIUNIT_ERROR = -2
    //参数错误；语种不支持
    const val INVALID_PARAM = 100001
    //鉴权失败
    const val AUTH_FAILED = 100002
    //触发内容安全
    const val RISK_CONTENT = 100003
    //language is not support
    const val LANGUAGE_NOT_SUPPORT = 100004
    //输入长度超限
    const val CONTENT_LENGTH_EXCEEDED = 100005
    //AI模型服务异常，如google的接口异常，超过并发等;
    const val AI_SERVICE_ERROR = 200001
    //the llm is stop Generate content
    const val AI_SERVICE_STOP = 200002
    const val AI_SERVICE_TOKEN_EXCEED = 200003

    //插件未初始化或者已被销毁需要重新初始化
    const val PLUGIN_INIT_ERROR = 100008
    //插件任务执行超时
    const val REQUEST_TIMEOUT = 100009
    //网络不可达
    const val NETWORK_ERROR = 100010
    //#文件解析失败
    const val FILE_PARSE_FAILED = 100011
    //##溯源文档摘要：文档在解析中或者向量化中
    const val FILE_NOT_PREPARED = 100012
    //录音内容较少，智能命名失败
    const val CONTENT_LESS_ERROR = 100013
    //server exception
    const val SERVER_ERROR = 100014
    //## 获取主题失败
    const val LLM_GET_THEME_FAILED = 100015
    //## 主题类型不支持
    const val LLM_THEME_NOT_SUPPORT = 100016

    //语音摘要内部错误
    const val VOICE_SUMMARY_INNER_ERROR = 300000
    //输入错误 # Unsupported language
    const val UNSUPPORTED_LANGUAGE = 300001
    //llm服务错误
    const val LLM_CLIENT_ERROR = 300002
    //ner服务内部错误
    const val NER_CLIENT_ERROR = 300003
    //无法生成标题
    const val AI_TITLE_SUPPORT_ERROR = 300004

    //内销文本类摘要错误 #摘要服务内部错误
    const val LLM_SUMMARY_INNER_ERROR = 400000

    const val SMART_EXPCETION = 60000
    const val FILE_SIZE_UNSUPPORT_ZERO = 60001
    const val FILE_SIZE_MAX_UNSUPPORT = 60002
    const val DURATION_MIN_UNSUPPORT_ZERO = 60003
    const val DURATION_MAX_UNSUPPORT = 60004
    const val FILE_FORMAT_UNSUPPORT = 60005
    const val FILE_NOT_EXIST = 60006
    const val CONVERT_TEXT_UNSUPPORT = 60007

    /*智能标题 detectorName、sceneName*/
    const val DETECT_UNIFIED_SUMMARY = "unified_summary"
    const val SCENE_UNIFIED_SUMMARY = "unified_summary"

    const val ACTION_CHECK_SUPPORT_SMART_NAME = "checkSupportSmartName"
    const val NEW_UNIFIED_SUMMARY_MANAGER = "newUnifiedSummaryManager"
    const val ACTION_START_SMART_NAME = "startSmartName"
    const val ACTION_START_SMART_NAME_V2 = "startSmartNameByBean"
    const val ACTION_START_CONVERT_OR_SMART_NAME = "startConvertOrSmartName"
    const val ACTION_CHECK_HAS_TASK_RUNNING = "checkHasTaskRunning"
    const val ACTION_CHECK_IS_TASK_RUNNING = "checkIsTaskRunning"
    const val ACTION_REGISTER_SMART_NAME_CALLBACK = "registerSmartNameCallback"
    const val ACTION_UN_REGISTER_SMART_NAME_CALLBACK = "unRegisterSmartNameCallback"
    const val ACTION_CANCEL_SMART_NAME_TASK = "cancelSmartNameTask"

    const val ACTION_FROM_JSON = "fromJson"
    const val ACTION_SET_SMART_NAME_SWITCH_STATUS = "setSmartNameSwitchStatus"
    const val ACTION_IS_SMART_NAME_SWITCH_OPEN = "isSmartNameSwitchOpen"
    const val ACTION_SMART_NAME_SWITCH_VALUE = "smartNameSwitchValue"
    const val ACTION_NEED_SHOW_SMART_GUIDE_DIALOG = "needShowSmartGuideDialog"
    const val ACTION_CLEAR_SMART_RETRY_CACHE_TASK = "clearSmartRetryCacheTask"
    const val ACTION_CHECK_TASK_RUNNING_MAX_LIMIT_SIZE = "checkTaskRunningMaxLimitSize"
    const val ACTION_RELEASE_ALL_TASK = "releaseAllTask"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun newUnifiedSummaryManager(): IUnifiedSummaryCallBack? {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, NEW_UNIFIED_SUMMARY_MANAGER).build()
            return OStitch.execute<IUnifiedSummaryCallBack>(apiRequest).result
        }
        return null
    }

    @JvmStatic
    fun checkSupportSmartName(context: Context?,  supportConvert: Boolean?, forceUpdate: Boolean = false): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_CHECK_SUPPORT_SMART_NAME)
                .param(context, supportConvert, forceUpdate)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun startSmartName(mediaId: Long): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_START_CONVERT_OR_SMART_NAME)
                .param(mediaId)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun startSmartName(mediaId: Long, jsonParams: String?): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_START_SMART_NAME)
                .param(mediaId, jsonParams)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun <T> startSmartNameByBean(mediaId: Long, smartNameParam: T?): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_START_SMART_NAME_V2)
                    .param(mediaId, smartNameParam)
                    .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun checkHasTaskRunning(): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_CHECK_HAS_TASK_RUNNING)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun checkIsTaskRunning(mediaId: Long): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_CHECK_IS_TASK_RUNNING)
                .param(mediaId)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun registerSmartNameCallback(mediaId: Long, callback: ISmartNameCallback) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_REGISTER_SMART_NAME_CALLBACK)
                .param(mediaId, callback)
                .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun unRegisterSmartNameCallback(mediaId: Long, callback: ISmartNameCallback) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_UN_REGISTER_SMART_NAME_CALLBACK)
                .param(mediaId, callback)
                .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun cancelSmartNameTask(mediaId: Long) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_CANCEL_SMART_NAME_TASK)
                .param(mediaId)
                .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    inline fun <reified T> fromJson(json: String?, cls: Class<T>): T? {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_FROM_JSON)
                .param(json, cls)
                .build()
            return OStitch.execute<T>(apiRequest).result
        }
        return null
    }

    @JvmStatic
    fun setSmartNameSwitchStatus(mContext: Context, isOpen: Boolean, needStatistics: Boolean = true) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_SET_SMART_NAME_SWITCH_STATUS)
                .param(mContext, isOpen, needStatistics)
                .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun isSmartNameSwitchOpen(mContext: Context): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_IS_SMART_NAME_SWITCH_OPEN)
                .param(mContext)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun smartNameSwitchValue(mContext: Context): Int {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_SMART_NAME_SWITCH_VALUE)
                .param(mContext)
                .build()
            return OStitch.execute<Int>(apiRequest).result ?: -1
        }
        return -1
    }

    @JvmStatic
    fun needShowSmartGuideDialog(mContext: Context): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_NEED_SHOW_SMART_GUIDE_DIALOG)
                .param(mContext)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun clearSmartRetryCacheTask() {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_CLEAR_SMART_RETRY_CACHE_TASK)
                .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun checkTaskRunningMaxLimitSize(): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_CHECK_TASK_RUNNING_MAX_LIMIT_SIZE)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun releaseAllTask() {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ACTION_RELEASE_ALL_TASK)
                .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }
}