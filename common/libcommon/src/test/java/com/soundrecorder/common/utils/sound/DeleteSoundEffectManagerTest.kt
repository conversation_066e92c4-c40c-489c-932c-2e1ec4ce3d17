/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: DeleteSoundEffectManager
 * Description:
 * Version: 1.0
 * Date: 2023/6/25
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/6/25 1.0 create
 */

package com.soundrecorder.common.utils.sound

import android.content.Context
import android.media.AudioManager
import android.os.Build
import android.provider.Settings
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class DeleteSoundEffectManagerTest {
    companion object {
        private const val SUPPORT_KEY = "global_delete_sound"
    }

    private var mockContext: Context? = null
    private var mockedStaticBaseApplication: MockedStatic<BaseApplication>? = null

    @Before
    fun setUp() {
        mockContext = Mockito.mock(Context::class.java)
        mockedStaticBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mockedStaticBaseApplication?.`when`<Context> { BaseApplication.getAppContext() }
            ?.thenReturn(mockContext)
    }

    @After
    fun release() {
        mockedStaticBaseApplication?.close()
        mockedStaticBaseApplication = null
        mockContext = null
    }

    @Test
    fun should_notNull_when_initData() {
        val manager = DeleteSoundEffectManager.getInstance()
        Assert.assertNull(Whitebox.getInternalState(manager, "mDeleteSoundPlayer"))

        manager.initData()
        Assert.assertNotNull(Whitebox.getInternalState(manager, "mDeleteSoundPlayer"))

        manager.release()
    }

    @Test
    fun should_when_isDeleteSoundSwitchOpen() {
        val manager = DeleteSoundEffectManager.getInstance()
        Settings.Secure.putInt(BaseApplication.getAppContext().contentResolver, SUPPORT_KEY, 0)
        var isDeleteSoundOpen = Whitebox.invokeMethod<Boolean>(manager, "isDeleteSoundSwitchOpen")
        Assert.assertFalse(isDeleteSoundOpen)

        Settings.Secure.putInt(BaseApplication.getAppContext().contentResolver, SUPPORT_KEY, 1)
        isDeleteSoundOpen = Whitebox.invokeMethod(manager, "isDeleteSoundSwitchOpen")
        Assert.assertTrue(isDeleteSoundOpen)

        manager.release()
    }

    @Test
    fun should_playDeleteSound() {
        val manager = DeleteSoundEffectManager.getInstance()
        Settings.Secure.putInt(BaseApplication.getAppContext().contentResolver, SUPPORT_KEY, 0)
        manager.playDeleteSound()
        Assert.assertNull(Whitebox.getInternalState(manager, "mDeleteSoundPlayer"))

        Settings.Secure.putInt(BaseApplication.getAppContext().contentResolver, SUPPORT_KEY, 1)
        val mockAudioManager = Mockito.mock(AudioManager::class.java)
        Mockito.`when`(mockContext!!.getSystemService(Context.AUDIO_SERVICE))
            .thenReturn(mockAudioManager)
        Mockito.`when`(mockAudioManager.ringerMode)
            .thenReturn(AudioManager.RINGER_MODE_SILENT, AudioManager.RINGER_MODE_NORMAL)
        manager.playDeleteSound()
        Assert.assertNull(Whitebox.getInternalState(manager, "mDeleteSoundPlayer"))

        manager.playDeleteSound()
        Assert.assertNotNull(Whitebox.getInternalState(manager, "mDeleteSoundPlayer"))

        manager.release()
    }

    @Test
    fun should_notNull_when_release() {
        val manager = DeleteSoundEffectManager.getInstance()
        manager.initData()
        Assert.assertNotNull(Whitebox.getInternalState(manager, "mDeleteSoundPlayer"))

        manager.release()
    }

    @Test
    fun should_when_judgeNotToPlay() {
        val funcName = "judgeNotToPlay"
        val mockAudioManager = Mockito.mock(AudioManager::class.java)
        Mockito.`when`(mockContext!!.getSystemService(Context.AUDIO_SERVICE))
            .thenReturn(mockAudioManager)
        Mockito.`when`(mockAudioManager.ringerMode).thenReturn(
            AudioManager.RINGER_MODE_SILENT,
            AudioManager.RINGER_MODE_VIBRATE, AudioManager.RINGER_MODE_NORMAL
        )

        val manager = DeleteSoundEffectManager.getInstance()
        var judgeNotToPlay = Whitebox.invokeMethod<Boolean>(manager, funcName, mockContext)
        Assert.assertTrue(judgeNotToPlay)

        judgeNotToPlay = Whitebox.invokeMethod(manager, funcName, mockContext)
        Assert.assertTrue(judgeNotToPlay)

        judgeNotToPlay = Whitebox.invokeMethod(manager, funcName, mockContext)
        Assert.assertFalse(judgeNotToPlay)

        // when manager is null
        Mockito.`when`(mockContext!!.getSystemService(Context.AUDIO_SERVICE))
            .thenReturn(null)
        judgeNotToPlay = Whitebox.invokeMethod(manager, funcName, mockContext)
        Assert.assertFalse(judgeNotToPlay)

        // when exception
        Mockito.`when`(mockContext!!.getSystemService(Context.AUDIO_SERVICE))
            .thenThrow(RuntimeException())
        judgeNotToPlay = Whitebox.invokeMethod(manager, funcName, mockContext)
        Assert.assertFalse(judgeNotToPlay)

        manager.release()
    }
}