/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ActivityTaskUtilsTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.task

import android.os.Build
import android.os.Bundle
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.permission.PermissionProxyActivity
import com.soundrecorder.common.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.task.ActivityTaskUtils.getAppActivities
import com.soundrecorder.common.task.ActivityTaskUtils.getLastActivity
import com.soundrecorder.common.task.ActivityTaskUtils.isEmptyExcludeSelf
import com.soundrecorder.common.task.ActivityTaskUtils.isFirstTaskBrowseFileActivity
import com.soundrecorder.common.task.ActivityTaskUtils.isTaskEmpty
import com.soundrecorder.common.task.ActivityTaskUtils.isTaskEmptyExceptActivity
import com.soundrecorder.common.task.ActivityTaskUtils.secondActivity
import com.soundrecorder.common.task.ActivityTaskUtils.topActivity
import com.soundrecorder.common.task.ActivityTaskUtils.topActivityOnFrontTask
import com.soundrecorder.common.task.ActivityTaskUtils.topActivityWithOutFinishingActivity
import com.soundrecorder.common.utils.TestExcludeActivity
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.Robolectric
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowLog::class, ShadowOS12FeatureUtil::class,
    ShadowFeatureOption::class, ShadowCOUIVersionUtil::class])
class ActivityTaskUtilsTest {

    @Test
    fun check_topActivity() {
        val controller =
            Robolectric.buildActivity(PermissionProxyActivity::class.java).create().start().resume()
        val act1 = controller.get()
        Assert.assertEquals(topActivity(act1.taskId), act1)
        Assert.assertEquals(topActivityOnFrontTask(), act1)
        Assert.assertEquals(act1.topActivity(), act1)
        Assert.assertEquals(topActivityWithOutFinishingActivity(), act1)
        act1.finish()
        controller.pause().stop().destroy()
        Assert.assertNull(topActivityOnFrontTask())
        Assert.assertNull(topActivityWithOutFinishingActivity())
        Assert.assertEquals(getAppActivities().size, 0)
    }

    @Test
    fun check_saveInstanceState() {
        val bundle = Bundle()
        val controller =
            Robolectric.buildActivity(PermissionProxyActivity::class.java).create().start().resume()
        val act1 = controller.get()
        controller.saveInstanceState(bundle).recreate()
        val act2 = controller.get()
        Assert.assertNotEquals(act1, act2)
        Assert.assertNotEquals(topActivityOnFrontTask(), act1)
        Assert.assertEquals(topActivityOnFrontTask(), act2)
        Assert.assertEquals(getAppActivities().size, 1)
        act2.finish()
        controller.pause().stop().destroy()
    }

    @Test
    fun check_secondActivity() {
        val cto1 =
            Robolectric.buildActivity(PermissionProxyActivity::class.java).create().start().resume()
        val act1 = cto1.get()
        Assert.assertNull(secondActivity(act1.taskId))
        val cto2 =
            Robolectric.buildActivity(PermissionProxyActivity::class.java).create().start().resume()
        val act2 = cto2.get()
        Assert.assertEquals(secondActivity(act2.taskId), act1)
        act1.finish()
        cto1.pause().stop().destroy()
        act2.finish()
        cto2.pause().stop().destroy()
    }

    @Test
    fun check_getLastActivity() {
        val cto1 =
            Robolectric.buildActivity(PermissionProxyActivity::class.java).create().start().resume()
        val act1 = cto1.get()
        Assert.assertEquals(getLastActivity(act1.taskId), act1)
        val cto2 =
            Robolectric.buildActivity(PermissionProxyActivity::class.java).create().start().resume()
        val act2 = cto2.get()
        Assert.assertEquals(getLastActivity(act2.taskId), act1)
        act1.finish()
        cto1.pause().stop().destroy()
        act2.finish()
        cto2.pause().stop().destroy()
    }

    @Test
    fun check_isTaskEmpty() {
        Assert.assertTrue(isTaskEmpty(-1))
        val cto1 =
            Robolectric.buildActivity(PermissionProxyActivity::class.java).create().start().resume()
        val act1 = cto1.get()
        Assert.assertFalse(isTaskEmpty(act1.taskId))
        act1.finish()
        cto1.pause().stop().destroy()
    }

    @Test
    fun check_isFirstTaskBrowseFileActivity() {
        val cto1 =
            Robolectric.buildActivity(PermissionProxyActivity::class.java).create().start().resume()
        val act1 = cto1.get()
        Assert.assertTrue(isFirstTaskBrowseFileActivity(act1.taskId, act1.javaClass.name))
        val cto2 =
            Robolectric.buildActivity(PermissionProxyActivity::class.java).create().start().resume()
        val act2 = cto2.get()
        Assert.assertTrue(
            isFirstTaskBrowseFileActivity(
                act2.taskId,
                act2.javaClass.name
            )
        )
        Assert.assertFalse(isFirstTaskBrowseFileActivity(act2.taskId, ""))
        act1.finish()
        cto1.pause().stop().destroy()
        act2.finish()
        cto2.pause().stop().destroy()
        Assert.assertFalse(isFirstTaskBrowseFileActivity(-1, ""))
    }

    @Test
    fun check_isTaskEmptyExceptActivity() {
        val cto1 =
            Robolectric.buildActivity(PermissionProxyActivity::class.java).create().start().resume()
        val act1 = cto1.get()
        Assert.assertTrue(isTaskEmptyExceptActivity(act1.taskId, act1))
        val cto2 =
            Robolectric.buildActivity(PermissionProxyActivity::class.java).create().start().resume()
        val act2 = cto2.get()
        Assert.assertFalse(isTaskEmptyExceptActivity(act1.taskId, act2))

        act1.finish()
        cto1.pause().stop().destroy()
        act2.finish()
        cto2.pause().stop().destroy()
    }

    @Test
    fun isEmptyExcludeSelf() {
        val act = Mockito.spy(Robolectric.buildActivity(PermissionProxyActivity::class.java).create().start().resume())
        Assert.assertTrue(act.get().isEmptyExcludeSelf())
        val act1 = Mockito.spy(Robolectric.buildActivity(PermissionProxyActivity::class.java).create().start().resume())
        Assert.assertFalse(act1.get().isEmptyExcludeSelf())
        act.get().finish()
        act.pause().stop().destroy()
        act1.get().finish()
        act1.pause().stop().destroy()
    }

    @Test
    fun textExcludeActivityTask() {
        val act = Mockito.spy(Robolectric.buildActivity(TestExcludeActivity::class.java).create().start().resume())
        Assert.assertTrue(act.get().isEmptyExcludeSelf())
        val act1 = Mockito.spy(Robolectric.buildActivity(TestExcludeActivity::class.java).create().start().resume())
        Assert.assertTrue(act1.get().isEmptyExcludeSelf())
        act.get().finish()
        act.pause().stop().destroy()
        act1.get().finish()
        act1.pause().stop().destroy()
    }

    @Test
    fun testClearAllTask() {
        val act = Mockito.spy(Robolectric.buildActivity(PermissionProxyActivity::class.java).create().start().resume())
        ActivityTaskUtils.clearAllTask()
        Assert.assertTrue(getAppActivities().none { !it.isFinishing })
        act.destroy()
    }

    @Test
    fun backToBrowse() {
        val act = Mockito.spy(
            Robolectric.buildActivity(TestExcludeActivity::class.java).create().start().resume()
        )
        Mockito.spy(
            Robolectric.buildActivity(TestExcludeActivity::class.java).create().start().resume()
        )
        ActivityTaskUtils.backToBrowse(act.get().taskId)
    }
}