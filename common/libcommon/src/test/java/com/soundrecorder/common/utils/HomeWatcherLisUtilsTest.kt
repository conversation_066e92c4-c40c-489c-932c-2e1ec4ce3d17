/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: HomeWatcherLisUtilsTest
 Description:
 Version: 1.0
 Date: 2022/9/23
 Author: W9013333(v-z<PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/9/23 1.0 create
 */

package com.soundrecorder.common.utils

import android.content.Intent
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.permission.PermissionProxyActivity
import com.soundrecorder.common.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@Suppress("DEPRECATION")
@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class,
    ShadowFeatureOption::class, ShadowCOUIVersionUtil::class])
class HomeWatcherLisUtilsTest {
    companion object {
        private const val SYSTEM_REASON = "reason"
        private const val SYSTEM_HOME_KEY = "homekey"
        private const val SYSTEM_RECENT_APPS = "recentapps"
    }

    private var activityController: ActivityController<PermissionProxyActivity>? = null

    @Before
    fun before() {
        activityController = Mockito.spy(Robolectric.buildActivity(PermissionProxyActivity::class.java))
    }

    @After
    fun after() {
        activityController = null
    }

    @Test
    fun test() {
        val activity = activityController?.create()?.start()?.resume()?.get() ?: return
        HomeWatchUtils(activity) {
            Assert.assertTrue(it == SYSTEM_HOME_KEY || it == SYSTEM_RECENT_APPS)
        }
        BaseApplication.getApplication().sendBroadcast(Intent(Intent.ACTION_CLOSE_SYSTEM_DIALOGS).apply {
            putExtra(SYSTEM_REASON, SYSTEM_HOME_KEY)
        })
        BaseApplication.getApplication().sendBroadcast(Intent(Intent.ACTION_CLOSE_SYSTEM_DIALOGS).apply {
            putExtra(SYSTEM_REASON, SYSTEM_RECENT_APPS)
        })
    }
}
