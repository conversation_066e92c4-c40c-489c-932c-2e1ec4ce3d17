package com.soundrecorder.common.buryingpoint

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.*
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class BuryingPointUtilTest {

    private var mockedStatic: MockedStatic<RecorderUserAction>? = null
    private var mockedBaseApplication: MockedStatic<BaseApplication>? = null
    private var mContext: Context? = null

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mockedStatic = Mockito.mockStatic(RecorderUserAction::class.java)
        mContext = ApplicationProvider.getApplicationContext()
        mockedBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mockedBaseApplication!!.`when`<Context> { BaseApplication.getAppContext() }?.thenReturn(mContext)
    }

    @After
    @Throws(Exception::class)
    fun tearDown() {
        mockedStatic?.close()
        mockedBaseApplication?.close()
        mockedBaseApplication = null
        mContext = null
    }

    @Test
    fun check_addFromNotification() {
        BuryingPointUtil.addFromNotification()
        mockedStatic?.verify({
            RecorderUserAction.addCommonUserAction(
                any(),
                anyInt(),
                anyInt(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addFromStatusBar() {
        BuryingPointUtil.addFromStatusBar()
        mockedStatic?.verify({
            RecorderUserAction.addCommonUserAction(
                any(),
                anyInt(),
                anyInt(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addUserAudioFileCount() {
        BuryingPointUtil.addUserAudioFileCount(2, 0, 1, 1, 0, 0)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyInt(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }
}