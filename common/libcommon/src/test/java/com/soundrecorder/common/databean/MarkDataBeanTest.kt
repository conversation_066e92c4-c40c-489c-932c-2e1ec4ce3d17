/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  MarkDataBeanTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.databean

import android.graphics.Bitmap
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.common.R
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.utils.MarkSerializUtil
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class MarkDataBeanTest {

    @Test
    fun enqueueIcon() {
        val mark = MarkDataBean(1000L, MarkSerializUtil.VERSION_PICTURE)
        mark.pictureFilePath = "IMG_SOUNDER_${System.currentTimeMillis()}.jpg"
        Assert.assertTrue(mark.getWaveImageLoadData().toString().contains(mark.pictureFilePath))
        val bm = mark.getIcon(
            BaseApplication.getAppContext(),
            android.R.drawable.btn_default,
            android.R.drawable.btn_default
        )
        Assert.assertTrue(bm != null)
    }

    @Test
    fun getAbsoluteFile() {
        val pictureFilePath = "IMG_SOUNDER_${System.currentTimeMillis()}.jpg"
        FileUtils.getAppFile(pictureFilePath, true)
        val mark = MarkDataBean(1000L, MarkSerializUtil.VERSION_PICTURE)
        mark.pictureFilePath = pictureFilePath
        val isPictureMark = mark.isPictureType()
        Assert.assertTrue(isPictureMark)
    }

    @Test
    fun start() {
        val pictureFilePath = "IMG_SOUNDER_${System.currentTimeMillis()}.jpg"
        FileUtils.getAppFile(pictureFilePath, true)
        val mark = MarkDataBean(1000L, MarkSerializUtil.VERSION_PICTURE)
        mark.pictureFilePath = pictureFilePath
        val isPictureMark = mark.isPictureType()
        Assert.assertTrue(isPictureMark)
        val bm = Whitebox.invokeMethod<Bitmap>(mark, "start", R.drawable.ic_big_speaker_black)
        Assert.assertNotNull(bm)
    }

    @Test
    fun release() {
        val pictureFilePath = "IMG_SOUNDER_${System.currentTimeMillis()}.jpg"
        FileUtils.getAppFile(pictureFilePath, true)
        val mark = MarkDataBean(1000L, MarkSerializUtil.VERSION_PICTURE)
        mark.pictureFilePath = pictureFilePath
        val isPictureMark = mark.isPictureType()
        Assert.assertTrue(isPictureMark)
        Assert.assertTrue(mark.src().exists())
        val bm = Whitebox.invokeMethod<Bitmap>(mark, "start", R.drawable.ic_big_speaker_black)
        Assert.assertNotNull(bm)
        mark.release()
        val pictureMarkIcon = Whitebox.getInternalState<Bitmap>(mark, "pictureMarkIcon")
        Assert.assertNull(pictureMarkIcon)
    }
}