package com.soundrecorder.common.transition;

import android.content.Context;
import android.os.Build;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;

import org.robolectric.annotation.Config;

import java.util.ArrayList;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class RemoveItemAnimatorTest {
    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @Test
    public void should_returnTrue_when_animateMove() {
        RemoveItemAnimator removeItemAnimator = new RemoveItemAnimator();
        RecyclerView.ViewHolder holder = new RecyclerView.ViewHolder(new View(mContext)) {
            @Override
            public String toString() {
                return super.toString();
            }
        };
        boolean flag = removeItemAnimator.animateMove(holder, 0, 1, 1, 0);
        Assert.assertTrue(flag);
        boolean flag2 = removeItemAnimator.animateMove(holder, 1, 0, 0, 1);
        Assert.assertFalse(flag2);
    }

    @Test
    public void should_returnTrue_when_animateChange() {
        RemoveItemAnimator removeItemAnimator = new RemoveItemAnimator();
        RecyclerView.ViewHolder newholder = new RecyclerView.ViewHolder(new View(mContext)) {
            @Override
            public String toString() {
                return super.toString();
            }
        };
        RecyclerView.ViewHolder oldholder = new RecyclerView.ViewHolder(new View(mContext)) {
            @Override
            public String toString() {
                return super.toString();
            }
        };
        boolean flag = removeItemAnimator.animateChange(newholder, oldholder, 0, 1, 1, 0);
        Assert.assertTrue(flag);
    }

    @Test
    public void should_returnValue_when_runPendingAnimations() {
        RemoveItemAnimator removeItemAnimator = new RemoveItemAnimator();
        RecyclerView.ViewHolder newholder = new RecyclerView.ViewHolder(new View(mContext)) {
            @Override
            public String toString() {
                return super.toString();
            }
        };
        RecyclerView.ViewHolder oldholder = new RecyclerView.ViewHolder(new View(mContext)) {
            @Override
            public String toString() {
                return super.toString();
            }
        };
        removeItemAnimator.animateAdd(newholder);
        removeItemAnimator.runPendingAnimations();
        ArrayList<RecyclerView.ViewHolder> mPendingRemovals = Whitebox.getInternalState(removeItemAnimator, "mPendingRemovals");
        Assert.assertEquals(mPendingRemovals.size(), 0);
    }

    @Test
    public void should_returnValue_when_endAnimations() {
        RemoveItemAnimator removeItemAnimator = new RemoveItemAnimator();
        RecyclerView.ViewHolder newholder = new RecyclerView.ViewHolder(new View(mContext)) {
            @Override
            public String toString() {
                return super.toString();
            }
        };
        RecyclerView.ViewHolder oldholder = new RecyclerView.ViewHolder(new View(mContext)) {
            @Override
            public String toString() {
                return super.toString();
            }
        };
        removeItemAnimator.animateAdd(newholder);
        removeItemAnimator.animateMove(newholder, 1, 1, 1, 1);
        removeItemAnimator.animateChange(oldholder, newholder, 1, 1, 1, 1);
        removeItemAnimator.endAnimations();
        ArrayList<RecyclerView.ViewHolder> mPendingRemovals = Whitebox.getInternalState(removeItemAnimator, "mPendingRemovals");
        Assert.assertEquals(mPendingRemovals.size(), 0);
    }

    @Test
    public void should_returnValue_when_animateMoveImpl() throws Exception {
        RemoveItemAnimator removeItemAnimator = new RemoveItemAnimator();
        RecyclerView.ViewHolder newholder = new RecyclerView.ViewHolder(new View(mContext)) {
            @Override
            public String toString() {
                return super.toString();
            }
        };
        Whitebox.invokeMethod(removeItemAnimator, "animateMoveImpl", newholder, 1, 1, 1, 1);
        ArrayList<RecyclerView.ViewHolder> mMoveAnimations = Whitebox.getInternalState(removeItemAnimator, "mMoveAnimations");
        Assert.assertEquals(mMoveAnimations.size(), 1);
    }

    @Test
    public void should_returnNotNull_when_endChangeAnimationIfNecessary() throws Exception {
        RemoveItemAnimator removeItemAnimator = new RemoveItemAnimator();
        RecyclerView.ViewHolder newholder = new RecyclerView.ViewHolder(new View(mContext)) {
            @Override
            public String toString() {
                return super.toString();
            }
        };
        removeItemAnimator.animateChange(newholder, newholder, 1, 1, 1, 1);
        Assert.assertNotNull(Whitebox.getInternalState(removeItemAnimator, "mPendingChanges"));
    }

    @Test
    public void should_returnNotNull_when_animateRemove() throws Exception {
        RemoveItemAnimator removeItemAnimator = new RemoveItemAnimator();
        RecyclerView.ViewHolder newholder = new RecyclerView.ViewHolder(new View(mContext)) {
            @Override
            public String toString() {
                return super.toString();
            }
        };
        removeItemAnimator.animateRemove(newholder);
        Assert.assertNotNull(Whitebox.getInternalState(removeItemAnimator, "mPendingChanges"));
    }

    @Test
    public void should_returnNotNull_when_animateRemoveImpl() throws Exception {
        RemoveItemAnimator removeItemAnimator = new RemoveItemAnimator();
        RecyclerView.ViewHolder newholder = new RecyclerView.ViewHolder(new View(mContext)) {
            @Override
            public String toString() {
                return super.toString();
            }
        };
        Whitebox.invokeMethod(removeItemAnimator, "animateRemoveImpl", newholder);
        Assert.assertNotNull(Whitebox.getInternalState(removeItemAnimator, "mPendingChanges"));
    }


    @After
    public void tearDown() {
        mContext = null;
    }
}
