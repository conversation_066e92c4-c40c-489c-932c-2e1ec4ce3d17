/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: TimeSetUtilsTest
 Description:
 Version: 1.0
 Date: 2022/9/23
 Author: W9013333(v-z<PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/9/23 1.0 create
 */

package com.soundrecorder.common.utils

import android.content.Intent
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.permission.PermissionProxyActivity
import com.soundrecorder.common.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class,
    ShadowFeatureOption::class, ShadowCOUIVersionUtil::class])
class TimeSetUtilsTest {
    private var activityController: ActivityController<PermissionProxyActivity>? = null

    @Before
    fun before() {
        activityController = Mockito.spy(Robolectric.buildActivity(PermissionProxyActivity::class.java))
    }

    @After
    fun after() {
        activityController = null
    }

    @Test
    fun test() {
        val activity = activityController?.create()?.start()?.resume()?.get() ?: return
        TimeSetUtils(activity) {
            Assert.assertTrue(it == Intent.ACTION_DATE_CHANGED || it == Intent.ACTION_TIME_CHANGED)
        }
        BaseApplication.getApplication().sendBroadcast(Intent(Intent.ACTION_DATE_CHANGED))
        BaseApplication.getApplication().sendBroadcast(Intent(Intent.ACTION_TIME_CHANGED))
    }
}
