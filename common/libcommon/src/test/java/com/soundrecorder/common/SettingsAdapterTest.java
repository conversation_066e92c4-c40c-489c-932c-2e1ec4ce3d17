/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : SettingsAdapterTest.java
 * Version Number : 1.0
 * Description    :
 * Author         : LI Kun
 * Date           : 2019/8/26
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019/8/26, LI Kun, create
 ************************************************************/

package com.soundrecorder.common;

import android.content.Context;
import android.content.res.Resources;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.common.shadows.ShadowOplusUsbEnvironment;
import com.soundrecorder.common.utils.SettingsAdapter;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOplusUsbEnvironment.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class SettingsAdapterTest {
    private static final String TEST = "test";
    private Context mContext;
    private SettingsAdapter mAdapter;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mAdapter = new SettingsAdapter();
    }

    @After
    public void release() {
        mContext = null;
        mAdapter = null;
    }

    @Test
    public void should_returnExpectString_when_get_with_keyFilePrefixIsInt() {
        Object result = mAdapter.get(R.string.key_file_prefix);
        Assert.assertEquals(mContext.getResources().getString(R.string.default_prefix), result.toString());
    }

    @Test(expected = Resources.NotFoundException.class)
    public void should_throwNotFoundException_when_get_with_invalidId() {
        Object result = mAdapter.get(0);
    }

    @Test
    public void should_returnExpectString_when_get_with_keyFilePrefixIsString() {
        String keyFilePrefix = mContext.getResources().getString(R.string.key_file_prefix);
        Object result = mAdapter.get(keyFilePrefix);
        Assert.assertEquals(mContext.getResources().getString(R.string.default_prefix), result.toString());
    }

    @Test
    public void should_returnNull_when_get_with_keyNotExist() {
        Object result = mAdapter.get(TEST);
        Assert.assertNull(result);
    }

    @Test
    public void should_returnNull_when_getStorageSdcard() {
        String result = mAdapter.getStorageSdcard();
        Assert.assertNotNull(result);
    }

    @Test
    public void should_returnNull_when_getStoragePhone() {
        String result = mAdapter.getStoragePhone();
        Assert.assertNotNull(result);
    }
}
