/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SummaryStaticUtilTest
 * Description:
 * Version: 1.0
 * Date: 2024/3/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/3/18 1.0 create
 */

package com.soundrecorder.common.buryingpoint

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.modulerouter.summary.SummaryAction
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class SummaryStaticUtilTest {
    private var mockedStatic: MockedStatic<RecorderUserAction>? = null
    private var mockedBaseApplication: MockedStatic<BaseApplication>? = null
    private var mContext: Context? = null

    @Before
    fun setUp() {
        mockedStatic = Mockito.mockStatic(RecorderUserAction::class.java)
        mContext = ApplicationProvider.getApplicationContext()
        mockedBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mockedBaseApplication?.`when`<Context> { BaseApplication.getAppContext() }?.thenReturn(mContext)
    }

    @After
    fun tearDown() {
        mockedStatic?.close()
        mockedBaseApplication?.close()
        mockedBaseApplication = null
        mContext = null
    }

    @Test
    fun should_correct_when_addShowStartSummaryEvent() {
        SummaryStaticUtil.addShowStartSummaryEvent("")
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                ArgumentMatchers.any(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyMap<String, String>(),
                ArgumentMatchers.anyBoolean())
        }, Mockito.times(1))
    }

    @Test
    fun should_correct_when_addClickStartSummaryEvent() {
        SummaryStaticUtil.addClickStartSummaryEvent("", SummaryAction.ERROR_CODE_DURATION_LARGE)
        SummaryStaticUtil.addClickStartSummaryEvent("", SummaryAction.ERROR_CODE_FORMAT)
        SummaryStaticUtil.addClickStartSummaryEvent("", SummaryAction.ERROR_CODE_DURATION_LARGE_WARN)
        SummaryStaticUtil.addClickStartSummaryEvent("", SummaryAction.ERROR_CODE_SIZE_LARGE)
        SummaryStaticUtil.addClickStartSummaryEvent("", SummaryAction.ERROR_CODE_NO_INTERNET)
        SummaryStaticUtil.addClickStartSummaryEvent("", SummaryAction.ERROR_CODE_SUPER_SAVE_MODE)
        SummaryStaticUtil.addClickStartSummaryEvent("", SummaryAction.ERROR_CODE_SIZE_SMALL)
        SummaryStaticUtil.addClickStartSummaryEvent("", SummaryAction.ERROR_CODE_SUCCESS)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                ArgumentMatchers.any(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyMap<String, String>(),
                ArgumentMatchers.anyBoolean())
        }, Mockito.times(8))
    }

    @Test
    fun should_correct_when_addShowViewSummaryEvent() {
        SummaryStaticUtil.addShowViewSummaryEvent("")
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                ArgumentMatchers.any(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyMap<String, String>(),
                ArgumentMatchers.anyBoolean())
        }, Mockito.times(1))
    }

    @Test
    fun should_correct_when_addClickViewSummaryEvent() {
        SummaryStaticUtil.addClickViewSummaryEvent("")
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                ArgumentMatchers.any(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyMap<String, String>(),
                ArgumentMatchers.anyBoolean())
        }, Mockito.times(1))
    }
}