package com.soundrecorder.common.utils.sound;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;

import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.utils.sound.DeleteSoundMediaPlayer;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class DeleteSoundMediaPlayerTest {

    private Context mContext;
    private MockedStatic<TextUtils> mTextUtilsMockedStatic;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mTextUtilsMockedStatic = mockStatic(TextUtils.class);
        mTextUtilsMockedStatic.when(() -> TextUtils.isEmpty(any())).thenAnswer(invocation -> {
            CharSequence a = invocation.getArgument(0);
            if (a == null || a.length() == 0) {
                return true;
            }
            return false;
        });
    }

    @After
    public void tearDown() {
        mContext = null;
        mTextUtilsMockedStatic.close();
        mTextUtilsMockedStatic = null;
    }

    @Test
    public void should_returnNotnull_when_playDeleteSound() {
        DeleteSoundMediaPlayer deleteSoundMediaPlayer = new DeleteSoundMediaPlayer();
        MediaPlayer mediaPlayer = Whitebox.getInternalState(deleteSoundMediaPlayer, "mediaPlayer");
        Assert.assertNotNull(mediaPlayer);
        deleteSoundMediaPlayer.playDeleteSound();
        Assert.assertFalse(mediaPlayer.isPlaying());
    }

    @Test
    public void should_returnNull_when_release() {
        DeleteSoundMediaPlayer deleteSoundMediaPlayer = new DeleteSoundMediaPlayer();
        deleteSoundMediaPlayer.release();
        MediaPlayer mediaPlayer = Whitebox.getInternalState(deleteSoundMediaPlayer, "mediaPlayer");
        Assert.assertNull(mediaPlayer);
    }

    @Test
    public void should_returnNull_when_getSoundPath() {
        DeleteSoundMediaPlayer deleteSoundMediaPlayer = new DeleteSoundMediaPlayer();
        Assert.assertNull(deleteSoundMediaPlayer.getSoundPath(mContext, ""));
        Assert.assertNull(deleteSoundMediaPlayer.getSoundPath(mContext, null));
        Assert.assertNull(deleteSoundMediaPlayer.getSoundPath(mContext, "global_delete.ogg"));

        Cursor cursor = Mockito.mock(Cursor.class);
        Mockito.when(cursor.moveToFirst()).thenReturn(true);
        Mockito.when(cursor.getInt(anyInt())).thenReturn(1);
        ContentResolver contentResolver = mock(ContentResolver.class);
        Mockito.when(contentResolver.query((Uri) any(), (String[]) any(), anyString(), (String[]) any(), anyString())).thenReturn(cursor);
        Context context = Mockito.mock(Context.class);
        Mockito.when(context.getContentResolver()).thenReturn(contentResolver);
        deleteSoundMediaPlayer.getSoundPath(mContext, "global_delete.ogg");
    }
}
