/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: CuttingStaticsUtilTest
 * Description:
 * Version: 1.0
 * Date: 2023/7/07
 * Author: W9020254
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9020254 2023/7/07 1.0 create
 */

package com.soundrecorder.common.buryingpoint

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.any
import org.mockito.ArgumentMatchers.anyBoolean
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.ArgumentMatchers.anyMap
import org.mockito.ArgumentMatchers.anyString
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class CuttingStaticsUtilTest {
    private var mockedStatic: MockedStatic<RecorderUserAction>? = null
    private val TEST_STRING = "test_string"

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mockedStatic = Mockito.mockStatic(RecorderUserAction::class.java)
    }

    @After
    @Throws(Exception::class)
    fun tearDown() {
        mockedStatic?.close()
    }

    @Test
    fun check_addPlayMoreTrim() {
        CuttingStaticsUtil.addPlayMoreTrim()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addTrimDragWave() {
        CuttingStaticsUtil.addTrimDragWave()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addTrimDragPreview() {
        CuttingStaticsUtil.addTrimDragPreview()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_doCutStart() {
        CuttingStaticsUtil.doCutStart()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_doCutEnd() {
        CuttingStaticsUtil.doCutEnd()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addCutTrimCancle() {
        CuttingStaticsUtil.addCutTrimCancle(TEST_STRING)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addTrimExtract() {
        CuttingStaticsUtil.addTrimExtract()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addCutTrimSave() {
        CuttingStaticsUtil.addCutTrimSave(TEST_STRING)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addTrimDelete() {
        CuttingStaticsUtil.addTrimDelete()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addCutTrimMenuSave() {
        CuttingStaticsUtil.addCutTrimMenuSave()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addCutTrimDialogSaveCancel() {
        CuttingStaticsUtil.addCutTrimDialogSaveCancel(TEST_STRING)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addActionSaveClipNameEdit() {
        CuttingStaticsUtil.addActionSaveClipNameEdit()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addCutTrimPlayPause() {
        CuttingStaticsUtil.addCutTrimPlayPause(TEST_STRING)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }
}