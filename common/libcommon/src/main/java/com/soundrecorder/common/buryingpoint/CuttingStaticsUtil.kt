/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: CuttingStaticsUtil
 * Description:
 * Version: 1.0
 * Date: 2023/7/07
 * Author: W9020254
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9020254 2023/7/07 1.0 create
 */

package com.soundrecorder.common.buryingpoint

import com.soundrecorder.base.BaseApplication

object CuttingStaticsUtil {

    @JvmStatic
    fun addPlayMoreTrim() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[RecorderUserAction.KEY_PLAY_TRIM] = RecorderUserAction.VALUE_PLAY_TRIM
        RecorderUserAction.addNewCommonUserAction(
                BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_TRIM,
                RecorderUserAction.EVENT_TRIM,
                eventInfo, false
        )
    }

    @JvmStatic
    fun addTrimDragWave() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[RecorderUserAction.KEY_TRIM_DRAG_WAVE] = RecorderUserAction.VALUE_TRIM_DRAG_WAVE
        RecorderUserAction.addNewCommonUserAction(
                BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_TRIM,
                RecorderUserAction.EVENT_TRIM,
                eventInfo,
                false
        )
    }

    @JvmStatic
    fun addTrimDragPreview() {
        val eventInfo: MutableMap<String?, String?> = java.util.HashMap()
        eventInfo[RecorderUserAction.KEY_TRIM_DRAG_PREVIEW] = RecorderUserAction.VALUE_TRIM_DRAG_PREVIEW
        RecorderUserAction.addNewCommonUserAction(
                BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_TRIM,
                RecorderUserAction.EVENT_TRIM,
                eventInfo,
                false
        )
    }

    /**
     * 设置缩略图裁切起点
     */
    @JvmStatic
    fun doCutStart() {
        val eventInfo: MutableMap<String?, String?> = java.util.HashMap()
        eventInfo[RecorderUserAction.KEY_TRIM_SET_START] = RecorderUserAction.VALUE_TRIM_SET_START
        RecorderUserAction.addNewCommonUserAction(
                BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_TRIM,
                RecorderUserAction.EVENT_TRIM,
                eventInfo,
                false
        )
    }

    /**
     * 设置缩略图裁切终点
     */
    @JvmStatic
    fun doCutEnd() {
        val eventInfo: MutableMap<String?, String?> = java.util.HashMap()
        eventInfo[RecorderUserAction.KEY_TRIM_SET_END] = RecorderUserAction.VALUE_TRIM_SET_END
        RecorderUserAction.addNewCommonUserAction(
                BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_TRIM,
                RecorderUserAction.EVENT_TRIM,
                eventInfo,
                false
        )
    }

    /**
     * 裁切录音页面-是否保留裁切结果
     * 0：点击关闭按钮
     * 1：点击退出弹框中的退出按钮
     * 2：点击退出弹框中的继续编辑按钮
     */
    @JvmStatic
    fun addCutTrimCancle(value: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[RecorderUserAction.KEY_TRIM_CANCLE] = value
        RecorderUserAction.addNewCommonUserAction(
                BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_TRIM,
                RecorderUserAction.EVENT_TRIM,
                eventInfo,
                false
        )
    }

    @JvmStatic
    fun addTrimExtract() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[RecorderUserAction.KEY_TRIM_EXTRACT] = RecorderUserAction.VALUE_TRIM_EXTRACT
        RecorderUserAction.addNewCommonUserAction(
                BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_TRIM,
                RecorderUserAction.EVENT_TRIM,
                eventInfo,
                false
        )
    }

    /**
     * 裁切录音页面-是否保留裁切结果
     * 0：保留
     * 1：不保留
     */
    @JvmStatic
    fun addCutTrimSave(value: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[RecorderUserAction.KEY_TRIM_SAVE] = value
        RecorderUserAction.addNewCommonUserAction(
                BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_TRIM,
                RecorderUserAction.EVENT_TRIM,
                eventInfo,
                false
        )
    }

    @JvmStatic
    fun addTrimDelete() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[RecorderUserAction.KEY_TRIM_DELETE] = RecorderUserAction.VALUE_TRIM_DELETE
        RecorderUserAction.addNewCommonUserAction(
                BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_TRIM,
                RecorderUserAction.EVENT_TRIM,
                eventInfo,
                false
        )
    }

    /**
     * 裁切录音页面-点击右上角保存按钮
     */
    @JvmStatic
    fun addCutTrimMenuSave() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[RecorderUserAction.KEY_TRIM_MENU_SAVE] = RecorderUserAction.DEFAULT_VALUE
        RecorderUserAction.addNewCommonUserAction(
                BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_TRIM,
                RecorderUserAction.EVENT_TRIM,
                eventInfo,
                false
        )
    }

    /**
     * 裁切录音页面-点击右上角保存按钮-另存为新录音弹框
     * 0：点击保存按钮
     * 1：点击取消按钮
     */
    @JvmStatic
    fun addCutTrimDialogSaveCancel(value: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[RecorderUserAction.KEY_TRIM_DIALOG_SAVE_CANCEL] = value
        RecorderUserAction.addNewCommonUserAction(
                BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_TRIM,
                RecorderUserAction.EVENT_TRIM,
                eventInfo,
                false
        )
    }

    @JvmStatic
    fun addActionSaveClipNameEdit() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[RecorderUserAction.KEY_TRIM_EDIT_NAME] = RecorderUserAction.VALUE_TRIM_EDIT_NAME
        RecorderUserAction.addNewCommonUserAction(
                BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_TRIM,
                RecorderUserAction.EVENT_TRIM,
                eventInfo,
                false
        )
    }

    /**
     * 裁切录音页面-点击播放/暂停
     * 0：播放
     * 1：暂停播放
     */
    @JvmStatic
    fun addCutTrimPlayPause(value: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[RecorderUserAction.KEY_TRIM_PLAY_PAUSE] = value
        RecorderUserAction.addNewCommonUserAction(
                BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_TRIM,
                RecorderUserAction.EVENT_TRIM,
                eventInfo,
                false
        )
    }
}