/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  DirectRecordStatus.kt
 * * Description : 定向录音信息
 * * Version     : 1.0
 * * Date        : 2024/8/20
 * * Author      : W9035969
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.databean

/**
 * 处理编码问题
 * More than 6 parameters (found 7). ParameterNumber
 */
data class DirectRecordStatus(val isDirectOn: Boolean = false, val directTime: String? = null)

data class DirectRecordTime(val startTime: Long = 0, val endTime: Long = 0)
