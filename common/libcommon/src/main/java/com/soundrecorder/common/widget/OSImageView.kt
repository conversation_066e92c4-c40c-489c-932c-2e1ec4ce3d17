package com.soundrecorder.common.widget

import android.animation.ValueAnimator
import android.content.Context
import android.content.res.Configuration
import android.os.Build
import android.util.AttributeSet
import androidx.annotation.VisibleForTesting
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import com.oplus.anim.EffectiveAnimationView
import com.oplus.anim.utils.EffectiveValueAnimator
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NightModeUtil
import com.soundrecorder.common.R

/*
File:OSImageView
Description:
无录音页面、无权限页面、搜索空页面、转文本初始界面、转文本空页面使用的自定义图片view
在xml中设置 viewType 表示不同的页面， xml中设置 anim_raw_json 和 anim_raw_json_night 表示图片亮色和暗色资源
Version:
Date:2022/5/24
Author:W9013204

------------------Revision History------------------
<author> <date> <version> <desc>
author：W9013204
date：2020/6/23
version：v1.2
desc：
1.优化代码，去掉type，改用资源值去判断，如果没有设置json动画资源，则默认为-1，此时应该显示静态图。
2.在 onConfigurationChanged 里面更新资源，避免非重建场景出现问题。
3.在初始化view的时候不再直接加载图片资源，在需要显示的时候才加载资源。

version: v1.3
1.修改默认为-999，因为repeatMode = -1表示无限循环,避免重复
2.新增自定义播放循环动画，播放指定时间段的动画
*/

class OSImageView : EffectiveAnimationView {
    companion object {
        private const val TAG = "OSImageView"
        const val DEFAULT_VALUE = -999
        const val DURATION_ONE_SEC_WITH_MS = 1000L
    }

    @VisibleForTesting
    var mJsonRawId = 0

    @VisibleForTesting
    var mImageDraw = 0

    @VisibleForTesting
    var mJsonRawIdNight = 0

    @VisibleForTesting
    var mLastWidth = DEFAULT_VALUE

    @VisibleForTesting
    var mLastHeight = DEFAULT_VALUE

    @VisibleForTesting
    var mLastUiMode = DEFAULT_VALUE

    @VisibleForTesting
    var mCurrentImageResourceId: Int = DEFAULT_VALUE

    //动画的帧率
    @VisibleForTesting
    var mFrameRate = DEFAULT_VALUE

    //开始执行动画的帧数
    @VisibleForTesting
    var mStartFrame = DEFAULT_VALUE

    //结束执行动画的帧数
    @VisibleForTesting
    var mEndFrame = DEFAULT_VALUE

    //动画的总帧数
    private var mFrameDuration = DEFAULT_VALUE

    //对应 ValueAnimator 的三种模式，0或者DEFAULT_VALUE都表示不重复
    @VisibleForTesting
    var mRepeatMode = DEFAULT_VALUE

    //重复执行的次数，-1表示无限循环
    @VisibleForTesting
    var mRepeatCount = DEFAULT_VALUE

    //执行指定frame区间动画的animator，可对此animator设置重复次数和模式
    //如需使用自定义播放frame区间，则需要设置 mFrameRate、mStartFrame、mEndFrame。
    //如需要重复播放自定义区间的动画，必须使用mRepeatAnimator并设置mRepeatMode、mRepeatCount参数。
    //如果想设置重复模式和次数，不想指定某段时间内的frame，而是全部重复执行，则对OSImageView设置repeatMode和repeatCount。
    private var mRepeatAnimator: ValueAnimator? = null

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        val obtainStyledAttributes = context.obtainStyledAttributes(attrs, R.styleable.OSImageView)
        mImageDraw = obtainStyledAttributes.getResourceId(R.styleable.OSImageView_img_draw, DEFAULT_VALUE)
        mJsonRawId = obtainStyledAttributes.getResourceId(R.styleable.OSImageView_anim_raw_json, DEFAULT_VALUE)
        mJsonRawIdNight = obtainStyledAttributes.getResourceId(R.styleable.OSImageView_anim_raw_json_night, DEFAULT_VALUE)
        mStartFrame = obtainStyledAttributes.getInt(R.styleable.OSImageView_anim_start_frame, DEFAULT_VALUE)
        mEndFrame = obtainStyledAttributes.getInt(R.styleable.OSImageView_anim_end_frame, DEFAULT_VALUE)
        mFrameDuration = obtainStyledAttributes.getInt(R.styleable.OSImageView_anim_frame_duration, DEFAULT_VALUE)
        mRepeatCount = obtainStyledAttributes.getInt(R.styleable.OSImageView_anim_json_repeatCount, DEFAULT_VALUE)
        mRepeatMode = obtainStyledAttributes.getInt(R.styleable.OSImageView_anim_json_repeatMode, DEFAULT_VALUE)
        mFrameRate = obtainStyledAttributes.getInt(R.styleable.OSImageView_anim_frame_rate, DEFAULT_VALUE)

        visibility = GONE
        scaleType = ScaleType.CENTER_CROP
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            isForceDarkAllowed = false
        }
        obtainStyledAttributes.recycle()

        addListener()
    }

    private fun addListener() {
        /*资源加载完成的回调，可用于获取相应的参数，暂时没用上,后续可以使用;用于获取动画的总帧数、开始帧数、结束帧数、帧率、时长等参数*/
/*        this.addOnCompositionLoadedListener(object : OnCompositionLoadedListener {
            override fun onCompositionLoaded(p0: EffectiveAnimationComposition?) {
                DebugUtil.i(
                    TAG,
                    "start = ${p0?.startFrame}, end = ${p0?.endFrame}, frameRate = ${p0?.frameRate},"
                            + " durationFrames = ${p0?.durationFrames}, duration = ${p0?.duration}, "
                )
            }
        })*/
    }

    /**
     * 设置自定义循环动画参数
     * @param repeatMode 动画的重复模式，默认从头开始
     *     ValueAnimator.RESTART 从头开始
     *     ValueAnimator.REVERSE 倒序开始
     *
     * @param repeatCount 动画的重复次数
     *     > 0 表示执行的次数
     *     ValueAnimator.INFINITE(-1)表示无限循环
     *     其他情况则不设置重复次数和重复模式
     */
    @VisibleForTesting
    fun setJsonRepeatMode(animator: ValueAnimator, repeatMode: Int, repeatCount: Int) {
        if (repeatCount > 0) {
            animator.repeatCount = repeatCount
        } else if (repeatCount == ValueAnimator.INFINITE) {
            animator.repeatCount = ValueAnimator.INFINITE
        } else {
            return
        }

        when (repeatMode) {
            //从头开始
            ValueAnimator.RESTART -> animator.repeatMode = ValueAnimator.RESTART
            //倒放
            ValueAnimator.REVERSE -> animator.repeatMode = ValueAnimator.REVERSE
            //默认从头开始
            else -> animator.repeatMode = ValueAnimator.RESTART
        }
    }

    /**
     * 初始化图片并且设置图片大小
     * @param width 可用空间的宽度 dp
     * @param height 可用空间的高度 dp
     */
    fun initView(width: Int, height: Int, from: String) {
        setScaleByEmptySize(width, height, from)
        initImageResource()
    }

    /**
     * 仅加载图片资源，在需要展示的时候才调用
     */
    fun initImageResource() {
        if (mCurrentImageResourceId != DEFAULT_VALUE) {
            //已经设置过资源了，不需要重复设置，只需要执行动画
            if (mJsonRawId != DEFAULT_VALUE || mJsonRawIdNight != DEFAULT_VALUE) {
                clearAnimation()
                prepareToPlay()
            }
            return
        }
        try {
            if (BaseUtil.isLightOS()) {
                setImageResource()
            } else {
                if (mJsonRawIdNight == DEFAULT_VALUE && mJsonRawId == DEFAULT_VALUE) {
                    //如果没有设置json资源，则显示普通图片
                    setImageResource()
                    return
                }
                var jsonId = DEFAULT_VALUE
                if (NightModeUtil.isNightMode(context)) {
                    //暗色模式,没有设置暗色模式资源，则优先使用亮色模式资源，如果也没有设置亮色，则不显示图片
                    if (mJsonRawIdNight == DEFAULT_VALUE) {
                        if (mJsonRawId != DEFAULT_VALUE) {
                            jsonId = mJsonRawId
                        }
                    } else {
                        jsonId = mJsonRawIdNight
                    }
                } else {
                    //亮色模式,跟暗色表现一样
                    if (mJsonRawId == DEFAULT_VALUE) {
                        if (mJsonRawIdNight != DEFAULT_VALUE) {
                            jsonId = mJsonRawIdNight
                        }
                    } else {
                        jsonId = mJsonRawId
                    }
                }
                if (jsonId != DEFAULT_VALUE) {
                    clearAnimation()
                    setAnimation(jsonId)
                    prepareToPlay()
                }
                mCurrentImageResourceId = jsonId
            }
        } catch (e: Exception) {
            mCurrentImageResourceId = DEFAULT_VALUE
            DebugUtil.e(TAG, "initImageResourceOrJsonAnimation error! ${e.message}")
        }
    }

    /**
     * 准备播放动画
     *  默认动画：默认不对动画进行任何修改，播放完整的动画
     *  自定义动画：播放指定阶段的动画，指定重复次数
     */
    @VisibleForTesting
    fun prepareToPlay() {
        if (BaseUtil.isLightOS()) {
            return
        }
        if (mStartFrame == DEFAULT_VALUE || mEndFrame == DEFAULT_VALUE) {
            DebugUtil.i(TAG, "prepareToPlay 默认动画方式...")
            playAnimation()
        } else {
            DebugUtil.i(TAG, "prepareToPlay 自定义动画方式...")
            if (mRepeatAnimator == null) {
                mRepeatAnimator = initCustomerAnimator(mStartFrame, mEndFrame)
            }
            playCustomAnimation()
        }
    }

    /**
     * 初始化自定义动画参数
     * @param startFrame 指定动画开始的帧数
     * @param startFrame 指定动画结束的帧数
     */
    @VisibleForTesting
    fun initCustomerAnimator(startFrame: Int, endFrame: Int): ValueAnimator? {
        if (startFrame >= endFrame) {
            return null
        }
        val animationDuration = getAnimationDuration(startFrame, endFrame)
        if (animationDuration <= 0) {
            return null
        }
        return EffectiveValueAnimator.ofInt(startFrame, endFrame).apply {
            duration = animationDuration
            addUpdateListener {
                frame = it.animatedValue as Int
            }
            setJsonRepeatMode(this, mRepeatMode, mRepeatCount)
        }
    }

    @VisibleForTesting
    fun playCustomAnimation() {
        DebugUtil.i(TAG, "playCustomAnimation...")
        mRepeatAnimator?.apply {
            if (isRunning) {
                cancel()
            }
            start()
        }
    }

    /**
     *
     * @return 返回执行指定帧需要的时长 ms
     */
    @VisibleForTesting
    fun getAnimationDuration(startFrame: Int, endFrame: Int): Long {
        if ((mStartFrame == DEFAULT_VALUE) || (mEndFrame == DEFAULT_VALUE) || (mFrameRate <= 0)) {
            return 0
        }
        val offsetFrame = (endFrame - startFrame).toFloat()
        if (offsetFrame <= 0) {
            return 0
        }
        val duration = (offsetFrame / mFrameRate) * DURATION_ONE_SEC_WITH_MS
        DebugUtil.i(TAG, "getAnimationDuration>> duration = $duration")
        return duration.toLong()
    }

    /**
     * 设置静态图片
     */
    @VisibleForTesting
    fun setImageResource() {
        if (mImageDraw != DEFAULT_VALUE) {
            setImageDrawable(ContextCompat.getDrawable(context, mImageDraw))
            mCurrentImageResourceId = mImageDraw
        }
    }

    /**
     * 设置当前图片的显示大小
     * 显示规则：
     * 当 height < 200dp，则隐藏图片
     * 当 height < 420dp,或者 width < 360dp,则显示为最大宽高的 3/5
     * 其他情况显示默认大小 w = 280dp ; H = 200dp
     *
     * @param width 可用空间的宽度 dp
     * @param height 可用空间的高度 dp
     */
    fun setScaleByEmptySize(width: Int, height: Int, from: String) {
        if (width == mLastWidth && height == mLastHeight) {
            DebugUtil.i(TAG, "setScaleByEmptySize 大小一致不再更新 width = $width, height = $height")
            return
        }
        clearAnimation()
        val needScale = getNeedScale(width, height)
        if (needScale > 0F) {
            visibility = VISIBLE
        }
        updateLayoutParams {
            this.width = (context.resources.getDimension(R.dimen.os_image_def_width) * needScale).toInt()
            this.height = (context.resources.getDimension(R.dimen.os_image_def_height) * needScale).toInt()
        }
        if (needScale <= 0F) {
            visibility = GONE
        }
        mLastWidth = width
        mLastHeight = height
        DebugUtil.i(TAG, "setScaleByEmptySize from = $from ,width = $width, height = $height, needScale = $needScale, visibility = $visibility")
    }

    /**
     * 获取需要缩放的比例
     */
    fun getNeedScale(width: Int, height: Int): Float {
        return if (height <= 200) {
            0f
        } else if (height < 420 || width < 360) {
            3f / 5f
        } else {
            1F
        }
    }

    override fun setImageResource(resId: Int) {
//        super.setImageResource(resId)
    }

    override fun onConfigurationChanged(newConfig: Configuration?) {
        super.onConfigurationChanged(newConfig)
        DebugUtil.d(TAG, "onConfigurationChanged uiMode = ${newConfig?.uiMode}, newConfig = $newConfig ")
        //切换暗色模式导致重建则重新加载设置资源，其他情况导致重建不需要重新设置图片资源，而大小改变是在外面调用的地方设置的，所以也不需要处理。
        if (mLastUiMode != newConfig?.uiMode) {
            //重置当前保存的资源id(mCurrentImageResourceId)，重新设置图片资源并记录到mCurrentImageResourceId
            mCurrentImageResourceId = DEFAULT_VALUE
            //如果当前view是可见的，则立即更新资源；不可见则只重置变量，等到下次需要显示的时候重新设置资源
            if (isVisible) {
                initImageResource()
            }
        }
        mLastUiMode = newConfig?.uiMode ?: DEFAULT_VALUE
    }

    /**
     * 取消执行动画，包括默认的动画和自定义循环动画
     */
    fun cancelJsonAnimation() {
        if (isAnimating) {
            cancelAnimation()
        }
        mRepeatAnimator?.apply {
            if (isRunning) {
                cancel()
            }
        }
    }

    fun release() {
        cancelJsonAnimation()
        super.setImageDrawable(null)
        mRepeatAnimator = null
        mCurrentImageResourceId = DEFAULT_VALUE
    }
}