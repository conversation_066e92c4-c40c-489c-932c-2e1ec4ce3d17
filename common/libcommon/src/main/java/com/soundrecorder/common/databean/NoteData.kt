/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  NoteData.kt
 * * Description : 摘要信息
 * * Version     : 1.0
 * * Date        : 2024/2/28
 * * Author      : W9041435
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.databean

import com.google.gson.annotations.SerializedName
import com.soundrecorder.base.ext.displayName
import com.soundrecorder.common.db.NoteDbUtils

object SummaryState {
    const val UNKNOWN = 0 // 未知
    const val UI_SUMMARY_NO_NETWORK = -1 //无网络连接
    const val UI_SUMMARY_REQUEST_OVER_TIME = -2 //请求超时
    const val UI_SUMMARY_REQUEST_FAIL = -3//请求失败
    const val UI_SUMMARY_ERROR = -4//异常中断
    const val UI_SUMMARY_TEXT_LIMIT = -5 //文本超长
    const val UI_SUMMARY_CONTENT_SAFETY = -6 //内容安全
    const val UI_SUMMARY_ABORT_STREAM = -7 //停止摘要
    const val UI_SUMMARY_OOM = -8 //内存溢出
    const val UI_ASR_SHORT_ERROR = -99 //通话内容过短
    const val UI_SUMMARY_FAIL = -100 //通话摘要生成失败
    const val UI_ASR_NO_NETWORK = -101 //无网络连接
    const val UI_ASR_REQUEST_OVER_TIME = -102 //请求超时
    const val UI_ASR_ERROR = -103 //异常中断
}

/**
 * 摘要
 */
data class NoteData(
    /**
     * 所有录音的uuid
     * 普通录音：自动生成
     * 通话录音：沿用Call_UUID
     */
    @SerializedName("record_uuid") var recordUUID: String,
    var recordType: Int, // 录音文件类型
    @SerializedName("note_id") var noteId: String?, //便签生成摘要数据id
    @SerializedName("note_content") var noteContent: String?, // 摘要具体内容
    @SerializedName("media_id") var mediaId: String, //  音频文件媒体库ID
    @SerializedName("media_path") var mediaPath: String, //音频文件路径
    var checkFlag: String? = "", // 音频文件校验标记，目前预留
    @SerializedName("note_state") var noteState: Int? = SummaryState.UNKNOWN, //摘要状态
) {
    var contactFlag: Boolean? = null

    /**
     * 是否是电话本通话录音
     */
    fun isContactCallType(): Boolean = isCallType() && contactFlag == true

    /**
     * 通话录音
     */
    fun isCallType(): Boolean = recordType == NoteDbUtils.SummaryType.SUMMARY_TYPE_CALL_RECORD
    override fun toString(): String {
        return "NoteData(recordId='$recordUUID', recordType=$recordType, noteId=$noteId, noteContent=${noteContent?.length}, " +
                "mediaId='$mediaId', fileName='${mediaPath.displayName()}', checkFlag=$checkFlag, noteState=$noteState, contactFlag=$contactFlag)"
    }
}

