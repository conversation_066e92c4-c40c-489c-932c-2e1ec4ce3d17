package com.soundrecorder.common.databean.markdata

import android.content.Context
import android.graphics.Bitmap
import android.graphics.RectF
import android.os.Build
import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.DrawableRes
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toBitmap
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.model.TimeStampGetter
import com.soundrecorder.base.utils.*
import com.soundrecorder.common.R
import com.soundrecorder.common.utils.MarkSerializUtil
import com.soundrecorder.common.utils.MarkSerializUtil.VERSION_NEW
import com.soundrecorder.common.utils.MarkSerializUtil.VERSION_OLD
import com.soundrecorder.common.utils.MarkSerializUtil.VERSION_PICTURE
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.imageload.ImageLoadData
import com.soundrecorder.imageload.ImageLoaderUtils
import com.soundrecorder.modulerouter.photoviewer.PhotoViewerData
import java.io.File
import java.io.Serializable


data class MarkDataBean(var timeInMills: Long, var version: Int = VERSION_NEW) :
    PictureMarkTarget(), Comparable<MarkDataBean>, Serializable, PhotoViewerData, TimeStampGetter {

    var isDefault: Boolean = true
    var defaultNo: Int = 0
    var markText: String = ""
    var pictureFilePath = ""

    /**
     * 图片标记图片的宽度，用于图文混排imageView的宽度计算
     */
    var pictureWith: Int = 0

    /**
     * 图片标记图片的高度，用于图文混排imageView高度的计算
     */
    var pictureHeight: Int = 0

    var keyId = ""

    /**
     * 校正后的时间
     */
    var correctTime: Long = 0

    /**
     * 用于adapter数据计算diff
     */
    var isUpdate = false

    var rect = RectF()

    private val smallImageLoadData by lazy {
        ImageLoadData(
            FileUtils.getAppFile(pictureFilePath, false),
            ViewUtils.dp2px(9f, true).toInt(),
            ViewUtils.dp2px(9f, true).toInt()
        )
    }

    private val middleImageLoadData by lazy {
        ImageLoadData(
            FileUtils.getAppFile(pictureFilePath, false),
            ViewUtils.dp2px(30f, true).toInt(),
            ViewUtils.dp2px(30f, true).toInt()
        )
    }


    init {
        correctTime = timeInMills
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    constructor(parcel: Parcel) : this(
        timeInMills = parcel.readLong(),
        version = parcel.readInt()
    ) {
        this.isDefault = parcel.readBoolean()
        this.defaultNo = parcel.readInt()
        this.markText = parcel.readString().toString()
        this.pictureFilePath = parcel.readString().toString()
        this.keyId = parcel.readString().toString()
        this.correctTime = parcel.readLong()
        this.pictureWith = parcel.readInt()
        this.pictureHeight = parcel.readInt()
    }

    /**
     * 校正时间
     */
    fun correctTime(time: Long) {
        DebugUtil.d(TAG, "数据库时间:$timeInMills 校正时间：$time")
        correctTime = time
    }

    fun getIcon(
        context: Context,
        @DrawableRes defaultRes: Int,
        @DrawableRes loadDefaultRes: Int
    ): Bitmap? {
        val d = when {
            !fileExists() -> {
                ContextCompat.getDrawable(context, defaultRes)?.toBitmap()
            }
            else -> {
                start(loadDefaultRes)
            }
        }
        return d
    }

    fun getShowTime(): String = TimeUtils.getFormatTimeExclusiveMill(correctTime)

    fun fileExists(): Boolean {
        return pictureFilePath.isNotEmpty()
    }

    override fun getWaveImageLoadData(): ImageLoadData {
        return smallImageLoadData
    }

    /**
     * 标记列表加载图片Key对象，coil的Key
     */
    fun getMarkListImageLoadData(): ImageLoadData {
        return middleImageLoadData
    }

    fun toStoreString(): String {
        val result: String = if (version == VERSION_NEW) {
            if (isDefault) {
                (timeInMills.toString() + MarkSerializUtil.SPLIT_BETWEEN_TIME_AND_MARK + defaultNo.toString() + MarkSerializUtil.SPLIT_BETWEEN_MARK_AND_MARK_TYPE
                        + MarkSerializUtil.MARK_TYPE_DEFAULT)
            } else {
                (timeInMills.toString() + MarkSerializUtil.SPLIT_BETWEEN_TIME_AND_MARK + markText + MarkSerializUtil.SPLIT_BETWEEN_MARK_AND_MARK_TYPE
                        + MarkSerializUtil.MARK_TYPE_EDIT)
            }
        } else if (version == VERSION_OLD) {
            timeInMills.toString()
        } else {
            ""
        }
        return result
    }


    override fun compareTo(other: MarkDataBean): Int {
        return (this.timeInMills - other.timeInMills).toInt()
    }

    companion object CREATOR : Parcelable.Creator<MarkDataBean> {

        private const val TAG = "MarkDataBean"

        @RequiresApi(Build.VERSION_CODES.Q)
        override fun createFromParcel(parcel: Parcel): MarkDataBean {
            return MarkDataBean(parcel)
        }

        override fun newArray(i: Int): Array<MarkDataBean?> {
            return arrayOfNulls(i)
        }
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    override fun writeToParcel(dest: Parcel, flags: Int) {
        dest.apply {
            writeLong(timeInMills)
            writeInt(version)
            writeBoolean(isDefault)
            writeInt(defaultNo)
            writeString(markText)
            writeString(pictureFilePath)
            writeString(keyId)
            writeLong(correctTime)
            writeInt(pictureWith)
            writeInt(pictureHeight)
        }
    }

    override fun describeContents(): Int {
        return 0
    }


    fun isTextType(): Boolean {
        return (version == VERSION_NEW) || (version == VERSION_OLD)
    }

    fun isPictureType(): Boolean {
        return (version == VERSION_PICTURE)
    }


    override fun toString(): String {
        return "$TAG{timeInMills=$timeInMills, version='$version', isDefault='$isDefault', defaultNo=$defaultNo, markText='$markText},relativePath=$pictureFilePath, keyId=$keyId"
    }

    override fun src(): File {
        return FileUtils.getAppFile(pictureFilePath, false)
    }

    override fun getTimeStamp(): Long {
        return this.timeInMills
    }

    override fun clearMemoryCacheByKey() {
        super.clearMemoryCacheByKey()
        ImageLoaderUtils.clearMemoryCacheByKey(getMarkListImageLoadData())
    }

    fun getRealMarkText(): String {
        return markText.ifEmpty {
            if (defaultNo > 0) {
                BaseApplication.getAppContext().resources.getString(R.string.default_flag_new, defaultNo)
            } else {
                BaseApplication.getAppContext().getString(R.string.custom_mark_description)
            }
        }
    }
}