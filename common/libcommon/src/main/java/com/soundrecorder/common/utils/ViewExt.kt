/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ViewExt.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/1/15
 * * Author      : W9035969
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.utils

import android.view.View
import com.oplus.anim.EffectiveAnimationView

/**
 * view显示隐藏扩展
 */
fun View?.gone() {
    this?.apply {
        visibility = View.GONE
    }
}

fun Any.gones(vararg views: View?) {
    views.forEach {
        it.gone()
    }
}

fun View?.visible() {
    this?.apply {
        visibility = View.VISIBLE
    }
}

fun Any.visibles(vararg views: View?) {
    views.forEach {
        it.visible()
    }
}

fun View?.invisible() {
    this?.apply {
        visibility = View.INVISIBLE
    }
}

fun Any.invisibles(vararg views: View?) {
    views.forEach {
        it.invisible()
    }
}

fun View?.isGone(): Boolean {
    this?.apply {
        return visibility == View.GONE
    }
    return false
}

fun View?.isVisible(): Boolean {
    this?.apply {
        return visibility == View.VISIBLE
    }
    return false
}

fun View?.isInvisible(): Boolean {
    this?.apply {
        return visibility == View.INVISIBLE
    }
    return false
}

fun View.toggleVisibility() {
    this.visibility == if (this.visibility == View.GONE) View.VISIBLE else View.GONE
}

/**
 * 点击
 */
fun View?.click(action: (v: View) -> Unit) {
    this?.setOnClickListener {
        action(it)
    }
}

fun Any.clicks(vararg views: View?, action: (v: View) -> Unit) {
    views.forEach { v ->
        v?.setOnClickListener {
            action(it)
        }
    }
}

fun View?.longClick(action: (v: View) -> Boolean) {
    this?.setOnLongClickListener {
        action(it)
    }
}

fun Any.longClicks(vararg views: View, action: (v: View) -> Unit) {
    views.forEach { v ->
        v.setOnClickListener {
            action(it)
        }
    }
}

/**
 * 播放 EffectiveAnimationView json动效
 * 智能命名动效扩展
 */
fun EffectiveAnimationView?.playAnimationExt(
    rawRes: Int = com.soundrecorder.common.R.raw.ic_ai_title_loading,
    isVisible: Boolean = true
) {
    this?.apply {
        clearAnimation()
        setAnimation(rawRes)
        playAnimation()
        if (isVisible) {
            visible()
        }
    }
}

fun EffectiveAnimationView?.cancelAnimationExt(isGone: Boolean = true) {
    this?.apply {
        cancelAnimation()
        if (isGone) {
            gone()
        }
    }
}