package com.soundrecorder.common.sync;


import android.content.Context;
import android.content.Intent;
import android.os.IBinder;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.JobIntentService;

import com.oplus.recorderlog.util.GsonUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.sync.encryptbox.EncryptBoxConstant;
import com.soundrecorder.common.sync.encryptbox.SyncBean;
import com.soundrecorder.common.sync.encryptbox.SyncBeanProcessor;

public class CommonIntentService extends JobIntentService {

    private static final String TAG = "CommonIntentService";
    private static final int JOB_ID = 10111;

    public static final String LOCAL_ENCRYPTBOX_ACTION = "com.oplus.soundrecorder.ENCYPTBOX_DATACHANGE";

    private SyncBeanProcessor mSyncBeanProcessor = null;

    @Override
    public void onCreate() {
        super.onCreate();
        mSyncBeanProcessor = new SyncBeanProcessor();
        DebugUtil.i(TAG, "CommonIntentService onCreate");
    }

    public static void enqueueWork(Context context, Intent work) {
        DebugUtil.i(TAG, "enqueueWork");
        enqueueWork(context, CommonIntentService.class, JOB_ID, work);
    }

    @Override
    public int onStartCommand(@Nullable Intent intent, int flags, int startId) {
        DebugUtil.i(TAG, "onStartCommand");
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    protected void onHandleWork(@NonNull Intent intent) {
        String action = intent.getAction();
        DebugUtil.i(TAG, "onHandleWork: intent: " + intent + ", action: " + action);
        if (!TextUtils.isEmpty(action)) {
            if (action.equalsIgnoreCase(LOCAL_ENCRYPTBOX_ACTION)) {
                processEncyptBoxChanged(intent);
            }
        }
    }

    @Override
    public void onDestroy() {
        DebugUtil.i(TAG, "CommonIntentService onDestroy");
        super.onDestroy();
    }


    @Override
    public IBinder onBind(@NonNull Intent intent) {
        DebugUtil.i(TAG, "CommonIntentService onBind");
        return super.onBind(intent);
    }

    private void processEncyptBoxChanged(Intent intent){
        String jsonString = "";
        try {
            jsonString = intent.getStringExtra(EncryptBoxConstant.ENCRYPTBOX_ACTION_INTENT_EXTRA_KEY);
        } catch (Exception e) {
            DebugUtil.e(TAG, "intent cannot get EncryptBoxConstant.ENCRYPTBOX_ACTION_INTENT_EXTRA_KEY");
        }
        if (!TextUtils.isEmpty(jsonString)) {
            try {
                SyncBean syncBean = GsonUtil.fromJson(jsonString, SyncBean.class);
                if (syncBean != null) {
                    mSyncBeanProcessor.processIncomingSyncBean(syncBean);
                }
            } catch (Exception e) {
                DebugUtil.e(TAG, "processEncyptBoxChanged", e);
            }
        }
    }

}
