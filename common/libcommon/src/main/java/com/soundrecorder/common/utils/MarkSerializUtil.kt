package com.soundrecorder.common.utils

import android.text.TextUtils
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.databean.markdata.MarkDataBean
import kotlin.math.abs

object MarkSerializUtil {
    const val MARK_TYPE_DEFAULT = 1
    const val MARK_TYPE_EDIT = 2

    const val MARK_TAG_HEAD = "oppoMark/"
    const val MARK_TAG_TAIL = "/oppoMark"

    const val SPLIT_BETWEEN_TIME_AND_MARK = "\uFFF1"
    const val SPLIT_BETWEEN_MARKS = "\uFFF0"
    const val SPLIT_BETWEEN_MARK_AND_AMPLITUDE = "\uFFF2"
    const val SPLIT_BETWEEN_MARK_AND_MARK_TYPE = "\uFFF3"

    const val TAG: String = "MarkSerializUtil"

    const val VERSION_OLD = 0
    const val VERSION_NEW = 1
    const val VERSION_PICTURE = 2

    fun parseMarkDataBeanListFromString(inputMarkString: String?): MutableList<MarkDataBean> {
        var secTimes: Array<String>? = null
        val result: MutableList<MarkDataBean> = mutableListOf()
        var processedMarkString: String?
        DebugUtil.i(
            TAG,
            ("readMarkTag  bookMark.length= " + (if ((inputMarkString == null)) 0 else inputMarkString.length))
        )
        if (!inputMarkString.isNullOrEmpty()) {
            processedMarkString = inputMarkString.replace(MARK_TAG_HEAD, "")
            processedMarkString = processedMarkString.replace(MARK_TAG_TAIL, "")
            secTimes = if (processedMarkString.contains(SPLIT_BETWEEN_MARKS)) {
                processedMarkString.split(SPLIT_BETWEEN_MARKS.toRegex()).toTypedArray()
            } else {
                processedMarkString.split(",".toRegex()).toTypedArray()
            }
        }
        secTimes?.forEach {
            val tmpItem = parseMarkDataBeanFromString(it)
            if (tmpItem != null) {
                result.add(tmpItem)
            }
        }
        result.sort()
        return result
    }


    private fun parseMarkDataBeanFromString(singleMarkString: String): MarkDataBean? {
        if (TextUtils.isEmpty(singleMarkString)) {
            DebugUtil.e(TAG, "input string is Empty")
            return null
        }
        val result: MarkDataBean?
        val indexOfColon: Int = singleMarkString.indexOf(SPLIT_BETWEEN_TIME_AND_MARK)
        val indexOfMarkTypeSplit: Int = singleMarkString.indexOf(SPLIT_BETWEEN_MARK_AND_MARK_TYPE)
        var timeText = ""
        if (indexOfColon > 0) {
            timeText = singleMarkString.substring(0, indexOfColon)
            val timeNum: Long
            try {
                timeNum = timeText.toLong()
            } catch (e: NumberFormatException) {
                DebugUtil.e(TAG, "parse error", e)
                return null
            }
            result = MarkDataBean(timeNum, VERSION_NEW)
            if (indexOfMarkTypeSplit > 0 && indexOfMarkTypeSplit > indexOfColon + 1 && indexOfMarkTypeSplit + 1 < singleMarkString.length) {
                val tmpMark: String = singleMarkString.substring(indexOfColon + 1, indexOfMarkTypeSplit)
                val tmpType: String = singleMarkString.substring(indexOfMarkTypeSplit + 1)
                var typeInt = 0
                try {
                    typeInt = tmpType.toInt()
                } catch (e: NumberFormatException) {
                    DebugUtil.e(TAG, "type from String $tmpType not int")
                }
                when (typeInt) {
                    MARK_TYPE_EDIT -> {
                        result.isDefault = false
                        result.markText = tmpMark
                    }
                    MARK_TYPE_DEFAULT -> {
                        result.isDefault = true
                        val flagIndex: Int
                        try {
                            flagIndex = tmpMark.toInt()
                        } catch (e: NumberFormatException) {
                            DebugUtil.e(TAG, "mark from String $tmpMark not int")
                            return null
                        }
                        result.defaultNo = flagIndex
                    }
                    else -> {
                        DebugUtil.e(TAG, "mark from String $typeInt not support")
                        return null
                    }
                }
            } else {
                return null
            }
        } else {
            val timeNum: Long = timeText.toLongOrNull() ?: return null
            result = MarkDataBean(timeNum, VERSION_OLD)
        }
        return result
    }


    fun convertStoredDBStringForMarkDataBeanList(markDataBeanList: MutableList<MarkDataBean>?): String {
        val markStringBuilder = StringBuilder()
        markDataBeanList?.filter {
            it.version < VERSION_PICTURE
        }?.forEachIndexed { index, markDataBean ->
            if (markDataBean.version != VERSION_NEW) {
                markStringBuilder.append(markDataBean.timeInMills)
                if (index + 1 < markDataBeanList.size) {
                    markStringBuilder.append(",")
                }
            } else {
                markStringBuilder.append(markDataBean.toStoreString())
                if (index + 1 < markDataBeanList.size) {
                    markStringBuilder.append(SPLIT_BETWEEN_MARKS)
                }
            }
        }
        return markStringBuilder.toString()
    }

    fun checkInTimeScopeInMarkList(markDataBeanList: MutableList<MarkDataBean>?, currentTimeMills: Long): Int {
        var result = -1
        if (markDataBeanList != null && markDataBeanList.size > 0) {
            for (i in markDataBeanList.indices) {
                val markDataBean = markDataBeanList[i]
                val isInScope = (abs(markDataBean.timeInMills - currentTimeMills) < Constants.SECOND)
                if (isInScope) {
                    result = i
                    break
                }
            }
        }
        return result
    }

    fun markListToString(markList: List<String>?, defaultMarkText: String): String? {
        val markStringBuffer = StringBuilder()
        if (markList != null && markList.size > 0) {
            for (i in markList.indices) {
                val formattedTime = markList[i]
                if (!TextUtils.isEmpty(formattedTime) && formattedTime.contains(":")) {
                    val secTime = TimeUtils.getSecTime(formattedTime)
                    markStringBuffer.append(secTime.toString() + SPLIT_BETWEEN_TIME_AND_MARK + defaultMarkText)
                } else {
                    markStringBuffer.append(formattedTime)
                }
                if (i + 1 < markList.size) {
                    markStringBuffer.append(SPLIT_BETWEEN_MARKS)
                }
            }
        }
        return markStringBuffer.toString()
    }

    fun getMarkString(markList: List<String?>?): String? {
        val sb = StringBuilder()
        if (markList != null && markList.size > 0) {
            for (i in markList.indices) {
                val formattedTime = markList[i]
                sb.append(formattedTime)
                if (i + 1 < markList.size) {
                    sb.append(SPLIT_BETWEEN_MARKS)
                }
            }
        }
        return sb.toString()
    }
}