/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: DeleteFileDialog
 * Description:
 * Version: 1.0
 * Date: 2022/10/28
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/10/28 1.0 create
 */

package com.soundrecorder.common.fileoperator.delete

import android.app.Activity
import android.content.DialogInterface
import androidx.appcompat.app.AlertDialog
import androidx.documentfile.provider.DocumentFile
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.soundrecorder.base.ext.TAG
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.R
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.utils.ViewUtils
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.nio.file.DirectoryNotEmptyException
import java.nio.file.Files
import java.nio.file.Paths
import java.nio.file.StandardCopyOption
import kotlin.io.path.Path

class DeleteFileDialog(val mActivity: Activity, private val title: String, private val message: String, private val buttonMessage: String) {

    private var mLogTag = "DeleteFileDialog"
    private var mDeleteDialog: AlertDialog? = null
    private var mDeleteFileDialogUtil: DeleteFileDialogUtil? = null
    private var mBeDeleteRecordList: MutableList<Record>? = null
    var mOnFileDeleteListener: OnFileDeleteListener? = null
    var mHideListener: DialogInterface.OnDismissListener? = null

    /**
     * @param deleteRecord 被删除的录音文件数据
     * @param isRecycle 是否从回收站删除录音
     */
    fun showDeleteDialog(deleteRecord: Record, isRecycle: Boolean = false) {
        showDeleteDialog(listOf(deleteRecord), false, isRecycle)
    }

    /**
     * @param deleteRecords 被删除的录音文件数据
     * @param isDeleteAll 是否删除全部录音文件
     * @param isRecycle 是否从回收站删除录音
     */
    fun showDeleteDialog(deleteRecords: List<Record>, isDeleteAll: Boolean = false, isRecycle: Boolean = false) {
        if (mDeleteDialog?.isShowing == true) {
            return
        }
        mBeDeleteRecordList = mutableListOf()
        mBeDeleteRecordList?.addAll(deleteRecords)

        mDeleteDialog = COUIAlertDialogBuilder(mActivity, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            .setPositiveButton(buttonMessage
            ) { _, _ -> deleteRecord(deleteRecords, isDeleteAll, isRecycle) }
            .setBlurBackgroundDrawable(true)
            .setTitle(title)
            .setMessage(message)
            .setNegativeButton(R.string.cancel, null)
            .setCancelable(true)
            .setOnDismissListener { dialog -> mHideListener?.onDismiss(dialog) }
            .show()
        ViewUtils.updateWindowLayoutParams(mDeleteDialog?.window)
    }

    fun deleteWithPermission(mActivity: Activity, selectedRecordList: ArrayList<Record>, deleteHasPermission: Boolean, isRecycle: Boolean) {
        if (mDeleteFileDialogUtil == null) {
            mDeleteFileDialogUtil = DeleteFileDialogUtil(mOnFileDeleteListener)
        }
        mDeleteFileDialogUtil?.deleteWithPermission(activity = mActivity, selectedRecordList, deleteHasPermission, isRecycle)
    }

    private fun deleteRecord(deleteRecords: List<Record>?, isDeleteAll: Boolean, isRecycle: Boolean) {
        if (deleteRecords.isNullOrEmpty()) {
            return
        }
        if (mDeleteFileDialogUtil == null) {
            mDeleteFileDialogUtil = DeleteFileDialogUtil(mOnFileDeleteListener)
        }
        mDeleteFileDialogUtil?.delete(mActivity, deleteRecords, isDeleteAll, isRecycle)
    }


    /**
     * 执行数据复制
     *
     * 数据复制存储路径：
     */
    private fun copyRecord(deleteRecords: List<Record>) {

        val sourceFile = DocumentFile.fromFile(File(""))

        val fileDir = mActivity.filesDir.path + File.separator
        for (record in deleteRecords) {
            val fileNewPath = fileDir + record.displayName
            val inputStream = FileInputStream(record.relativePath)
            val outputStream = FileOutputStream(fileNewPath)
            val oldPath = Path("")
            Files.copy(oldPath, outputStream)
        }
    }

    /**
     * 文件复制工具方法
     */
    fun copyFile(sourcePath: String, destinationPath: String) {
        val source = Paths.get(sourcePath)
        val destination = Paths.get(destinationPath)

        try {
            val copyPath = Files.copy(source, destination, StandardCopyOption.REPLACE_EXISTING)
            DebugUtil.d(TAG, "copyFile path = $copyPath")
        } catch (e: IOException) {
            println("Input/output error occurred.")
        } catch (e: NoSuchFileException) {
            println("Source or destination file does not exist.")
        } catch (e: DirectoryNotEmptyException) {
            println("Destination is a directory and not an ordinary file.")
        }
    }


    fun release() {
        DebugUtil.e(mLogTag, "release")
        dismiss()
        mDeleteFileDialogUtil?.release()
        mDeleteFileDialogUtil = null
        mBeDeleteRecordList?.clear()
        mBeDeleteRecordList = null
        mDeleteDialog = null
    }

    fun dismiss() {
        mDeleteDialog?.dismiss()
    }

    fun isShowing(): Boolean = mDeleteDialog?.isShowing == true

    fun getOperating(): Boolean = mDeleteFileDialogUtil?.getOperating() ?: false

    fun resetOperating() {
        mDeleteFileDialogUtil?.resetOperating()
    }
}