/*
 Copyright (C), 2008-2023 OPLUS Mobile Comm Corp., Ltd.
 File: VibrateUtils.kt
 Description:
 Version: 1.0
 Date: 2023/3/6
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2023/3/6 1.0 create
 */

package com.soundrecorder.common.utils

import android.annotation.SuppressLint
import android.content.Context
import android.provider.Settings
import com.oplus.os.LinearmotorVibrator
import com.oplus.os.WaveformEffect
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.OSDKCompatUtils

object VibrateUtils {

    private const val TAG = "VibrateUtils"
    private const val FEATURE_LUXUNVIBRATOR_SUPPORT = "oplus.software.vibrator_luxunvibrator"

    private val sIsLineSupported by lazy {
        OSDKCompatUtils.getFeatureOldCompat(FEATURE_LUXUNVIBRATOR_SUPPORT, false)
    }

    @JvmStatic
    @SuppressLint("WrongConstant")
    fun vibrate(context: Context, effect: Int = WaveformEffect.EFFECT_CUSTOMIZED_SPREAD_OUT) {
        if (!BaseUtil.isAndroidUOrLater) {
            DebugUtil.d(TAG, "vibrate, os <U return")
            return
        }
        if (!sIsLineSupported) {
            DebugUtil.d(TAG, "vibrate, sIsLineSupported is false return")
            return
        }
        //受触感振动控制
        val vibrateWhenTouch: Int = Settings.System.getInt(
            context.contentResolver,
            Settings.System.HAPTIC_FEEDBACK_ENABLED, 1
        )
        if (vibrateWhenTouch != 1) {
            DebugUtil.d(TAG, "vibrate, vibrateWhenTouch != 1 return")
            return
        }
        kotlin.runCatching {
            val lineVibrator =
                context.getSystemService(LinearmotorVibrator.LINEARMOTORVIBRATOR_SERVICE) as? LinearmotorVibrator
            val waveformEffect: WaveformEffect = WaveformEffect.Builder()
                .setEffectType(effect)
                .build()
            lineVibrator?.vibrate(waveformEffect)
            DebugUtil.d(TAG, "vibrate success")
        }.onFailure {
            DebugUtil.d(TAG, "vibrate, error = $it")
        }
    }
}