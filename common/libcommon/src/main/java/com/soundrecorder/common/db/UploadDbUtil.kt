/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.db

import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.text.TextUtils
import com.soundrecorder.common.databean.UploadRecord
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.base.utils.DebugUtil
import java.lang.Exception

object UploadDbUtil {

    const val TAG = "UploadDbUtil"

    @JvmStatic
    fun insertUploadRecords(context: Context?, list: MutableList<UploadRecord>?): Int {
        var insertCount = 0
        if ((context == null) || list.isNullOrEmpty()) {
            DebugUtil.e(TAG, "insertUploadRecords context null return or inputlist is empty")
            return insertCount
        }
        var contentValues: Array<ContentValues> = Array(list.size) { list.get(it).getContentValuesWithoutId() }
        try {
            insertCount = context.contentResolver.bulkInsert(DatabaseConstant.UploadUri.UPLOAD_CONTENT_URI, contentValues)
            DebugUtil.i(TAG, "insertUploadRecords: insertCount: " + insertCount)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getUploadRecorByonlyId error", e)
        } finally {
        }
        return insertCount
    }

    @JvmStatic
    fun getUploadRecordsByonlyId(context: Context?, onlyId: String?): MutableList<UploadRecord> {
        var result: MutableList<UploadRecord> = mutableListOf()
        if (context == null || TextUtils.isEmpty(onlyId)) {
            DebugUtil.e(TAG, "getUploadRecorByonlyId context null return or input onlyId is empty")
            return result
        }
        var where: String = DatabaseConstant.UploadColumn.ONLY_ID + " = ?"
        var whereArgs = arrayOf(onlyId)
        var cursor: Cursor? = null
        try {
            cursor = context.contentResolver.query(DatabaseConstant.UploadUri.UPLOAD_CONTENT_URI, null, where, whereArgs, null)
            if ((cursor != null) && (cursor.count > 0)) {
                var upLoadRecord = UploadRecord()
                while (cursor.moveToNext()) {
                    upLoadRecord = UploadRecord(cursor)
                    result.add(upLoadRecord)
                }
            }
            DebugUtil.i(
                TAG,
                "getUploadRecordsByonlyId: input onlyId: " + onlyId + ", outcount: " + cursor?.count
            )
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getUploadRecordsByonlyId error", e)
        } finally {
            cursor?.close()
        }
        return result
    }

    @JvmStatic
    fun updateEtagByOnlyIdAndSeqNum(context: Context?, onlyId: String?, seqNum: Int, eTag: String?): Boolean {
        if (context == null || TextUtils.isEmpty(onlyId)) {
            DebugUtil.e(
                TAG,
                "updateEtag context null return or input onlyId is empty or url is empty"
            )
            return false
        }
        var updateSuc = false
        var where: String = DatabaseConstant.UploadColumn.ONLY_ID + " = ?" + " AND " + DatabaseConstant.UploadColumn.UPLOAD_SEQ_NUM + " = ?"
        var whereArgs = arrayOf(onlyId, seqNum.toString())
        var contentValue: ContentValues = ContentValues()
        contentValue.put(DatabaseConstant.UploadColumn.UPLOAD_ETAG, eTag)
        try {
            var updateCnt = context.contentResolver.update(DatabaseConstant.UploadUri.UPLOAD_CONTENT_URI, contentValue, where, whereArgs)
            updateSuc = updateCnt > 0
            DebugUtil.i(
                TAG,
                "updateEtag: input onlyId: " + onlyId + ", seq: " + seqNum + ", updateCount: " + updateCnt
            )
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getUploadRecorByonlyId error", e)
        }
        return updateSuc
    }

    @JvmStatic
    fun deleteByOnlyId(context: Context?, onlyId: String?): Int {
        var deleteCount: Int = 0
        if (context == null || TextUtils.isEmpty(onlyId)) {
            DebugUtil.e(TAG, "deleteByOnlyId context null return or input onlyId is empty")
            return deleteCount
        }

        var where: String = DatabaseConstant.UploadColumn.ONLY_ID + " = ?"
        var whereArgs = arrayOf(onlyId)
        try {
            deleteCount = context.contentResolver.delete(DatabaseConstant.UploadUri.UPLOAD_CONTENT_URI, where, whereArgs)
            DebugUtil.i(TAG, "deleteByOnlyId: onlyId: " + onlyId + ", deleteCount: " + deleteCount)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "deleteByOnlyId error", e)
        } finally {
        }
        return deleteCount
    }

    @JvmStatic
    fun updateAllUrlAndCleanETagByOnlyId(context: Context?, onlyId: String?, pairlist: List<Pair<Int, String>>?): Int {
        var updateCnt: Int = -1
        if (onlyId?.isEmpty() ?: false) return updateCnt
        if (pairlist?.isEmpty() ?: false) return updateCnt
        var iterator = pairlist!!.iterator()
        var pair: Pair<Int, String>? = null
        var where: String = ""
        var whereArgs = arrayOf(onlyId)
        var contentValues: ContentValues = ContentValues()
        while (iterator.hasNext()) {
            pair = iterator.next()
            where = DatabaseConstant.UploadColumn.ONLY_ID + " = ? AND " + DatabaseConstant.UploadColumn.UPLOAD_SEQ_NUM + " = ?"
            whereArgs = arrayOf(onlyId, pair.first.toString())
            contentValues.clear()
            contentValues.put(DatabaseConstant.UploadColumn.UPLOAD_URL, pair.second)
            contentValues.put(DatabaseConstant.UploadColumn.UPLOAD_ETAG, "")
            try {
                var updateCount = context?.contentResolver?.update(DatabaseConstant.UploadUri.UPLOAD_CONTENT_URI, contentValues, where, whereArgs)
                        ?: -1
                DebugUtil.i(TAG, "")
                updateCnt = if (updateCount > 0) {
                    updateCnt + updateCount
                } else {
                    updateCnt
                }
            } catch (e: Exception) {
                DebugUtil.e(TAG, "updateAllUrlByOnly error", e)
            }
        }
        return updateCnt
    }

    @JvmStatic
    fun getAllUploadRecords(context: Context?): MutableList<UploadRecord> {
        var result: MutableList<UploadRecord> = mutableListOf()
        if (context == null) {
            DebugUtil.e(TAG, "getAllUploadRecords context null return or input onlyId is empty")
            return result
        }
        var cursor: Cursor? = null
        try {
            cursor = context.contentResolver.query(DatabaseConstant.UploadUri.UPLOAD_CONTENT_URI, null, null, null, null)
            if ((cursor != null) && (cursor.count > 0)) {
                var upLoadRecord = UploadRecord()
                while (cursor.moveToNext()) {
                    upLoadRecord = UploadRecord(cursor)
                    result.add(upLoadRecord)
                }
            }
            DebugUtil.i(TAG, "getAllUploadRecords, count: " + cursor?.count)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getAllUploadRecords error", e)
        } finally {
            cursor?.close()
        }
        return result
    }
}