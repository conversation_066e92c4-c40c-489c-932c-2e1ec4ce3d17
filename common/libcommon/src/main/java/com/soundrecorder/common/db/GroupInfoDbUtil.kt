/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/01/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.db

import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteException
import android.provider.MediaStore
import android.text.TextUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.common.R
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.constant.DatabaseConstant.GroupInfoUri
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.constant.RecordConstant.GROUP_TYPE_CUSTOM
import com.soundrecorder.common.constant.RecordConstant.LOCAL_EDITING
import com.soundrecorder.common.constant.RecordConstant.RECORD_DELETED
import com.soundrecorder.common.constant.RecordConstant.RECORD_DIRTY_MEGA_ONLY
import com.soundrecorder.common.constant.RecordConstant.RECORD_NOT_DIRTY
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.databean.MediaCounter
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.utils.RecordModeUtil
import com.soundrecorder.common.utils.RecordModeUtil.cleanRelativePath
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_ACTIVITY
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_NORMAL
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction.getFileBeingRecorded
import java.io.File
import java.util.UUID

/**
 * 更新 table group_info工具类，仅操作数据库信息，
 * 不涉及云同步逻辑
 */
object GroupInfoDbUtil {

    const val TAG = "GroupInfoDbUtil"
    const val DEFAULT_ALL_UUID = "0000_0000_0000_0001"
    const val DEFAULT_CALLING_UUID = "0000_0000_0000_0002"
    const val DEFAULT_COMMON_UUID = "0000_0000_0000_0003"
    const val DEFAULT_PRIVATE_UUID = "0000_0000_0000_0004"
    const val DEFAULT_RECENTLY_DELETE_UUID = "0000_0000_0000_0005"

    private var mDefaultGroupInfoCommon: GroupInfo? = null
    private var mDefaultGroupInfoCalling: GroupInfo? = null
    private var mDefaultGroupInfoAll: GroupInfo? = null
    //私密相关 备用
    private var mDefaultGroupInfoPrivate: GroupInfo? = null
    private var mDefaultGroupInfoDeleted: GroupInfo? = null
    private val isSupportCall = RecordModeUtil.hasCallRecording(BaseApplication.getAppContext())

    private var stopCalculateGroupCount: Boolean = false
    /**
     * 初始化默认的分组信息
     */
    @JvmStatic
    fun initDefaultGroupInfoData(context: Context?, db: SQLiteDatabase?) {
        if ((context == null || db == null)) {
            DebugUtil.e(TAG, "initDefaultGroupInfoData params are illegal ")
            return
        }
        val typeList = if (isSupportCall) {
                arrayListOf(GroupInfo.INT_DEFAULT_ALL,
                    GroupInfo.INT_DEFAULT_CALLING,
                    GroupInfo.INT_DEFAULT_COMMON,
//            GroupInfo.INT_DEFAULT_PRIVATE,
                    GroupInfo.INT_DEFAULT_RECENTLY_DELETED
                )
            } else {
                arrayListOf(GroupInfo.INT_DEFAULT_ALL,
                    GroupInfo.INT_DEFAULT_COMMON,
//            GroupInfo.INT_DEFAULT_PRIVATE,
                    GroupInfo.INT_DEFAULT_RECENTLY_DELETED
                )
            }
        db.beginTransaction()
        try {
            typeList.forEach {
                val groupName = getDefaultGroupNameByGroupType(context, it)
                val groupUUID = getDefaultGroupUuidByGroupType(it)
                val sqlProjection = "(${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_NAME}," +
                        "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_UUID}," +
                        "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_TYPE}," +
                        "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_SORT})"
                val sql = "INSERT INTO ${DatabaseConstant.TABLE_NAME_GROUP_INFO}$sqlProjection " +
                        "SELECT '$groupName', '$groupUUID', $it, 0 " +
                        "WHERE NOT EXISTS(SELECT $sqlProjection FROM ${DatabaseConstant.TABLE_NAME_GROUP_INFO} " +
                        "WHERE ${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_UUID}='$groupUUID')"
                DebugUtil.d(TAG, "initDefaultGroupInfoData: SQL = $sql")
                db.execSQL(sql)
            }
            db.setTransactionSuccessful()
        } catch (e: SQLiteException) {
            DebugUtil.e(TAG, "initDefaultGroupInfoData SQLiteException", e)
        } finally {
            db.endTransaction()
        }
    }

    @JvmStatic
    private fun checkAndUpdateDefaultInitData(context: Context?) {
        if ((context == null)) {
            DebugUtil.e(TAG, "initDefaultData context null return ")
            return
        }
        val callingGroupName = context.getString(R.string.incall_recording_tab)
        val commonGroupName = context.getString(R.string.normal_recording_tab)
        val allGroupName = context.getString(R.string.all_the_recordings)
        val deletedGroupName = context.getString(R.string.recycle_recently_deleted)
        mDefaultGroupInfoAll = localInsertOrUpdateDefaultGroupInfo(context, allGroupName, GroupInfo.INT_DEFAULT_ALL)
        mDefaultGroupInfoCalling = localInsertOrUpdateDefaultGroupInfo(context, callingGroupName, GroupInfo.INT_DEFAULT_CALLING)
        mDefaultGroupInfoCommon = localInsertOrUpdateDefaultGroupInfo(context, commonGroupName, GroupInfo.INT_DEFAULT_COMMON)
        mDefaultGroupInfoDeleted = localInsertOrUpdateDefaultGroupInfo(context, deletedGroupName, GroupInfo.INT_DEFAULT_RECENTLY_DELETED)
    }

    /**
     * 插入或更新默认分组信息
     */
    @JvmStatic
    private fun localInsertOrUpdateDefaultGroupInfo(
        context: Context?,
        groupName: String,
        groupType: Int
    ): GroupInfo? {
        if ((context == null) || (TextUtils.isEmpty(groupName))) {
            DebugUtil.e(TAG, "insertOrUpdateDefaultGroupInfo context null return or name is empty")
            return null
        }
        var groupInfo = getDefaultGroupInfoByGroupType(context, groupType)
        val defaultGroupInfoUUID = getDefaultGroupUuidByGroupType(groupType)
        if (groupInfo == null) {
            groupInfo = GroupInfo(groupName, groupType)
            groupInfo.mUuId = defaultGroupInfoUUID
            insertGroupInfo(context, groupInfo)
        } else {
            if (groupInfo.mGroupName != groupName) {
                groupInfo.mGroupName = groupName
                groupInfo.mUuId = defaultGroupInfoUUID
                updateGroupInfo(context, groupInfo)
            }
        }
        return groupInfo
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    private fun getDefaultGroupUuidByGroupType(groupType: Int): String {
        when (groupType) {
            GroupInfo.INT_DEFAULT_ALL -> return DEFAULT_ALL_UUID
            GroupInfo.INT_DEFAULT_COMMON -> return DEFAULT_COMMON_UUID
            GroupInfo.INT_DEFAULT_CALLING -> return DEFAULT_CALLING_UUID
            GroupInfo.INT_DEFAULT_PRIVATE -> return DEFAULT_PRIVATE_UUID
            GroupInfo.INT_DEFAULT_RECENTLY_DELETED -> return DEFAULT_RECENTLY_DELETE_UUID
        }
        return DEFAULT_COMMON_UUID
    }

    @JvmStatic
    fun checkGroupExists(context: Context?, groupInfo: GroupInfo): Boolean {
        val existGroupInfo = getGroupInfoByUuid(context, groupInfo.mUuId)
        return existGroupInfo != null
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    private fun getDefaultGroupNameByGroupType(context: Context?, groupType: Int): String? {
        when (groupType) {
            GroupInfo.INT_DEFAULT_ALL -> return context?.getString(R.string.all_the_recordings)
            GroupInfo.INT_DEFAULT_CALLING -> return context?.getString(R.string.incall_recording_tab)
            GroupInfo.INT_DEFAULT_COMMON -> return context?.getString(R.string.normal_recording_tab)
            GroupInfo.INT_DEFAULT_PRIVATE -> return ""
            GroupInfo.INT_DEFAULT_RECENTLY_DELETED -> return context?.getString(R.string.recycle_recently_deleted)
        }
        return context?.getString(R.string.normal_recording_tab)
    }
    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    private fun getDefaultGroupInfoByGroupType(
        context: Context?,
        groupType: Int
    ): GroupInfo? {
        if (context == null) {
            DebugUtil.e(TAG, "getDefaultGroupInfoByGroupType context null return ")
            return null
        }
        var groupInfo: GroupInfo? = null
        var cursor: Cursor? = null
        val where = "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_TYPE} = ? "
        val whereArgs = arrayOf(groupType.toString())
        try {
            cursor = context.contentResolver.query(
                GroupInfoUri.GROUP_INFO_URI,
                null,
                where,
                whereArgs,
                null
            )
            if ((cursor != null) && (cursor.count == 1)) {
                while (cursor.moveToNext()) {
                    groupInfo = GroupInfo(cursor)
                }
            }
            DebugUtil.d(
                TAG,
                "getDefaultGroupInfoByGroupType: groupInfo: $groupInfo"
            )
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getDefaultGroupInfoByGroupType error", e)
        } finally {
            cursor?.close()
        }
        return groupInfo
    }

    @JvmStatic
    fun checkNameForDefaultGroup(defaultGroup: GroupInfo) {
        val name = getDefaultGroupNameByGroupType(BaseApplication.getAppContext(), defaultGroup.mGroupType)
        if (name?.isNotEmpty() == true && defaultGroup.mGroupName != name) {
            defaultGroup.mGroupName = name
        }
    }

    /**
     * 批量插入GroupInfo
     */
    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun batchInsertGroupInfo(context: Context?, list: MutableList<GroupInfo>): Int {
        var insertCount = 0
        if ((context == null) || (list.isEmpty())) {
            DebugUtil.e(TAG, "batchInsertGroupInfo context null return or inputlist is empty")
            return insertCount
        }
        val contentValuesArray: Array<ContentValues> =
            Array(list.size) { list[it].covertToContentValuesWithoutId() }
        try {
            insertCount = context.contentResolver.bulkInsert(
                GroupInfoUri.GROUP_INFO_URI,
                contentValuesArray
            )
            DebugUtil.i(TAG, "batchInsertGroupInfo: insertCount: $insertCount")
        } catch (e: Exception) {
            DebugUtil.e(TAG, "batchInsertGroupInfo error", e)
        }
        return insertCount
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun insertGroupInfo(context: Context?, groupInfo: GroupInfo): Boolean {
        if ((context == null)) {
            DebugUtil.e(TAG, "insertGroupInfo context null return or input list is empty")
            return false
        }
        val contentValues = groupInfo.covertToContentValuesWithoutId()
        if (TextUtils.isEmpty(groupInfo.mUuId)) {
            contentValues.put(
                DatabaseConstant.GroupInfoColumn.COLUMN_NAME_UUID,
                UUID.randomUUID().toString()
            )
        }

        try {
            val insertUri = context.contentResolver.insert(
                GroupInfoUri.GROUP_INFO_URI,
                contentValues
            )
            DebugUtil.i(TAG, "insertGroupInfo: insertUri: $insertUri")
            return insertUri != null
        } catch (e: Exception) {
            DebugUtil.e(TAG, "insertGroupInfo error", e)
        }
        return false
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun deleteByGroupName(context: Context?, groupName: String?): Boolean {
        if (context == null || TextUtils.isEmpty(groupName)) {
            DebugUtil.e(TAG, "deleteByUUId context null return or input onlyId is empty")
            return false
        }

        val where: String = DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_NAME + " = ?"
        val whereArgs = arrayOf(groupName)
        try {
            val deleteCount = context.contentResolver.delete(
                GroupInfoUri.GROUP_INFO_URI,
                where,
                whereArgs
            )
            DebugUtil.i(TAG, "deleteByGroupName: $groupName, deleteCount: $deleteCount")
            return  deleteCount > 0
        } catch (e: Exception) {
            DebugUtil.e(TAG, "deleteByGroupName error", e)
        }
        return false
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun deleteByUUId(context: Context?, uuid: String?): Boolean {
        if (context == null || TextUtils.isEmpty(uuid)) {
            DebugUtil.e(TAG, "deleteByUUId context null return or uuid is empty")
            return false
        }

        val where: String = DatabaseConstant.GroupInfoColumn.COLUMN_NAME_UUID + " = ?"
        val whereArgs = arrayOf(uuid)
        try {
            val deleteCount = context.contentResolver.delete(
                GroupInfoUri.GROUP_INFO_URI,
                where,
                whereArgs
            )
            DebugUtil.i(TAG, "deleteByUUId: UUID: $uuid, deleteCount: $deleteCount")
            return deleteCount > 0
        } catch (e: Exception) {
            DebugUtil.e(TAG, "deleteByUUId error", e)
        }
        return false
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun deleteByGroupId(context: Context?, groupId: Int): Boolean {
        if (context == null) {
            DebugUtil.e(TAG, "deleteByGroupId context null return")
            return false
        }

        val where: String = DatabaseConstant.GroupInfoColumn.COLUMN_NAME_ID + " = ?"
        val whereArgs = arrayOf(groupId.toString())
        try {
            val deleteCount = context.contentResolver.delete(
                GroupInfoUri.GROUP_INFO_URI,
                where,
                whereArgs
            )
            DebugUtil.i(TAG, "deleteByUUId, deleteCount: $deleteCount")
            return deleteCount > 0
        } catch (e: Exception) {
            DebugUtil.e(TAG, "deleteByUUId error", e)
        }
        return false
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun deleteRecords(context: Context?, where: String?, whereArgs: Array<String>?): Boolean {
        if (context == null) {
            DebugUtil.e(TAG, "deleteRecords context null return")
            return false
        }
        try {
            val deleteCount = context.contentResolver.delete(
                GroupInfoUri.GROUP_INFO_URI,
                where,
                whereArgs
            )
            DebugUtil.i(TAG, "deleteRecords: deleteCount = $deleteCount")
            return deleteCount > 0
        } catch (e: Exception) {
            DebugUtil.e(TAG, "deleteRecords error", e)
        }
        return false
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun updateGroupInfoDeleteState(context: Context?, groupInfo: GroupInfo): Boolean {
        if ((context == null)) {
            DebugUtil.e(TAG, "updateGroupInfoDeleteField context null return")
            return false
        }
        val contentValues = groupInfo.covertAllToContentValues()
        try {
            val where: String = DatabaseConstant.GroupInfoColumn.COLUMN_NAME_UUID + " = ?"
            val whereArgs = arrayOf(groupInfo.mUuId)
            val updateCount = context.contentResolver.update(
                GroupInfoUri.GROUP_INFO_URI,
                contentValues,
                where,
                whereArgs
            )
            return updateCount > 0
        } catch (e: Exception) {
            DebugUtil.e(TAG, "updateGroupInfoDeleteField error", e)
        }
        return false
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun updateGroupInfo(context: Context?, groupInfo: GroupInfo): Boolean {
        if ((context == null)) {
            DebugUtil.e(TAG, "updateGroupInfo context null return")
            return false
        }
        val contentValues = groupInfo.covertToContentValuesWithoutDeleteField()
        try {
            val where: String = "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_UUID} = ? or " +
                    "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_NAME} = ?"
            val whereArgs = arrayOf(groupInfo.mUuId, groupInfo.mGroupName)
            val updateCount = context.contentResolver.update(
                GroupInfoUri.GROUP_INFO_URI,
                contentValues,
                where,
                whereArgs
            )
            return updateCount > 0
        } catch (e: Exception) {
            DebugUtil.e(TAG, "updateGroupInfo error", e)
        }
        return false
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun updateGroupInfoByUUID(context: Context?, uuid: String, contentValues: ContentValues): Boolean {
        if ((context == null)) {
            DebugUtil.e(TAG, "updateGroupInfo context null return")
            return false
        }

        try {
            val where = "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_UUID} = ? "
            val whereArgs = arrayOf(uuid)
            val updateCount = context.contentResolver.update(
                GroupInfoUri.GROUP_INFO_URI,
                contentValues,
                where,
                whereArgs
            )
            return updateCount > 0
        } catch (e: Exception) {
            DebugUtil.e(TAG, "updateGroupInfoByUUID error", e)
        }
        return false
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun updateGroupRecordsCount(context: Context?, groupInfoList: List<GroupInfo>): List<GroupInfo>? {
        try {
            return RecorderDBUtil.getInstance(context).processGroupCount(groupInfoList)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "updateGroupRecordsCount error", e)
        }
       return null
    }

    /**
     * 在用户拖拽列表后调用，传入的list为已经排好序的列表
     * 更新group_info表记录的group_sort字段值
     *
     */
    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun updateGroupInfoSort(context: Context?, sortedList: MutableList<GroupInfo>): Boolean {
        var result = false
        if ((context == null) || (sortedList.isEmpty())) {
            DebugUtil.e(TAG, "updateGroupInfoSeqNumber context null return or input list is empty")
            return false
        }
        try {
            var updateIndex = 0
            var totalNeedUpdateCount = 0
            sortedList.forEachIndexed { index, groupInfo ->
                if (groupInfo.mGroupSort != index) {
                    groupInfo.mGroupSort = index
                    groupInfo.mDirty = RECORD_DIRTY_MEGA_ONLY
                    totalNeedUpdateCount++
                    val where: String = DatabaseConstant.GroupInfoColumn.COLUMN_NAME_UUID + " = ?"
                    val whereArgs = arrayOf(groupInfo.mUuId)
                    val updateCount = context.contentResolver.update(
                        GroupInfoUri.GROUP_INFO_URI,
                        groupInfo.covertToContentValuesWithoutDeleteField(),
                        where,
                        whereArgs
                    )
                    DebugUtil.i(TAG, "updateGroupInfoSeqNumber: update: $groupInfo")
                    if (updateCount > 0) {
                        updateIndex++
                    }
                }
            }
            result = (updateIndex == totalNeedUpdateCount)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "updateGroupInfoSeqNumber error", e)
        }
        return result
    }

    /**
     * 不区分分组类型，获取表中所有分组信息
     */
    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun getAllGroupInfoList(context: Context?): MutableList<GroupInfo> {
        val list: MutableList<GroupInfo> = mutableListOf()
        if (context == null) {
            DebugUtil.e(TAG, "getAllGroupInfoList context null return ")
            return list
        }
        var cursor: Cursor? = null
        //查询所有的分组列表，应将已标记为删除状态的记录过滤掉
        val selection = "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_DELETED} != ?"
        val selectionArgs = arrayOf(RECORD_DELETED.toString())
        try {
            cursor = context.contentResolver.query(
                GroupInfoUri.GROUP_INFO_URI,
                null,
                selection,
                selectionArgs,
                null
            )
            if ((cursor != null) && (cursor.count > 0)) {
                while (cursor.moveToNext()) {
                    val groupInfo = GroupInfo(cursor)
                    if (!isSupportCall && groupInfo.isCallingGroup()) {
                        continue
                    }
                    list.add(groupInfo)
                }
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getAllGroupInfoList error", e)
        } finally {
            cursor?.close()
        }
        if (list.isNotEmpty()) {
            //先取出默认分组
            val defaultGroups: List<GroupInfo> =  list.filter { it.isDefaultGroup() }
            //自定义分组排序规则：先按mGroupSort排序，如果mGroupSort相同，则按照创建顺序，新的在前面。即mId大的在前。
            val sortedCustomGroupInfos: List<GroupInfo> =
                list.filter { it.isCustomGroup() }
                    .sortedWith(compareBy<GroupInfo> { it.mGroupSort }.thenByDescending { it.mId })
            val resultList: MutableList<GroupInfo> = mutableListOf()
            resultList.addAll(defaultGroups)
            resultList.addAll(sortedCustomGroupInfos)
            DebugUtil.i(TAG, "getAllGroupInfoList: $resultList")
            return resultList
        }
        return list
    }


    /**
     * 获取指定类型的分组信息且升序排列
     */
    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    private fun getGroupInfoList(context: Context?, bDefaultGroup: Boolean): MutableList<GroupInfo> {
        val result: MutableList<GroupInfo> = mutableListOf()
        if (context == null) {
            DebugUtil.e(TAG, "getGroupInfoList context null return ")
            return result
        }
        var cursor: Cursor? = null
        val condition = if (bDefaultGroup) {
            " != ?"
        } else {
            " = ?"
        }
        val where: String = DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_TYPE + condition
        val whereArgs = arrayOf(GroupInfo.INT_DEFAULT_NONE.toString())
        val sortOrder = "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_SORT} ASC"
        try {
            cursor = context.contentResolver.query(
                GroupInfoUri.GROUP_INFO_URI,
                null,
                where,
                whereArgs,
                sortOrder
            )
            if ((cursor != null) && (cursor.count > 0)) {
                while (cursor.moveToNext()) {
                    val groupInfo = GroupInfo(cursor)
                    result.add(groupInfo)
                }
            }
            DebugUtil.i(
                TAG,
                "getGroupInfoList: outcount: " + cursor?.count
            )
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getGroupInfoList error", e)
        } finally {
            cursor?.close()
        }
        return result
    }

    /**
     * 获取所有自定义分组信息且升序排列
     */
    @JvmStatic
    fun getCustomGroupsFromDB(context: Context?): MutableList<GroupInfo> {
       return getGroupInfoList(context, false)
    }

    /**
     * 获取所有默认分组信息且升序排列
     */
    @JvmStatic
    fun getDefaultGroupsFromDB(context: Context?): MutableList<GroupInfo> {
        return getGroupInfoList(context, true)
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun getGroupInfoByGroupId(context: Context?, groupId: Int): GroupInfo? {
        if (context == null) {
            DebugUtil.e(TAG, "getGroupInfoByGroupId context null return ")
            return null
        }
        var groupInfo: GroupInfo? = null
        var cursor: Cursor? = null
        val where: String = DatabaseConstant.GroupInfoColumn.COLUMN_NAME_ID + " = ?"
        val whereArgs = arrayOf(groupId.toString())
        try {
            cursor = context.contentResolver.query(
                GroupInfoUri.GROUP_INFO_URI,
                null,
                where,
                whereArgs,
                null
            )
            if ((cursor != null) && (cursor.count == 1)) {
                while (cursor.moveToNext()) {
                    groupInfo = GroupInfo(cursor)
                }
            }
            DebugUtil.i(
                TAG,
                "getGroupInfoByGroupId: groupInfo: $groupInfo"
            )
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getGroupInfoByGroupId error", e)
        } finally {
            cursor?.close()
        }
        return groupInfo
    }

    @JvmStatic
    fun getGroupIdByGroupUUID(context: Context?, uuid: String?): Int {
        val groupInfo = getGroupInfoByUuid(context, uuid)
        return groupInfo?.mId ?: -1
    }
    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun getGroupInfoByUuid(context: Context?, uuid: String?): GroupInfo? {
        if (context == null || TextUtils.isEmpty(uuid)) {
            DebugUtil.e(TAG, "getGroupInfoByUuid context null return ")
            return null
        }
        var groupInfo: GroupInfo? = null
        var cursor: Cursor? = null
        val where: String = DatabaseConstant.GroupInfoColumn.COLUMN_NAME_UUID + " = ?"
        val whereArgs = arrayOf(uuid)
        try {
            cursor = context.contentResolver.query(
                GroupInfoUri.GROUP_INFO_URI,
                null,
                where,
                whereArgs,
                null
            )
            if ((cursor != null) && (cursor.count == 1)) {
                while (cursor.moveToNext()) {
                    groupInfo = GroupInfo(cursor)
                }
            }
/*
            DebugUtil.i(
                TAG,
                "getGroupInfoByUuid: groupInfo: $groupInfo"
            )
*/
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getGroupInfoByUuid error", e)
        } finally {
            cursor?.close()
        }
        return groupInfo
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun getGroupInfoByGlobalId(context: Context?, globalId: String?): GroupInfo? {
        if (context == null || TextUtils.isEmpty(globalId)) {
            DebugUtil.e(TAG, "getGroupInfoByGlobalId context null return ")
            return null
        }
        var groupInfo: GroupInfo? = null
        var cursor: Cursor? = null
        val where: String = DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID + " = ?"
        val whereArgs = arrayOf(globalId)
        try {
            cursor = context.contentResolver.query(
                GroupInfoUri.GROUP_INFO_URI,
                null,
                where,
                whereArgs,
                null
            )
            if ((cursor != null) && (cursor.count == 1)) {
                while (cursor.moveToNext()) {
                    groupInfo = GroupInfo(cursor)
                }
            }
            DebugUtil.i(
                TAG,
                "getGroupInfoByGlobalId: groupInfo: $groupInfo"
            )
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getGroupInfoByGlobalId error", e)
        } finally {
            cursor?.close()
        }
        return groupInfo
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun getGroupInfoByName(context: Context?, groupName: String?): GroupInfo? {
        if (context == null || TextUtils.isEmpty(groupName)) {
            DebugUtil.e(TAG, "getGroupInfoByName context null return ")
            return null
        }
        var groupInfo: GroupInfo? = null
        var cursor: Cursor? = null
        val where: String = DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_NAME + " = ?"
        val whereArgs = arrayOf(groupName)
        try {
            cursor = context.contentResolver.query(
                GroupInfoUri.GROUP_INFO_URI,
                null,
                where,
                whereArgs,
                null
            )
            if ((cursor != null) && (cursor.count == 1)) {
                while (cursor.moveToNext()) {
                    groupInfo = GroupInfo(cursor)
                }
            }
            DebugUtil.i(
                TAG,
                "getGroupInfoByName: groupInfo: $groupInfo"
            )
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getGroupInfoByName error", e)
        } finally {
            cursor?.close()
        }
        return groupInfo
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun getGroupInfoByNameExcludeSelf(
        oldGroupInfo: GroupInfo?,
        context: Context?,
        groupName: String?
    ): GroupInfo? {
        if (context == null || TextUtils.isEmpty(groupName)) {
            DebugUtil.e(TAG, "getGroupInfoByNameExcludeSelf context null return ")
            return null
        }
        var groupInfo: GroupInfo? = null
        var cursor: Cursor? = null
        val where: String = DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_NAME + " = ?"
        val whereArgs = arrayOf(groupName)
        try {
            cursor = context.contentResolver.query(
                GroupInfoUri.GROUP_INFO_URI,
                null,
                where,
                whereArgs,
                null
            )
            if ((cursor != null) && (cursor.count == 1)) {
                while (cursor.moveToNext()) {
                    val groupInfoInDb = GroupInfo(cursor)
                    if (oldGroupInfo?.mUuId != groupInfoInDb.mUuId) { //除开当前操作组，查询出来的组信息与当前操作组的UUid不相等，则认为是存在相同组。
                        groupInfo = groupInfoInDb
                    }
                }
            }
            DebugUtil.i(
                TAG,
                "getGroupInfoByNameExcludeSelf: groupInfo: $groupInfo"
            )
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getGroupInfoByNameExcludeSelf error", e)
        } finally {
            cursor?.close()
        }
        return groupInfo
    }

    /**
     * 本地db已同步到云端的count
     *
     * @return
     */
    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun getLocalGroupInfoCloudDataCount(context: Context?): Int {
        val projection = arrayOf(DatabaseConstant.GroupInfoColumn.COLUMN_NAME_ID)
        var selection = "(%s not null)"
        selection = String.format(selection, DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID)
        val selectionArgs = arrayOf<String>()
        val queryBundle =
            RecorderDBUtil.createSqlQueryBundle(selection, selectionArgs, null, null, null)
        var cursor: Cursor? = null
        var count = 0
        try {
            cursor = context?.contentResolver?.query(GroupInfoUri.GROUP_INFO_URI, projection, queryBundle, null)
            if (cursor != null) {
                count = cursor.count
            }
        } catch (e: Throwable) {
            DebugUtil.e(TAG, "query local synced count error", e)
        } finally {
            cursor?.close()
            DebugUtil.i(TAG, "query local synced count:$count")
        }
        return count
    }
    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun getDirtyDataForUnCommitMetadata(context: Context?): MutableList<GroupInfo> {
        val selection = "(${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID} is null or " +
                "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID} = '')" +
                " and ${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_TYPE} = $GROUP_TYPE_CUSTOM" +
                " and ${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_DIRTY} != $RECORD_NOT_DIRTY" +
                " and ${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_DELETED} != $RECORD_DELETED" +
                " and ${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_LOCAL_EDIT_STATUS} != $LOCAL_EDITING"
        val projection = getProjections()
        val selectionArgs = arrayOf<String>()
        val queryBundle =
            RecorderDBUtil.createSqlQueryBundle(selection, selectionArgs, null, null, null)
        var cursor: Cursor? = null
        val resultList = mutableListOf<GroupInfo>()
        try {
            cursor = context?.contentResolver?.query(GroupInfoUri.GROUP_INFO_URI, projection, queryBundle, null)
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    val groupInfo = GroupInfo(cursor)
                    resultList.add(groupInfo)
                }
            }
        } catch (e: Throwable) {
            DebugUtil.e(TAG, "getDirtyDataForUnCommitMetadata error", e)
        } finally {
            DebugUtil.i(TAG, "getDirtyDataForUnCommitMetadata count: ${cursor?.count}")
            cursor?.close()
        }
        return resultList
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun getDirtyDataForChangedMetadata(context: Context?): MutableList<GroupInfo> {
        val selection = "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_DIRTY} != $RECORD_NOT_DIRTY " +
                "and (${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID} is not null or " +
                "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID} != '') " +
                " and ${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_TYPE} = $GROUP_TYPE_CUSTOM" +
                " and ${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_LOCAL_EDIT_STATUS} != $LOCAL_EDITING"
        val projection = getProjections()
        val selectionArgs = arrayOf<String>()
        val queryBundle =
            RecorderDBUtil.createSqlQueryBundle(selection, selectionArgs, null, null, null)
        var cursor: Cursor? = null
        val resultList = mutableListOf<GroupInfo>()
        try {
            cursor = context?.contentResolver?.query(GroupInfoUri.GROUP_INFO_URI, projection, queryBundle, null)
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    val groupInfo = GroupInfo(cursor)
                    resultList.add(groupInfo)
                    DebugUtil.e(TAG, "getDirtyDataForChangedMetadata $groupInfo")
                }
            }
        } catch (e: Throwable) {
            DebugUtil.e(TAG, "getDirtyDataForChangedMetadata error", e)
        } finally {
            DebugUtil.i(TAG, "getDirtyDataForChangedMetadata count: ${cursor?.count}")
            cursor?.close()
        }
        return resultList
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun getLocalGroupInfoInDbByCloudGroupInfo(context: Context?, cloudGroupInfo: GroupInfo?): GroupInfo? {
        if (context == null || cloudGroupInfo == null) {
            DebugUtil.e(TAG, "getLocalGroupInfoInDbByCloudGroupInfo context null return ")
            return null
        }
        if (TextUtils.isEmpty(cloudGroupInfo.mGroupGlobalId)
            || TextUtils.isEmpty(cloudGroupInfo.mGroupName)
            || TextUtils.isEmpty(cloudGroupInfo.mUuId)) {
            DebugUtil.e(TAG, "getLocalGroupInfoInDbByCloudGroupInfo cloudGroupInfo params are illegal")
        }
        var localGroupInfoInDb: GroupInfo? = null
        var cursor: Cursor? = null
        val where: String = "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_NAME} = ? or " +
                "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_UUID} = ? or " +
                "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID} = ? "
        val whereArgs = arrayOf(cloudGroupInfo.mGroupName, cloudGroupInfo.mUuId, cloudGroupInfo.mGroupGlobalId)
        try {
            cursor = context.contentResolver.query(
                GroupInfoUri.GROUP_INFO_URI,
                null,
                where,
                whereArgs,
                null
            )
            if ((cursor != null) && (cursor.count == 1)) {
                while (cursor.moveToNext()) {
                    localGroupInfoInDb = GroupInfo(cursor)
                }
            }
            DebugUtil.i(
                TAG,
                "getLocalGroupInfoInDbByCloudGroupInfo: localGroupInfoInDb: $localGroupInfoInDb"
            )
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getLocalGroupInfoInDbByCloudGroupInfo error", e)
        } finally {
            cursor?.close()
        }
        return localGroupInfoInDb
    }

    @JvmStatic
    fun clearCloudColumnByUUid(context: Context?, uuid: String, bResetUUID: Boolean): Boolean {
        val cv = ContentValues()
        if (bResetUUID) {
            cv.put(DatabaseConstant.GroupInfoColumn.COLUMN_NAME_UUID, UUID.randomUUID().toString())
        }
        val strNull: String? = null
        cv.put(DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID, strNull)
        cv.put(DatabaseConstant.GroupInfoColumn.COLUMN_NAME_CLOUD_SYS_VERSION, 0)
        cv.put(DatabaseConstant.GroupInfoColumn.COLUMN_NAME_DIRTY, RECORD_DIRTY_MEGA_ONLY)
        cv.put(
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS,
            RecordConstant.SYNC_STATUS_BACKUP_START
        )
        cv.put(
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_LOCAL_EDIT_STATUS,
            RecordConstant.LOCAL_EDIT_COMPLET
        )
        cv.put(DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_DATE, 0)
        return updateGroupInfoByUUID(context, uuid, cv)
    }


    @JvmStatic
    fun getCallingGroupItemInfo(context: Context?): GroupInfo {
        return getGroupInfoByUuid(context, DEFAULT_CALLING_UUID) ?: genDefaultCallGroupInfo()
    }

    @JvmStatic
    fun getCommonGroupItemInfo(context: Context?): GroupInfo {
        return getGroupInfoByUuid(context, DEFAULT_COMMON_UUID) ?: genDefaultCommonGroupInfo()
    }

    @JvmStatic
    fun getAllGroupItemInfo(context: Context?): GroupInfo {
        return getGroupInfoByUuid(context, DEFAULT_ALL_UUID) ?: genDefaultAllGroupInfo()
    }

    @JvmStatic
    fun getDeletedGroupItemInfo(context: Context?): GroupInfo {
        return getGroupInfoByUuid(context, DEFAULT_RECENTLY_DELETE_UUID) ?: genDefaultDeletedGroupInfo()
    }
    @JvmStatic
    fun getProjections(): Array<String?>? {
        return arrayOf(
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_ID,
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_UUID,
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_NAME,
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_SORT,
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_COLOR,
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_COUNT,
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID,
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_DIRTY,
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_DELETED,
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_IS_PRIVATE,
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_CLOUD_SYS_VERSION,
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_TYPE,
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_TYPE,
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS,
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_DOWNLOAD_STATUS,
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_ERROR_CODE,
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_LOCAL_EDIT_STATUS,
            DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_DATE
        )
    }

    @JvmStatic
    fun genDefaultAllGroupInfo(): GroupInfo {
        return GroupInfo(
            DEFAULT_ALL_UUID,
            BaseApplication.getAppContext().getString(R.string.all_the_recordings),
            GroupInfo.INT_DEFAULT_ALL,
            NumberConstant.NUM_1
        )
    }

    @JvmStatic
    fun genDefaultCallGroupInfo(): GroupInfo {
        return GroupInfo(
            DEFAULT_CALLING_UUID,
            BaseApplication.getAppContext().getString(R.string.incall_recording_tab),
            GroupInfo.INT_DEFAULT_CALLING,
            NumberConstant.NUM_2
        )
    }

    @JvmStatic
    fun genDefaultCommonGroupInfo(): GroupInfo {
        return GroupInfo(
            DEFAULT_COMMON_UUID,
            BaseApplication.getAppContext().getString(R.string.normal_recording_tab),
            GroupInfo.INT_DEFAULT_COMMON,
            NumberConstant.NUM_3
        )
    }

    @JvmStatic
    fun genDefaultDeletedGroupInfo(): GroupInfo {
        return GroupInfo(
            DEFAULT_RECENTLY_DELETE_UUID,
            BaseApplication.getAppContext().getString(R.string.recycle_recently_deleted),
            GroupInfo.INT_DEFAULT_RECENTLY_DELETED,
            NumberConstant.NUM_4
        )
    }

    @JvmStatic
    fun genDefaultGroups(): MutableList<GroupInfo> {
        val groupList: MutableList<GroupInfo> = mutableListOf()
        groupList.add(genDefaultAllGroupInfo())
        groupList.add(genDefaultCallGroupInfo())
        groupList.add(genDefaultCommonGroupInfo())
        groupList.add(genDefaultDeletedGroupInfo())
        return groupList
    }

    /**
     * 保存录音文件时，需要根据当前默认的分组选项设置groupid 和groupuuid
     * 从SP中获取当前录音文件的分组信息，并根据分组信息返回对应的groupInfo
     * @param saveFromWhere 标记录音保存时的来源
     * @return groupInfo 返回的对象仅包括groupid和groupuuid，其他信息为空
     */
    @JvmStatic
    fun getGroupInfoForSaveRecordFile(saveFromWhere: Int): GroupInfo {
        DebugUtil.d(TAG, "getGroupInfoForSaveRecordFile, saveFromWhere = $saveFromWhere")
        if (saveFromWhere == MSG_ARG2_SAVE_RECORD_FROM_ACTIVITY || saveFromWhere == MSG_ARG2_SAVE_RECORD_FROM_NORMAL) {
            val groupId =
                PrefUtil.getInt(BaseApplication.getAppContext(), PrefUtil.KEY_RECORD_GROUP_ID, -1)
            val groupUUID = PrefUtil.getString(
                BaseApplication.getAppContext(),
                PrefUtil.KEY_RECORD_GROUP_UUID,
                DEFAULT_COMMON_UUID
            )
            return if (groupId == -1) {
                genDefaultCommonGroupInfo()
            } else {
                val groupInfo = GroupInfo(groupId, groupUUID)
                return if (groupInfo.isCallingGroup() || groupInfo.isAllGroup() || groupInfo.isRecentlyDeleteGroup()) {
                    genDefaultCommonGroupInfo()
                } else {
                    groupInfo
                }
            }
        } else {
            return genDefaultCommonGroupInfo()
        }
    }

    @JvmStatic
    fun getGroupTypeByGroupUUID(groupUUID: String?): Int {
        return when (groupUUID) {
            DEFAULT_ALL_UUID -> GroupInfo.INT_DEFAULT_ALL

            DEFAULT_CALLING_UUID -> GroupInfo.INT_DEFAULT_CALLING

            DEFAULT_COMMON_UUID -> GroupInfo.INT_DEFAULT_COMMON

            DEFAULT_RECENTLY_DELETE_UUID -> GroupInfo.INT_DEFAULT_RECENTLY_DELETED

            else -> GroupInfo.INT_DEFAULT_NONE
        }
    }

    @JvmStatic
    fun isCustomGroup(groupUUID: String?): Boolean {
        return GroupInfo.INT_DEFAULT_NONE == getGroupTypeByGroupUUID(groupUUID)
    }

    @JvmStatic
    private fun getMediaCursor(): Cursor? {
        var cursor: Cursor? = null
        var whereClause =
            CursorHelper.getAllRecordForFilterAndQueryWhereClause(BaseApplication.getAppContext())
        var selectionArg = CursorHelper.getsAcceptableAudioTypes()
        val orderClause =
            "${MediaStore.Audio.Media.DATE_MODIFIED} DESC,${MediaStore.Audio.Media.TITLE} DESC"
        // 当前正在录制的音频，过滤该条数据
        val fileBeingRecorded = getFileBeingRecorded()
        if (!fileBeingRecorded.isNullOrBlank()) {
            whereClause += " and " + MediaStore.Audio.Media.DATA + " != ?"
            val argsList: MutableList<String> = selectionArg.toMutableList()
            argsList.add(fileBeingRecorded)
            selectionArg = argsList.toTypedArray<String>()
        }
        kotlin.runCatching {
            cursor = BaseApplication.getAppContext().contentResolver.query(
                MediaDBUtils.BASE_URI, CursorHelper.getProjection(),
                whereClause, selectionArg, orderClause
            )
            DebugUtil.d(TAG, "getMediaCursor count: ${cursor?.count}")
        }.onFailure {
            DebugUtil.e(TAG, "getMediaCursor error: $it")
        }
        return cursor
    }

    /**
     * 查询所有媒体文件，并计算数量, 在媒体库数据插入录音库时调用
     */
    @JvmStatic
    fun queryAndCalculateAllMediaDataCount(): MediaCounter? {
        var mediaCounter: MediaCounter? = null
        getMediaCursor()?.use {
            mediaCounter = calculateListCount(it)
        }
        return mediaCounter
    }

    @JvmStatic
    private fun getRecordsGroupTypeMap(recordsList: List<Record>?): Map<String, Int>? {
        if (!recordsList.isNullOrEmpty()) {
            val map = mutableMapOf<String, Int>()
            recordsList.forEach {
                if (it.groupUuid.isNullOrEmpty()) {
                    GroupInfoManager.getInstance(BaseApplication.getAppContext())
                        .resetGroupInfoForRecord(it)
                }
                map[it.data] = getGroupTypeByGroupUUID(it.groupUuid)
            }
            return map
        }
        return null
    }
    @JvmStatic
    private fun processDuplicateItemsMap(recordsList: List<Record>?): Map<String, Record>? {
        val removedDuplicateItemsMap: HashMap<String, Record> = HashMap()
        recordsList?.let {
            val groupByList = recordsList.groupBy { it.data }
            val duplicates = groupByList.filter { it.value.size > 1 }
            DebugUtil.d(
                TAG,
                "processDuplicateItemsMap,recordsList: ${recordsList.size},groupByList:${groupByList.size}, duplicates:${duplicates.size}"
            )
            duplicates.forEach { (key, value) ->
                value.forEach {
                    if (it.groupUuid.isNullOrEmpty()) {
                        GroupInfoManager.getInstance(BaseApplication.getAppContext())
                            .resetGroupInfoForRecord(it)
                    }
                    if (removedDuplicateItemsMap.containsKey(it.data)) {
                        if (isCustomGroup(it.groupUuid)) {
                            removedDuplicateItemsMap[it.data] = it
                        }
                    } else {
                        removedDuplicateItemsMap[it.data] = it
                    }
                }
            }
        }
        return removedDuplicateItemsMap
    }

    @JvmStatic
    private fun calculateListCount(cursor: Cursor): MediaCounter {
        DebugUtil.d(TAG, "calculateListCount")
        val mediaCounter = MediaCounter()
        //先查一遍records表，获取Records表的真实分组，以备判断跟媒体库默认的分组信息是否匹配
        val recordsList =
            RecorderDBUtil.getInstance(BaseApplication.getAppContext())
                .queryAllRecordsWithoutRecycleBin()

        //数据库里可能存在重复的数据，这里需要去重处理，否则界面上显示的数据重复，引发计数异常或编辑时被同时选中
        val nonDuplicateItemsMap = processDuplicateItemsMap(recordsList)
        val resultList = mutableListOf<Record>()
        if (nonDuplicateItemsMap == null) {
            resultList.addAll(recordsList)
        } else {
            val nonDuplicatesList =
                recordsList.filter { record -> !nonDuplicateItemsMap.containsKey(record.data) }
            resultList.addAll(nonDuplicatesList)
            resultList.addAll(nonDuplicateItemsMap.values.toList())
        }
        val recordsGroupTypeMap = getRecordsGroupTypeMap(resultList)
        cursor.use {
            if (!cursor.moveToFirst()) {
                cursor.close()
                return@use
            }
            do {
                // cursor是来自媒体库的查询结果，获取文件路径，查询记录的分组类型
                val columnDataIndex = cursor.getColumnIndex(MediaStore.Audio.Media.DATA)
                var filePath: String? = null
                if (columnDataIndex >= 0) {
                    filePath = cursor.getString(columnDataIndex)
                    if (!filePath.isNullOrEmpty() && !File(filePath).exists()) {
                        continue
                    }
                }
                val realGroupType = recordsGroupTypeMap?.get(filePath)

                val columnRelativePathIndex = cursor.getColumnIndex(MediaStore.Audio.Media.RELATIVE_PATH)
                var relativePath: String? = null
                if (columnRelativePathIndex >= 0) {
                    relativePath = cursor.getString(columnRelativePathIndex)
                    if (!relativePath.isNullOrBlank()) {
                        parseRelativePathColumn(relativePath, realGroupType, mediaCounter)
                    }
                }
            } while (cursor.moveToNext())
        }
        cursor.close()
        return mediaCounter
    }

    @JvmStatic
    private fun parseRelativePathColumn(
        pathValue: String,
        realGroupType: Int?,
        mediaCounter: MediaCounter
    ) {
        when (pathValue.cleanRelativePath()) {
            RecordModeConstant.RELATIVE_PATH_STANDARD,
            RecordModeConstant.RELATIVE_PATH_BASE,
            RecordModeConstant.RELATIVE_PATH_MEETING,
            RecordModeConstant.RELATIVE_PATH_INTERVIEW -> {
                if (realGroupType != null) {
                    //分组为“普通录音”和record表真实分组一致，则计数为普通录音
                    if (realGroupType == GroupInfo.INT_DEFAULT_COMMON) {
                        mediaCounter.addCommonCount()
                    }
                } else {
                    //未查询到record表真实分组，则根据路径类型计数为普通录音
                    mediaCounter.addCommonCount()
                }
            }
            RecordModeConstant.RELATIVE_PATH_CALL -> {
                if (realGroupType != null) {
                    //分组为“通话录音”和record表真实分组一致，则计数为通话录音
                    if (realGroupType == GroupInfo.INT_DEFAULT_CALLING) {
                        mediaCounter.addCallCount()
                    }
                    //通话录音的文件可以移动到普通录音，record表真实分组为“普通录音”，则计数为普通录音
                    if (realGroupType == GroupInfo.INT_DEFAULT_COMMON) {
                        mediaCounter.addCommonCount()
                    }
                }  else {
                    //未查询到record表真实分组，则根据路径类型计数为“通话录音”
                    mediaCounter.addCallCount()
                }
            }
        }
        mediaCounter.addAllCount()
    }

    @JvmStatic
    fun getMediaCount(): Int {
        val cursor = getMediaCursor()
        val mediaDbCount = cursor?.count ?: 0
        cursor?.close()
        return  mediaDbCount
    }

    @JvmStatic
    fun setStopCalculateGroupCount(bStop: Boolean) {
        stopCalculateGroupCount = bStop
    }
}