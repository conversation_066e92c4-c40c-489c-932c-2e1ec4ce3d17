package com.soundrecorder.common.utils;
/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  OifaceBindUtils.java
 * * Description: OifaceBindUtils.java
 * * Version: 1.0
 * * Date : 2019/11/15
 * * Author: liuyulong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * liuyulong  2019/11/15      1.0    build this module
 ****************************************************************/

import android.os.IBinder;
import android.os.RemoteException;

import com.coui.appcompat.version.COUIVersionUtil;
import com.oplus.oiface.OifaceManager;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.ReflectionCache;
import com.soundrecorder.common.constant.DatabaseConstant;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * @hide
 */
public class OifaceBindUtils {

    private static final String TAG = "OifaceBindUtils";

    public static final int BIND_TASK = 2;
    public static final int UNBIND_TASK = 0;
    private static final String OIFACE_DESCRIPTOR = "com.oppo.oiface.IOIfaceService";
    private static final int TRANSACTION_BIND_TASK = (IBinder.FIRST_CALL_TRANSACTION + 6);
    private static OifaceBindUtils sInstance = null;
    private IBinder mRemote = null;
    private OifaceManager mOifaceManager = null;

    synchronized static public OifaceBindUtils getInstance() {
        if (sInstance == null) {
            sInstance = new OifaceBindUtils();
        }
        return sInstance;
    }

    private OifaceBindUtils() {
        if (isAfterOS11()) {
            try {
                mOifaceManager = OifaceManager.getInstance(DatabaseConstant.PACKAGE_NAME);
                DebugUtil.i(TAG, "INIT OifaceBindUtils success");
            } catch (Exception e) {
                DebugUtil.e(TAG, "INIT OifaceBindUtils faled, ", e);
            }
        } else {
            connectOifaceService();
            DebugUtil.i(TAG, "INIT OifaceBindUtils: connectOifaceService");
        }
    }

    private IBinder connectOifaceService() {
        Class<?> aClass = null;
        try {
            aClass = ReflectionCache.build().forName("android.os.ServiceManager");
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            return null;
        }
        Method method = null;
        try {
            method = ReflectionCache.build().getMethod(aClass, "checkService", String.class);
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
            return null;
        }
        try {
            mRemote = (IBinder) method.invoke(null, "oiface");
        } catch (IllegalAccessException e) {
            e.printStackTrace();
            return null;
        } catch (InvocationTargetException e) {
            e.printStackTrace();
            return null;
        }
        if (mRemote != null) {
            try {
                mRemote.linkToDeath(mDeathRecipient, 0);
            } catch (RemoteException e) {
                mRemote = null;
            }
        }
        return mRemote;
    }

    private IBinder.DeathRecipient mDeathRecipient = new IBinder.DeathRecipient() {
        @Override
        public void binderDied() {
            mRemote = null;
        }
    };

    public void bindTaskWithOiface(int type) {
        if (isAfterOS11()) {
            int tid = android.os.Process.myTid();
            if (mOifaceManager != null) {
                if (type == BIND_TASK) {
                    boolean bindSuc = mOifaceManager.bindGameTask(OifaceManager.CPU_CLUSTER_SILVER, tid);
                    DebugUtil.i(TAG, "OifaceBindUtils bindTaskWithOiface: bindGameTask silver " + bindSuc);
                } else if (type == UNBIND_TASK) {
                    boolean unbindsuc = mOifaceManager.bindGameTask(OifaceManager.BIND_CPU_CLUSTER_ALL, tid);
                    DebugUtil.i(TAG, "OifaceBindUtils bindTaskWithOiface: bindGameTask unbind silver " + unbindsuc);
                } else {
                    DebugUtil.e(TAG, "OifaceBindUtils bindTaskWithOiface: input type illegal: " + type);
                }
            } else {
                DebugUtil.e(TAG, "OifaceBindUtils bindTaskWithOiface: no OifaceManager ");
            }
        } else {
            if ((mRemote == null) && (connectOifaceService() == null)) {
                mRemote = null;
                return;
            }
            android.os.Parcel data = android.os.Parcel.obtain();
            android.os.Parcel reply = android.os.Parcel.obtain();
            int tid = android.os.Process.myTid();
            try {
                data.writeInterfaceToken(OIFACE_DESCRIPTOR);
                data.writeInt(type);
                data.writeInt(tid);
                boolean bindSuc = mRemote.transact(TRANSACTION_BIND_TASK, data, reply, android.os.IBinder.FLAG_ONEWAY);
                DebugUtil.i(TAG, "OifaceBindUtils bindTaskWithOiface: old: " + bindSuc + ", input type: " + type);
            } catch (Exception e) {
                DebugUtil.e(TAG, "OifaceBindUtils bindTaskWithOiface: old error ", e);
                mRemote = null;
                e.printStackTrace();
            } finally {
                data.recycle();
                reply.recycle();
            }
        }
    }

    public void bindTaskWithOiface(int type, int id) {
        if (isAfterOS11()) {
            if (mOifaceManager != null) {
                if (type == BIND_TASK) {
                    boolean bindSuc = mOifaceManager.bindGameTask(OifaceManager.CPU_CLUSTER_SILVER, id);
                    DebugUtil.i(TAG, "OifaceBindUtils bindTaskWithOiface: bindGameTask silver " + bindSuc);
                } else if (type == UNBIND_TASK) {
                    boolean unbindSuc = mOifaceManager.bindGameTask(OifaceManager.BIND_CPU_CLUSTER_ALL, id);
                    DebugUtil.i(TAG, "OifaceBindUtils bindTaskWithOiface: bindGameTask unbind silver " + unbindSuc);
                } else {
                    DebugUtil.e(TAG, "OifaceBindUtils bindTaskWithOiface: input type illegal: " + type);
                }
            } else {
                DebugUtil.e(TAG, "OifaceBindUtils bindTaskWithOiface: no OifaceManager ");
            }
        } else {
            if ((mRemote == null) && (connectOifaceService() == null)) {
                mRemote = null;
                return;
            }
            android.os.Parcel data = android.os.Parcel.obtain();
            android.os.Parcel reply = android.os.Parcel.obtain();
            try {
                data.writeInterfaceToken(OIFACE_DESCRIPTOR);
                data.writeInt(type);
                data.writeInt(id);
                boolean bindSuc = mRemote.transact(TRANSACTION_BIND_TASK, data, reply, android.os.IBinder.FLAG_ONEWAY);
                DebugUtil.i(TAG, "OifaceBindUtils bindTaskWithOiface: old: " + bindSuc + ", input type: " + type + ", inputTid: " + id);
            } catch (Exception e) {
                DebugUtil.e(TAG, "OifaceBindUtils bindTaskWithOiface: old error ", e);
                mRemote = null;
                e.printStackTrace();
            } finally {
                data.recycle();
                reply.recycle();
            }
        }
    }

    private boolean isAfterOS11() {
        boolean result = false;
        try {
            result = (COUIVersionUtil.getOSVersionCode() >= COUIVersionUtil.COUI_8_0);
        } catch (Exception e) {
            DebugUtil.e(TAG, "isOsVervionAfter11", e);
        }
        DebugUtil.i(TAG, "isOsVervionAfter11: " + result);
        return result;
    }

}