/**********************************************************
 * Copyright 2010-2017 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description : RecorderDatabaseHelper
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-3-12, huang<PERSON><PERSON>, create
 ***********************************************************/


package com.soundrecorder.common.db;

import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLOUM_NAME_SYNC_DOWNLOAD_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_DATA;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_FILE_PATH;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_BUCKET_DISPLAY_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_BUCKET_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CALLER_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CALL_AVATAR_COLOR;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CALL_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_CHECK_PAYLOAD;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_SYS_VERSION;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_CREATED;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_MODIFIED;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE_TIME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRECT_TIME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DURATION;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_ERROR_CODE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_FAIL_COUNT;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_RECYCLE_PATH;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_UUID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_DIRECT_ON;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_MARKLIST_SHOWING;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_RECYCLE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_LAST_FAIL_TIME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_LEVEL;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_LOCAL_EDIT_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MARK_DATA;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MD5;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MIGRATE_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MIMETYPE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_ORIGINAL_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_PARSE_CALL;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_PRIVATE_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_RECORD_TYPE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_RELATIVE_PATH;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SCAN_OSHARE_TEXT;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SIZE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_DATE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_TYPE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID;
import static com.soundrecorder.common.constant.DatabaseConstant.StatusColumn.COLUMN_NAME_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.StatusColumn.COLUMN_NAME_TYPE;
import static com.soundrecorder.common.constant.DatabaseConstant.TABLE_NAME_CONVERT;
import static com.soundrecorder.common.constant.DatabaseConstant.TABLE_NAME_CONVERT_TEMP;
import static com.soundrecorder.common.constant.DatabaseConstant.TABLE_NAME_GROUP_INFO;
import static com.soundrecorder.common.constant.DatabaseConstant.TABLE_NAME_RECORDER;
import static com.soundrecorder.common.constant.DatabaseConstant.TABLE_NAME_RECORDER_TMP;

import com.soundrecorder.common.constant.DatabaseConstant;

import static com.soundrecorder.common.constant.DatabaseConstant.TABLE_NAME_SEARCH_HISTORY;
import static com.soundrecorder.common.constant.DatabaseConstant.TABLE_NAME_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.TABLE_NAME_UPLOAD;
import static com.soundrecorder.common.db.NoteDbUtils.DATABASE_VERSION_NOTE_UPDATE_PRIMARY_KEY;

import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteException;
import android.database.sqlite.SQLiteOpenHelper;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.soundrecorder.base.utils.PrefUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.soundrecorder.base.utils.DebugUtil;

public class RecorderDatabaseHelper extends SQLiteOpenHelper {

    private static final String TAG = "RecorderDatabaseHelper";
    private static final String DATABASE_NAME = "recorder.db";

    private static final int DATABASE_VERSION_BASE = 1;
    //2: update table records add column relative_path
    private static final int DATABASE_VERSION_ADD_RELATIVE_PATH = 2;
    //3：update table records add column amp_file_path
    private static final int DATABASE_VERSION_ADD_AMP_PATH = 3;
    //4: add table convert(_id、record_id、media_path、convert_textfile_path、chunk_name、complete_status、only_id、version、taskId、upload_request_id、upload_key、part_count、upload_status、convert_status、upload_all_url)
    private static final int DATABASE_VERSION_ADD_CONVERT_TABLE = 4;
    //5；update table records add column private_status、migrate_status
    private static final int DATABASE_VERSION_ADD_PRIVATE_SYNC = 5;
    /**
     * 6: update table convert add column only_id、version、taskId、upload_request_id、upload_key、part_count、upload_status、convert_status、upload_all_url
     * 6: add table upload(_id、only_id、upload_url、file_rang_start、file_rang_end、etag、sequence_num)
     */
    private static final int DATABASE_VERSION_NEW_CONVERT_TABLE = 6;
    /**
     * 7: update table convert add column history_role_name、server_plan_code、can_show_speaker_role、speaker_role_isshowing、speaker_role_original_number、speaker_role_has_firstshow
     * 7: update table records add column is_marklist_showing
     */
    private static final int DATABASE_VERSION_NEW_CONVERT_SPEAKER_TABLE = 7;

    /**
     * 8: 创建了图片标记的表 picture_mark
     */
    private static final int DATABASE_VERSION_PICTURE_MARK = PictureMarkDbUtils.DATABASE_VERSION_PICTURE_MARK;
    /**
     * 接入cloudKit，增加云端返回字段：sys_version、file_checkPayload
     * 开发过程中，开始定义为8，需求并行，关键词升级为9，后面测试过程中字段类型发生变化，更改为10，
     */
    private static final int DATABASE_VERSION_CLOUD_KIT = 10;

    /**
     * 定向录音增加 is_directOn，direct_time
     */
    private static final int DATABASE_VERSION_DIRECT_RECORD = 13;

    /**
     * 1、录音回收站，新增回收站相关表，修改好以后补充到此处描述
     * <p>
     * 2、所有数据库版本号升级都整理到此处， 并且需要有对应描述
     */
    private static final int DATABASE_VERSION_RECYCLE_BIN = 15;

    /**
     * 数据库版本号：18， 用于录音分组
     */
    private static final int DATABASE_VERSION_RECORDER_GROUP = 18;

    /**
     * 数据库：20，用于AI 录音需求
     */
    private static final int DATABASE_VERSION_AI = 20;

    /**
     * 录音摘要，创建了摘要表 note, 11
     */
    private static final int DATABASE_VERSION_NOTE = NoteDbUtils.DATABASE_VERSION_NOTE;

    /**
     * 创建搜索历史表,19
     */
    private static final int DATABASE_VERSION_SEARCH_HISTORY = 19;

    /**
     * 临时升级，数据库未新增删除字段；
     * 为了解决遗留数据库升级，record表无direct相关字段
     */
    private static final int DATABASE_VERSION_TEMP = 17;

    /**
     * 9 ：创建了关键词的表 key_word
     * 【特别注意】：修改数据库版本号，必须要重新单独定义一个变量，然后赋值给此变量！！ 否则极易出现升级问题
     * 涉及修改数据库必须全体走读！！！
     */
    private static final int DATABASE_VERSION = DATABASE_VERSION_AI;

    private Context mContext;

    public RecorderDatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
        mContext = context;
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        DebugUtil.e(TAG, "onCreate");
        try {
            createRecordsTable(db);
            createStatusTable(db);
            createConvertVadTable(db);
            createUploadTable(db);
            PictureMarkDbUtils.createPictureMArkTable(db);
            KeyWordDbUtils.createKeyWordTable(db);
            NoteDbUtils.createNoteTable(db);
            CollectionInfoDbUtils.createCollectionInfoTable(db);
            PrefUtil.putBoolean(mContext, PrefUtil.KEY_DATA_CLEARED, true);
            createGroupInfoTable(db);
            GroupInfoDbUtil.initDefaultGroupInfoData(mContext, db);
            SearchHistoryDBUtils.createSearchHistoryTable(db);
        } catch (Exception e) {
            DebugUtil.e(TAG, "onCreate db error", e);
        }
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        DebugUtil.e(TAG, "onUpgrade from " + oldVersion + " to " + newVersion);
        if (oldVersion > newVersion) {
            onDowngrade(db, oldVersion, newVersion);
            return;
        }
        upgradeRecordTable(db, oldVersion, newVersion);
        upgradeConvertTable(db, oldVersion, newVersion);
        upgradeStatusTable(db, oldVersion, newVersion);
        upgradeUploadTable(db, oldVersion, newVersion);
        PictureMarkDbUtils.upgradePictureMarkTable(db, oldVersion, newVersion);
        KeyWordDbUtils.upgradeKeyWordTable(db, oldVersion, newVersion);
        NoteDbUtils.upgradeNoteTable(db, oldVersion, newVersion);
        CollectionInfoDbUtils.upgradeCollectionInfoTable(db, oldVersion, newVersion);
        upgradeGroupInfoTable(db, oldVersion, newVersion);
        upgradeSearchHistoryTable(db, oldVersion, newVersion);
    }

    @Override
    public void onDowngrade(SQLiteDatabase sqLiteDatabase, int i, int i1) {
        DebugUtil.e(TAG, "onDowngrade, downgrade from " + i
                + " to " + i1 + "");
        try {
            downgradeRecorderTable(sqLiteDatabase, i, i1);
            downgradeStatusTable(sqLiteDatabase, i, i1);
            downgradeConvertTable(sqLiteDatabase, i, i1);
            PictureMarkDbUtils.downgradePictureMarkTable(sqLiteDatabase, i1);
            downgradeUploadTable(sqLiteDatabase, i, i1);
            KeyWordDbUtils.downgradeKeyWordTable(sqLiteDatabase, i, i1);
            NoteDbUtils.downgradeNoteTable(sqLiteDatabase, i, i1);

            CollectionInfoDbUtils.downgradeCollectionInfoTable(sqLiteDatabase, i, i1);
            downgradeGroupInfoTable(sqLiteDatabase, i, i1);
            downgradeSearchHistoryTabke(sqLiteDatabase, i, i1);
        } catch (Exception e) {
            DebugUtil.e(TAG, "downgrade Failed: can't downgrade from " + i
                    + " to " + i1 + ", downgrade false, wipe data");
            try {
                sqLiteDatabase.execSQL("DROP TABLE IF EXISTS " + TABLE_NAME_RECORDER);
                sqLiteDatabase.execSQL("DROP TABLE IF EXISTS " + TABLE_NAME_STATUS);
                sqLiteDatabase.execSQL("DROP TABLE IF EXISTS " + TABLE_NAME_CONVERT);
                sqLiteDatabase.execSQL("DROP TABLE IF EXISTS " + TABLE_NAME_UPLOAD);
                KeyWordDbUtils.dropKeyWordTable(sqLiteDatabase);
                NoteDbUtils.dropNoteTable(sqLiteDatabase);

                CollectionInfoDbUtils.dropCollectionInfoTable(sqLiteDatabase);
                onCreate(sqLiteDatabase);
            } catch (Exception otherE) {
                DebugUtil.e(TAG, "onDowngrade db error", otherE);
            }
        }
    }

    /**
     * ------downgrade table  start------
     */
    private void downgradeConvertTable(SQLiteDatabase sqLiteDatabase, int fromVersion, int toVersion) {
        // under db-version 4, table-convert not exist
        if (toVersion < DATABASE_VERSION_ADD_CONVERT_TABLE) {
            deleteTableIfExist(sqLiteDatabase, TABLE_NAME_CONVERT);
        } else {
            //firs rename original table;
            DebugUtil.e(TAG, "downgradeConvertTable: fromVersion: " + fromVersion + ", toVersion: " + toVersion + ", start");
            String altTable = "ALTER TABLE " + TABLE_NAME_CONVERT + " RENAME TO " + TABLE_NAME_CONVERT_TEMP;
            sqLiteDatabase.execSQL(altTable);
            //second create new table
            createConvertVadTable(sqLiteDatabase);
            //third copy tmp to new table
            String selectionColumns = getConvertTableSelectionClums(toVersion);
            if (!TextUtils.isEmpty(selectionColumns)) {
                String cpyTable = "INSERT INTO " + TABLE_NAME_CONVERT + " SELECT " + selectionColumns + " FROM " + TABLE_NAME_CONVERT_TEMP;
                sqLiteDatabase.execSQL(cpyTable);
            }
            //last drop tmp
            deleteTableIfExist(sqLiteDatabase, TABLE_NAME_CONVERT_TEMP);
        }
        DebugUtil.e(TAG, "downgradeConvertTable: fromVersion: " + fromVersion + ", toVersion: " + toVersion + ", end");
    }

    private void downgradeStatusTable(SQLiteDatabase sqLiteDatabase, int fromVersion, int toVersion) {
        //do nothing;
    }

    private void downgradeUploadTable(SQLiteDatabase sqLiteDatabase, int fromVersion, int toVersion) {
        // under db-version 6, no table upload
        if (toVersion < DATABASE_VERSION_NEW_CONVERT_TABLE) {
            deleteTableIfExist(sqLiteDatabase, TABLE_NAME_UPLOAD);
        }
    }

    private void downgradeRecorderTable(SQLiteDatabase sqLiteDatabase, int fromVersion, int toVersion) {
        //firs rename original table;
        DebugUtil.e(TAG, "downgradeRecorderTable: fromVersion: " + fromVersion + ", toVersion: " + toVersion + ", start");
        String altTable = "ALTER TABLE " + TABLE_NAME_RECORDER + " RENAME TO " + TABLE_NAME_RECORDER_TMP;
        sqLiteDatabase.execSQL(altTable);
        //second create new table, 降级到老版本，直接调用建表的逻辑即可，因为老版本上的字段和数据库版本号已经确定
        createRecordsTable(sqLiteDatabase);
        //third copy tmp to new table
        String selectionColumns = getRecorderSelectionClumns(toVersion);
        String cpyTable = "INSERT INTO " + TABLE_NAME_RECORDER + " SELECT " + selectionColumns + " FROM " + TABLE_NAME_RECORDER_TMP;
        sqLiteDatabase.execSQL(cpyTable);
        //last drop tmp
        deleteTableIfExist(sqLiteDatabase, TABLE_NAME_RECORDER_TMP);
        DebugUtil.e(TAG, "downgradeRecorderTable: fromVersion: " + fromVersion + ", toVersion: " + toVersion + ", end");
    }

    private void downgradeGroupInfoTable(SQLiteDatabase sqLiteDatabase, int fromVersion, int toVersion) {
        if (toVersion < DATABASE_VERSION_RECORDER_GROUP) {
            deleteTableIfExist(sqLiteDatabase, TABLE_NAME_GROUP_INFO);
        }
    }

    private void downgradeSearchHistoryTabke(SQLiteDatabase db, int fromVersion, int toVersion) {
        if (toVersion < DATABASE_VERSION_SEARCH_HISTORY) {
            deleteTableIfExist(db, TABLE_NAME_SEARCH_HISTORY);
        }
    }

    private String getRecorderSelectionClumns(int currentVersion) {
        // db-version 1
        String[] baseColumns = new String[]{COLUMN_NAME_ID, COLUMN_NAME_UUID, COLUMN_NAME_DATA, COLUMN_NAME_SIZE, COLUMN_NAME_DISPLAY_NAME, COLUMN_NAME_MIMETYPE,
                COLUMN_NAME_DATE_CREATED, COLUMN_NAME_DATE_MODIFIED, COLUMN_NAME_RECORD_TYPE, COLUMN_NAME_MARK_DATA,
                COLUMN_NAME_AMP_DATA, COLUMN_NAME_DURATION, COLUMN_NAME_BUCKET_ID, COLUMN_NAME_BUCKET_DISPLAY_NAME,
                COLUMN_NAME_DIRTY, COLUMN_NAME_DELETE, COLUMN_NAME_MD5, COLUMN_NAME_FILE_ID, COLUMN_NAME_GLOBAL_ID,
                COLUMN_NAME_SYNC_TYPE, COLUMN_NAME_SYNC_UPLOAD_STATUS, COLOUM_NAME_SYNC_DOWNLOAD_STATUS, COLUMN_NAME_ERROR_CODE,
                COLUMN_NAME_LEVEL, COLUMN_NAME_LOCAL_EDIT_STATUS, COLUMN_NAME_SYNC_DATE, COLUMN_NAME_FAIL_COUNT, COLUMN_NAME_LAST_FAIL_TIME};
        List<String> columnsList = new ArrayList<>(Arrays.asList(baseColumns));
        //db version 2 add column relative_path
        if (currentVersion >= DATABASE_VERSION_ADD_RELATIVE_PATH) {
            columnsList.add(COLUMN_NAME_RELATIVE_PATH);
        }
        //db version 3 add column amp_file_path
        if (currentVersion >= DATABASE_VERSION_ADD_AMP_PATH) {
            columnsList.add(COLUMN_NAME_AMP_FILE_PATH);
        }

        //db version 5, add column private_status、migrate_status
        if (currentVersion >= DATABASE_VERSION_ADD_PRIVATE_SYNC) {
            columnsList.add(COLUMN_NAME_PRIVATE_STATUS);
            columnsList.add(COLUMN_NAME_MIGRATE_STATUS);
        }
        /*db version 7, add column is_marklist_showing*/
        if (currentVersion >= DATABASE_VERSION_NEW_CONVERT_SPEAKER_TABLE) {
            columnsList.add(COLUMN_NAME_IS_MARKLIST_SHOWING);
        }
        /*db version 10, add column sys_version、file_checkPayload*/
        if (currentVersion >= DATABASE_VERSION_CLOUD_KIT) {
            columnsList.add(COLUMN_NAME_CLOUD_SYS_VERSION);
            columnsList.add(COLUMN_NAME_CLOUD_CHECK_PAYLOAD);
        }
        /*db version 13, add column is_directOn、direct_time*/
        if (currentVersion >= DATABASE_VERSION_DIRECT_RECORD) {
            columnsList.add(COLUMN_NAME_IS_DIRECT_ON);
            columnsList.add(COLUMN_NAME_DIRECT_TIME);
        }
        if (currentVersion >= DATABASE_VERSION_RECYCLE_BIN) {
            columnsList.add(COLUMN_NAME_IS_RECYCLE);
            columnsList.add(COLUMN_NAME_DELETE_TIME);
            columnsList.add(COLUMN_NAME_FILE_RECYCLE_PATH);
        }
        if (currentVersion >= DATABASE_VERSION_RECORDER_GROUP) {
            columnsList.add(COLUMN_NAME_GROUP_ID);
            columnsList.add(COLUMN_NAME_GROUP_UUID);
            columnsList.add(COLUMN_NAME_CALLER_NAME);
            columnsList.add(COLUMN_NAME_ORIGINAL_NAME);
            columnsList.add(COLUMN_NAME_CALL_AVATAR_COLOR);
        }

        if (currentVersion >= DATABASE_VERSION_AI) {
            columnsList.add(COLUMN_NAME_SCAN_OSHARE_TEXT);
            columnsList.add(COLUMN_NAME_CALL_NAME);
            columnsList.add(COLUMN_NAME_PARSE_CALL);
        }
        String columnStr = TextUtils.join(",", columnsList);
        DebugUtil.i(TAG, "getRecorderSelectionClums: inputVersion: " + currentVersion + ", result: " + columnStr);
        return columnStr;
    }

    private String getConvertTableSelectionClums(int version) {
        if (version < DATABASE_VERSION_ADD_CONVERT_TABLE) {
            return null;
        }

        // db-version 4 -create table
        String[] baseColumns = new String[]{DatabaseConstant.ConvertColumn._ID, DatabaseConstant.ConvertColumn.RECORD_ID, DatabaseConstant.ConvertColumn.MEDIA_PATH,
                DatabaseConstant.ConvertColumn.CONVERT_TEXTFILE_PATH, DatabaseConstant.ConvertColumn.CHUNK_NAME, DatabaseConstant.ConvertColumn.COMPLETE_STATUS};
        List<String> columnsList = new ArrayList<>(Arrays.asList(baseColumns));

        // db version  6 update table add column
        if (version >= DATABASE_VERSION_NEW_CONVERT_TABLE) {
            columnsList.add(DatabaseConstant.ConvertColumn.ONLY_ID);
            columnsList.add(DatabaseConstant.ConvertColumn.VERSION);
            columnsList.add(DatabaseConstant.ConvertColumn.TASKID);
            columnsList.add(DatabaseConstant.ConvertColumn.UPLOAD_REQUEST_ID);
            columnsList.add(DatabaseConstant.ConvertColumn.UPLOAD_KEY);
            columnsList.add(DatabaseConstant.ConvertColumn.PART_COUNT);
            columnsList.add(DatabaseConstant.ConvertColumn.UPLOAD_STATUS);
            columnsList.add(DatabaseConstant.ConvertColumn.CONVERT_STATUS);
            columnsList.add(DatabaseConstant.ConvertColumn.UPLOAD_ALL_URL);
        }
        // db version  7 update
        if (version >= DATABASE_VERSION_NEW_CONVERT_SPEAKER_TABLE) {
            columnsList.add(DatabaseConstant.ConvertColumn.HISTORY_ROLENAME);
            columnsList.add(DatabaseConstant.ConvertColumn.SERVER_PLAN_CODE);
            columnsList.add(DatabaseConstant.ConvertColumn.CAN_SHOW_SPEAKER_ROLE);
            columnsList.add(DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ISSHOWING);
            columnsList.add(DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ORIGINAL_NUMBER);
            columnsList.add(DatabaseConstant.ConvertColumn.SPEAKER_ROLE_HAS_FIRSTSHOW);
        }
        if (version >= DATABASE_VERSION_DIRECT_RECORD) {
            columnsList.add(DatabaseConstant.ConvertColumn.IS_DIRECT_ON);
            columnsList.add(DatabaseConstant.ConvertColumn.DIRECT_TIME);
        }
        String columnStr = TextUtils.join(",", columnsList);
        DebugUtil.i(TAG, "getConvertTableSelectionClums: inputVersion: " + version + ", result: " + columnStr);
        return columnStr;
    }
    /**------downgrade table  end------*/

    /**
     * ------upgrade table  start------
     */
    private void upgradeRecordTable(SQLiteDatabase db, int fromVersion, int toVersion) {
        DebugUtil.i(TAG, "upgradeRecordTable, from version " + fromVersion + " to " + toVersion);
        try {
            if ((fromVersion < DATABASE_VERSION_RECORDER_GROUP)) {
                // upgrade to 18, add column group_id, group_uuid, caller_name, original_name
                String addGroupIdSql = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_GROUP_ID + " INTEGER";
                execSQL(db, addGroupIdSql);
                String addGroupUuidSql = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_GROUP_UUID + " TEXT";
                execSQL(db, addGroupUuidSql);
                String addCallerNameSql = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_CALLER_NAME + " TEXT";
                execSQL(db, addCallerNameSql);
                String addOriginalNameSql = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_ORIGINAL_NAME + " TEXT";
                execSQL(db, addOriginalNameSql);
                String addCallAvatarColorSql = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_CALL_AVATAR_COLOR + " TEXT";
                execSQL(db, addCallAvatarColorSql);
            }

            if ((fromVersion < DATABASE_VERSION_ADD_RELATIVE_PATH)) {
                // upgrade to 2, add column relative_path
                String addRelativePathString = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_RELATIVE_PATH + " TEXT";
                db.execSQL(addRelativePathString);
            }
            if (fromVersion < DATABASE_VERSION_ADD_AMP_PATH) {
                // upgrade to 3, add column amp_file_path
                String addApmFilePathString = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_AMP_FILE_PATH + " TEXT";
                db.execSQL(addApmFilePathString);
            }

            if (fromVersion < DATABASE_VERSION_ADD_PRIVATE_SYNC) {
                /*upgrade to 5, add column private_status、migrate_status*/
                String addPrivateStatusString = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_PRIVATE_STATUS + " INTEGER";
                String addMigrateStatusString = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_MIGRATE_STATUS + " INTEGER";
                db.execSQL(addPrivateStatusString);
                db.execSQL(addMigrateStatusString);
            }

            if (fromVersion < DATABASE_VERSION_NEW_CONVERT_SPEAKER_TABLE) {
                /*upgrade to 7, add column is_marklist_showing*/
                String addIsMarkListShowing = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_IS_MARKLIST_SHOWING + " INTEGER DEFAULT 0 ";
                db.execSQL(addIsMarkListShowing);
            }
            if (fromVersion < DATABASE_VERSION_CLOUD_KIT) {
                /*upgrade to 10, add column sysversion、file_checkPayload*/
                String addSysVersion = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_CLOUD_SYS_VERSION + " BIGINT ";
                String addFilePayLoad = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_CLOUD_CHECK_PAYLOAD + " TEXT ";
                execSQL(db, addSysVersion);
                execSQL(db, addFilePayLoad);
            }

            /*
             * from数据库版本 13<=?<17
             * 兼容处理问题版本is_direct 相关字段不存在，导致数据库异常，录音无法删除
             * */
            boolean isDirectQuestionVersion = (fromVersion >= DATABASE_VERSION_DIRECT_RECORD) && (fromVersion < DATABASE_VERSION_TEMP)
                    && !isColumnExist(db, TABLE_NAME_RECORDER, COLUMN_NAME_IS_DIRECT_ON);
            if (fromVersion < DATABASE_VERSION_DIRECT_RECORD || isDirectQuestionVersion) {
                // 正常升级 or异常sql问题修复
                String directOn = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_IS_DIRECT_ON + " INTEGER DEFAULT 0";
                String directTime = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_DIRECT_TIME + " TEXT ";
                execSQL(db, directOn);
                execSQL(db, directTime);
            }

            //回收站需求,扩展数据库字段
            boolean needUpgradeRecycleColumn = (fromVersion >= DATABASE_VERSION_RECYCLE_BIN) && (fromVersion < DATABASE_VERSION_TEMP)
                    && !isColumnExist(db, TABLE_NAME_RECORDER, COLUMN_NAME_IS_RECYCLE);
            if (fromVersion < DATABASE_VERSION_RECYCLE_BIN || needUpgradeRecycleColumn) {
                String isRecycle = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_IS_RECYCLE + " INTEGER DEFAULT 0 ";
                String deleteTime = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_DELETE_TIME + "  BIGINT DEFAULT 0 ";
                String recycleFilePath = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_FILE_RECYCLE_PATH + " TEXT ";
                execSQL(db, isRecycle);
                execSQL(db, deleteTime);
                execSQL(db, recycleFilePath);
            }

            if (fromVersion < DATABASE_VERSION_AI) {
                /*upgrade to 18, add column scan_oshare_text callsource callname parse_call*/
                String scanOShare = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_SCAN_OSHARE_TEXT + " INTEGER DEFAULT 0 ";
                String callName = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_CALL_NAME + " TEXT ";
                String parseCall = "ALTER TABLE " + TABLE_NAME_RECORDER + " ADD " + COLUMN_NAME_PARSE_CALL + " INTEGER DEFAULT 0 ";
                db.execSQL(scanOShare);
                db.execSQL(callName);
                db.execSQL(parseCall);
            }

        } catch (Exception e) {
            DebugUtil.e(TAG, "upgradeRecordTable db error", e);
        }

    }

    private void upgradeConvertTable(SQLiteDatabase db, int fromVersion, int toVersion) {
        DebugUtil.i(TAG, "upgradeConvertTable, from version " + fromVersion + " to " + toVersion);
        try {
            if (fromVersion < DATABASE_VERSION_ADD_CONVERT_TABLE) {
                //避免隔代降级未delete，再升级， create table 时存在-column not match
                deleteTableIfExist(db, TABLE_NAME_CONVERT);
                // upgrade to 4, create table convert
                createConvertVadTableBase(db);
            }

            if (fromVersion < DATABASE_VERSION_NEW_CONVERT_TABLE) {
                // upgrade to 6, add column only_id、version、taskId、upload_request_id、upload_key、part_count、upload_status、convert_status、upload_all_url
                DebugUtil.i(TAG, "upgrade convert Table");
                String addOnlyIdString = "ALTER TABLE " + TABLE_NAME_CONVERT + " ADD " + DatabaseConstant.ConvertColumn.ONLY_ID + " TEXT ";
                String addVersionString = "ALTER TABLE " + TABLE_NAME_CONVERT + " ADD " + DatabaseConstant.ConvertColumn.VERSION + " INTEGER DEFAULT 0 ";
                String addTaskIdString = "ALTER TABLE " + TABLE_NAME_CONVERT + " ADD " + DatabaseConstant.ConvertColumn.TASKID + " TEXT ";
                String addUploadRequestIdString = "ALTER TABLE " + TABLE_NAME_CONVERT + " ADD " + DatabaseConstant.ConvertColumn.UPLOAD_REQUEST_ID + " TEXT ";
                String addPartIndexString = "ALTER TABLE " + TABLE_NAME_CONVERT + " ADD " + DatabaseConstant.ConvertColumn.UPLOAD_KEY + " TEXT ";
                String addPartCountString = "ALTER TABLE " + TABLE_NAME_CONVERT + " ADD " + DatabaseConstant.ConvertColumn.PART_COUNT + " INTEGER DEFAULT 0 ";
                String addUploadStatus = "ALTER TABLE " + TABLE_NAME_CONVERT + " ADD " + DatabaseConstant.ConvertColumn.UPLOAD_STATUS + " INTEGER DEFAULT 0 ";
                String addConvertStatus = "ALTER TABLE " + TABLE_NAME_CONVERT + " ADD " + DatabaseConstant.ConvertColumn.CONVERT_STATUS + " INTEGER DEFAULT 0 ";
                String addUploadAllUrl = "ALTER TABLE " + TABLE_NAME_CONVERT + " ADD " + DatabaseConstant.ConvertColumn.UPLOAD_ALL_URL + " TEXT";
                db.execSQL(addOnlyIdString);
                db.execSQL(addVersionString);
                db.execSQL(addTaskIdString);
                db.execSQL(addUploadRequestIdString);
                db.execSQL(addPartIndexString);
                db.execSQL(addPartCountString);
                db.execSQL(addUploadStatus);
                db.execSQL(addConvertStatus);
                db.execSQL(addUploadAllUrl);
            }

            if (fromVersion < DATABASE_VERSION_NEW_CONVERT_SPEAKER_TABLE) {
                // upgrade to 7,add column history_role_name、server_plan_code、can_show_speaker_role、speaker_role_isshowing、speaker_role_original_number、speaker_role_has_firstshow
                DebugUtil.i(TAG, "upgrade convert Table: " + DATABASE_VERSION_NEW_CONVERT_SPEAKER_TABLE);
                String addHistoryRoleName = "ALTER TABLE " + TABLE_NAME_CONVERT + " ADD " + DatabaseConstant.ConvertColumn.HISTORY_ROLENAME + " TEXT";
                String addServPlanCode = "ALTER TABLE " + TABLE_NAME_CONVERT + " ADD " + DatabaseConstant.ConvertColumn.SERVER_PLAN_CODE + " INTEGER DEFAULT 0 ";
                String addCanShowSpeakerRole = "ALTER TABLE " + TABLE_NAME_CONVERT + " ADD " + DatabaseConstant.ConvertColumn.CAN_SHOW_SPEAKER_ROLE + " INTEGER DEFAULT 0 ";
                String addSpeakerRoleIsShowing = "ALTER TABLE " + TABLE_NAME_CONVERT + " ADD " + DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ISSHOWING + " INTEGER DEFAULT 0 ";
                String addSpeakerRoleOriginalNumber = "ALTER TABLE " + TABLE_NAME_CONVERT + " ADD " + DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ORIGINAL_NUMBER + " INTEGER DEFAULT 0 ";
                String addSpeakerRoleHasFirstShow = "ALTER TABLE " + TABLE_NAME_CONVERT + " ADD " + DatabaseConstant.ConvertColumn.SPEAKER_ROLE_HAS_FIRSTSHOW + " INTEGER DEFAULT 0 ";
                db.execSQL(addHistoryRoleName);
                db.execSQL(addServPlanCode);
                db.execSQL(addCanShowSpeakerRole);
                db.execSQL(addSpeakerRoleIsShowing);
                db.execSQL(addSpeakerRoleOriginalNumber);
                db.execSQL(addSpeakerRoleHasFirstShow);
            }

            /*
             * from数据库版本 13<=?<17
             * 兼容处理问题版本is_direct 相关字段不存在，导致数据库异常，录音无法删除
             * */
            boolean isDirectQuestionVersion = (fromVersion >= DATABASE_VERSION_DIRECT_RECORD) && (fromVersion < DATABASE_VERSION_TEMP)
                    && !isColumnExist(db, TABLE_NAME_CONVERT, DatabaseConstant.ConvertColumn.IS_DIRECT_ON);

            if (fromVersion < DATABASE_VERSION_DIRECT_RECORD || isDirectQuestionVersion) {
                // 正常升级 or异常sql问题修复
                String isDirectOn = "ALTER TABLE " + TABLE_NAME_CONVERT + " ADD "
                        + DatabaseConstant.ConvertColumn.IS_DIRECT_ON + " INTEGER DEFAULT 0 ";
                String directTime = "ALTER TABLE " + TABLE_NAME_CONVERT + " ADD " + DatabaseConstant.ConvertColumn.DIRECT_TIME + " TEXT";
                execSQL(db, isDirectOn);
                execSQL(db, directTime);
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "upgradeConvertTable db error", e);
        }
    }

    private void upgradeStatusTable(SQLiteDatabase db, int fromVersion, int toVersion) {
        DebugUtil.i(TAG, "upgradeStatusTable, Upgrading database from version " + fromVersion + " to " + toVersion);
    }

    private void upgradeUploadTable(SQLiteDatabase db, int fromVersion, int toVersion) {
        if (fromVersion < DATABASE_VERSION_NEW_CONVERT_TABLE) {
            //避免隔代降级未delete，再升级， create table 时存在-column not match
            deleteTableIfExist(db, TABLE_NAME_UPLOAD);
            // upgrade to 6, add table upload
            createUploadTable(db);
        }
    }

    private void upgradeGroupInfoTable(SQLiteDatabase db, int fromVersion, int toVersion) {
        if (fromVersion < DATABASE_VERSION_RECORDER_GROUP) {
            deleteTableIfExist(db, TABLE_NAME_GROUP_INFO);
            createGroupInfoTable(db);
            GroupInfoDbUtil.initDefaultGroupInfoData(mContext, db);
        }
    }

    private void upgradeSearchHistoryTable(SQLiteDatabase db, int fromVersion, int toVersion) {
        if (fromVersion < DATABASE_VERSION_SEARCH_HISTORY) {
            deleteTableIfExist(db, TABLE_NAME_SEARCH_HISTORY);
            SearchHistoryDBUtils.createSearchHistoryTable(db);
        }
    }

    private void upgradeRecycleBinTable(SQLiteDatabase db, int fromVersion, int toVersion) {
        if (fromVersion < DATABASE_VERSION_RECYCLE_BIN) {
            //首次升级，创建录音回收站表
            RecycleBinDbUtils.createRecycleBinTable(db);
        }
    }


    /**------upgrade table  end------*/

    /**
     * ------create table  start------
     */

    private void createConvertVadTableBase(@NonNull SQLiteDatabase db) {
        DebugUtil.i(TAG, "createConvertVadTableBase");
        db.execSQL("CREATE TABLE IF NOT EXISTS " + TABLE_NAME_CONVERT + " ("
                + DatabaseConstant.ConvertColumn._ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
                + DatabaseConstant.ConvertColumn.RECORD_ID + " INTEGER ,"
                + DatabaseConstant.ConvertColumn.MEDIA_PATH + " TEXT ,"
                + DatabaseConstant.ConvertColumn.CONVERT_TEXTFILE_PATH + " TEXT ,"
                + DatabaseConstant.ConvertColumn.CHUNK_NAME + " TEXT,"
                + DatabaseConstant.ConvertColumn.COMPLETE_STATUS + " INTEGER DEFAULT 0"
                + ");");


    }

    private void createRecordsTable(@NonNull SQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS " + TABLE_NAME_RECORDER + " ("
                + DatabaseConstant.RecorderColumn.COLUMN_NAME_ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
                + COLUMN_NAME_UUID + " TEXT UNIQUE,"
                + COLUMN_NAME_DATA + " TEXT NOT NULL,"
                + COLUMN_NAME_SIZE + " BIGINT DEFAULT 0,"
                + COLUMN_NAME_DISPLAY_NAME + " TEXT,"
                + COLUMN_NAME_MIMETYPE + " TEXT,"
                + COLUMN_NAME_DATE_CREATED + " BIGINT,"
                + COLUMN_NAME_DATE_MODIFIED + " BIGINT,"
                + COLUMN_NAME_RECORD_TYPE + " INTEGER,"
                + COLUMN_NAME_MARK_DATA + " BLOB,"
                + COLUMN_NAME_AMP_DATA + " BLOB,"
                + COLUMN_NAME_DURATION + " BIGINT,"
                + COLUMN_NAME_BUCKET_ID + " BIGINT,"
                + COLUMN_NAME_BUCKET_DISPLAY_NAME + " TEXT,"
                + COLUMN_NAME_DIRTY + " INTEGER DEFAULT 0,"
                + COLUMN_NAME_DELETE + " INTEGER DEFAULT 0,"
                + COLUMN_NAME_MD5 + " TEXT,"
                + COLUMN_NAME_FILE_ID + " TEXT,"
                + COLUMN_NAME_GLOBAL_ID + " TEXT,"
                + COLUMN_NAME_SYNC_TYPE + " INTEGER,"
                + COLUMN_NAME_SYNC_UPLOAD_STATUS + " INTEGER,"
                + COLOUM_NAME_SYNC_DOWNLOAD_STATUS + " INTEGER,"
                + COLUMN_NAME_ERROR_CODE + " INTEGER DEFAULT 0,"
                + COLUMN_NAME_LEVEL + " INTEGER DEFAULT 0,"
                + COLUMN_NAME_LOCAL_EDIT_STATUS + " INTEGER DEFAULT 0,"
                + COLUMN_NAME_SYNC_DATE + " BIGINT DEFAULT 0,"
                + COLUMN_NAME_FAIL_COUNT + " INTEGER DEFAULT 0,"
                + COLUMN_NAME_LAST_FAIL_TIME + " BIGINT DEFAULT 0,"
                + COLUMN_NAME_RELATIVE_PATH + " TEXT,"
                + COLUMN_NAME_AMP_FILE_PATH + " TEXT,"
                + COLUMN_NAME_PRIVATE_STATUS + " INTEGER DEFAULT 0,"
                + COLUMN_NAME_MIGRATE_STATUS + " INTEGER DEFAULT 0,"
                + COLUMN_NAME_IS_MARKLIST_SHOWING + " INTEGER DEFAULT 0,"
                + COLUMN_NAME_CLOUD_SYS_VERSION + " BIGINT,"
                + COLUMN_NAME_CLOUD_CHECK_PAYLOAD + " TEXT,"
                + COLUMN_NAME_IS_DIRECT_ON + " INTEGER DEFAULT 0,"
                + COLUMN_NAME_DIRECT_TIME + " TEXT,"
                + COLUMN_NAME_IS_RECYCLE + " INTEGER DEFAULT 0,"
                + COLUMN_NAME_FILE_RECYCLE_PATH + " TEXT,"
                + COLUMN_NAME_GROUP_ID + " INTEGER,"
                + COLUMN_NAME_GROUP_UUID + " TEXT,"
                + COLUMN_NAME_CALLER_NAME + " TEXT,"
                + COLUMN_NAME_ORIGINAL_NAME + " TEXT,"
                + COLUMN_NAME_CALL_AVATAR_COLOR + " TEXT,"
                + COLUMN_NAME_DELETE_TIME +  "  BIGINT DEFAULT 0, "
                + COLUMN_NAME_SCAN_OSHARE_TEXT +  "  INTEGER DEFAULT 0, "
                + COLUMN_NAME_CALL_NAME +  "  TEXT, "
                + COLUMN_NAME_PARSE_CALL +  "  INTEGER DEFAULT 0 "
                + ");");
    }

    private void createStatusTable(@NonNull SQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS " + TABLE_NAME_STATUS + " ("
                + DatabaseConstant.StatusColumn.COLUMN_NAME_ID + " INTEGER PRIMARY KEY,"
                + COLUMN_NAME_TYPE + " TEXT UNIQUE NOT NULL,"
                + COLUMN_NAME_STATUS + " INTEGER"
                + ");");
    }

    private void createConvertVadTable(@NonNull SQLiteDatabase db) {
        DebugUtil.i(TAG, "createConvertVadTable");
        db.execSQL("CREATE TABLE IF NOT EXISTS " + TABLE_NAME_CONVERT + " ("
                + DatabaseConstant.ConvertColumn._ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
                + DatabaseConstant.ConvertColumn.RECORD_ID + " INTEGER ,"
                + DatabaseConstant.ConvertColumn.MEDIA_PATH + " TEXT ,"
                + DatabaseConstant.ConvertColumn.CONVERT_TEXTFILE_PATH + " TEXT ,"
                + DatabaseConstant.ConvertColumn.CHUNK_NAME + " TEXT,"
                + DatabaseConstant.ConvertColumn.COMPLETE_STATUS + " INTEGER DEFAULT 0, "
                + DatabaseConstant.ConvertColumn.ONLY_ID + " TEXT, "
                + DatabaseConstant.ConvertColumn.VERSION + " INTEGER DEFAULT 0, "
                + DatabaseConstant.ConvertColumn.TASKID + " TEXT, "
                + DatabaseConstant.ConvertColumn.UPLOAD_REQUEST_ID + " TEXT, "
                + DatabaseConstant.ConvertColumn.UPLOAD_KEY + " TEXT, "
                + DatabaseConstant.ConvertColumn.PART_COUNT + " INTEGER, "
                + DatabaseConstant.ConvertColumn.UPLOAD_STATUS + " INTEGER, "
                + DatabaseConstant.ConvertColumn.CONVERT_STATUS + " INTEGER, "
                + DatabaseConstant.ConvertColumn.UPLOAD_ALL_URL + " TEXT, "
                + DatabaseConstant.ConvertColumn.HISTORY_ROLENAME + " TEXT, "
                + DatabaseConstant.ConvertColumn.SERVER_PLAN_CODE + " INTEGER DEFAULT 0,"
                + DatabaseConstant.ConvertColumn.CAN_SHOW_SPEAKER_ROLE + " INTEGER DEFAULT 0,"
                + DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ISSHOWING + " INTEGER DEFAULT 0,"
                + DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ORIGINAL_NUMBER + " INTEGER DEFAULT 0,"
                + DatabaseConstant.ConvertColumn.SPEAKER_ROLE_HAS_FIRSTSHOW + " INTEGER DEFAULT 0,"
                + DatabaseConstant.ConvertColumn.IS_DIRECT_ON + " INTEGER DEFAULT 0,"
                + DatabaseConstant.ConvertColumn.DIRECT_TIME + " TEXT "
                + ");");
    }

    private void createGroupInfoTable(@NonNull SQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS " + TABLE_NAME_GROUP_INFO + " ("
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_UUID + " TEXT UNIQUE,"
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_NAME + " TEXT NOT NULL UNIQUE,"
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_COLOR + " INTEGER DEFAULT 0,"
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_COUNT + " INTEGER DEFAULT 0,"
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_SORT + " INTEGER,"
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_TYPE + " INTEGER,"
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_CLOUD_SYS_VERSION + " BIGINT,"
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_TYPE + " INTEGER,"
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS + " INTEGER,"
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_DOWNLOAD_STATUS + " INTEGER,"
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_ERROR_CODE + " INTEGER DEFAULT 0,"
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_LOCAL_EDIT_STATUS + " INTEGER DEFAULT 0,"
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_DATE + " BIGINT DEFAULT 0,"
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID + " TEXT,"
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_DIRTY + " INTEGER DEFAULT 0,"
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_DELETED + " INTEGER DEFAULT 0,"
                + DatabaseConstant.GroupInfoColumn.COLUMN_NAME_IS_PRIVATE + " INTEGER DEFAULT 0"
                + ");");
    }

    private void createUploadTable(@NonNull SQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS " + TABLE_NAME_UPLOAD + " ("
                + DatabaseConstant.UploadColumn._ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
                + DatabaseConstant.UploadColumn.ONLY_ID + " TEXT, "
                + DatabaseConstant.UploadColumn.UPLOAD_URL + " TEXT,"
                + DatabaseConstant.UploadColumn.UPLOAD_FILE_RANG_START + " BIGINT DEFAULT 0,"
                + DatabaseConstant.UploadColumn.UPLOAD_FILE_RANG_END + " BIGINT DEFAULT 0,"
                + DatabaseConstant.UploadColumn.UPLOAD_SEQ_NUM + " INTEGER DEFAULT 0,"
                + DatabaseConstant.UploadColumn.UPLOAD_ETAG + " TEXT"
                + ");");
    }

    /**
     * ------create table  end------
     */
    private void deleteTableIfExist(@NonNull SQLiteDatabase db, String tableName) {
        db.execSQL("DROP TABLE IF EXISTS " + tableName);
    }

    private void execSQL(@NonNull SQLiteDatabase db, @NonNull String sql) {
        try {
            db.execSQL(sql);
        } catch (SQLiteException e) {
            DebugUtil.e(TAG, "sql error " + e);
        }
    }

    private boolean isColumnExist(@NonNull SQLiteDatabase db, String tableName, String columnName) {
        Cursor cursor = null;
        try {
            cursor = db.rawQuery("SELECT " + columnName + " FROM " + tableName + " LIMIT 0", null);
            return true;
        } catch (Exception e) {
            DebugUtil.e(TAG, "isColumnExit error,table=" + tableName + ", name=" + columnName, e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return false;
    }
}
