/**********************************************************
 * Copyright 2010-2017 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description : RecordModelConst
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-3-12, huang<PERSON>wang, create
 ***********************************************************/

package com.soundrecorder.common.constant;

public class RecordConstant {

    public static final int GROUP_TYPE_CUSTOM = 0;
    public static final int RECORD_BACKUP_STATE_DEFAULT = 0;
    public static final int RECORD_BACKUP_STATE_ADDED = 1;
    public static final int RECORD_BACKUP_STATE_MODIFIED = 2;
    public static final int RECORD_BACKUP_STATE_DELETED = 3;
    public static final int RECORD_BACKUP_STATE_SYNCDELETED = 4;

    public static final int SYNC_TYPE_BACKUP = 0;
    public static final int SYNC_TYPE_RECOVERY = 1;

    public static final int RECORD_NOT_DIRTY = 0;
    public static final int RECORD_DIRTY_MEGA_ONLY = 1;
    public static final int RECORD_DIRTY_FILE_ONLY = 2;
    public static final int RECORD_DIRTY_FILE_AND_MEGA = 3;

    public static final int RECORD_NORMAL = 0;
    public static final int RECORD_DELETED = 1;

    /*定向录音是否开启*/
    public static final int DIRECT_RECORD_ON = 1;

    public static final int DIRECT_RECORD_OFF = 0;

    public static final int RECORD_PRIVATE_NORMAL = 0;
    public static final int RECORD_PRIVETE_ENCRYPT = 1;

    public static final int SYNC_STATUS_DEFAULT = 0;
    // 备份开始
    public static final int SYNC_STATUS_BACKUP_START = 1;
    // 备份上传文件
    public static final int SYNC_STATUS_BACKUPING_FILE = 2;
    // 备份-上传文件成功
    public static final int SYNC_STATUS_BACKUP_FILE_SUC = 3;
    // 备份-上传文件失败
    public static final int SYNC_STATUS_BACKUP_FILE_FAILED = 4;
    //备份一条本地有文件的元数据
    public static final int SYNC_STATUS_LOCALLY_EXISTS_METADATA = 5;
    // 备份流程完成成功，元数据上传成功
    public static final int SYNC_STATUS_BACKUP_MEGADATA_SUC = 6;
    public static final int SYNC_STATUS_BACKUP_MEGADATA_FAILED = 7;


    public static final int SYNC_STATUS_RECOVERY_START = 51;
    public static final int SYNC_STATUS_RECOVERYING_MEGADATA = 52;
    //元数据下载成功
    public static final int SYNC_STATUS_RECOVERY_MEGADATA_SUC = 53;
    // 元数据失败
    public static final int SYNC_STATUS_RECOVERY_MEGADATA_FAILED = 54;
    // 下载元数据对应音频文件
    public static final int SYNC_STATUS_RECOVERYING_FILE = 55;
    // 下载对应音频文件成功
    public static final int SYNC_STATUS_RECOVERY_FILE_SUC = 56;
    //下载对应音频文件失败
    public static final int SYNC_STATUS_RECOVERY_FILE_FAILED = 57;
    public static final int SYNC_STATUS_RECOVERY_MOVING_FILE = 58;
    // 下载音频文件没有所有文件管理权限
    public static final int SYNC_STATUS_RECOVERY_FILE_JUMP = 59;


    public static final int LOCAL_EDITING = 1;
    public static final int LOCAL_EDIT_COMPLET = 0;

    public static final String MIMETYPE_MP3 = "audio/mpeg";
    public static final String MIMETYPE_RAW = "audio/raw";
    public static final String MIMETYPE_WAV = "audio/x-wav";
    public static final String MIMETYPE_AMR = "audio/amr";
    public static final String MIMETYPE_AMR_WB = "audio/amr-wb";
    public static final String MIMETYPE_3GPP = "audio/3gpp";
    public static final String MIMETYPE_ACC = "audio/aac";
    public static final String MIMETYPE_ACC_ADTS = "audio/aac-adts";

    public static final String POSTFIX_MP3 = "mp3";
    public static final String POSTFIX_WAV = "wav";
    public static final String POSTFIX_AMR = "amr";

    public static final String POSTFIX_POINT_AMR = ".amr";

    /**
     * AAC文件底层读取数据一帧时长
     * 经验值
     * 单位 ms
     */
    public static final int AAC_FRAME_DURATION = 23;

    /**
     * 回收站文件最多存储30天
     */
    public static final long RECYCLE_STORAGE_TIME = 30 * 24 * 60 * 60 * 1000L;

    /*通话录音：不分组/按联系人分组*/
    public static final int CALL_RECORD_NOT_GROUPING = 0;
    public static final int CALL_RECORD_CONTACT_GROUPING = 1;
}
