/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecoverFileDialogUtil
 * Description:
 * Version: 1.0
 * Date: 2022/10/31
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/10/31 1.0 create
 */

package com.soundrecorder.common.fileoperator.recover

import android.app.Activity
import android.app.KeyguardManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.common.R
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.executor.ExecutorManager
import com.soundrecorder.common.db.GroupInfoManager
import com.soundrecorder.common.fileoperator.CheckOperatorWithPermission
import com.soundrecorder.common.utils.FileDealUtil
import com.soundrecorder.common.utils.sound.DeleteSoundEffectManager
import com.soundrecorder.modulerouter.RecordMediaCompareAction
import com.soundrecorder.modulerouter.SeedlingAction
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class RecoverFileDialogUtil(private val onFileRecoverListener: OnRecoverFileListener? = null) {
    companion object {
        private const val ACTION_LOCK_SCREEN = "android.app.action.CONFIRM_DEVICE_CREDENTIAL"
        const val LOCK_SCREEN_START_TYPE = "start_type"
        const val LOCK_SCREEN_CUSTOMIZE_HEAD = "customize_head"
        const val LOCK_SCREEN_CUSTOMIZE_HEAD_STR = "customize_head_str"
        const val LOCK_SCREEN_CUSTOMIZE_HEAD_STR_PSWD = "customize_head_str_password"
        const val REQUEST_LOCK_SCREEN_RESULT_SUCCESS = 3
    }

    private val mLogTag = "RecoverFileDialogUtil"
    private var mOperatePermission: CheckOperatorWithPermission? = null

    /**
     * @param activity
     * @param beRecoverRecordList
     * @param isRecoverAll
     */
    fun recover(activity: Activity, beRecoverRecordList: List<Record>, isRecoverAll: Boolean) {
        if (isRecoverAll && beRecoverRecordList.size > 1) {
            recoverAllRecord(activity, beRecoverRecordList)
        } else {
            recoverWithPermission(activity, beRecoverRecordList)
        }
    }

    fun recoverWithPermission(activity: Activity, beRecoverRecordList: List<Record>) {
        if (mOperatePermission == null) {
            mOperatePermission = CheckOperatorWithPermission(activity)
        }
        mOperatePermission?.dismissAllFileDialog()

        mOperatePermission?.recoverRecords(onFileRecoverListener?.provideRecoverRequestCode(), beRecoverRecordList) {
            CoroutineScope(Dispatchers.IO).launch {
                onFileRecoverListener?.onRecoverFileBefore()
                //这里先查一次所有的自定义分组，为后续判断是否自定义分组已被删除做准备
                GroupInfoManager.getInstance(BaseApplication.getAppContext()).queryAllCustomGroupInfoList()
                val recoverSuccessCount = recoverRecord(activity, beRecoverRecordList)
                onFileRecoverListener?.onRecoverFileResult(recoverSuccessCount == beRecoverRecordList.size)
                CloudStaticsUtil.addCloudLog("DeleteFileDialogUtil", "deleteWithPermission,  " +
                        "deleteSize=${beRecoverRecordList.size}, success count=$recoverSuccessCount")
            }
        }
    }

    /**
     * 从回收站把录音文件恢复到媒体库
     * @param
     */
    private fun recoverRecord(activity: Activity, beDeleteRecordList: List<Record>): Int {
        var index = 0
        var recoverSuccessCount = 0
        var bNeedContinueCompare = false
        if (RecordMediaCompareAction.isMediaComparing()) {
            RecordMediaCompareAction.doStopMediaCompare(true)
            bNeedContinueCompare = true
            DebugUtil.i(
                mLogTag,
                "mediaComparing, need to stop diff compare when operate batch: recoverRecords!"
            )
        }

        while (!Thread.currentThread().isInterrupted && (index < beDeleteRecordList.size)) {
            val targetRecord: Record = beDeleteRecordList[index]
            FileDealUtil.recoveryRecord(
                activity,
                targetRecord,
                onFileRecoverListener?.provideRecoverRequestCode()
            ).run {
                    if (this) {
                        recoverSuccessCount++
                    }
                }
            index++
            DebugUtil.i(mLogTag, "recover index:$index")
        }

        if (recoverSuccessCount > 0) {
            DeleteSoundEffectManager.getInstance().playDeleteSound() //播放删除、恢复音效

            if (bNeedContinueCompare) {
                ExecutorManager.recordDataSyncExecutor?.execute {
                    RecordMediaCompareAction.doMediaCompare(false)
                    DebugUtil.i(
                        mLogTag,
                        "mediaComparing, Continue to diff compare after operate batch: recoverRecords!"
                    )
                }
            }
            // 从回收站恢复之后，刷新一下分组表的相关记录
            GroupInfoManager.getInstance(BaseApplication.getAppContext()).verifyGroupCount()
        }

        DebugUtil.i(mLogTag, "recoverRecord success:$recoverSuccessCount")
        // 发送录音记录恢复事件，用于决定是否需要更新录音摘要卡
        SeedlingAction.sendRecordRecoverEvent()
        return recoverSuccessCount
    }

    private fun recoverAllRecord(activity: Activity, beRecoverRecordList: List<Record>) {
        val context = activity
        val km = activity.getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
        val isInChildMode = FeatureOption.checkInChildrenMode(BaseApplication.getAppContext())
        if (km.isDeviceSecure && !isInChildMode) {
            DebugUtil.d(mLogTag, "[onClick] show lock screen")
            val recordingType = context.getString(R.string.normal_recording_tab)
            val textHint: String = context.getString(R.string.delete_all_item_text_tip, recordingType)
            val patternHint: String = context.getString(R.string.delete_all_item_pattern_tip, recordingType)
            try {
                val intent = Intent(ACTION_LOCK_SCREEN)
                intent.putExtra(LOCK_SCREEN_START_TYPE, LOCK_SCREEN_CUSTOMIZE_HEAD)
                intent.putExtra(LOCK_SCREEN_CUSTOMIZE_HEAD_STR, patternHint)
                intent.putExtra(LOCK_SCREEN_CUSTOMIZE_HEAD_STR_PSWD, textHint)
                val pm: PackageManager = context.packageManager
                val resolveInfos = pm.queryIntentActivities(intent, PackageManager.GET_RESOLVED_FILTER)
                if (resolveInfos.size != 0) {
                    val activityInfo = resolveInfos[0].activityInfo
                    if (activityInfo != null) {
                        val packageName = activityInfo.packageName
                        if (packageName != null) {
                            intent.setPackage(packageName)
                            DebugUtil.i(mLogTag, "Intent to $packageName")
                        }
                    }
                }
                activity.startActivityForResult(intent, REQUEST_LOCK_SCREEN_RESULT_SUCCESS)
            } catch (ignored: Exception) {
                DebugUtil.e(mLogTag, "start lock screen faid!", ignored)
                recoverWithPermission(activity, beRecoverRecordList)
            }
        } else {
            recoverWithPermission(activity, beRecoverRecordList)
        }
    }

    fun getOperating(): Boolean = mOperatePermission?.getOperating() ?: false

    fun resetOperating() {
        mOperatePermission?.resetContinueOperator()
    }

    fun release() {
        mOperatePermission?.release()
        mOperatePermission = null
    }
}

interface OnRecoverFileListener {
    fun onRecoverFileBefore() {}
    fun onRecoverFileResult(deleteSuccess: Boolean) {}
    fun provideRecoverRequestCode(): Int? = null
}