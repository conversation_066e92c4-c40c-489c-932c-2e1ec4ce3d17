/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  -
 ** Description: 实现  在孔雀、平板上的左右缩进
 ** Version: 1.0
 ** Date : 2023/4/1
 ** Author: 17610071263
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  17610071263     2023/4/1      1.0            add file
 ****************************************************************/
package com.soundrecorder.common.widget

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.preference.COUIPreferenceFragment

open class AutoIndentPreferenceFragment : COUIPreferenceFragment() {
    override fun onCreateRecyclerView(
        inflater: LayoutInflater,
        parent: ViewGroup?,
        savedInstanceState: Bundle?
    ): RecyclerView {
        val recyclerView = inflater.inflate(
            com.support.preference.R.layout.coui_preference_percent_recyclerview,
            parent,
            false
        ) as COUIRecyclerView
        recyclerView.setEnablePointerDownAction(false)
        recyclerView.layoutManager = onCreateLayoutManager()
        return recyclerView
    }
}