package com.soundrecorder.common.databean

data class ConvertStatus(var uploadStatus: Int = UPLOAD_STATUS_UNINIT, var convertStatus: Int = CONVERT_STATUS_UNINIT) {


    companion object {
        const val UPLOAD_STATUS_UNINIT = 1000

        const val UPLOAD_STATUS_UPLOADING = 1002
        const val UPLOAD_STATUS_UPLOAD_SUC = 1008
        const val UPLOAD_STATUS_UPLOAD_FAIL = 1009
        const val UPLOAD_STATUS_UPLOAD_ABORT_SUC = 1010

        const val CONVERT_STATUS_UNINIT = 2000
        const val CONVERT_STATUS_INIT = 2001
        const val CONVERT_STATUS_ADD_TASK_SUC = 2002
        const val CONVERT_STATUS_ADD_TASK_FAIL = 2003
        const val CONVERT_STATUS_QUERY_TASK = 2004
        const val CONVERT_STATUS_QUERY_TASK_TIMEOUT = 2005
        const val CONVERT_STATUS_QUERY_FAIL = 2006
        const val CONVERT_STATUS_QUERY_SUC = 2007

        const val CONVERT_STATUS_ABORTTASK_SUC = 3001
        const val CONVERT_STATUS_ABORTTASK_FAIL = 3002

        const val UPLOAD_STATUS_NO_NETWORK = 4001
        const val CONVERT_STATUS_NO_NETWORK = 4002

        const val UPLOAD_STATUS_EXCEPTION = 5000
        const val UPLOAD_STATUS_UNSUPPORTED_FILE_FORMAT = 5001
        const val UPLOAD_STATUS_UNSUPPORTED_FILE_DURATION = 5002
        const val UPLOAD_STATUS_UNSUPPORTED_FILE_SIZE = 5003
        const val UPLOAD_STATUS_UNSUPPORTED_FILE_DURATION_ZERO = 5005

        const val EXCEPTION = 6000
        const val ENCRYPT_EXCEPTION = 6001
        const val JSONPARSE_EXCEPTION = 6002
        const val NETWORKERROR_EXCEPTION = 6003
        const val USERTIMEOUT_EXCEPTION = 6004
    }
}