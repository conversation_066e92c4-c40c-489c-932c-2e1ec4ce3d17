/**********************************************************
 * Copyright 2010-2017 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description : Record
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-3-12, huang<PERSON>wang, create
 ***********************************************************/

package com.soundrecorder.common.databean;

import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLOUM_NAME_SYNC_DOWNLOAD_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_DATA;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_FILE_PATH;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_BUCKET_DISPLAY_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CALLER_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CALL_AVATAR_COLOR;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CALL_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_CHECK_PAYLOAD;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_SYS_VERSION;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_CREATED;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_MODIFIED;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE_TIME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRECT_TIME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DURATION;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_ERROR_CODE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_FAIL_COUNT;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_RECYCLE_PATH;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_UUID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_DIRECT_ON;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_MARKLIST_SHOWING;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_RECYCLE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_LAST_FAIL_TIME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_LEVEL;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MARK_DATA;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MD5;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MIGRATE_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MIMETYPE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_ORIGINAL_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_PARSE_CALL;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_PRIVATE_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_RECORD_TYPE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_RELATIVE_PATH;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SCAN_OSHARE_TEXT;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SIZE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_DATE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_TYPE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID;
import static com.soundrecorder.common.constant.RecordConstant.DIRECT_RECORD_OFF;
import static com.soundrecorder.common.constant.RecordConstant.DIRECT_RECORD_ON;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_BACKUP_STATE_ADDED;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_BACKUP_STATE_DEFAULT;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_BACKUP_STATE_DELETED;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_BACKUP_STATE_MODIFIED;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_BACKUP_STATE_SYNCDELETED;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_DELETED;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_NORMAL;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_NOT_DIRTY;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_PRIVETE_ENCRYPT;

import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;

import java.io.File;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;

import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.share.ShareAction;
import com.soundrecorder.common.utils.AmpFileUtil;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.base.utils.MD5Utils;

import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.base.utils.SyncTimeUtils;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.utils.RecordModeUtil;

public class Record {
    /**
     * cursor type
     */
    public static final int TYPE_FROM_MEDIA = 1;
    public static final int TYPE_FROM_RECORD = 2;
    private static final String TAG = "Record";
    private static final int LOG_MAX_VISIBLE_LEN = 20;

    public String mUuid;
    public String mData;
    public long mFileSize;
    private String mDisplayName;
    public String mMimeType;
    public long mDateCreated;
    public String mMD5;
    public String mGlobalId;
    private long mId;
    private long mDateModied;
    private int mRecordType;
    private byte[] mMarkData;
    private byte[] mAmpData;
    private long mDuration;
    private String mBuckedDisplayName;
    private int mDirty;
    private boolean mDeleted;
    private String mFileId;
    private int mSyncType;
    private int mSyncUploadStatus;
    private int mSyncDownloadStatus;
    private int mErrorCode;
    private int mLevel;
    private long mSyncDate;
    private long mFailedCount;
    private long mLastFailedTime;

    private int mEditStatus;
    private String mRelativePath;
    private String mAmpFilePath;
    private String mOwnerPackageName;

    private int mSyncPrivateStatus;
    private int mSyncMigrateStatus;
    private int mIsMarkListShowing;
    private List<MarkDataBean> markDataBeanList;
    private String tempId;
    /*接入cloukit新增2个字段*/
    // 云端元数据版本号
    private long sysVersion;
    // 上传文件返回临时
    private String checkPayload;
    /*是否开启定向录音*/
    private boolean mIsDirectOn = false;
    /*定向录音开始、结束时间:0-1,2-3,3-4*/
    private String mDirectTime;

    /*文件是否在回收站*/
    private boolean mIsRecycle = false;
    /*文件在回收站的绝对路径*/
    private String mRecycleFilePath;
    /* 进入回收站的时间*/
    private long mDeleteTime = 0;

    private int mGroupId = -1;

    private String mGroupUuid;
    private String mCallerName;
    private String mOriginalName;
    private String mCallAvatarColor;

    /**默认值0，代表未扫描
     * 1：表示该录音文件已扫描了/Documents/oshare/Recording目录的转写文件*/
    private int mIsScanOshareText;
    /*联系人名称*/
    private String mCallName;
    /*是否已经解析过用户昵称*/
    private boolean mIsParseCallName;

    /**
     * 判断是否已经扫描过转写文件
     * 默认值0，代表未扫描
     * 1：表示该录音文件已扫描了/Documents/oshare/Recording目录的转写文件
     */
    public boolean isScanOshareText() {
        return mIsScanOshareText == 1;
    }

    public void setIsScanOshareText(int mIsScanOshareText) {
        this.mIsScanOshareText = mIsScanOshareText;
    }

    public String getCallName() {
        return mCallName;
    }

    public void setCallName(String mCallName) {
        this.mCallName = mCallName;
    }

    public boolean isIsParseCallName() {
        return mIsParseCallName;
    }

    public void setIsParseCallName(boolean mIsParseCallName) {
        this.mIsParseCallName = mIsParseCallName;
    }

    public String getCallAvatarColor() {
        return mCallAvatarColor;
    }

    public void setCallAvatarColor(String mCallerName) {
        this.mCallAvatarColor = mCallerName;
    }

    public String getCallerName() {
        return mCallerName;
    }

    public void setCallerName(String mCallerName) {
        this.mCallerName = mCallerName;
    }

    public String getOriginalName() {
        return mOriginalName;
    }

    public void setOriginalName(String mOriginalName) {
        this.mOriginalName = mOriginalName;
    }

    public int getGroupId() {
        return mGroupId;
    }

    public void setGroupId(int groupId) {
        this.mGroupId = groupId;
    }

    public String getGroupUuid() {
        return mGroupUuid;
    }

    public void setGroupUuid(String groupUuid) {
        this.mGroupUuid = groupUuid;
    }

    public boolean isRecycle() {
        return mIsRecycle;
    }

    public void setIsRecycle(boolean mIsRecycle) {
        this.mIsRecycle = mIsRecycle;
    }

    public String getRecycleFilePath() {
        return mRecycleFilePath;
    }

    public void setRecycleFilePath(String mRecycleFilePath) {
        this.mRecycleFilePath = mRecycleFilePath;
    }

    public long getDeleteTime() {
        return mDeleteTime;
    }

    public void setDeleteTime(long mDeleteTime) {
        this.mDeleteTime = mDeleteTime;
    }

    public void setDirectOn(boolean directOn) {
        mIsDirectOn = directOn;
    }

    public boolean getDirectOn() {
        return mIsDirectOn;
    }

    public void setDirectTime(String mDirectTime) {
        this.mDirectTime = mDirectTime;
    }

    public String getDirectTime() {
        return mDirectTime;
    }

    public long getSysVersion() {
        return sysVersion;
    }

    public void setSysVersion(long sysVersion) {
        this.sysVersion = sysVersion;
    }

    public String getCheckPayload() {
        return checkPayload;
    }

    public void setCheckPayload(String checkPayload) {
        this.checkPayload = checkPayload;
    }

    public List<MarkDataBean> getMarkDataBeanList() {
        return markDataBeanList;
    }

    public void setMarkDataBeanList(List<MarkDataBean> markDataBeanList) {
        this.markDataBeanList = markDataBeanList;
    }

    public Record() {

    }

    public long getId() {
        return mId;
    }

    public void setId(long id) {
        mId = id;
    }

    public String getUuid() {
        return mUuid;
    }

    public void setUuid(String uuid) {
        this.mUuid = uuid;
    }

    public String getData() {
        return mData;
    }

    public void setData(String data) {
        this.mData = data;
    }

    public long getFileSize() {
        return mFileSize;
    }

    public void setFileSize(long fileSize) {
        this.mFileSize = fileSize;
    }

    public String getDisplayName() {
        return mDisplayName;
    }

    public void setDisplayName(String displayName) {
        this.mDisplayName = displayName;
    }

    public String getMimeType() {
        return mMimeType;
    }

    public void setMimeType(String mimeType) {
        this.mMimeType = mimeType;
    }

    public long getDateCreated() {
        return mDateCreated;
    }

    public void setDateCreated(long dateCreated) {
        this.mDateCreated = dateCreated;
    }

    public long getDateModied() {
        return mDateModied;
    }

    public void setDateModied(long dateModied) {
        this.mDateModied = dateModied;
    }

    public int getRecordType() {
        return mRecordType;
    }

    public void setRecordType(int recordType) {
        this.mRecordType = recordType;
    }

    public byte[] getMarkData() {
        return mMarkData;
    }

    public void setMarkData(byte[] rarkData) {
        this.mMarkData = rarkData;
    }

    public byte[] getAmpData() {
        return mAmpData;
    }

    public void setAmpData(byte[] ampData) {
        this.mAmpData = ampData;
    }

    public long getDuration() {
        return mDuration;
    }

    public void setDuration(long duration) {
        this.mDuration = duration;
    }

    public String getBuckedDisplayName() {
        return mBuckedDisplayName;
    }

    public void setBuckedDisplayName(String buckedDisplayName) {
        this.mBuckedDisplayName = buckedDisplayName;
    }

    public boolean isDirty() {
        return mDirty > RECORD_NOT_DIRTY;
    }

    public void setDirty(int dirtyInt) {
        this.mDirty = dirtyInt;
    }

    public int getDirty() {
        return mDirty;
    }

    public boolean isDeleted() {
        return mDeleted;
    }

    public void setDeleted(boolean deleted) {
        this.mDeleted = deleted;
    }

    public String getMD5() {
        return mMD5;
    }

    public void setMD5(String md5) {
        this.mMD5 = md5;
    }

    public String getFileId() {
        return mFileId;
    }

    public void setFileId(String fileId) {
        this.mFileId = fileId;
    }

    public String getGlobalId() {
        return mGlobalId;
    }

    public void setGlobalId(String globalId) {
        this.mGlobalId = globalId;
    }

    public int getSyncType() {
        return mSyncType;
    }

    public void setSyncType(int syncType) {
        this.mSyncType = syncType;
    }

    public int getSyncStatus() {
        return mSyncUploadStatus;
    }

    public void setSyncUploadStatus(int syncStatus) {
        this.mSyncUploadStatus = syncStatus;
    }

    public int getErrorCode() {
        return mErrorCode;
    }

    public void setErrorCode(int errorCode) {
        this.mErrorCode = errorCode;
    }

    public int getLevel() {
        return mLevel;
    }

    public void setLevel(int level) {
        this.mLevel = level;
    }

    public long getSyncDate() {
        return mSyncDate;
    }

    public void setSyncDate(long mSyncDate) {
        this.mSyncDate = mSyncDate;
    }

    public long getFailedCount() {
        return mFailedCount;
    }

    public void setFailedCount(long mFailedCount) {
        this.mFailedCount = mFailedCount;
    }

    public long getLastFailedTime() {
        return mLastFailedTime;
    }

    public void setLastFailedTime(long mLastFailedTime) {
        this.mLastFailedTime = mLastFailedTime;
    }


    public int getSyncDownlodStatus() {
        return mSyncDownloadStatus;
    }

    public int getEditStatus() {
        return mEditStatus;
    }

    public void setEditStatus(int mEditStatus) {
        this.mEditStatus = mEditStatus;
    }

    public void setSyncDownlodStatus(int mSyncDownlodStatus) {
        this.mSyncDownloadStatus = mSyncDownlodStatus;
    }

    public String getAmpFilePath() {
        return mAmpFilePath;
    }

    public void setAmpFilePath(String mAmpFilePath) {
        this.mAmpFilePath = mAmpFilePath;
    }

    public int getSyncPrivateStatus() {
        return mSyncPrivateStatus;
    }

    public void setSyncPrivateStatus(int status) {
        this.mSyncPrivateStatus = status;
    }

    public int getSyncMigrateStatus() {
        return mSyncMigrateStatus;
    }

    public void setSyncMigrateStatus(int status) {
        this.mSyncMigrateStatus = status;
    }

    public int getIsMarkListShowing() {
        return mIsMarkListShowing;
    }

    public void setIsMarkListShowing(int status) {
        this.mIsMarkListShowing = status;
    }

    public String getTempId() {
        return tempId;
    }

    public void setTempId(String tempId) {
        this.tempId = tempId;
    }

    public Record(Cursor cursor, int type) {
        if (cursor == null) {
            return;
        }

        if (type == TYPE_FROM_MEDIA) {
            int idIndex = cursor.getColumnIndex(MediaStore.Files.FileColumns._ID);
            if (idIndex >= 0) {
                mId = cursor.getLong(idIndex);
            }
            int dataIndex = cursor.getColumnIndex(MediaStore.Audio.Media.DATA);
            if (dataIndex >= 0) {
                setData(cursor.getString(dataIndex));
            }
            int sizeIndex = cursor.getColumnIndex(MediaStore.Audio.Media.SIZE);
            if (sizeIndex >= 0) {
                setFileSize(cursor.getLong(sizeIndex));
            }
            int displayNameIndex = cursor.getColumnIndex(MediaStore.Audio.Media.DISPLAY_NAME);
            if (displayNameIndex >= 0) {
                setDisplayName(cursor.getString(displayNameIndex));
            }
            int mimeTypeIndex = cursor.getColumnIndex(MediaStore.Audio.Media.MIME_TYPE);
            if (mimeTypeIndex >= 0) {
                setMimeType(cursor.getString(mimeTypeIndex));
            }
            int addTimeIndex = cursor.getColumnIndex(MediaStore.Audio.Media.DATE_ADDED);
            if (addTimeIndex >= 0) {
                setDateCreated(cursor.getLong(addTimeIndex));
            }
            int modifyTimeIndex = cursor.getColumnIndex(MediaStore.Audio.Media.DATE_MODIFIED);
            if (modifyTimeIndex >= 0) {
                setDateModied(cursor.getLong(modifyTimeIndex));
            }
            int durationIndex = cursor.getColumnIndex(MediaStore.Audio.Media.DURATION);
            if (durationIndex >= 0) {
                setDuration(cursor.getLong(durationIndex));
            }
            int bucketDisplayNameIndex = cursor.getColumnIndex(MediaStore.Video.VideoColumns.BUCKET_DISPLAY_NAME);
            if (bucketDisplayNameIndex >= 0) {
                setBuckedDisplayName(cursor.getString(bucketDisplayNameIndex));
            }
            if (BaseUtil.isAndroidQOrLater()) {
                int relativePathIndex = cursor.getColumnIndex(MediaStore.Video.VideoColumns.RELATIVE_PATH);
                if (relativePathIndex >= 0) {
                    setRelativePath(cursor.getString(relativePathIndex));
                }
            }
            if (BaseUtil.isAndroidROrLater()) {
                int ownerPackageNameIndex = cursor.getColumnIndex(MediaStore.Audio.Media.OWNER_PACKAGE_NAME);
                if (ownerPackageNameIndex > 0) {
                    setOwnerPackageName(cursor.getString(ownerPackageNameIndex));
                }
            }
        } else if (type == TYPE_FROM_RECORD) {
            int idIndex = cursor.getColumnIndex(COLUMN_NAME_ID);
            if (idIndex >= 0) {
                mId = cursor.getLong(idIndex);
            }
            int uuIdIndex = cursor.getColumnIndex(COLUMN_NAME_UUID);
            if (uuIdIndex >= 0) {
                setUuid(cursor.getString(uuIdIndex));
            }
            int dataIndex = cursor.getColumnIndex(COLUMN_NAME_DATA);
            if (dataIndex >= 0) {
                setData(cursor.getString(dataIndex));
            }
            int fileSizeIndex = cursor.getColumnIndex(COLUMN_NAME_SIZE);
            if (fileSizeIndex >= 0) {
                setFileSize(cursor.getLong(fileSizeIndex));
            }
            int displayNameIndex = cursor.getColumnIndex(COLUMN_NAME_DISPLAY_NAME);
            if (displayNameIndex >= 0) {
                setDisplayName(cursor.getString(displayNameIndex));
            }
            int mimeTypeIndex = cursor.getColumnIndex(COLUMN_NAME_MIMETYPE);
            if (mimeTypeIndex >= 0) {
                setMimeType(cursor.getString(mimeTypeIndex));
            }
            int createDateIndex = cursor.getColumnIndex(COLUMN_NAME_DATE_CREATED);
            if (createDateIndex >= 0) {
                setDateCreated(cursor.getLong(createDateIndex));
            }
            int modifyDataIndex = cursor.getColumnIndex(COLUMN_NAME_DATE_MODIFIED);
            if (modifyDataIndex >= 0) {
                setDateModied(cursor.getLong(modifyDataIndex));
            }
            int recordTypeIndex = cursor.getColumnIndex(COLUMN_NAME_RECORD_TYPE);
            if (recordTypeIndex >= 0) {
                setRecordType(cursor.getInt(recordTypeIndex));
            }
            int markDataIndex = cursor.getColumnIndex(COLUMN_NAME_MARK_DATA);
            if (markDataIndex >= 0) {
                setMarkData(cursor.getBlob(markDataIndex));
            }
            int ampDataIndex = cursor.getColumnIndex(COLUMN_NAME_AMP_DATA);
            if (ampDataIndex >= 0) {
                setAmpData(cursor.getBlob(ampDataIndex));
            }
            int durationIndex = cursor.getColumnIndex(COLUMN_NAME_DURATION);
            if (durationIndex >= 0) {
                setDuration(cursor.getLong(durationIndex));
            }
            int buckIndex = cursor.getColumnIndex(COLUMN_NAME_BUCKET_DISPLAY_NAME);
            if (buckIndex >= 0) {
                setBuckedDisplayName(cursor.getString(buckIndex));
            }
            int dirtyIndex = cursor.getColumnIndex(COLUMN_NAME_DIRTY);
            if (dirtyIndex >= 0) {
                setDirty(cursor.getInt(dirtyIndex));
            }
            int deleteIndex = cursor.getColumnIndex(COLUMN_NAME_DELETE);
            if (deleteIndex >= 0) {
                setDeleted(cursor.getInt(deleteIndex) == RECORD_DELETED);
            }
            int md5Index = cursor.getColumnIndex(COLUMN_NAME_MD5);
            if (md5Index >= 0) {
                setMD5(cursor.getString(md5Index));
            }
            int fileIdIndex = cursor.getColumnIndex(COLUMN_NAME_FILE_ID);
            if (fileIdIndex >= 0) {
                setFileId(cursor.getString(fileIdIndex));
            }
            int globalIdIndex = cursor.getColumnIndex(COLUMN_NAME_GLOBAL_ID);
            if (globalIdIndex >= 0) {
                setGlobalId(cursor.getString(globalIdIndex));
            }
            int syncTypeIndex = cursor.getColumnIndex(COLUMN_NAME_SYNC_TYPE);
            if (syncTypeIndex >= 0) {
                setSyncType(cursor.getInt(syncTypeIndex));
            }
            int syncUploadStatusIndex = cursor.getColumnIndex(COLUMN_NAME_SYNC_UPLOAD_STATUS);
            if (syncUploadStatusIndex >= 0) {
                setSyncUploadStatus(cursor.getInt(syncUploadStatusIndex));
            }
            int syncDownloadIndex = cursor.getColumnIndex(COLOUM_NAME_SYNC_DOWNLOAD_STATUS);
            if (syncDownloadIndex >= 0) {
                setSyncDownlodStatus(cursor.getInt(syncDownloadIndex));
            }
            int errorCodeIndex = cursor.getColumnIndex(COLUMN_NAME_ERROR_CODE);
            if (errorCodeIndex >= 0) {
                setErrorCode(cursor.getInt(errorCodeIndex));
            }
            int levelIndex = cursor.getColumnIndex(COLUMN_NAME_LEVEL);
            if (levelIndex >= 0) {
                setLevel(cursor.getInt(levelIndex));
            }
            int syncDateIndex = cursor.getColumnIndex(COLUMN_NAME_SYNC_DATE);
            if (syncDateIndex >= 0) {
                setSyncDate(cursor.getLong(syncDateIndex));
            }
            int failCntIndex = cursor.getColumnIndex(COLUMN_NAME_FAIL_COUNT);
            if (failCntIndex >= 0) {
                setFailedCount(cursor.getInt(failCntIndex));
            }
            int lastFailTimeIndex = cursor.getColumnIndex(COLUMN_NAME_LAST_FAIL_TIME);
            if (lastFailTimeIndex >= 0) {
                setLastFailedTime(cursor.getLong(lastFailTimeIndex));
            }
            int relativePathIndex = cursor.getColumnIndex(COLUMN_NAME_RELATIVE_PATH);
            if (relativePathIndex >= 0) {
                setRelativePath(cursor.getString(relativePathIndex));
            }
            int ampFilePathIndex = cursor.getColumnIndex(COLUMN_NAME_AMP_FILE_PATH);
            if (ampFilePathIndex >= 0) {
                setAmpFilePath(cursor.getString(ampFilePathIndex));
            }
            int privateStatusIndex = cursor.getColumnIndex(COLUMN_NAME_PRIVATE_STATUS);
            if (privateStatusIndex >= 0) {
                setSyncPrivateStatus(cursor.getInt(privateStatusIndex));
            }
            int migrateStatusIndex = cursor.getColumnIndex(COLUMN_NAME_MIGRATE_STATUS);
            if (migrateStatusIndex >= 0) {
                setSyncMigrateStatus(cursor.getInt(migrateStatusIndex));
            }
            int markListShowIndex = cursor.getColumnIndex(COLUMN_NAME_IS_MARKLIST_SHOWING);
            if (markListShowIndex >= 0) {
                setIsMarkListShowing(cursor.getInt(markListShowIndex));
            }
            int sysVersionIndex = cursor.getColumnIndex(COLUMN_NAME_CLOUD_SYS_VERSION);
            int checkLoadIndex = cursor.getColumnIndex(COLUMN_NAME_CLOUD_CHECK_PAYLOAD);
            if (sysVersionIndex >= 0) {
                setSysVersion(cursor.getLong(sysVersionIndex));
            }
            if (checkLoadIndex >= 0) {
                setCheckPayload(cursor.getString(checkLoadIndex));
            }

            int isDirectOnIndex = cursor.getColumnIndex(COLUMN_NAME_IS_DIRECT_ON);
            if (isDirectOnIndex >= 0) {
                mIsDirectOn = cursor.getInt(isDirectOnIndex) == DIRECT_RECORD_ON;
            }
            int directTimeIndex = cursor.getColumnIndex(COLUMN_NAME_DIRECT_TIME);
            if (directTimeIndex >= 0) {
                mDirectTime = cursor.getString(directTimeIndex);
            }

            int isRecycleIndex = cursor.getColumnIndex(COLUMN_NAME_IS_RECYCLE);
            if (isRecycleIndex >= 0) {
                mIsRecycle = cursor.getInt(isRecycleIndex) == RECORD_DELETED;
            }

            int fileRecyclePathIndex = cursor.getColumnIndex(COLUMN_NAME_FILE_RECYCLE_PATH);
            if (fileRecyclePathIndex >= 0) {
                mRecycleFilePath = cursor.getString(fileRecyclePathIndex);
            }

            int groupIdIndex = cursor.getColumnIndex(COLUMN_NAME_GROUP_ID);
            if (groupIdIndex >= 0) {
                mGroupId = cursor.getInt(groupIdIndex);
            }

            int groupUuidIndex = cursor.getColumnIndex(COLUMN_NAME_GROUP_UUID);
            if (groupUuidIndex >= 0) {
                mGroupUuid = cursor.getString(groupUuidIndex);
            }
            int callerNameIndex = cursor.getColumnIndex(COLUMN_NAME_CALLER_NAME);
            if (callerNameIndex >= 0) {
                mCallerName = cursor.getString(callerNameIndex);
            }
            int originalNameIndex = cursor.getColumnIndex(COLUMN_NAME_ORIGINAL_NAME);
            if (originalNameIndex >= 0) {
                mOriginalName = cursor.getString(originalNameIndex);
            }

            int deleteTimeIndex = cursor.getColumnIndex(COLUMN_NAME_DELETE_TIME);
            if (directTimeIndex >= 0) {
                mDeleteTime = cursor.getLong(deleteTimeIndex);
            }
            int callAvatarColorIndex = cursor.getColumnIndex(COLUMN_NAME_CALL_AVATAR_COLOR);
            if (callAvatarColorIndex >= 0) {
                mCallAvatarColor = cursor.getString(callAvatarColorIndex);
            }
            int scanOShareIndex = cursor.getColumnIndex(COLUMN_NAME_SCAN_OSHARE_TEXT);
            if (scanOShareIndex >= 0) {
                mIsScanOshareText = cursor.getInt(scanOShareIndex);
            }
            int callNameIndex = cursor.getColumnIndex(COLUMN_NAME_CALL_NAME);
            if (callNameIndex >= 0) {
                mCallName = cursor.getString(callNameIndex);
            }
            int parseCallInfoIndex = cursor.getColumnIndex(COLUMN_NAME_PARSE_CALL);
            if (parseCallInfoIndex >= 0) {
                mIsParseCallName = cursor.getInt(parseCallInfoIndex) == 1;
            }
        }
    }

    public ContentValues convertToContentValues() {
        ContentValues values = new ContentValues();
        if (mUuid != null) {
            values.put(COLUMN_NAME_UUID, mUuid);
        }
        if (mData != null) {
            values.put(COLUMN_NAME_DATA, mData);
        }
        values.put(COLUMN_NAME_SIZE, mFileSize);
        if (mDisplayName != null) {
            values.put(COLUMN_NAME_DISPLAY_NAME, mDisplayName);
        }
        if (mMimeType != null) {
            values.put(COLUMN_NAME_MIMETYPE, mMimeType);
        }
        values.put(COLUMN_NAME_DATE_CREATED, mDateCreated);
        values.put(COLUMN_NAME_DATE_MODIFIED, mDateModied);
        values.put(COLUMN_NAME_RECORD_TYPE, mRecordType);
        values.put(COLUMN_NAME_MARK_DATA, mMarkData);
        values.put(COLUMN_NAME_AMP_DATA, mAmpData);
        values.put(COLUMN_NAME_DURATION, mDuration);
        if (mBuckedDisplayName != null) {
            values.put(COLUMN_NAME_BUCKET_DISPLAY_NAME, mBuckedDisplayName);
        }
        values.put(COLUMN_NAME_DIRTY, mDirty);
        values.put(COLUMN_NAME_DELETE, (mDeleted ? RECORD_DELETED : RECORD_NORMAL));
        if (mMD5 != null) {
            values.put(COLUMN_NAME_MD5, mMD5);
        }
        if (mFileId != null) {
            values.put(COLUMN_NAME_FILE_ID, mFileId);
        }
        if (mGlobalId != null) {
            values.put(COLUMN_NAME_GLOBAL_ID, mGlobalId);
            values.put(COLUMN_NAME_CLOUD_SYS_VERSION, sysVersion);
        }
        if (checkPayload != null) {
            values.put(COLUMN_NAME_CLOUD_CHECK_PAYLOAD, checkPayload);
        }
        values.put(COLUMN_NAME_SYNC_TYPE, mSyncType);
        values.put(COLUMN_NAME_SYNC_UPLOAD_STATUS, mSyncUploadStatus);
        values.put(COLOUM_NAME_SYNC_DOWNLOAD_STATUS, mSyncDownloadStatus);
        values.put(COLUMN_NAME_ERROR_CODE, mErrorCode);
        values.put(COLUMN_NAME_LEVEL, mLevel);
        values.put(COLUMN_NAME_SYNC_DATE, mSyncDate);
        values.put(COLUMN_NAME_FAIL_COUNT, mFailedCount);
        values.put(COLUMN_NAME_LAST_FAIL_TIME, mLastFailedTime);
        if (mRelativePath != null) {
            values.put(COLUMN_NAME_RELATIVE_PATH, mRelativePath);
        }
        if (mAmpFilePath != null) {
            values.put(COLUMN_NAME_AMP_FILE_PATH, mAmpFilePath);
        }
        values.put(COLUMN_NAME_PRIVATE_STATUS, mSyncPrivateStatus);
        values.put(COLUMN_NAME_MIGRATE_STATUS, mSyncMigrateStatus);
        values.put(COLUMN_NAME_IS_MARKLIST_SHOWING, mIsMarkListShowing);
        //记录定向录音数据
        values.put(COLUMN_NAME_IS_DIRECT_ON, (mIsDirectOn ? DIRECT_RECORD_ON : DIRECT_RECORD_OFF));
        values.put(COLUMN_NAME_DIRECT_TIME, mDirectTime);

        values.put(COLUMN_NAME_IS_RECYCLE, mIsRecycle);
        if (mRecycleFilePath != null) {
            values.put(COLUMN_NAME_FILE_RECYCLE_PATH, mRecycleFilePath);
        }

        values.put(COLUMN_NAME_GROUP_ID, mGroupId);
        values.put(COLUMN_NAME_GROUP_UUID, mGroupUuid);
        values.put(COLUMN_NAME_CALLER_NAME, mCallerName);
        values.put(COLUMN_NAME_ORIGINAL_NAME, mOriginalName);
        if (mCallAvatarColor != null) {
            values.put(COLUMN_NAME_CALL_AVATAR_COLOR, mCallAvatarColor);
        }

        if (mDeleteTime > 0) {
            values.put(COLUMN_NAME_DELETE_TIME, mDeleteTime);
        }
        values.put(COLUMN_NAME_SCAN_OSHARE_TEXT, mIsScanOshareText);
        if (mIsParseCallName) {
            values.put(COLUMN_NAME_CALL_NAME, mCallName);
            values.put(COLUMN_NAME_PARSE_CALL, mIsParseCallName ? 1 : 0);
        }
        return values;
    }


    public Record constructDBRecordFromMediaRecord(Record mediaRecord) {
        if (mediaRecord == null) {
            return this;
        }
        setData(mediaRecord.getData());
        setFileSize(mediaRecord.getFileSize());
        long fileSize = mediaRecord.getFileSize();
        if (!TextUtils.isEmpty(mediaRecord.getData())) {
            File fileData = new File(mediaRecord.getData());
            if (fileData.exists()) {
                long realFileSize = fileData.length();
                if (realFileSize != fileSize) {
                    DebugUtil.i(TAG, "constructDBRecordFromMediaRecord, realFileSize: " + realFileSize + ", fileSize in mediaRecord: " + fileSize);
                }
                setFileSize(realFileSize);
                String md5fromMedia = mediaRecord.getMD5();
                String md5toDb = md5fromMedia;
                if (TextUtils.isEmpty(md5fromMedia)) {
                    md5toDb = MD5Utils.getMD5(fileData);
                } else {
                    if (realFileSize != fileSize) {
                        md5toDb = MD5Utils.getMD5(fileData);
                    }
                }
                setMD5(md5toDb);
            }
        }
        setDisplayName(mediaRecord.getDisplayName());
        setMimeType(mediaRecord.getMimeType());
        setDateCreated(mediaRecord.getDateCreated());
        setDateModied(mediaRecord.getDateModied());
        setDuration(mediaRecord.getDuration());
        setBuckedDisplayName(mediaRecord.getBuckedDisplayName());
        if (mediaRecord.getRelativePath() != null) {
            setRelativePath(mediaRecord.getRelativePath());
        }
        return this;
    }

    public boolean isInvalidOriginalPath() {
        try {
            return TextUtils.isEmpty(mData) || fileNotExist();
        } catch (Exception e) {
            DebugUtil.e(TAG, "isInvalidOriginalPath, e = " + e);
        }
        return false;
    }

    public boolean checkMd5() {
        return checkMd5(null);
    }

    public boolean checkMd5(Uri uri) {
        if (TextUtils.isEmpty(mMD5)) {
            long time = System.currentTimeMillis();
            if (BaseUtil.isAndroidQOrLater()) {
                Context context = BaseApplication.getAppContext();
                Uri mediaUri = (uri != null) ? uri : FileUtils.getMediaUriByRelativePath(context, mRelativePath, mDisplayName);
                if (mediaUri != null) {
                    try {
                        mMD5 = MD5Utils.getMD5(mediaUri);
                    } catch (Exception e) {
                        DebugUtil.e(TAG, "checkMd5, e=" + e);
                    }
                }
            } else {
                try {
                    File file = new File(mData);
                    if (file.exists()) {
                        mMD5 = MD5Utils.getMD5(file);
                    }
                } catch (Exception e) {
                    DebugUtil.e(TAG, "checkMd5, e=" + e);
                }
            }
            DebugUtil.v(TAG, "checkMd5, cost time=" + (System.currentTimeMillis() - time));
        }

        return !TextUtils.isEmpty(mMD5);
    }

    public boolean fileNotExist() {
        if (BaseUtil.isAndroidQOrLater()) {
            return !FileUtils.isFileExist(mRelativePath, mDisplayName);
        } else {
            if (!TextUtils.isEmpty(mData)) {
                File file = new File(mData);
                return !file.exists();
            }
        }
        return true;
    }


    public boolean fileExist() {
        if (BaseUtil.isAndroidQOrLater()) {
            return FileUtils.isFileExist(mRelativePath, mDisplayName);
        } else {
            if (!TextUtils.isEmpty(mData)) {
                File file = new File(mData);
                return file.exists();
            }
        }
        return false;
    }


    public boolean fileExistAndCheckSize() {
        if (BaseUtil.isAndroidQOrLater()) {
            long realFileSize = FileUtils.getRealFileSize(mRelativePath, mDisplayName);
            if (realFileSize == -1) {
                return false;
            } else {
                DebugUtil.i(TAG, " mFileSize: " + mFileSize + ", size from file : " + realFileSize);
                return realFileSize == mFileSize;
            }
        } else {
            if (!TextUtils.isEmpty(mData)) {
                File file = new File(mData);
                DebugUtil.i(TAG, "file name is : " + mDisplayName + ", mFileSize: " + mFileSize + ", size from file : " + file.length());
                return file.exists() && (file.length() == mFileSize);
            }
        }
        return false;
    }


    public String getConcatRelativePath() {
        String result;
        if (!TextUtils.isEmpty(mRelativePath)) {
            if (mRelativePath.endsWith(File.separator)) {
                result = mRelativePath + mDisplayName;
            } else {
                result = mRelativePath + File.separator + mDisplayName;
            }
        } else {
            result = mData;
        }
        return result;
    }

    public boolean sameAddedTime(Record record) {
        return (record != null) && (mDateCreated == record.mDateCreated);
    }

    public boolean sameFileSize(Record record) {
        return (record != null) && (mFileSize == record.mFileSize);
    }

    public boolean sameMD5(Record record) {
        return (record != null) && checkMd5() && record.checkMd5() && mMD5.equals(record.mMD5);
    }

    public boolean sameDisplayName(Record record) {
        return (record != null) && record.getDisplayName() != null && record.getDisplayName().equals(mDisplayName);
    }

    public boolean sameSysVersion(Record record) {
        return (record != null) && sysVersion == record.sysVersion;
    }

    public boolean sameGlobalId(Record record) {
        return (record != null) && (hasGlobalId()) && (mGlobalId.equals(record.mGlobalId));
    }

    public boolean hasGlobalId() {
        return !TextUtils.isEmpty(mGlobalId);
    }

    public boolean hasFileId() {
        return !TextUtils.isEmpty(mFileId);
    }


    public String getDirectoryPath() {
        if (!TextUtils.isEmpty(mData) && !TextUtils.isEmpty(mDisplayName)) {
            try {
                return mData.substring(0, mData.length() - mDisplayName.length());
            } catch (Exception e) {
                DebugUtil.e(TAG, "getDirectoryPath e = " + e);
            }
        }

        return "";
    }

    public boolean rename() {
        String newName = FileUtils.getNewDisplayName(mRelativePath, mDisplayName);
        String newTitle = MediaDBUtils.getTitleByName(newName);
        String suffix = FileUtils.getSuffix(newName);
        Uri uriWithId = ContentUris.withAppendedId(MediaDBUtils.BASE_URI, mId);
        int result = -1;
        try {
            result = MediaDBUtils.rename(uriWithId, newTitle, suffix, mMimeType);
        } catch (Throwable e) {
            DebugUtil.e(TAG, "rename", e);
        }
        if (result > 0) {
            mDisplayName = newName;
            int lastIndexOfFileDiscriptor = mData.lastIndexOf(File.separator);
            if (lastIndexOfFileDiscriptor > 0) {
                mData = mData.substring(0, lastIndexOfFileDiscriptor) + File.separator + newName;
            }
        }
        DebugUtil.i(TAG, mDisplayName + " rename to " + newName
                + " , result = " + result
                + " , id = " + mId
                + " , mimeType = " + mMimeType);
        return result > 0;
    }

    public int getRawRecordLocalState() {
        if (isLocalEncryptedRecord()) {
            return RECORD_BACKUP_STATE_SYNCDELETED;
        }
        if (isLocalAddedRawRecord()) {
            return RECORD_BACKUP_STATE_ADDED;
        }
        /*if (isNoCloudRecycleRecord()) {
            //依然属于新增？，不过新增以后存储在回收站？
            return RECORD_BACKUP_STATE_DELETED;
        }*/
        if (isLocalModifiedRecord()) {
            return RECORD_BACKUP_STATE_MODIFIED;
        }
        if (isLocalDeletedRecord()) {
            return RECORD_BACKUP_STATE_DELETED;
        }
        return RECORD_BACKUP_STATE_DEFAULT;
    }


    private boolean isLocalAddedRawRecord() {
        return !mDeleted && TextUtils.isEmpty(mGlobalId) && !TextUtils.isEmpty(mFileId);
    }

    /**
     * 在本地回收站，并且未参与过云同步的文件
     * @return
     */
    private boolean isNoCloudRecycleRecord() {
        if (TextUtils.isEmpty(mGlobalId) && isRecycle()) {
            return mDirty > 0;
        } else {
            return false;
        }
    }

    private boolean isLocalModifiedRecord() {
        if (!TextUtils.isEmpty(mGlobalId) && !TextUtils.isEmpty(mFileId)) {
            return (mDirty > 0) && (!mDeleted);
        } else {
            return false;
        }
    }

    private boolean isLocalDeletedRecord() {
        if (!TextUtils.isEmpty(mGlobalId)) {
            return mDeleted;
        } else if (!TextUtils.isEmpty(mFileId)) {
            return mDeleted;
        } else {
            return false;
        }
    }

    private boolean isLocalEncryptedRecord() {
        if ((mDirty > 0) && (mSyncPrivateStatus == RECORD_PRIVETE_ENCRYPT)) {
            return true;
        } else {
            return false;
        }
    }

    public String getRelativePath() {
        return mRelativePath;
    }

    public void setRelativePath(String mRelativePath) {
        this.mRelativePath = RecordModeUtil.cleanRelativePath(mRelativePath);
    }

    public boolean canStartSync() {
        if (mFailedCount > SyncTimeUtils.MAX_COUNT) {
            DebugUtil.d(TAG, "canStartSync, failed too many times, count=" + mFailedCount);
            return false;
        }
        long startSyncTime = SyncTimeUtils.getTime((int) mFailedCount, mLastFailedTime);
        Calendar lastFailedCalendar = Calendar.getInstance();
        lastFailedCalendar.setTimeInMillis(mLastFailedTime);
        String lastFaildate = new SimpleDateFormat("yyyy:MM:dd:HH:mm:ss").format(lastFailedCalendar.getTime());
        Calendar startSyncCalendar = Calendar.getInstance();
        startSyncCalendar.setTimeInMillis(startSyncTime);
        String startSyncDate = new SimpleDateFormat("yyyy:MM:dd:HH:mm:ss").format(startSyncCalendar.getTime());
        String nowDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy:MM:dd:HH:mm:ss"));
        DebugUtil.i(TAG, "canStartSync,: displayname: " + mDisplayName + "  mFailedCount: " + mFailedCount + ", mLastFailedTime: " + lastFaildate + ", startSyncTime: " + startSyncDate + ", now: " + nowDate);
        return startSyncTime <= System.currentTimeMillis();
    }

    public boolean hasAmpFile() {
        boolean hasAmpFile = false;
        if (!TextUtils.isEmpty(mAmpFilePath)) {
            File file = new File(mAmpFilePath);
            hasAmpFile = file.exists();
        }
        DebugUtil.i(TAG, "hasAmpFile: " + hasAmpFile + ", mAmpFileName: " + FileUtils.getDisplayNameByPath(mAmpFilePath));
        return hasAmpFile;
    }

    public String checkAndSaveAmpFile(Context context, String ampString) {
        if (!hasAmpFile()) {
            File ampFile = AmpFileUtil.saveAmpFile(context, ampString);
            if (ampFile != null) {
                setAmpFilePath(ampFile.getAbsolutePath());
            }
        }
        DebugUtil.i(TAG, "checkAndSaveAmpFile: mAmpFileName: " + FileUtils.getDisplayNameByPath(mAmpFilePath));
        return mAmpFilePath;
    }

    public String updateAndSaveAmpFile(Context context, String ampString) {
        File ampFile = AmpFileUtil.saveAmpFile(context, ampString);
        if (ampFile != null) {
            setAmpFilePath(ampFile.getAbsolutePath());
        }
        DebugUtil.i(TAG, "updateAndSaveAmpFile: mAmpFileName: " + FileUtils.getDisplayNameByPath(mAmpFilePath));
        return mAmpFilePath;
    }

    public boolean deleteAmpFile() {
        boolean delsuc = true;
        if (!TextUtils.isEmpty(mAmpFilePath)) {
            File file = new File(mAmpFilePath);
            boolean fileExist = file.exists();
            if (fileExist) {
                delsuc = file.delete();
                DebugUtil.i(TAG, "deleteAmpFile: mAmpFileName: " + FileUtils.getDisplayNameByPath(mAmpFilePath) + ", delsuc: " + delsuc);
            }
        }
        return delsuc;
    }


    public String getOwnerPackageName() {
        return mOwnerPackageName;
    }

    public void setOwnerPackageName(String ownerPackageName) {
        this.mOwnerPackageName = ownerPackageName;
    }

    /**
     * 对比两个record业务自定义字段内容是否相同
     * @param record 比较对象
     * @return
     */
    public boolean isSameBusinessContent(Record record) {
        return Arrays.equals(this.mMarkData, record.mMarkData);
    }

    @Override
    public String toString() {
        return "Record{"
                + "mId=" + mId
                + ", mRelativePath='" + mRelativePath + '\''
                + ", mData='" + mData + '\''
                + ", mFileSize=" + mFileSize
                + ", mDisplayName='" + mDisplayName + '\''
                + ", mMimeType='" + mMimeType + '\''
                + ", mDateCreated=" + mDateCreated
                + ", mDateModied=" + mDateModied
                + ", mRecordType=" + mRecordType
                + ", mMarkData=" + Arrays.toString(mMarkData)
                + ", mAmpData=" + getLogStringForByteArray(mAmpData)
                + ", mDuration=" + mDuration
                + ", mBuckedDisplayName='" + mBuckedDisplayName + '\''
                + ", mDirty=" + mDirty
                + ", mDeleted=" + mDeleted
                + ", mMD5='" + mMD5 + '\''
                + ", mFileId='" + mFileId + '\''
                + ", mGlobalId='" + mGlobalId + '\''
                + ", mSyncType=" + mSyncType
                + ", mSyncUploadStatus=" + mSyncUploadStatus
                + ", mSyncDownloadStatus=" + mSyncDownloadStatus
                + ", mErrorCode=" + mErrorCode
                + ", mLevel=" + mLevel
                + ", mSyncDate=" + mSyncDate
                + ", mFailedCount=" + mFailedCount
                + ", mLastFailedTime=" + mLastFailedTime
                + ", mOwnerPackageName" + mOwnerPackageName
                + ", mSyncPrivateStatus=" + mSyncPrivateStatus
                + ", mIsMarkListShowing=" + mIsMarkListShowing
                + ", sysVersion=" + sysVersion
                + ", checkPayload=" + checkPayload
                + ", isDirectOn=" + mIsDirectOn
                + ", mDirectTime=" + mDirectTime
                + ", mIsRecycle=" + mIsRecycle
                + ", mGroupUuid=" + mGroupUuid
                + ", mGroupId=" + mGroupId
                + ", mOriginalName=" + mOriginalName
                + ", mCallerName=" + mCallerName
                + ", mCallCirleColor=" + mCallAvatarColor
                + ", mDeleteTime=" + mDeleteTime
                + ", mRecycleFilePath=" + mRecycleFilePath
                + ", mIsScanOshareText=" + mIsScanOshareText
                + '}';
    }


    private String getLogStringForByteArray(byte[] inputByteArray) {
        String ampString = "";
        if (inputByteArray != null) {
            if (inputByteArray.length > LOG_MAX_VISIBLE_LEN) {
                byte[] subAmpData = new byte[LOG_MAX_VISIBLE_LEN];
                System.arraycopy(inputByteArray, 0, subAmpData, 0, LOG_MAX_VISIBLE_LEN);
                ampString = Arrays.toString(subAmpData);
            } else {
                ampString = Arrays.toString(inputByteArray);
            }
        }
        return ampString;
    }

    /**
     * 判断文件是否是在OShare目录下
     */
    public boolean isOShareFile() {
        return ShareAction.getRelativePathOShare().equals(mRelativePath);
    }

}
