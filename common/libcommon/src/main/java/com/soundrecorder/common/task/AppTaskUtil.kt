/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  AppTaskUtil
 * * Description: 进程是否从其他应用跳转过来task判断
 * * Version: 1.0
 * * Date : 2025/5/28
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/5/28   1.0    build this module
 ****************************************************************/
package com.soundrecorder.common.task

object AppTaskUtil {
    const val TAG = "AppTaskUtil"
    val mMapIsFromOtherApp = HashMap<Int, Boolean>()
    var isSupportSmartName: Boolean = false

    @JvmStatic
    fun isFromOtherApp(taskId: Int): Boolean {
        if (mMapIsFromOtherApp.isEmpty()) {
            return false
        }
        return mMapIsFromOtherApp[taskId] ?: false
    }
}