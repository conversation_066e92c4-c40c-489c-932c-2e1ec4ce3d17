/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryRecordParam
 * Description:AISummaryRecordParam
 * Version: 1.0
 * Date: 2025/5/14
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/14      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.common.databean

import com.oplus.recorderlog.util.GsonUtil
import java.util.UUID

data class AISummaryRecordParam(
    val sessionId: String,
    val timeout: Long = TIMEOUT,
    val inputLanguage: String = LANGUAGE_DEFAULT,
    val outputLanguage: String = LANGUAGE_DEFAULT,
    val content: String,
) {
    companion object {
        private const val TAG = "AISummaryRecordParam"

        const val TIMEOUT = 5 * 60 * 1000L
        const val LANGUAGE_DEFAULT = "zh"

        fun toJson(param: AISummaryRecordParam): String {
            return GsonUtil.toJson(param)
        }

        fun toConvertAISummaryRecordParam(beanConvertText: BeanConvertText?): AISummaryRecordParam? {
            if (beanConvertText == null || beanConvertText.sublist.isNullOrEmpty()) {
                return null
            }

            val combinedText = beanConvertText.sublist.joinToString(separator = " ") { it.recgText }

            val sessionId = if (beanConvertText.traceId.isNullOrBlank()) {
                UUID.randomUUID().toString()
            } else {
                beanConvertText.traceId ?: UUID.randomUUID().toString()
            }

            if (combinedText.isNotEmpty()) {
                return AISummaryRecordParam(
                    sessionId = sessionId,
                    content = combinedText,
                )
            }
            return null
        }

        fun toConvertAISummaryRecordParam(convertContentItems: ArrayList<ConvertContentItem>?): AISummaryRecordParam? {
            if (convertContentItems.isNullOrEmpty()) {
                return null
            }

            val combinedText = convertContentItems.joinToString(separator = " ") { it.textContent }

            return AISummaryRecordParam(
                sessionId = UUID.randomUUID().toString(),
                content = combinedText,
            )
        }
    }

    override fun toString(): String {
        return "ConvertTitleParam(sessionId=$sessionId, content=$content," +
                "inputLanguage=$inputLanguage, outputLanguage:$outputLanguage)"
    }
}