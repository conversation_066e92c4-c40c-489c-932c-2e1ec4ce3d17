/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - UniDirectionalRecordUtils
 ** Description: XXXXXXXXXXXXXXXXXXXXX.
 ** Version: 1.0
 ** Date : 2024/9/27
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>	       <data> 	   <version >	     <desc>
 **  王靖(80266080)    2024/9/27     1.0          build this module
 ****************************************************************/
package com.soundrecorder.common.utils

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.media.AudioManager
import android.provider.Settings
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.OS12FeatureUtil

object UniDirectionalRecordUtils {
    const val TAG = "UniDirectionalRecordUtils"

    /*定向录音相关*/
    private const val UNIDIRECTIONAL_RECORDING_SUPPORT_KEY = "unidirectional_recording_support"
    private const val UNIDIRECTIONAL_RECORDING_SUPPORT_VALUE =
        "unidirectional_recording_support=true"
    private const val SET_UNIDIRECTIONAL_RECORDING_ON = "unidirectional_recording=on"
    private const val SET_UNIDIRECTIONAL_RECORDING_OFF = "unidirectional_recording=off"
    private const val SET_SENSOR_ANGLE = "device_foldangle"

    private const val ANGLE_GAP = 10
    private const val DEFAULT_ANGLE = -1000

    private var register = false
    private var lastAngle = DEFAULT_ANGLE

    /**
     * 是否支持定向录音
     */
    private var supportDirectRecording: Boolean? = null

    /**
     * 是否支持定向录音
     * os 15.0及以上且含定向feature为true
     */
    @JvmStatic
    fun isSupportDirectionalRecording(context: Context): Boolean {
        if (BaseUtil.isRealme()) {
            // Realme 不支持定向录音
            return false
        }
        if (supportDirectRecording == null) {
            supportDirectRecording =
                OS12FeatureUtil.isColorOS15OrLater() && hasDirectionalRecordingFeature(context)
        }
        return supportDirectRecording ?: false
    }

    /**
     * 读取设备feature是否支持定向录音
     */
    @JvmStatic
    fun hasDirectionalRecordingFeature(context: Context): Boolean {
        return runCatching {
            val supportValue =
                (context.getSystemService(Context.AUDIO_SERVICE) as? AudioManager)?.getParameters(
                    UNIDIRECTIONAL_RECORDING_SUPPORT_KEY
                )
            DebugUtil.d(TAG, "hasDirectionalRecordingFeature, value=$supportValue")
            return supportValue == UNIDIRECTIONAL_RECORDING_SUPPORT_VALUE
        }.getOrNull() ?: false
    }

    /**
     * 开启or关闭定向录音
     */
    @JvmStatic
    fun setUniDirectionalRecordingSwitch(context: Context, isTurnOn: Boolean): Boolean {
        if (!isSupportDirectionalRecording(context)) {
            DebugUtil.w(
                TAG,
                "setUniDirectionalRecordingSwitch not support directional"
            )
            return false
        }
        DebugUtil.i(
            TAG,
            "setUniDirectionalRecordingSwitch,${needRegisterAngleSensor()} isOn=$isTurnOn"
        )
        if (needRegisterAngleSensor()) {
            if (isTurnOn) {
                registerAngleWindowListener(context)
            } else {
                unregisterAngleListener(context)
            }
        }

        val param = if (isTurnOn) {
            SET_UNIDIRECTIONAL_RECORDING_ON
        } else {
            SET_UNIDIRECTIONAL_RECORDING_OFF
        }
        return runCatching {
            (context.getSystemService(Context.AUDIO_SERVICE) as? AudioManager)?.setParameters(param)
            true
        }.onFailure {
            DebugUtil.e(TAG, "setUniDirectionalRecordingSwitch $it")
        }.getOrNull() ?: false
    }

    @JvmStatic
    fun writeRecorderUnidirectionalModeSetting(context: Context, isOn: Boolean) {
        DebugUtil.i(TAG, "writeRecorderUnidirectionalModeSetting, isOn=$isOn")
        val value = if (isOn) 1 else 0
        runCatching {
            Settings.System.putInt(context.contentResolver, "sound_unidirectional_mode", value)
        }.onFailure {
            DebugUtil.e(TAG, "writeRecorderUnidirectionalModeSetting $it")
        }
    }

    /**
     * 大折手机才需要注册该sensor，并下发角度信息给音频驱动
     */
    @JvmStatic
    fun needRegisterAngleSensor(): Boolean {
        val result = FeatureOption.getIsFoldFeature() && !FeatureOption.hasSupportDragonfly()
        DebugUtil.i(
            TAG,
            "needRegisterAngle ${FeatureOption.getIsFoldFeature()} ${FeatureOption.hasSupportDragonfly()}"
        )
        return result
    }

    @JvmStatic
    private fun registerAngleWindowListener(context: Context) {
        if (register) {
            return
        }
        kotlin.runCatching {
            DebugUtil.i(TAG, "registerAngleWindowListener")
            val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
            val angleSensor = sensorManager.getDefaultSensor(Sensor.TYPE_HINGE_ANGLE)
            sensorManager.registerListener(
                eventListener,
                angleSensor,
                SensorManager.SENSOR_DELAY_FASTEST
            )
        }.onFailure {
            DebugUtil.e(TAG, "registerAngleWindowListener error : $it")
        }
        register = true
    }

    @JvmStatic
    private fun unregisterAngleListener(context: Context) {
        kotlin.runCatching {
            DebugUtil.i(TAG, "unregisterAngleListener")
            val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
            sensorManager.unregisterListener(eventListener)
            register = false
            lastAngle = DEFAULT_ANGLE
        }.onFailure {
            DebugUtil.e(TAG, "unregisterListener error : $it")
        }
    }

    @JvmStatic
    fun setWindowAngle(context: Context, angle: Int) {
        val param = "$SET_SENSOR_ANGLE=$angle"
        runCatching {
            DebugUtil.d(TAG, "setWindowAngle $param")
            (context.getSystemService(Context.AUDIO_SERVICE) as? AudioManager)?.setParameters(param)
        }.onFailure {
            DebugUtil.e(TAG, "setWindowAngle $it")
        }
    }

    private val eventListener = object : SensorEventListener {
        override fun onSensorChanged(event: SensorEvent) {
            kotlin.runCatching {
                if (Math.abs(event.values[0] - lastAngle) > ANGLE_GAP) {
                    DebugUtil.d(
                        TAG,
                        "onSensorChanged: " + event.values[0] + " lastAngle:" + lastAngle
                    )
                    lastAngle = event.values[0].toInt()
                    setWindowAngle(BaseApplication.getAppContext(), lastAngle)
                }
            }.onFailure {
                DebugUtil.e(TAG, "onSensorChanged exception:$it")
            }
        }

        override fun onAccuracyChanged(sensor: Sensor, accuracy: Int) {
            //
        }
    }
}