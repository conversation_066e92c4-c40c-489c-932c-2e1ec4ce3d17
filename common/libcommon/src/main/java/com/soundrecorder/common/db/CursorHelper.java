/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.soundrecorder.common.db;

import static com.soundrecorder.common.constant.Constants.RECORDINGS;
import static com.soundrecorder.common.constant.RecordConstant.MIMETYPE_ACC;
import static com.soundrecorder.common.constant.RecordConstant.MIMETYPE_ACC_ADTS;
import static com.soundrecorder.common.constant.RecordConstant.MIMETYPE_AMR;
import static com.soundrecorder.common.constant.RecordConstant.MIMETYPE_AMR_WB;
import static com.soundrecorder.common.constant.RecordConstant.MIMETYPE_MP3;
import static com.soundrecorder.common.constant.RecordConstant.MIMETYPE_WAV;

import android.content.Context;
import android.provider.MediaStore;

import androidx.annotation.NonNull;


import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.soundrecorder.base.utils.BaseUtil;

import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.constant.RecordModeConstant;
import com.soundrecorder.common.databean.GroupInfo;
import com.soundrecorder.common.utils.RecordModeUtil;

public class CursorHelper {
    public static final String DEFAULT_DIR = "/storage/emulated/0";
    private static final String[] PROJECTION = {MediaStore.Audio.Media._ID, MediaStore.Audio.Media.DISPLAY_NAME,
            MediaStore.Audio.Media.DATE_ADDED, MediaStore.Audio.Media.SIZE, MediaStore.Audio.Media.DATA,
            MediaStore.Audio.Media.DURATION, MediaStore.Audio.Media.DATE_MODIFIED, MediaStore.Audio.Media.MIME_TYPE};
    private static final String[] PROJECTION_Q = {MediaStore.Audio.Media._ID, MediaStore.Audio.Media.DISPLAY_NAME,
            MediaStore.Audio.Media.DATE_ADDED, MediaStore.Audio.Media.SIZE, MediaStore.Audio.Media.DATA,
            MediaStore.Audio.Media.DURATION, MediaStore.Audio.Media.DATE_MODIFIED, MediaStore.Audio.Media.RELATIVE_PATH,
            MediaStore.Audio.Media.OWNER_PACKAGE_NAME, MediaStore.Audio.Media.MIME_TYPE};
    private static final String TAG = "CursorHelper";
    private static final String[] sAcceptableAudioTypes = new String[]{MIMETYPE_AMR, MIMETYPE_WAV, MIMETYPE_MP3, MIMETYPE_AMR_WB, MIMETYPE_ACC_ADTS, MIMETYPE_ACC};
    private static final String sAcceptableAudioTypesSQL = " in (?, ?, ?, ?, ?, ?) ";

    private static String mergeRelativePath(String dir) {
        return addQuotation(RecordModeConstant.STORAGE_RECORD_ABOVE_Q + dir + File.separator);
    }

    private static String addQuotation(String src) {
        return "'" + src + "'";
    }

    public static String getWhereClauseFromRecorderTypeForSyncMediaAudioFiles(Context context, int recordType, String[] supportMimeTypes) {
        String where = null;
        if (BaseUtil.isAndroidQOrLater()) {
            where = getWhereFromRecorderTypeAboveQForSyncMediaAudioFiles(recordType, supportMimeTypes);
        } else {
            where = getWhereFromRecorderTypeBlowQForSyncMediaAudioFiles(context, recordType, supportMimeTypes);
        }
        DebugUtil.i(TAG, "getAllRecordWhereClause where is " + where);
        return where;
    }

    @NonNull
    private static String getWhereFromRecorderTypeBlowQForSyncMediaAudioFiles(Context context, int recordType, String[] supportMimeTypes) {
        String where;
        String phoneDir = BaseUtil.getPhoneStorageDir(context);
        if (phoneDir == null) {
            phoneDir = DEFAULT_DIR;
        }
        String sdcardDir = BaseUtil.getSDCardStorageDir(context);
        String relativePath = getRelativePathFromRecorderType(recordType);
        String normalPhoneDir = phoneDir + File.separator + relativePath;
        String normalSdDir = sdcardDir + File.separator + relativePath;
        StringBuilder whereStringSb = new StringBuilder();
        String mimeTypeInString = getMimeTypeInString(MediaStore.Audio.Media.MIME_TYPE, supportMimeTypes);
        whereStringSb.append(mimeTypeInString);
        if (whereStringSb.length() > 0) {
            whereStringSb.append(" AND (");
        } else {
            whereStringSb.append(" (");
        }
        whereStringSb.append(MediaStore.Audio.Media.DATA + " LIKE '" + normalPhoneDir + "%' OR "
                + MediaStore.Audio.Media.DATA + " LIKE '" + normalSdDir + "%'");
        whereStringSb.append(")");
        whereStringSb.append(" AND (" + MediaStore.Audio.Media.SIZE + "!=" + 0 + ")");
        where = whereStringSb.toString();
        return where;
    }


    private static String getRelativePathFromRecorderType(int recordType) {
        String result = RecordModeUtil.getRelativePathByRecordType(RecordModeConstant.RECORD_TYPE_STANDARD, true);
        switch (recordType) {
            case RecordModeConstant.RECORD_TYPE_STANDARD:
            case RecordModeConstant.RECORD_TYPE_CONFERENCE:
            case RecordModeConstant.RECORD_TYPE_INTERVIEW:
            case RecordModeConstant.RECORD_TYPE_CALL:
            case RecordModeConstant.RECORD_TYPE_OPPO_SHARE:
                result = RecordModeUtil.getRelativePathByRecordType(recordType, true);
                break;
            case RecordModeConstant.RECORD_TYPE_OTHER:
                result = RecordModeConstant.STORAGE_RECORD_ABOVE_Q;
                break;
            default:
                break;
        }
        return result;
    }

    private static String getWhereFromRecorderTypeAboveQForSyncMediaAudioFiles(int recordType, String[] supportMimeTypes) {
        String relativePath = getRelativePathFromRecorderType(recordType);
        String where = "";
        String mimeTypeInString = getMimeTypeInString(MediaStore.Audio.Media.MIME_TYPE, supportMimeTypes);
        switch (recordType) {
            case RecordModeConstant.RECORD_TYPE_STANDARD:
            case RecordModeConstant.RECORD_TYPE_CONFERENCE:
            case RecordModeConstant.RECORD_TYPE_INTERVIEW:
            case RecordModeConstant.RECORD_TYPE_CALL:
            case RecordModeConstant.RECORD_TYPE_OPPO_SHARE:
                where = mimeTypeInString
                        + " AND (" + MediaStore.Audio.Media.RELATIVE_PATH + " LIKE '" + relativePath + "%')"
                        + " AND (" + MediaStore.Audio.Media.SIZE + "!=" + 0 + ")";
                break;
            case RecordModeConstant.RECORD_TYPE_OTHER:
                where = mimeTypeInString
                        + " AND (" + MediaStore.Audio.Media.RELATIVE_PATH + " LIKE '" + relativePath + "')"
                        + " AND (" + MediaStore.Audio.Media.SIZE + "!=" + 0 + ")";
                break;
            default:
                break;
        }
        return where;
    }

    @NonNull
    public static String getMimeTypeInString(String mimeTypeColumnName, String[] supportMimeTypes) {
        String mimeTypeInString = "";
        StringBuilder whereStringSb = new StringBuilder();
        if (supportMimeTypes.length > 0) {
            whereStringSb.append("(");
            whereStringSb.append(mimeTypeColumnName + " in (");
            for (int i = 0; i < supportMimeTypes.length; i++) {
                whereStringSb.append("'" + supportMimeTypes[i] + "'");
                if (i < supportMimeTypes.length - 1) {
                    whereStringSb.append(", ");
                }
            }
            whereStringSb.append("))");
        }
        mimeTypeInString = whereStringSb.toString();
        return mimeTypeInString;
    }

    public static String getCallRecordWhereClause(Context context) {
        String where = getRecordQueryWhereClause(context, Collections.singletonList(RecordModeConstant.BUCKET_VALUE_CALL));
        DebugUtil.d(TAG, "getCallRecordWhereClause where is " + where);
        return where;
    }

    public static String getAllRecordContainCallWhereClause(Context context) {
        String where = getRecordQueryWhereClause(context, Collections.singletonList(RecordModeConstant.BUCKET_VALUE_ALL));
        DebugUtil.d(TAG, "getAllRecordContainWhereClause where is " + where);
        return where;
    }

    public static String getAllRecordForFilterAndQueryWhereClause(Context context) {
        return getAllRecordForFilterAndQueryWhereClause(context, RecordModeConstant.BUCKET_VALUE_ALL);
    }

    public static String getAllRecordForFilterAndQueryWhereClause(Context context, int filter) {
        List<Integer> supportFilterList = getAllSupportRecordForFilter(context, filter);

        String where = getRecordQueryWhereClause(context, supportFilterList);
        DebugUtil.d(TAG, "getAllRecordForFilterAndQueryWhereClause where is " + where);
        return where;
    }

    public static List<Integer> getAllSupportRecordForFilter(Context context, int filter) {
        boolean isSupportCallingRecords = RecordModeUtil.hasCallRecording(context);
        boolean isSupportThreeMode = RecordModeUtil.isSupportMultiRecordMode(context);

        List<Integer> supportFilterList = new ArrayList<>();
        // 首页显示域不受支持模式影响，4个目录都显示
        supportFilterList.add(RecordModeConstant.BUCKET_VALUE_STANDARD);
        supportFilterList.add(RecordModeConstant.BUCKET_VALUE_INTERVIEW);
        supportFilterList.add(RecordModeConstant.BUCKET_VALUE_CONFERENCE);
        supportFilterList.add(RecordModeConstant.BUCKET_VALUE_CALL);

        DebugUtil.d(TAG, "filter: " + filter
                + "; getAllRecordForFilter isSupportThreeMode: " + isSupportThreeMode
                + ", isSupportCallingRecords : " + isSupportCallingRecords
                + ", supportFilterList is " + supportFilterList);

        if (filter == RecordModeConstant.BUCKET_VALUE_ALL) {
            return supportFilterList;
        } else if (supportFilterList.contains(filter)) {
            return Collections.singletonList(filter);
        } else {
            return Collections.emptyList();
        }
    }

    private static List<Integer> checkSupportLists(List<Integer> supportFilter) {
        //if the filter list is empty or contains all
        List<Integer> newSupportFilter = new ArrayList<>();
        if (supportFilter == null || supportFilter.isEmpty()) {
            addAllSupportList(newSupportFilter);
        } else if (supportFilter.contains(RecordModeConstant.BUCKET_VALUE_ALL) || supportFilter.contains(GroupInfo.INT_DEFAULT_ALL)) {
            addAllSupportList(newSupportFilter);
        } else {
            newSupportFilter.addAll(supportFilter);
        }

        return newSupportFilter;
    }

    private static void addAllSupportList(List<Integer> supportFilter) {
        supportFilter.add(RecordModeConstant.BUCKET_VALUE_STANDARD);
        supportFilter.add(RecordModeConstant.BUCKET_VALUE_INTERVIEW);
        supportFilter.add(RecordModeConstant.BUCKET_VALUE_CONFERENCE);
        supportFilter.add(RecordModeConstant.BUCKET_VALUE_CALL);
    }

    public static StringBuilder getRecordForFilterClause(List<Integer> supportFilterList, String phoneDir, String sdcardDir) {
        String standardRelativePath = RecordModeUtil.getRelativePathByRecordType(RecordModeConstant.RECORD_TYPE_STANDARD, false);
        String meetingRelativePath = RecordModeUtil.getRelativePathByRecordType(RecordModeConstant.RECORD_TYPE_CONFERENCE, false);
        String interviewRelativePath = RecordModeUtil.getRelativePathByRecordType(RecordModeConstant.RECORD_TYPE_INTERVIEW, false);
        String callRelativePath = RecordModeUtil.getRelativePathByRecordType(RecordModeConstant.RECORD_TYPE_CALL, false);
        String oppoShareRelativePath = RecordModeUtil.getRelativePathByRecordType(RecordModeConstant.RECORD_TYPE_OPPO_SHARE, false);

        String normalPhoneDir = phoneDir + File.separator + standardRelativePath;
        String normalSdDir = sdcardDir + File.separator + standardRelativePath;
        String normalParentPhoneDir = phoneDir + File.separator + RECORDINGS;
        String normalParentSdDir = sdcardDir + File.separator + RECORDINGS;
        String interViewPhoneDir = phoneDir + File.separator + interviewRelativePath;
        String interViewSdDir = sdcardDir + File.separator + interviewRelativePath;
        String meetingPhoneDir = phoneDir + File.separator + meetingRelativePath;
        String meetingSdDir = sdcardDir + File.separator + meetingRelativePath;
        String callPhoneDir = phoneDir + File.separator + callRelativePath;
        String callSdDir = sdcardDir + File.separator + callRelativePath;
        String oppoSharePhoneDir = phoneDir + File.separator + oppoShareRelativePath;
        String oppoShareSdDir = sdcardDir + File.separator + oppoShareRelativePath;

        boolean isAndroidQOrLater = BaseUtil.isAndroidQOrLater();
        List<Integer> newSupportLists = checkSupportLists(supportFilterList);
        StringBuilder builder = new StringBuilder();
        //默认读取Recordings目录文件
        if (isAndroidQOrLater) {
            builder.append(addQuotation(RecordModeConstant.STORAGE_RECORD_ABOVE_Q));
        } else {
            builder.append(MediaStore.Audio.Media.DATA + " LIKE '")
                    .append(normalParentPhoneDir).append("%' OR ")
                    .append(MediaStore.Audio.Media.DATA + " LIKE '")
                    .append(normalParentSdDir).append("%'");
        }

        for (int pos = 0; pos < newSupportLists.size(); pos++) {
            switch (newSupportLists.get(pos)) {
                case RecordModeConstant.BUCKET_VALUE_STANDARD: {
                    if (isAndroidQOrLater) {
                        builder.append(",")
                                .append(mergeRelativePath(RecordModeConstant.DIR_STANDARD));
                        // 查询路径增加OShare目录
                        builder.append(",").append("'").append(RecordModeConstant.INSTANCE.getDIR_OPPO_SHARE())
                                .append(File.separator).append("'");
                    } else {
                        builder.append(" OR ")
                                .append(MediaStore.Audio.Media.DATA + " LIKE '")
                                .append(normalPhoneDir).append("%' OR ")
                                .append(MediaStore.Audio.Media.DATA + " LIKE '")
                                .append(normalSdDir).append("%'");
                        // 查询路径增加OShare目录
                        builder.append(" OR ")
                                .append(MediaStore.Audio.Media.DATA + " LIKE '")
                                .append(oppoSharePhoneDir).append("%' OR ")
                                .append(MediaStore.Audio.Media.DATA + " LIKE '")
                                .append(oppoShareSdDir).append("%'");
                    }
                    break;
                }
                case RecordModeConstant.BUCKET_VALUE_INTERVIEW: {
                    if (isAndroidQOrLater) {
                        builder.append(",")
                                .append(mergeRelativePath(RecordModeConstant.DIR_INTERVIEW));
                    } else {
                        builder.append(" OR ")
                                .append(MediaStore.Audio.Media.DATA + " LIKE '")
                                .append(interViewPhoneDir).append("%' OR ")
                                .append(MediaStore.Audio.Media.DATA + " LIKE '")
                                .append(interViewSdDir).append("%'");
                    }
                    break;
                }
                case RecordModeConstant.BUCKET_VALUE_CONFERENCE: {
                    if (isAndroidQOrLater) {
                        builder.append(",")
                                .append(mergeRelativePath(RecordModeConstant.DIR_MEETING));
                    } else {
                        builder.append(" OR ")
                                .append(MediaStore.Audio.Media.DATA + " LIKE '")
                                .append(meetingPhoneDir).append("%' OR ")
                                .append(MediaStore.Audio.Media.DATA + " LIKE '")
                                .append(meetingSdDir).append("%'");
                    }
                    break;
                }
                case RecordModeConstant.BUCKET_VALUE_CALL: {
                    if (isAndroidQOrLater) {
                        builder.append(",")
                                .append(mergeRelativePath(RecordModeConstant.DIR_CALL));
                    } else {
                        builder.append(" OR ")
                                .append(MediaStore.Audio.Media.DATA + " LIKE '")
                                .append(callPhoneDir).append("%' OR ")
                                .append(MediaStore.Audio.Media.DATA + " LIKE '")
                                .append(callSdDir).append("%'");
                    }
                    break;
                }
            }
        }
        DebugUtil.d(TAG, builder.toString());
        return builder;
    }

    private static StringBuilder getOplusRecordQueryWhereClause(StringBuilder builder, String phoneDir, String sdcardDir) {
        boolean isAndroidQOrLater = BaseUtil.isAndroidQOrLater();
        DebugUtil.d(TAG, "builder" + builder.toString());
        if (isAndroidQOrLater) {
            String oplusRelative = "";
            if (BaseUtil.isAndroidROrLater()) {
                oplusRelative = RecordModeConstant.OP_STORAGE_RECORD_ABOVE_AND_R;
            } else if (BaseUtil.isAndroidQ()) {
                oplusRelative = RecordModeConstant.OP_STORAGE_RECORD_BELOW_Q;
            }
            builder = mergeOPRelativePath(builder, ",", oplusRelative);
            DebugUtil.d(TAG, "builder" + builder.toString());
        } else {
            String oplusRelativePath = RecordModeConstant.OP_STORAGE_RECORD_BELOW_Q;
            String oneplusPhoneDir = phoneDir + File.separator + oplusRelativePath;
            String onePlusSdDir = sdcardDir + File.separator + oplusRelativePath;
            builder = builder.append(" OR ")
                    .append(MediaStore.Audio.Media.DATA + " LIKE '")
                    .append(oneplusPhoneDir).append("%' OR ")
                    .append(MediaStore.Audio.Media.DATA + " LIKE '")
                    .append(onePlusSdDir).append("%'");
        }
        return builder;
    }

    private static StringBuilder getMethordRecordQueryWhereClause(StringBuilder builder) {
        boolean isAndroidQOrLater = BaseUtil.isAndroidQOrLater();
        StringBuilder whereBuilder = new StringBuilder();
        whereBuilder.append("(" + MediaStore.Audio.Media.MIME_TYPE + " " + getsAcceptableAudioTypesSQL() + ") ");
        if (isAndroidQOrLater) {
            whereBuilder = getWhereRelativePathMethod(whereBuilder, builder);
        } else {
            whereBuilder = getWhereDATAMethod(whereBuilder, builder);
        }
        whereBuilder.append(" AND (" + MediaStore.Audio.Media.SIZE + "!=" + 0 + ")");
        return whereBuilder;
    }

    private static StringBuilder getWhereRelativePathMethod(StringBuilder whereBuilder, StringBuilder builder) {
        return whereBuilder.append(" AND ")
                .append(MediaStore.Audio.Media.RELATIVE_PATH)
                .append(" COLLATE NOCASE in (")
                .append(builder).append(")");
    }

    private static StringBuilder getWhereDATAMethod(StringBuilder whereBuilder, StringBuilder builder) {
        return whereBuilder.append(" AND (( ")
                .append(builder).append("))");
    }

    private static String getRecordQueryWhereClause(Context context, List<Integer> supportFilterList) {
        String phoneDir = BaseUtil.getPhoneStorageDir(context);
        if (phoneDir == null) {
            phoneDir = DEFAULT_DIR;
        }
        String sdcardDir = BaseUtil.getSDCardStorageDir(context);

        StringBuilder builder = getRecordForFilterClause(supportFilterList, phoneDir, sdcardDir);
        if (BaseUtil.isOnePlus()) {
            builder = getOplusRecordQueryWhereClause(builder, phoneDir, sdcardDir);
        }
        builder = getMethordRecordQueryWhereClause(builder);
        DebugUtil.d(TAG, "getRecordQueryWhereClause == " + builder.toString());
        return builder.toString();
    }

    private static StringBuilder mergeOPRelativePath(StringBuilder builder, String commaSymbol, String oneplusRelative) {
        builder.append(commaSymbol);
        builder.append(addQuotation(oneplusRelative + File.separator));
        return builder;
    }

    public static String[] getProjection() {
        if (BaseUtil.isAndroidQOrLater()) {
            return PROJECTION_Q.clone();
        } else {
            return PROJECTION.clone();
        }
    }

    public static String[] getsAcceptableAudioTypes() {
        return sAcceptableAudioTypes;
    }

    public static String getsAcceptableAudioTypesSQL() {
        return sAcceptableAudioTypesSQL;
    }
}
