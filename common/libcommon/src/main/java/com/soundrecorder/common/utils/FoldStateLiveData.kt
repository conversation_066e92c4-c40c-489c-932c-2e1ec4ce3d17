/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: FoldStateLiveData
 * Description:
 * Version: 1.0
 * Date: 2023/4/6
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/4/6 1.0 create
 */

package com.soundrecorder.common.utils

import android.database.ContentObserver
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import androidx.lifecycle.LiveData
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil

class FoldStateLiveData : LiveData<Boolean>() {
    companion object {
        private const val TAG = "FoldStateLiveData"

        /**
         * 折叠屏feature
         */
        private const val OPLUS_SYSTEM_FOLDING_MODE = "oplus_system_folding_mode"

        /**
         * 是否支持蜻蜓设备判断
         */
        private const val OPLUS_SOFTWARE_FOLD_REMAP_DISPLAY_DISABLED = "oplus.software.fold_remap_display_disabled"

        /**
         * 大屏
         */
        private const val SYSTEM_FOLDING_MODE_OPEN = 1

        /**
         * 小屏
         */
        private const val SYSTEM_FOLDING_MODE_CLOSE = 0

        @JvmStatic
        fun hasFoldingClose(): Boolean {
            val contentResolver = BaseApplication.getApplication().contentResolver
            return Settings.Global.getInt(contentResolver, OPLUS_SYSTEM_FOLDING_MODE, -1) == SYSTEM_FOLDING_MODE_CLOSE
        }
    }

    private val contentResolver = BaseApplication.getApplication().contentResolver

    private val contentObservable = object : ContentObserver(Handler(Looper.getMainLooper())) {
        override fun onChange(selfChange: Boolean) {
            hasFoldingClose().run {
                DebugUtil.d(TAG, "change to $this")
                if (value != this) {
                    value = this
                }
            }
        }
    }

    override fun onActive() {
        contentResolver.registerContentObserver(Settings.Global.getUriFor(OPLUS_SYSTEM_FOLDING_MODE), false, contentObservable)
    }

    override fun onInactive() {
        contentResolver.unregisterContentObserver(contentObservable)
    }
}