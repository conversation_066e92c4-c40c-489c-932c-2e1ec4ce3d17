/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ContactCallRecordUtil
 * Description:
 * Version: 1.0
 * Date: 2024/3/4
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2024/3/4 1.0 create
 */

package com.soundrecorder.common.utils.calling

import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.os.bundleOf
import com.soundrecorder.base.ext.displayName
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.databean.NoteData
import com.soundrecorder.common.databean.Record

/**
 * 电话本通话录音相关接口
 */
object ContactCallRecordUtil {
    const val TAG = "ContactCallRecordUtil"

    /**
     * 权限要求：com.oplus.permission.safe.PHONE
     * */
    private const val CALL_RECORDING_AUTHORITY = "com.oplus.contacts.CallRecordingProvider"
    private const val METHOD_GET_CALLING_UUID = "getCalllogUuid"
    private const val METHOD_SET_SUMMARY_FLAG = "setCalllogSummaryFlag"
    private const val PARAM_KEY_RECORD_PATH = "recording_path"
    /*电话本收到通知后会将contactsprovider通话摘要缓存数据写进通话记录表*/
    private const val ACTION_UPDATE_CONTACT_SUMMARY_FLAG = "com.oplus.contacts.external.ACTION_RECORDING_SUMMARY_UPDATED"
    private const val PACKAGE_NAME_CONTACT = "com.android.contacts"
    private const val PERMISSION_OPLUS_PHONE = "com.oplus.permission.safe.PHONE"
    private const val URI_CALL_RECORDING = "content://$CALL_RECORDING_AUTHORITY/call_recording"
    private const val CALL_RECORDING_COLUMN_PATH = "path"

    private val callRecordingUri by lazy { Uri.parse(URI_CALL_RECORDING) }

    /**
     *查询录音文件关联的通话记录uuid
     * @param context
     * @param displayName
     * 目前通话录音数据表存的录音数据path如下：
     * /storage/emulated/0/Music/Recordings/Call Recordings/中国移动-**********.mp3
     * 参数中传入的path使用文件名这一段即可（中国移动-**********.mp3、18826512971-**********.awb，....）
     * @return 查询到的call_uuid
     */
    @JvmStatic
    fun queryCallRecordingUUID(context: Context, displayName: String): String? {
        return runCatching {
            context.contentResolver.acquireUnstableContentProviderClient(CALL_RECORDING_AUTHORITY)?.use {
                it.call(
                    METHOD_GET_CALLING_UUID, null, bundleOf(PARAM_KEY_RECORD_PATH to displayName))
            }?.getString("calllog_uuid").apply {
                DebugUtil.i(TAG, "queryCallRecordingUUID displayName=$displayName, result=$this")
            }
        }.onFailure {
            DebugUtil.e(TAG, "queryCallRecordingUUID  exception = $it")
        }.getOrNull()
    }

    /**
     * 设置录音关联的通话记录有通话摘要
     * @param context
     * @param displayName
     * @param flag true：1有摘要 false：-1无摘要
     * ---注意：录音清除摘要不同步到电话本，电话本通话、录音摘要公共一个flag，且flag为一次性，一通电话下多个录音摘要，删除其中一个录音摘要，会导致电话本对应callid无摘要入口
     * @param notifyContact 是否通知电话本
     * @return true: 缓存成功 false：缓存失败
     */
    @JvmStatic
    fun setCallRecordingFlag(context: Context, displayName: String, flag: Boolean, notifyContact: Boolean = true): Boolean {
        return runCatching {
            context.contentResolver.acquireUnstableContentProviderClient(CALL_RECORDING_AUTHORITY)?.use {
                it.call(
                    METHOD_SET_SUMMARY_FLAG, null, bundleOf(PARAM_KEY_RECORD_PATH to displayName, "flag" to if (flag) 1 else -1))
            }?.getBoolean("flag_set").apply {
                DebugUtil.i(TAG, "setCallRecordingFlag,flag=$flag,result=$this,displayName=$displayName, notifyContact=$notifyContact")
                if (notifyContact && this == true) {
                    // 更新数据库成功，通知电话本更新UI
                    notifyContactUpdateSummaryFlag(context)
                }
            } ?: false
        }.onFailure {
            DebugUtil.e(TAG, "queryCallRecordingUUID  exception = $it")
        }.getOrNull() ?: false
    }

    /**
     * 发送广播给电话本，将contactsprovider通话摘要缓存数据写进通话记录表
     * 电话本进程如果当前是存活状态，那么收到这个广播后会立即更新call表，如果当前电话本进程没有存在，那么下一次进程冷启动时会去更新call表
     */
    @JvmStatic
    fun notifyContactUpdateSummaryFlag(context: Context) {
        DebugUtil.i(TAG, "notifyContactUpdateSummaryFlag")
        context.sendBroadcast(Intent(ACTION_UPDATE_CONTACT_SUMMARY_FLAG).apply {
            setPackage(PACKAGE_NAME_CONTACT)
        }, PERMISSION_OPLUS_PHONE)
    }

    @JvmStatic
    fun clearCallRecordFlag(context: Context, noteList: List<NoteData>?) {
        noteList?.run {
            DebugUtil.i(TAG, "clearCallRecordFlag, size=$size")
            forEach {
                it.mediaPath.displayName()?.let {
                    setCallRecordingFlag(context, it, false, false)
                }
            }
            notifyContactUpdateSummaryFlag(context)
        }
    }

    /**
     * 更新通话录音的存储路径。
     *
     * @param context 应用上下文，用于访问ContentResolver。
     * @param oldPath 旧路径，需要被更新的通话录音的当前存储路径。
     * @param newPath 新路径，通话录音将被更新为存储在此路径。
     */
    @JvmStatic
    fun updateCallingRecordPath(context: Context, oldPath: String, newPath: String) {
        if (oldPath == newPath) {
            return
        }
        DebugUtil.i(TAG, "updateCallingRecordPath: $oldPath to $newPath")
        runCatching {
            val updateValues = ContentValues().apply {
                put(CALL_RECORDING_COLUMN_PATH, newPath)
            }
            val resolver = context.contentResolver
            val rowsAffected = resolver.update(
                callRecordingUri,
                updateValues,
                "$CALL_RECORDING_COLUMN_PATH = ?",
                arrayOf(oldPath)
            )
            DebugUtil.i(TAG, "updateCallingRecordPath: result=$rowsAffected")
        }.onFailure {
            DebugUtil.e(TAG, "updateCallingRecordPath: ERROR! $it")
        }
    }

    @JvmStatic
    fun updatePathIfCallingRecord(
        context: Context,
        oldRecord: Record,
        newPath: String
    ) {
        if (!oldRecord.relativePath.endsWith(RecordModeConstant.DIR_CALL_END)) {
            return
        }
        updateCallingRecordPath(context, oldRecord.data, newPath)
    }
}