package com.oplus.recorderlog.log.openId

import android.content.Context
import android.text.TextUtils
import com.oplus.recorderlog.log.RecorderLogger
import com.oplus.stdid.sdk.StdIDSDK
import java.util.*

object OpenIdWraper {


    private var mDUID: String = ""
    private var mUUID: String = ""
    private var mGUID: String = ""
    private var mOUID: String = ""

    fun init(context: Context?): OpenIdWraper {
        StdIDSDK.init(context)
        val isSupport = StdIDSDK.isSupported()
        RecorderLogger.d("OpenIdUtils", "isSupport:$isSupport", false)
        if (isSupport) {
            mDUID = StdIDSDK.getDUID(context)
            mGUID = StdIDSDK.getGUID(context)
            mOUID = StdIDSDK.getOUID(context)
        }
        mUUID = UUID.randomUUID().toString()
        RecorderLogger.i(
            "OpenIdUtils",
            "oldId is empty: " + TextUtils.isEmpty(mDUID) + ", mBackUpId: " + mUUID + ", other1: " + mDUID + ", other2: " + mGUID + ", other3: " + mOUID, false)
        StdIDSDK.clear(context)
        return this
    }

    fun getDUID(): String {
        return mDUID
    }

    fun getUUID(): String {
        return mUUID
    }

    fun getGUID(): String {
        return mGUID
    }

    fun getOUID(): String {
        return mOUID
    }
}