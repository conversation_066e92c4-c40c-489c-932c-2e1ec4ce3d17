/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: UsbEnvironmentCompat
 * Description:
 * Version: 1.0
 * Date: 2023/10/7
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/10/7 1.0 create
 */

package com.oplus.recorderlog.util

import android.content.Context
import android.os.UserHandle
import com.heytap.addon.app.OplusActivityManager
import com.heytap.addon.multiuser.OplusMultiUserManager
import com.heytap.addon.os.OplusBuild
import com.heytap.addon.os.OplusUsbEnvironment
import com.oplus.recorderlog.log.RecorderLogger
import java.io.File

object AddonAdapterCompatQ : AddonAdapterCompat.IUsbEnvironment {

    override fun isVolumeMounted(context: Context?, path: String): Bo<PERSON>an {
        return OplusUsbEnvironment.isVolumeMounted(context, path)
    }

    override fun getInternalSdDirectory(context: Context?): File? {
        return OplusUsbEnvironment.getInternalSdDirectory(context)
    }

    override fun getInternalSdState(context: Context?): String? {
        return OplusUsbEnvironment.getInternalSdState(context)
    }

    override fun getInternalPath(context: Context?): String? {
        return OplusUsbEnvironment.getInternalPath(context)
    }

    override fun getExternalSdDirectory(context: Context?): File? {
        return OplusUsbEnvironment.getExternalSdDirectory(context)
    }

    override fun getExternalSdState(context: Context?): String? {
        return OplusUsbEnvironment.getExternalSdState(context)
    }

    override fun getExternalPath(context: Context?): String? {
        return OplusUsbEnvironment.getExternalPath(context)
    }

    override fun getExternalStorageDirectory(): File? {
        return OplusUsbEnvironment.getExternalStorageDirectory()
    }

    override fun isInternalSdMounted(context: Context?): Boolean {
        return getInternalSdState(context) == OplusUsbEnvironment.MEDIA_MOUNTED
    }

    override fun isExternalSdMounted(context: Context?): Boolean {
        return getExternalSdState(context) == OplusUsbEnvironment.MEDIA_MOUNTED
    }

    override fun isMultiSystemUserHandle(userHandler: UserHandle): Boolean {
        return OplusMultiUserManager.getInstance().isMultiSystemUserHandle(userHandler)
    }

    override fun getOplusOSVERSION(): Int {
        return OplusBuild.getOplusOSVERSION()
    }

    override fun getVirtualcommDeviceType(context: Context): Int {
        RecorderLogger.e("AddonAdapterCompatQ", "getVirtualcommDeviceType fail by android Q", false)
        return -1
    }

    override fun addBackgroundRestrictedInfo(packageName: String, pkgList: List<String>) {
        val oplusActivityManager = OplusActivityManager()
        oplusActivityManager.addBackgroundRestrictedInfo(packageName, pkgList)
    }
}