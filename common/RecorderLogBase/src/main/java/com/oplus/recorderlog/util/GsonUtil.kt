package com.oplus.recorderlog.util

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.oplus.recorderlog.log.RecorderLogger

@Suppress("TooGenericExceptionCaught")
object GsonUtil {

    private const val TAG = "GsonUtil"
    private val gson = Gson()

    @JvmStatic
    fun getGson(): Gson {
        return gson
    }

    @JvmStatic
    fun <T> toJson(t: T): String {
        return try {
            return gson.toJson(t)
        } catch (e: Exception) {
            RecorderLogger.e(TAG, "toJson error $e", false)
            ""
        }
    }

    @JvmStatic
    fun <T> fromJson(json: String?, cls: Class<T>): T? {
        return try {
            return gson.fromJson(json, cls)
        } catch (e: Exception) {
            RecorderLogger.e(TAG, "fromJson error $e", false)
            null
        }
    }

    @JvmStatic
    fun <T> toList(json: String?): List<T>? {
        return try {
            gson.fromJson(json, object : TypeToken<List<T>?>() {}.type)
        } catch (e: Exception) {
            RecorderLogger.e(TAG, "toList error $e", false)
            null
        }
    }
}