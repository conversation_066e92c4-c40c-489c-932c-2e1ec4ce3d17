<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/parent_start_guide_line"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.0" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/parent_end_guide_line"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.4" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/child_start_guide_line"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="1.0" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/child_end_guide_line"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="1.0" />


    <FrameLayout
        android:id="@+id/fl_left_container"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/view_subwindow_line"
        app:layout_constraintStart_toStartOf="@id/parent_start_guide_line"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view_subwindow_line"
        android:layout_width="@dimen/sub_window_center_line__width"
        android:layout_height="match_parent"
        android:background="?attr/couiColorDivider"
        android:visibility="invisible"
        android:forceDarkAllowed="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/parent_end_guide_line"
        app:layout_constraintTop_toTopOf="parent" />

    <!--android:clickable="true"防止点击事件穿透-->
    <FrameLayout
        android:id="@+id/fl_right_container"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/child_end_guide_line"
        app:layout_constraintStart_toStartOf="@id/child_start_guide_line"
        android:clickable="true"
        android:importantForAccessibility="no"
        app:layout_constraintTop_toTopOf="parent" />

    <ViewStub
        android:id="@+id/view_right_mask"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/child_end_guide_line"
        app:layout_constraintStart_toStartOf="@id/child_start_guide_line"
        android:clickable="true"
        android:importantForAccessibility="no"
        app:layout_constraintTop_toTopOf="parent"
        android:layout="@layout/view_stub_right_mask"/>
</androidx.constraintlayout.widget.ConstraintLayout>