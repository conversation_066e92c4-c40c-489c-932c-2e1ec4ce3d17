<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp16"
        android:paddingTop="@dimen/dp14"
        android:paddingEnd="@dimen/dp16"
        android:paddingBottom="@dimen/dp8">
        <TextView
            android:id="@+id/summary"
            android:textColor="@color/percent_55_black"
            android:fontFamily="sans-serif-medium"
            android:textFontWeight="500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/sp12" />

    </LinearLayout>

</layout>