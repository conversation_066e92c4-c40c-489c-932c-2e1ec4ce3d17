<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/seek_bar_area"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:visibility="visible"
    tools:visibility="visible">
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@id/playProgress"
        android:layout_toStartOf="@id/play_total_duration"
        android:layout_centerVertical="true"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingHorizontal="@dimen/dp9">
        <com.soundrecorder.common.widget.seekbar.COUISeekBarOS15
            android:id="@+id/seek_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="true"
            app:couiSeekBarProgressColor="@color/seek_bar_progress"
            app:couiSeekBarThumbColor="@color/seek_bar_thumb"
            app:couiSeekBarBackgroundHeight="@dimen/dp4"
            app:couiSeekBarProgressHeight="@dimen/dp4"
            app:couiSeekBarBackGroundEnlargeScale="6"
            android:layoutDirection="locale"/>
    </FrameLayout>

    <TextView
        android:id="@+id/playProgress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:textColor="@color/item_record_extra_info_color"
        style="@style/couiTextAppearanceArticleBody"
        android:textSize="@dimen/text_item_play_progress_size"
        android:fontFamily="sys-sans-en"
        android:fontFeatureSettings="tnum"
        android:lineHeight="@dimen/sp16"
        tools:text="00:00" />

    <TextView
        android:id="@+id/play_total_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:textSize="@dimen/text_item_play_progress_size"
        android:text="@{itemRecord.durationText()}"
        android:textColor="@color/item_record_extra_info_color"
        android:lineHeight="@dimen/sp16"
        style="@style/couiTextAppearanceArticleBody"
        android:fontFamily="sys-sans-en"
        android:fontFeatureSettings="tnum"
        tools:text="00:00" />
</merge>