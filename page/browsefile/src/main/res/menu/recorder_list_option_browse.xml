<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <item
        android:id="@+id/item_cancel"
        android:title="@string/cancel"
        android:visible="false"
        app:showAsAction="always" />

    <item
        android:id="@+id/item_search"
        android:icon="@drawable/menu_ic_search"
        android:title="@string/search"
        app:actionViewClass="com.coui.appcompat.searchview.COUISearchBar"
        app:showAsAction="always|collapseActionView" />

    <item
        android:id="@+id/item_speaker"
        android:icon="@drawable/ic_big_speaker_black"
        android:title="@string/talk_back_speaker_play"
        app:showAsAction="always" />

    <item
        android:id="@+id/item_select_all"
        android:title="@string/select_all"
        android:visible="false"
        app:showAsAction="always" />

    <item
        android:id="@+id/item_edit_big"
        android:icon="@drawable/menu_ic_edit"
        android:visible="false"
        android:title="edit"
        app:showAsAction="always"/>

    <item
        android:id="@+id/item_setting_big"
        android:icon="@drawable/menu_ic_setting"
        android:visible="false"
        android:title="setting"
        app:showAsAction="always"/>

    <group
        android:id="@+id/group_0">
        <item
            android:id="@+id/item_edit"
            android:title="@string/edit_new"
            app:showAsAction="never" />

        <item
            android:id="@+id/item_setting"
            android:title="@string/app_name_settings"
            app:showAsAction="never" />
    </group>

    <group
        android:id="@+id/group_1">

        <item
            android:id="@+id/group_sort_by"
            android:title="@string/menu_group_sort"
            app:showAsAction="never"
            android:visible="true">
            <menu>
                <item
                    android:id="@+id/not_grouping"
                    android:checked="true"
                    android:checkable="true"
                    android:title="@string/group_not_sort"
                    app:showAsAction="never" />

                <item
                    android:id="@+id/grouping_by_contact"
                    android:title="@string/group_sort_by_contact"
                    app:showAsAction="never"
                    android:checkable="true"/>
            </menu>
        </item>
    </group>
</menu>