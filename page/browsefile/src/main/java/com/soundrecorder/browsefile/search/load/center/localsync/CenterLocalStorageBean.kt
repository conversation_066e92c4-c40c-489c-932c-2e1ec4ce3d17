/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/8/31
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.search.load.center.localsync

import com.soundrecorder.base.utils.DebugUtil

/**
 * item of filter saved in sp
 * format: Aes(recordId1-lastModify1,recordId2-lastModify2,recordId3-lastModify3)
 */
class CenterLocalStorageBean(val originList: MutableList<CenterLocalStorageItem>?) {

    companion object {
        private const val TAG = "CenterLocalStorageBean"
        private const val KEY_CONNECT_DOT = ","

        /**
         * convert the str to CenterLocalStorageBean
         */
        fun convertStrToBean(curStr: String): CenterLocalStorageBean? {
            DebugUtil.d(TAG, "convertStrToBean curStr is $curStr")
            return if (curStr.isNotEmpty()) {
                CenterLocalStorageBean(convertStrToList(curStr))
            } else {
                null
            }
        }

        /**
         * convert the media info str saved in local storage to list
         * format: zip(recordId1-lastModify1,recordId2-lastModify2,recordId3-lastModify3)
         */
        private fun convertStrToList(curStr: String?): MutableList<CenterLocalStorageItem>? {
            if (curStr.isNullOrEmpty()) {
                return null
            }

            val originStr = GzipUtil.uncompress(curStr)
            if (originStr.isNullOrEmpty()) {
               return null
            }

            val items = originStr.split(KEY_CONNECT_DOT)
            if (items.isNullOrEmpty()) {
                return null
            }

            return mutableListOf<CenterLocalStorageItem>().apply {
                for (item in items) {
                    CenterLocalStorageItem.convertToItem(item)?.let {
                        add(it)
                    }
                }
            }
        }
    }

    private var zipStr: String? = null

    private fun initGzipStrIfNecessary() {
        if (zipStr.isNullOrEmpty() && !originList.isNullOrEmpty()) {
            val builder = StringBuilder()
            for (item in originList) {
                builder.append(item.toString()).append(KEY_CONNECT_DOT)
            }
            builder.substring(0, builder.length - 1).let {
                DebugUtil.d(TAG, "initGzipStrIfNecessary, origin is $it")
                zipStr = GzipUtil.compress(it)
            }
        }
    }

    override fun toString(): String {
        initGzipStrIfNecessary()
        return zipStr ?: ""
    }
}