/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ShadowWrapperView
 * Description:
 * Version: 1.0
 * Date: 2023/12/20
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/12/20 1.0 create
 */

package com.soundrecorder.browsefile.drag.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import com.coui.appcompat.imageview.COUIRoundImageView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.R
import kotlin.math.min
import kotlin.math.pow

class ShadowWrapperView(context: Context, attrs: AttributeSet?) : View(context, attrs) {
    companion object {
        private const val TAG = "ShadowWrapperView"
        private const val ITEM_SCALE = 0.90
        private const val MAX_SHADOW = 3
        private const val SHADOW_ALPHA = 0.85F
        private const val SHADOW = 2
    }

    private val mShadowPaint: Paint = Paint(Paint.ANTI_ALIAS_FLAG)

    private var wrapperItemCount: Int = 0
    private var wrapperWidth = -1F
    private var wrapperHeight = -1F
    private var itemMarginTop =
            resources.getDimensionPixelOffset(R.dimen.drag_shadow_item_marginTop).toFloat()

    private var layoutPadding =
            resources.getDimensionPixelOffset(R.dimen.drag_shadow_item_layout_padding).toFloat()

    private val shadowDy =
            resources.getDimensionPixelOffset(R.dimen.drag_shadow_dy).toFloat()

    private var shadowBgView: COUIRoundImageView? = null
    var shadowContent: ShadowContent? = null

    init {
        alpha = SHADOW_ALPHA
    }

    fun updateShadowContent(shadowContent: ShadowContent) {
        this.shadowContent = shadowContent
        wrapperItemCount = min(shadowContent.getItemCount(), MAX_SHADOW)
        wrapperWidth = (shadowContent.getContentWidth() * shadowContent.getScale())
        wrapperHeight =
                shadowContent.getContentHeight() * shadowContent.getScale() + getItemsExtraHeight()
        mShadowPaint.setShadowLayer(
                shadowContent.getShadowBlur(),
                0F,
                shadowDy,
                shadowContent.getShadowColor()
        )

        if (wrapperItemCount > 1) {
            shadowBgView = COUIRoundImageView(shadowContent.getContext()).also { sv ->
                sv.measure(
                        MeasureSpec.makeMeasureSpec(
                                shadowContent.getContentWidth().toInt(),
                                MeasureSpec.EXACTLY
                        ),
                        MeasureSpec.makeMeasureSpec(
                                shadowContent.getContentHeight().toInt(),
                                MeasureSpec.EXACTLY
                        )
                )
                sv.setType(SHADOW)
                sv.background = null
                val defaultDrawable =
                        ContextCompat.getDrawable(
                                shadowContent.getContext(),
                                R.drawable.bg_drag_item
                        ) as GradientDrawable
                sv.background = defaultDrawable
                sv.setImageDrawable(null)
            }
        }
        if (shadowContent.getScale() > 1.0F) {
            layoutPadding *= shadowContent.getScale()
        }
        DebugUtil.d(
                TAG,
                "wrapperWidth=$wrapperWidth wrapperHeight=$wrapperHeight " +
                        "vw=${shadowContent.getContentWidth()} vh=${shadowContent.getContentHeight()} " +
                        "eh=${getItemsExtraHeight()} p=$layoutPadding"
        )
        postInvalidate()
    }

    fun getItemsExtraHeight(): Float =
            if (wrapperItemCount < 1) 0F else (wrapperItemCount - 1) * itemMarginTop

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        if (shadowContent == null) {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        }
        setMeasuredDimension(
                MeasureSpec.makeMeasureSpec(
                        (wrapperWidth + layoutPadding * 2).toInt(), MeasureSpec.EXACTLY
                ), MeasureSpec.makeMeasureSpec(
                (wrapperHeight + layoutPadding * 2).toInt(), MeasureSpec.EXACTLY
        )
        )
    }

    override fun onDraw(canvas: Canvas) {

        shadowContent?.let { sc ->
            canvas.save()
            canvas.translate(layoutPadding, layoutPadding)
            mShadowPaint.color = sc.getContentBgColor()
            for (index in (wrapperItemCount - 1) downTo 1) {
                drawItem(canvas, index, sc)
            }
            drawContent(canvas, sc)
            canvas.restore()
        } ?: super.onDraw(canvas)
    }

    private fun drawContent(canvas: Canvas, content: ShadowContent) {
        canvas.save()
        canvas.scale(content.getScale(), content.getScale())
        if (!content.isDrawContentBg()) {
            mShadowPaint.color = Color.TRANSPARENT
        }
        canvas.drawRoundRect(
                0F,
                0F,
                content.getContentWidth(),
                content.getContentHeight(),
                content.getShadowRadius(),
                content.getShadowRadius(),
                mShadowPaint
        )

        content.drawContent(canvas)
        canvas.restore()
    }

    private fun drawItem(canvas: Canvas?, index: Int, content: ShadowContent) {
        canvas?.run {
            val save = save()

            val pow = ITEM_SCALE.pow(index.toDouble())
            shadowBgView?.let { vw ->
                val reduceScale = (1.0F - pow) * content.getScale() / 2
                translate(
                        (reduceScale * vw.width).toFloat(),
                        index * itemMarginTop + (reduceScale * vw.height).toFloat() * 2F
                )
                (content.getScale() * pow).toFloat().let {
                    scale(it, it)
                }
                drawRoundRect(
                        0F,
                        0F,
                        vw.width.toFloat(),
                        vw.height.toFloat(),
                        content.getShadowRadius(),
                        content.getShadowRadius(),
                        mShadowPaint
                )
                vw.draw(canvas)
            }

            restoreToCount(save)
        }
    }

    fun measureShadowBg() {
        shadowContent?.let { sc ->
            DebugUtil.d(TAG, "measureShadowBg")
            shadowBgView?.run {
                measure(
                        MeasureSpec.makeMeasureSpec(sc.getContentWidth().toInt(), MeasureSpec.EXACTLY),
                        MeasureSpec.makeMeasureSpec(sc.getContentHeight().toInt(), MeasureSpec.EXACTLY)
                )
                layout(0, 0, sc.getContentWidth().toInt(), sc.getContentHeight().toInt())
            }
        }
    }

    interface ShadowContent {
        fun getShadowRadius(): Float
        fun getShadowColor(): Int
        fun getScale(): Float
        fun drawContent(canvas: Canvas)
        fun getContext(): Context
        fun getItemCount(): Int
        fun getContentWidth(): Float
        fun getContentHeight(): Float
        fun getShadowBlur(): Float
        fun getContentBgColor(): Int
        fun isDrawContentBg(): Boolean
        fun release() {
            // default empty impl
        }
    }
}