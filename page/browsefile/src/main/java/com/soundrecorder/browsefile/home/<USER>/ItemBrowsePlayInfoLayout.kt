/*********************************************************************
 * * Copyright (C), 2022, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/3/2
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home.view

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.accessibility.AccessibilityEvent
import android.view.animation.PathInterpolator
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.view.isVisible
import com.coui.appcompat.seekbar.COUISeekBar
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.currentInMsFormatTimeExclusive
import com.soundrecorder.base.ext.durationInMsFormatTimeExclusive
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.RecorderTextUtils
import com.soundrecorder.browsefile.R
import com.soundrecorder.common.utils.PathInterpolatorHelper
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.common.widget.seekbar.COUISeekBarOS15

/**
 * 首页快捷播放布局，包括播放进度、当前时间和总时间
 */
class ItemBrowsePlayInfoLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr) {

    companion object {
        const val TAG = "ItemBrowsePlayInfoLayout"

        const val PROGRESS_MAX = 1000
        const val MS_100 = 100
    }

    private val seekBar: COUISeekBarOS15
    private val playProgress: TextView
    private val playTotalDuration: TextView

    var onSeekBarListener: OnSeekBarChangeListener? = null

    var duration: Long = 0
        set(value) {
            field = value
            playTotalDuration.text = value.durationInMsFormatTimeExclusive(true)
        }

    private var enterAnim: Animator? = null
    private var exitAnim: Animator? = null

    init {
        LayoutInflater.from(context).inflate(
            R.layout.item_play_info,
            this,
            true
        )

        playTotalDuration = findViewById(R.id.play_total_duration)
        playProgress = findViewById(R.id.playProgress)
        seekBar = findViewById(R.id.seek_bar)

        initSeekBar()
    }

    private fun initSeekBar() {
        seekBar.apply {
            max = PROGRESS_MAX
            isHapticFeedbackEnabled = false
            //talkback
            contentDescription = RecorderTextUtils.getNewProgressDescription(context, progress.toLong())
            accessibilityDelegate = object : AccessibilityDelegate() {
                override fun sendAccessibilityEvent(host: View, eventType: Int) {
                    if (eventType == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
                        contentDescription =
                            RecorderTextUtils.getNewProgressDescription(context, progress.toLong())
                    }
                    super.sendAccessibilityEvent(host, eventType)
                }
            }
            //设置seekbar最大拖拽距离
            setMaxMovingDistance(ViewUtils.dp2px(NumberConstant.NUM_F2_0).toInt())
            //seekListener
            setOnSeekBarChangeListener(object : COUISeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(
                    seekBar: COUISeekBar,
                    progress: Int,
                    fromUser: Boolean
                ) {
                    val curTime = seekBarProgressToDuration(progress)
                    updatePlayProgressText(curTime)
                }

                override fun onStartTrackingTouch(seekBar: COUISeekBar) {
                    onSeekBarListener?.updateTouchSeekbar(true)
                    if (exitAnim?.isRunning == true || <EMAIL> != VISIBLE) {
                        DebugUtil.d(TAG, "onStartTrackingTouch: exitAnim is running or play area not visible ")
                        return
                    }
                    onSeekBarListener?.onStartTrackingTouch()
                }

                override fun onStopTrackingTouch(seekBar: COUISeekBar) {
                    onSeekBarListener?.updateTouchSeekbar(false)
                    if (exitAnim?.isRunning == true || <EMAIL> != VISIBLE) {
                        DebugUtil.d(TAG, "onStopTrackingTouch: exitAnim is running or play area not visible ")
                        return
                    }
                    var curTime = seekBarProgressToDuration(progress)
                    if ((curTime > MS_100) && (curTime == duration)) {
                        curTime -= MS_100
                    }
                    DebugUtil.i(TAG, "onStopTrackingTouch = $curTime")
                    onSeekBarListener?.onSeekToTime(curTime)
                }
            })
        }
    }

    fun updatePlayProgress(curTime: Long?): Boolean {
        if (onSeekBarListener?.getTrackingTouch() == true) {
            //When the finger drags the seek bar, don't update progress.
            DebugUtil.i(TAG, "When the finger drags the seek bar, no update is progress.")
            return false
        }
        seekBar.progress = durationToSeekBarProgress(curTime ?: 0)
        updatePlayProgressText(curTime ?: 0)
        return true
    }

    private fun updatePlayProgressText(curTime: Long) {
        playProgress.text = curTime.currentInMsFormatTimeExclusive(duration)
    }

    private fun durationToSeekBarProgress(timeInMs: Long): Int {
        return if ((duration > 0) && (duration - timeInMs >= MS_100)) {
            (timeInMs / duration.toFloat() * PROGRESS_MAX).toInt()
        } else {
            PROGRESS_MAX
        }
    }

    private fun seekBarProgressToDuration(progress: Int): Long {
        return (progress / PROGRESS_MAX.toFloat() * duration).toLong()
    }

    fun isNotCompleteVisible(): Boolean {
        return !isVisible || layoutParams.height < getLayoutHeight()
    }

    private fun getLayoutHeight(): Int {
        val resources = BaseApplication.getApplication().resources
        val defaultHeight = resources.getDimensionPixelSize(R.dimen.seekbar_layout_height)

        /**
         * offset 字体大小切换显示不全容错值 16sp和16dp不对等
         */
        val offset = resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.sp16) - resources.getDimensionPixelSize(
            com.soundrecorder.common.R.dimen.dp16
        )
        return defaultHeight + (if (offset < 0) 0 else offset)
    }

    /**
     * 展开动效
     * 如果正在展开，不处理
     * 如果在关闭，取消掉关闭，并从当前状态展开
     */
    fun startOrContinueEnterAnim(
        extraAnimator: Array<Animator>? = null,
        animatorListener: AnimatorListenerAdapter? = null
    ) {
        if (enterAnim?.isRunning == true) {
            DebugUtil.i(TAG, "startOrContinueEnterAnim, enter anim is running")
            return
        }

        cancelExitAnimation()
        continueEnterAnim(extraAnimator, animatorListener)
    }

    private fun continueEnterAnim(
        extraAnimator: Array<Animator>? = null,
        animatorListener: AnimatorListenerAdapter? = null
    ) {
        //从当前高度执行
        DebugUtil.i(TAG, "continueEnterAnim")
        val cardHeightAnim = ValueAnimator.ofInt(layoutParams.height, getLayoutHeight()).apply {
            duration = 367
            interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
            addUpdateListener {
                setItemHeight(it.animatedValue as Int)
            }
        }

        val textAlphaAnim = ValueAnimator.ofFloat(playProgress.alpha, 1f).apply {
            duration = 250
            startDelay = 117
            interpolator = PathInterpolator(0.5f, 0f, 0.67f, 1f)
            addUpdateListener {
                playProgress.alpha = it.animatedValue as Float
                playTotalDuration.alpha = it.animatedValue as Float
            }
        }
        val seekBarAlphaAnim = ObjectAnimator.ofFloat(seekBar, "alpha", seekBar.alpha, 1f).apply {
            duration = 317
            startDelay = 50
            interpolator = PathInterpolator(0.5f, 0f, 0.7f, 1f)
        }

        val animatorList = mutableListOf<Animator>().also {
            if (!extraAnimator.isNullOrEmpty()) {
                it.addAll(extraAnimator)
            }
            it.add(cardHeightAnim)
            it.add(textAlphaAnim)
            it.add(seekBarAlphaAnim)
        }
        enterAnim = AnimatorSet().also {
            it.playTogether(animatorList)
            it.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animation: Animator) {
                    animatorListener?.onAnimationStart(animation)
                    visibility = View.VISIBLE
                }

                override fun onAnimationEnd(animation: Animator) {
                    animatorListener?.onAnimationEnd(animation)
                    visibility = View.VISIBLE
                    animation.removeListener(this)
                    enterAnim = null
                }
            })
            it.start()
        }
    }

    /**
     * 关闭动效
     * 如果正在关闭，不处理
     * 如果在展开，取消掉展开，并从当前状态关闭
     */
    fun startOrContinueExitAnim(
        extraAnimator: Array<Animator>? = null,
        animatorListener: AnimatorListenerAdapter? = null
    ) {
        if (exitAnim?.isRunning == true) {
            DebugUtil.i(TAG, "startOrContinueEnterAnim, exit anim is running")
            return
        }

        cancelEnterAnimation()
        continueExitAnim(extraAnimator, animatorListener)
    }

    private fun continueExitAnim(
        extraAnimator: Array<Animator>? = null,
        animatorListener: AnimatorListenerAdapter? = null
    ) {
        DebugUtil.i(TAG, "continueExitAnim")
        val cardHeightAnim = ValueAnimator.ofInt(layoutParams.height, 0).apply {
            duration = 367
            interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
            addUpdateListener {
                setItemHeight(it.animatedValue as Int)
            }
        }
        val textAlphaAnim = ValueAnimator.ofFloat(playProgress.alpha, 0f).apply {
            duration = 100
            interpolator = PathInterpolator(0.2f, 0f, 1f, 1f)
            addUpdateListener {
                playProgress.alpha = it.animatedValue as Float
                playTotalDuration.alpha = it.animatedValue as Float
            }
        }

        val seekBarAlphaAnim = ObjectAnimator.ofFloat(seekBar, "alpha", seekBar.alpha, 0f).apply {
            duration = 133
            interpolator = PathInterpolator(0.33f, 0f, 0.7f, 1f)
        }

        val animatorList = mutableListOf<Animator>().also {
            if (!extraAnimator.isNullOrEmpty()) {
                it.addAll(extraAnimator)
            }
            it.add(cardHeightAnim)
            it.add(textAlphaAnim)
            it.add(seekBarAlphaAnim)
        }
        exitAnim = AnimatorSet().also {
            it.playTogether(animatorList)
            it.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animation: Animator) {
                    DebugUtil.d(TAG, "onAnimationStart")
                    animatorListener?.onAnimationStart(animation)
                    visibility = View.VISIBLE
                }

                override fun onAnimationEnd(animation: Animator) {
                    DebugUtil.d(TAG, "onAnimationEnd")
                    animatorListener?.onAnimationEnd(animation)
                    visibility = View.GONE
                    /*停止fling等滚动，避免播放A，拖动seekbar快速切B-A，A才执行stopTracking，导致又从上次位置继续播放*/
                    seekBar.stopPhysicsMove()
                    animation.removeListener(this)
                    exitAnim = null
                }
            })
            it.start()
        }
    }

    private fun setItemHeight(height: Int) {
        val tempLayoutParams = layoutParams
        if (tempLayoutParams.height != height) {
            tempLayoutParams.height = height
            layoutParams = tempLayoutParams
        }
    }

    fun setExpandState() {
        playProgress.alpha = 1f
        playTotalDuration.alpha = 1f
        seekBar.alpha = 1f
        setItemHeight(getLayoutHeight())
        visibility = View.VISIBLE
    }

    fun setShrinkState() {
        playProgress.alpha = 0f
        playTotalDuration.alpha = 0f
        seekBar.alpha = 0f
        setItemHeight(0)
        visibility = View.GONE
    }

    fun getPlayProgress(): TextView {
        return playProgress
    }

    fun getPlayTotalDuration(): TextView {
        return playTotalDuration
    }

    fun cancelAnimation() {
        cancelEnterAnimation()
        cancelExitAnimation()
    }

    private fun cancelEnterAnimation() {
        enterAnim?.cancel()
        enterAnim?.removeAllListeners()
        enterAnim = null
    }

    private fun cancelExitAnimation() {
        exitAnim?.cancel()
        exitAnim?.removeAllListeners()
        exitAnim = null
    }

    fun isAnimating(): Boolean {
        return !(enterAnim == null && exitAnim == null)
    }
}

interface OnSeekBarChangeListener {
    fun onStartTrackingTouch()
    fun onSeekToTime(curTime: Long)
    fun updateTouchSeekbar(touch: Boolean)
    fun getTrackingTouch(): Boolean
}