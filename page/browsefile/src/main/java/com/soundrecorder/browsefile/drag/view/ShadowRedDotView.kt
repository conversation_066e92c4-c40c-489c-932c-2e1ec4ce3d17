/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ShadowRedDotView
 * Description:
 * Version: 1.0
 * Date: 2023/12/20
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/12/20 1.0 create
 */

package com.soundrecorder.browsefile.drag.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import com.coui.appcompat.reddot.COUIHintRedDot
import com.coui.appcompat.roundRect.COUIRoundRectUtil
import com.soundrecorder.browsefile.R

class ShadowRedDotView(context: Context, attrs: AttributeSet?) :
        COUIHintRedDot(context, attrs) {
    private val mCornerRadius: Int
    private val innerColor = resources.getColor(com.support.appcompat.R.color.coui_color_primary_green)
    private val outerColor = resources.getColor(R.color.drag_shadow_dot_edge_color)
    private val curRectF = RectF()
    private val stokeSize = resources.getDimensionPixelOffset(R.dimen.drag_shadow_stroke)

    private val strokePaint = Paint(Paint.ANTI_ALIAS_FLAG).also {
        it.color = innerColor
        it.style = Paint.Style.FILL
        val a = context.obtainStyledAttributes(attrs, com.support.reddot.R.styleable.COUIHintRedDot, 0, 0)
        mCornerRadius = a.getDimensionPixelSize(
                com.support.reddot.R.styleable.COUIHintRedDot_couiCornerRadius,
                0
        )
        a.recycle()
    }

    override fun onDraw(canvas: Canvas) {
        if (width == height) {
            drawCircle(canvas)
        } else {
            drawPath(canvas)
        }

        super.onDraw(canvas)
    }

    private fun drawPath(canvas: Canvas) {
        curRectF.left = 0F
        curRectF.top = 0F
        curRectF.right = width.toFloat()
        curRectF.bottom = height.toFloat()
        var path = COUIRoundRectUtil.getInstance().getPath(curRectF, mCornerRadius.toFloat())
        strokePaint.color = outerColor
        canvas.drawPath(path, strokePaint)

        curRectF.left = stokeSize.toFloat()
        curRectF.top = curRectF.left
        curRectF.right = width.toFloat() - curRectF.left
        curRectF.bottom = height.toFloat() - curRectF.left
        path = COUIRoundRectUtil.getInstance().getPath(curRectF, mCornerRadius - curRectF.left)
        strokePaint.color = innerColor
        canvas.drawPath(path, strokePaint)
    }

    private fun drawCircle(canvas: Canvas?) {
        strokePaint.color = outerColor

        canvas?.drawCircle(
                width.toFloat() / 2,
                height.toFloat() / 2,
                width.toFloat() / 2,
                strokePaint
        )

        strokePaint.color = innerColor
        canvas?.drawCircle(
                width.toFloat() / 2,
                height.toFloat() / 2,
                width.toFloat() / 2 - stokeSize,
                strokePaint
        )
    }
}