/******************************************************************
 * Copyright (C), 2020-2021, OPPO Mobile Comm Corp., Ltd
 * VENDOR_EDIT
 * File: - SearchSortUtils.java
 * Description:
 * Version: 1.0
 * Date :  2020/10/12
 * Author: <EMAIL>
 **
 * ---------------- Revision History: ---------------------------
 *      <author>        <data>        <version >        <desc>
 *    Yongjiang,Lu      2020/10/12          1.0         build this module
 ********************************************************************/

package com.soundrecorder.browsefile.search.load.mediadb;

import android.database.Cursor;
import android.provider.MediaStore;
import android.text.TextUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;

import com.soundrecorder.base.ext.ExtKt;
import com.soundrecorder.browsefile.search.load.ItemSearchViewModel;
import com.soundrecorder.common.constant.DatabaseConstant;

import com.soundrecorder.base.utils.DebugUtil;

import com.soundrecorder.base.utils.FeatureOption;

public class SearchSortUtils {

    private static final String TAG = "SearchSortUtils";
    private static final int TIME_ONE_SECOND = 1000;
    private static final int CONVERT_COMPLETE_FLAG = 1;

    public static void doSortSearchBeans(ArrayList<ItemSearchViewModel> searchBeans) {
        Collections.sort(searchBeans, new Comparator<ItemSearchViewModel>() {
            @Override
            public int compare(ItemSearchViewModel o1, ItemSearchViewModel o2) {
                int o1Score = 0;
                int o2Score = 0;
                int magic = 100;
                String searchValue = o1.getSearchValue().replaceAll(" ", "");
                String o1Name = o1.getDisplayName().replaceAll(" ", "");
                String o2Name = o2.getDisplayName().replaceAll(" ", "");
                if (o1Name.contains(searchValue)) {
                    float searchLengthWithValueLength = o1.getSearchValue().length() * 1f / o1.getDisplayName().length() * 1f;
                    o1Score += (searchLengthWithValueLength * magic);
                }
                if (o2Name.contains(searchValue)) {
                    float searchLengthWithValueLength = o2.getSearchValue().length() * 1f / o2.getDisplayName().length() * 1f;
                    o2Score += (searchLengthWithValueLength * magic);
                }
                return (o2Score - o1Score);
            }
        });
    }


    public static ArrayList<ItemSearchViewModel> cursorToArrayList(Cursor cursor, String searchValue) {
        ArrayList<ItemSearchViewModel> searchBeans = new ArrayList<>();
        try {
            if (cursor.moveToFirst()) {
                do {
                    long duration = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DURATION));
                    String playPath = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DATA));
                    String playName = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DISPLAY_NAME));
                    long id = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media._ID));
                    long dateModified = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DATE_MODIFIED));
                    String mimeType = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.MIME_TYPE));
                    ItemSearchViewModel bean = new ItemSearchViewModel();
                    bean.setDisplayName(playName);
                    bean.setTitle(ExtKt.title(playName));
                    bean.setTitleColorIndex(getSearchValueIndexWithText(bean, bean.getTitle(), searchValue));
                    if (bean.getRemove()) {
                        continue;
                    }
                    bean.setMDuration(duration);
                    bean.setData(playPath);
                    bean.setSearchValue(searchValue);
                    bean.setMediaId(id);
                    bean.setDateModified(dateModified);
                    bean.setMimeType(mimeType);

                    boolean isConvertCompleted = false;
                    try {
                        int columnIndexCompleteStatus = cursor.getColumnIndexOrThrow(DatabaseConstant.ConvertColumn.COMPLETE_STATUS);
                        int completeStatus = cursor.getInt(columnIndexCompleteStatus);
                        isConvertCompleted = (completeStatus == CONVERT_COMPLETE_FLAG);
                    } catch (IllegalArgumentException e) {
                        DebugUtil.e(TAG, "get convert completed status error.", e);
                    }
                    bean.setConvertCompleted(isConvertCompleted);
                    searchBeans.add(bean);
                } while (cursor.moveToNext());
            }
        } catch (Throwable e) {
            DebugUtil.v(TAG, "cursorToArrayList:" + e);
        } finally {
            try {
                cursor.close();
            } catch (Exception e) {

            }
        }
        return searchBeans;
    }

    private static ArrayList<Integer> getSearchValueIndexWithText(ItemSearchViewModel searchBean, String text, String searchValue) {
        ArrayList<Integer> integers = new ArrayList<>();
        if (TextUtils.isEmpty(text)) {
            return integers;
        }
        text = text.toLowerCase();
        searchValue = searchValue.toLowerCase();
        if (text.contains(searchValue)) {
            int index = text.indexOf(searchValue);
            integers.add(index);
            integers.add(index + searchValue.length());
            searchBean.setAllContains(true);
            return integers;
        } else {
            int oldIndex = -1;
            for (int i = 0; i < searchValue.length(); i++) {
                String searchI = searchValue.substring(i, i + 1);
                if ((oldIndex + 1) < text.length()) {
                    int fromIndex = oldIndex + 1;
                    int result = text.indexOf(searchI, fromIndex);
                    if (result != -1) {
                        integers.add(result);
                        oldIndex = result;
                    } else {
                        searchBean.setRemove(true);
                        return integers;
                    }
                } else {
                    searchBean.setRemove(true);
                    return integers;
                }
            }
        }

        return integers;
    }
}
