/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  Indicator.java
 * * Description: Indicator.java
 * * Version: 1.0
 * * Date : 2019/11/5
 * * Author: liuyulong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * liuyulong  2019/11/5      1.0    build this module
 ****************************************************************/
package com.soundrecorder.browsefile.home.view.convert;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorFilter;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.graphics.drawable.Animatable;
import android.graphics.drawable.Drawable;

import androidx.annotation.NonNull;

import java.util.ArrayList;
import java.util.HashMap;

import com.soundrecorder.base.utils.DebugUtil;

public abstract class Indicator extends Drawable implements Animatable {

    private static final String TAG = "BaseIndicator";

    private HashMap<AnimatorSet, Animator.AnimatorListener> mUpdateListeners = new HashMap<>();

    private ArrayList<AnimatorSet> mAnimatorSets;
    private int alpha = 255;

    private static final Rect ZERO_BOUNDS_RECT = new Rect();
    private Rect drawBounds = ZERO_BOUNDS_RECT;

    private Paint mPaint = new Paint();

    Indicator() {
        mPaint.setColor(Color.WHITE);
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setAntiAlias(true);
    }

    public int getColor() {
        return mPaint.getColor();
    }

    public void setColor(int color) {
        mPaint.setColor(color);
    }

    @Override
    public void setAlpha(int alpha) {
        this.alpha = alpha;
    }

    @Override
    public int getAlpha() {
        return alpha;
    }

    @Override
    public int getOpacity() {
        return PixelFormat.OPAQUE;
    }

    @Override
    public void setColorFilter(ColorFilter colorFilter) {

    }

    @Override
    public void draw(@NonNull Canvas canvas) {
        draw(canvas, mPaint);
    }

    abstract void draw(Canvas canvas, Paint paint);

    abstract ArrayList<AnimatorSet> onCreateAnimators();

    @Override
    public void start() {
        DebugUtil.d(TAG, "start: ");
        ensureAnimators();

        if (mAnimatorSets == null) {
            return;
        }

        // If the animators has not ended, do nothing.
        if (isStarted()) {
            return;
        }
        startAnimators();
        invalidateSelf();
    }

    private void startAnimators() {
        for (int i = 0; i < mAnimatorSets.size(); i++) {
            DebugUtil.d(TAG, "startAnimators, size:" + mAnimatorSets.size());
            AnimatorSet animator = mAnimatorSets.get(i);

            //when the animator restart , add the updateListener again because they
            // was removed by animator stop .
            Animator.AnimatorListener updateListener = mUpdateListeners.get(animator);
            if (updateListener != null) {
                animator.addListener(updateListener);
            }

            animator.start();
        }
    }

    private void stopAnimators() {
        if (mAnimatorSets != null) {
            for (AnimatorSet animatorSet : mAnimatorSets) {
                if (animatorSet != null && animatorSet.isStarted()) {
                    animatorSet.removeAllListeners();
                    animatorSet.end();
                }
            }
        }

        mAnimatorSets = null;

        if (mUpdateListeners != null && mUpdateListeners.size() != 0) {
            mUpdateListeners.clear();
        }
    }

    private void ensureAnimators() {
        if (mAnimatorSets == null) {
            mAnimatorSets = onCreateAnimators();
        }
    }

    @Override
    public void stop() {
        stopAnimators();
    }

    private boolean isStarted() {
        for (AnimatorSet animatorSet : mAnimatorSets) {
            return animatorSet.isStarted();
        }
        return false;
    }

    @Override
    public boolean isRunning() {
        for (AnimatorSet animatorSet : mAnimatorSets) {
            return animatorSet.isRunning();
        }
        return false;
    }

    /**
     * Your should use this to add AnimatorUpdateListener when
     * create animator , otherwise , animator doesn't work when
     * the animation restart .
     */
    void addUpdateListener(AnimatorSet animatorSet, Animator.AnimatorListener updateListener) {
        mUpdateListeners.put(animatorSet, updateListener);
    }

    @Override
    protected void onBoundsChange(Rect bounds) {
        super.onBoundsChange(bounds);
        setDrawBounds(bounds);
    }

    private void setDrawBounds(Rect drawBounds) {
        setDrawBounds(drawBounds.left, drawBounds.top, drawBounds.right, drawBounds.bottom);
    }

    private void setDrawBounds(int left, int top, int right, int bottom) {
        this.drawBounds = new Rect(left, top, right, bottom);
    }

    void postInvalidate() {
        invalidateSelf();
    }

    public Rect getDrawBounds() {
        return drawBounds;
    }

    int getWidth() {
        return drawBounds.width();
    }

    int getHeight() {
        return drawBounds.height();
    }

    public int centerX() {
        return drawBounds.centerX();
    }

    public int centerY() {
        return drawBounds.centerY();
    }

    public float exactCenterX() {
        return drawBounds.exactCenterX();
    }

    public float exactCenterY() {
        return drawBounds.exactCenterY();
    }
}
