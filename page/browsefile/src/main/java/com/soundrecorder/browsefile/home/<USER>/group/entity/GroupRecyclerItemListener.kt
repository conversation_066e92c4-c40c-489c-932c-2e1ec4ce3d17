/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: GroupRecyclerItemListener.kt
 ** Description: 分组项item的各种事件监听体。
 ** Version: 1.0
 ** Date : 2025/02/25
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  ******** 2025/02/25      1.0     create file
 ****************************************************************/
package com.soundrecorder.browsefile.home.view.group.entity

import android.view.View

data class GroupRecyclerItemListener(
    val onSelectedChangedListener: ((GroupItem?, () -> Unit) -> Unit)? = null,
    val onCheckedChangedListener: (() -> Unit)? = null,
    val onItemMoveListener: ((Boolean) -> Unit)? = null,
    val onCreateNewGroupClickListener: View.OnClickListener? = null,
    val onDragResultListener: ((List<GroupItem>) -> Unit)? = null,
    val onItemLongClickListener: ((Int?) -> Unit)? = null
)