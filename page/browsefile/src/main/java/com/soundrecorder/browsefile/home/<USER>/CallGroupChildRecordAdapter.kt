/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CallGroupChildRecordAdapter.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/1/15
 * * Author      : W9035969
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home.item

import android.app.Activity
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.view.IRecyclerAdapterData
import com.soundrecorder.browsefile.R
import com.soundrecorder.common.transition.RemoveItemAnimator

/**
 * 通话录音按联系人分组，group列表下子类录音列表
 */
class CallGroupChildRecordAdapter(
    val mContext: Context,
    private val owner: LifecycleOwner,
    private val browseViewListener: IBrowseViewHolderListener
) : RecyclerView.Adapter<ViewHolder>(),
    IRecyclerAdapterData {

    companion object {
        private const val TAG = "CallGroupChildAdapter"
        private const val DELAY_REFRESH = 1000L
    }

    //private var list = mutableListOf<String>()
    private val mLayoutInflater = LayoutInflater.from(mContext)
    private var bindRecyclerView: RecyclerView? = null

    fun refresh(list: MutableList<ItemBrowseRecordViewModel>) {
        this.mChildDatas = list
        notifyDataSetChanged()
    }

    private var mChildDatas: MutableList<ItemBrowseRecordViewModel>? = null

    fun setData(data: MutableList<ItemBrowseRecordViewModel>?) {
        mChildDatas = data
        notifyDataSetChanged()
    }

    fun getData(): MutableList<ItemBrowseRecordViewModel>? {
        return mChildDatas
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        //return ItemGroupChildViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_call_record_group_child, parent, false))
        return ItemBrowseViewHolder(
            DataBindingUtil.inflate(
                mLayoutInflater,
                R.layout.item_browse,
                parent,
                false),
            browseViewListener, true).also {
            it.adapterData = this
        }
    }

    override fun getItemCount(): Int {
        return mChildDatas?.size ?: 0
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val taskId = (holder.itemView.context as Activity).taskId
        mChildDatas?.let {
            val item = it[position]
            item.taskId = taskId
            val isLastPosition = (position == it.size - 1)
            (holder as? ItemBrowseViewHolder)?.onBindViewHolder(item, owner, isLastPosition)
        }
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        bindRecyclerView = recyclerView
        RemoveItemAnimator().let {
            it.addDuration = 0
            it.removeDuration = BrowseAdapter.DURATION_REMOVE
            it.changeDuration = BrowseAdapter.DURATION_CHANGE
            it.moveDuration = BrowseAdapter.DURATION_CHANGE
            recyclerView.itemAnimator = it
        }
    }

    @Suppress("TooGenericExceptionCaught")
    fun deleteItems(selectedMediaIdList: ArrayList<String>?, callback: ((Long) -> Unit)): Long {
        val delayRefresh = 0L
        if (selectedMediaIdList.isNullOrEmpty() || mChildDatas.isNullOrEmpty()) {
            return delayRefresh
        }
        DebugUtil.d(TAG, "deleteItems, selectedMediaIdList:$selectedMediaIdList")
        try {
            var iterator: Iterator<BaseItemRecordViewModel>?
            var index: Int = 0
            for (mediaId in selectedMediaIdList) {
                //index = 1 + mSingHeader.size
                iterator = mChildDatas!!.iterator()
                while (iterator.hasNext()) {
                    val itemRecord = iterator.next()
                    if (itemRecord.mediaId.toString() == mediaId) {
                        //DebugUtil.d(TAG, "deleteItems, itemRecord.mediaId:${itemRecord.mediaId}")
                        iterator.remove()
                        notifyItemRemoved(index)
                        break
                    }
                    index++
                }
            }

            if (mChildDatas.isNullOrEmpty()) {
                //runnable?.run()
                callback.invoke(delayRefresh)
            } else {
                //bindRecyclerView?.postDelayed(runnable, DELAY_REFRESH)
                callback.invoke(delayRefresh)
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "deleteItems error! ${e.message}")
            //runnable?.run()
            callback.invoke(delayRefresh)
        }
        return delayRefresh
    }
}
