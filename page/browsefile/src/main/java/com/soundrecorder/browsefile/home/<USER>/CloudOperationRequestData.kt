/*************************************************************************************************
 * Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CloudOperationRequestData.kt
 * Description: 请求云端配置的数据结构
 *
 * Version: 1.0
 * Date: 2024/9/18
 * Author: 80266877
 * TAG:
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * 80266877    2024/9/18   1.0              build this module
 ************************************************************************************************/
package com.soundrecorder.browsefile.home.cloudtips

import com.google.gson.annotations.SerializedName

/**
 * 请求云端运营配置
 */
data class CloudOperationRequestData(
    /**
     * 云同步开关
     */
    @SerializedName("swAutoSync") private val isAutoSync: Boolean,


    /**
     * 允许使用移动数据开关
     */
    @SerializedName("swMobileDataAllow") private val isMobileDataAllow: Boolean,

    /**
     * 剩余存储空间大小
     */
    @SerializedName("localRemainStorageSpace") private val remainStorage: Long
)
