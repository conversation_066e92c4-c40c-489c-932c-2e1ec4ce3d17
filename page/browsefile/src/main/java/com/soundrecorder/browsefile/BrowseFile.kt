/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BrowseFile
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile

import android.content.Intent
import android.content.res.Configuration
import android.graphics.Rect
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.core.view.WindowCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.IntentExt.getBooleanValue
import com.soundrecorder.base.ext.IntentExt.getIntValue
import com.soundrecorder.base.ext.IntentExt.getLongValue
import com.soundrecorder.base.ext.IntentExt.getStringValue
import com.soundrecorder.base.ext.dismissWhenShowing
import com.soundrecorder.base.ext.isFlexibleWindow
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.ext.replaceFragmentByTag
import com.soundrecorder.base.splitwindow.WindowLayoutChangeListener
import com.soundrecorder.base.splitwindow.bracketspace.BracketSpaceProviderAgent
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.browsefile.databinding.ActivityBrowseFileBinding
import com.soundrecorder.browsefile.home.BrowseFragment
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel
import com.soundrecorder.browsefile.home.load.ConvertingInfo
import com.soundrecorder.browsefile.parentchild.BrowseFileActivityViewModel
import com.soundrecorder.browsefile.parentchild.BrowsePanelController
import com.soundrecorder.browsefile.parentchild.listener.IBrowseFileActivityListener
import com.soundrecorder.browsefile.sau.SAUUpdateHelper
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.constant.OplusCompactConstant
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.common.permission.PermissionActivity
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.permission.PermissionUtils.isNetWorkGranted
import com.soundrecorder.common.share.OShareConvertUtil
import com.soundrecorder.common.sync.RecordDataSyncHelper
import com.soundrecorder.common.task.ActivityTaskUtils
import com.soundrecorder.common.task.ActivityTaskUtils.anyExcludeSelf
import com.soundrecorder.common.task.AppTaskUtil
import com.soundrecorder.common.task.RecordRouterManager
import com.soundrecorder.common.utils.CoroutineUtils
import com.soundrecorder.common.utils.RecordModeUtil.isFromOther
import com.soundrecorder.modulerouter.miniapp.MiniAppConstant
import com.soundrecorder.modulerouter.miniapp.MiniRecorderAction
import com.soundrecorder.modulerouter.playback.PlaybackFragmentAction
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.modulerouter.recorder.RecordAction
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import com.soundrecorder.modulerouter.sellmode.SellModeAction
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class BrowseFile : PermissionActivity(), IBrowseFileActivityListener {

    companion object {
        const val TAG = "BrowseFile"

        /*用于识别,统计页面 勿改*/
        private const val FUNCTION_NAME = "BrowseFile"
        private const val UPDATE_GUIDE_TEXT_DELAY = 200
        const val BUNDLE_KEY_SELECT_PLAY_MODEL = "bundle_select_play_model"
        const val SOURCE_FROM_PHONE_BOOK = "contacts"
        const val KEY_PACKAGE = "package"
        const val SIZE_NUM_1 = 1

        private var isFirstGetPermission: Boolean? = null
        private var isFromSettingCallRecording: Boolean = false
        private var originWindowType: WindowType = WindowType.SMALL
    }

    private var mSauHelper: SAUUpdateHelper? = null
    private var isFromOtherApp = false
    private var isFinishByRepeatLaunch = false
    private var mBrowseFileActivityViewModel: BrowseFileActivityViewModel? = null
    private var mBrowsePanelController: BrowsePanelController? = null
    private var childMaskView: View? = null
    private var parentMaskView: View? = null
    private lateinit var binding: ActivityBrowseFileBinding
    private var disableDialog: AlertDialog? = null

    /*上层已处理，该回调仅在清除数据后第一次会回调该方法*/
    private var mPermissionGrantedListener = {
        CoroutineUtils.ioToMain({ PermissionUtils.hasFirstCheckAllPermissionsOnResumeForBrowseFile() }, {
            DebugUtil.i(TAG, "onPermissionGranted set isFirstGetPermission = $isFirstGetPermission")
            if (isFirstGetPermission != true) {
                isFirstGetPermission = true
                SellModeAction.checkAndStartSellModeService(BaseApplication.getAppContext())
            }
            mBrowsePanelController?.getBrowseFragment()?.onPermissionGranted(it)

            PlaybackFragmentAction.onPermissionGranted(mBrowsePanelController?.getPlaybackContainerFragment())
        }, lifecycleScope)
    }

    private val rootLayoutChangeListener: WindowLayoutChangeListener =
        object : WindowLayoutChangeListener() {
            override fun onLayoutChange(v: View?, rect: Rect, oldRect: Rect) {
                // 分屏下，其他应用切换成浮窗，应用不会走重建，走的onlayoutChange
                checkWindowTypeChanged()
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        DebugUtil.i(TAG, "onCreate")
        super.onCreate(savedInstanceState)
        // check是否需要处理副屏接续内屏逻辑
        MiniRecorderAction.checkMiniAppContinueAction(this, intent) {
            disableDialog = it
        }
        addBuryLaunch()
        if (alreadyRecordedFinishNewIncoming()) {
            return
        }
        setPermissionGrantedListener(mPermissionGrantedListener)
        SellModeAction.createSellModeScreenStateListener(this) { !isAllFileDialogShowing() }
        WindowCompat.setDecorFitsSystemWindows(window, false)
        binding = ActivityBrowseFileBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initAppTaskUtil()
        initObserver()
        if (savedInstanceState == null) {
            replaceFragmentByTag(R.id.fl_left_container, BrowseFragment(), BrowsePanelController.TAG_LEFT_FRAGMENT)
            // 主进程启动+非重建，更新录音启动时间
            if (!isFromOtherApp) {
                lifecycleScope.launch(Dispatchers.IO) {
                    BracketSpaceProviderAgent.updateAppLaunchTime(BaseApplication.getAppContext())
                }
                intent?.pauseFileName()
                parsePlayIntentParam(intent)
            }
            val isFromPhoneBook = try {
                intent?.getStringExtra(RecorderDataConstant.START_FROM_OTHER_SOURCE).equals(SOURCE_FROM_PHONE_BOOK)
            } catch (ignored: Exception) {
                false
            }
            if (isFromPhoneBook) {
                intent?.intPhoneBook()
            }
            originWindowType = ScreenUtil.getWindowType(resources.configuration)
            intent?.extras.also {
                if (it != null && it.size() == SIZE_NUM_1) {
                    isFromSettingCallRecording =
                        (it.getString(KEY_PACKAGE) == BaseUtil.PACKAGE_INCALLUI)
                }
            }
            readOSharePath(true)
        }
        if (isNetWorkGranted(this)) {
            delayOnCreate()
        }
        binding.clRoot.addOnLayoutChangeListener(rootLayoutChangeListener)
    }

    private fun addBuryLaunch() {
        when (intent.action) {
            OplusCompactConstant.START_BROWSE_ACTION_BEFOR,
            OplusCompactConstant.START_BROWSE_ACTION_AFTER -> BuryingPoint.addThroughCallRecording()
            OplusCompactConstant.PROVIDER_START_BROWSE_FILE_FROM_SMALLCARD -> {
                BuryingPoint.addLaunchAppSmallCard(RecorderUserAction.VALUE_LAUNCH_APP_SMALL_CARD_RECORD_SAVE)
            }
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        DebugUtil.i(TAG, "onNewIntent")
        setIntent(intent)
        readOSharePath(false)
        intent?.pauseFileName()
        mBrowsePanelController?.getBrowseFragment()?.onNewIntent()
        PlaybackFragmentAction.onNewIntent(mBrowsePanelController?.getPlaybackContainerFragment(), intent)
        parsePlayIntentParam(intent)
    }

    override fun canShowOpenAllFilePermissionOnResume(): Boolean {
        return intent.getBooleanValue(
            MiniAppConstant.EXTRA_NAME_SHOW_ALL_FILE_PERMISSION_RESUME,
            true
        )
    }

    override fun startActivityForResult(intent: Intent, requestCode: Int) {
        PlaybackFragmentAction.setRequestCodeX(mBrowsePanelController?.getPlaybackContainerFragment(), requestCode)
        super.startActivityForResult(intent, requestCode)
    }

    override fun startActivityForResult(intent: Intent, requestCode: Int, options: Bundle?) {
        PlaybackFragmentAction.setRequestCodeX(mBrowsePanelController?.getPlaybackContainerFragment(), requestCode)
        super.startActivityForResult(intent, requestCode, options)
    }

    override fun onPrivacyPolicySuccess(type: Int, pageFrom: Int?) {
        super.onPrivacyPolicySuccess(type, pageFrom)
        DebugUtil.d(TAG, "onPrivacyPolicySuccess, type:$type, pageFrom:$pageFrom")
        when (pageFrom) {
            PrivacyPolicyConstant.PAGE_FROM_BROWSE -> mBrowsePanelController?.getBrowseFragment()?.onPrivacyPolicySuccess(type, pageFrom)
            PrivacyPolicyConstant.PAGE_FROM_PLAYBACK -> {
                PlaybackFragmentAction.onPrivacyPolicySuccess(
                    mBrowsePanelController?.getPlaybackContainerFragment(), type)
            }
            PrivacyPolicyConstant.PAGE_FROM_CALL_RECORD -> mBrowsePanelController?.getCallRecordListFragment()?.onPrivacyPolicySuccess(type, pageFrom)
        }
        readOSharePath(true)
    }

    override fun onPrivacyPolicyFail(type: Int, pageFrom: Int?) {
        super.onPrivacyPolicyFail(type, pageFrom)
        if (pageFrom == PrivacyPolicyConstant.PAGE_FROM_BROWSE) {
            mBrowsePanelController?.getBrowseFragment()?.onPrivacyPolicyFail(type)
        }
    }

    private fun Intent.pauseFileName() {
        ItemBrowseRecordViewModel.addAnimatorDisplayName = try {
            getStringExtra(RecorderDataConstant.SHOULD_AUTO_FIND_FILE_NAME)
        } catch (e: Exception) {
            null
        }
    }

    override fun userChange() {
        fragmentUserChange()
        super.userChange()
    }

    private fun initAppTaskUtil() {
        isFromOtherApp = intent.isFromOther()
        AppTaskUtil.mMapIsFromOtherApp[taskId] = isFromOtherApp
        if (!isFromOtherApp) {
            ActivityTaskUtils.setMainTaskId(taskId)
            DebugUtil.i(TAG, "set mainTaskId: $taskId")
        } else {
            DebugUtil.i(TAG, "set mainTaskId isFromOtherApp , no taskId:  now TaskId: ${ActivityTaskUtils.getMainTaskId()}")
        }
        DebugUtil.i(TAG, "MapIsFromOtherApp[taskId = $taskId], isFromOtherApp = $isFromOtherApp")
    }


    override fun onDestroy() {
        super.onDestroy()
        DebugUtil.i(TAG, "onDestroy, isFinishing=$isFinishing")
        if (!isFinishByRepeatLaunch) {
            clearCacheData()
        }
        isFinishByRepeatLaunch = false
        mBrowsePanelController?.onDestroy()
        mBrowsePanelController = null
        mSauHelper?.release()
        mSauHelper = null
        if (::binding.isInitialized) {
            binding.clRoot.removeOnLayoutChangeListener(rootLayoutChangeListener)
        }

       disableDialog.dismissWhenShowing()
        disableDialog = null
    }

    /**
     * 焦点切换的时候会回调
     */
    override fun onTopResumedActivityChanged(isTopResumedActivity: Boolean) {
        super.onTopResumedActivityChanged(isTopResumedActivity)
        DebugUtil.i(TAG, "onTopResumedActivityChanged:$isTopResumedActivity, isInMultiWindowMode:$isInMultiWindowMode")
        if ((isTopResumedActivity) && (isInMultiWindowMode)
            && (mBrowsePanelController?.getBrowseFragment()?.isDeviceSecureDelete == false)) {
            // 刷新数据，便于清除数据后处理完用户须知弹窗后获取问卷数据
            mBrowsePanelController?.getBrowseFragment()?.refreshData()
        }
    }

    private fun clearCacheData() {
        if (isFinishing) {
            ConvertingInfo.clearConvertingInfo()
            SmartNameAction.clearSmartRetryCacheTask()
        }
    }

    override fun onAgreeClick() {
        // 记录不同意隐私政策，为了拦截不同意进入录音触发云同步，关闭开关操作在子线程中，开关回调晚于permissionGranted执行
        val isAgreeCloudPermission = if (BaseUtil.isLightOS()) true else hasConvertPermission()
        RecordDataSyncHelper.mIsInterceptFirstEnterSync = !isAgreeCloudPermission
        super.onAgreeClick()
    }

    //todo 需要修改
    override fun canShowPermissionsDialogWhenCannotRequestPermissions(): Boolean {
        if (mBrowseFileActivityViewModel?.hasPlayPageData() == true) {
            // 播放存在就要显示
            return true
        }
        return false
    }

    override fun canShowPermissionsDialogWhenHasPermissionsRationale(): Boolean {
        if (mBrowseFileActivityViewModel?.hasPlayPageData() == true) {
            // 播放存在就要显示
            return true
        }
        return false
    }

    override fun doFinishActivityWhenRefusePermission() {
        if (mBrowseFileActivityViewModel?.hasPlayPageData() == true) {
            mBrowseFileActivityViewModel?.clearPlayRecordData()
        }
    }

    private fun alreadyRecordedFinishNewIncoming(): Boolean {
        val isSelf = RecordRouterManager.getInstance().getBaseActivityIsSelf(BaseApplication.getAppContext())
        val isSideBarStart = RecorderViewModelAction.isFromSlidBar()
        val isLastTaskRecordActivity = RecordAction.isRecorderActivity(ActivityTaskUtils.getLastActivity(taskId))
        val isSuperPowerSaveModeState = RecordRouterManager.getInstance().isSuperPowerSaveModeState(BaseApplication.getAppContext())
        val isSecondRecordActivity = RecordAction.isRecorderActivity(ActivityTaskUtils.secondActivity(taskId))
        val isSuperPowerSaveModeCondition = isSuperPowerSaveModeState && isSecondRecordActivity
        val hasBrowseFile = anyExcludeSelf(BrowseFile::class.java)
        val fromLauncher = intent.hasCategory(Intent.CATEGORY_LAUNCHER) && intent.action.equals(Intent.ACTION_MAIN)
        val isSideBarStartCondition = isSideBarStart && isLastTaskRecordActivity
        DebugUtil.d(
            TAG,
            "alreadyRecordedFinishNewIncoming self:$isSelf, lastTaskRecord:$isLastTaskRecordActivity," +
                    " isSuperPowerSaveModeState:$isSuperPowerSaveModeState, hasBrowseFile = $hasBrowseFile,fromLauncher = $fromLauncher"
        )
        if (!isFromOtherApp && isSelf && (isSideBarStartCondition || isSuperPowerSaveModeCondition)) {
            isFinishByRepeatLaunch = true
            finish()
            return true
        }
        if (hasBrowseFile && fromLauncher) {
            isFinishByRepeatLaunch = true
            finish()
            return true
        }
        return false
    }

    private fun delayOnCreate() {
        DebugUtil.i(TAG, "delayOnCreate")
        if (mSauHelper == null) {
            mSauHelper = SAUUpdateHelper()
        }
        mSauHelper?.runSauUpdate(this, UPDATE_GUIDE_TEXT_DELAY.toLong())
    }

    private fun initObserver() {
        mBrowseFileActivityViewModel = ViewModelProvider(this)[BrowseFileActivityViewModel::class.java]
        mBrowseFileActivityViewModel?.isFromOtherApp = intent?.isFromOther() ?: false
        mBrowseFileActivityViewModel?.windowType?.value = ScreenUtil.getWindowType(resources.configuration)
        mBrowsePanelController = BrowsePanelController(this, mBrowseFileActivityViewModel)
    }

    private fun Intent.intPhoneBook() {
        ItemBrowseRecordViewModel.livePhoneBookMode[taskId] = true
        ItemBrowseRecordViewModel.addScrollAndTipsFilePath = try {
            getStringExtra(RecorderDataConstant.SHOULD_AUTO_FIND_FILE_PATH)
        } catch (ignored: Exception) {
            null
        }
        ItemBrowseRecordViewModel.addScrollAndTipsDisplayName = try {
            getStringExtra(RecorderDataConstant.SHOULD_AUTO_FIND_FILE_NAME)
        } catch (ignored: Exception) {
            null
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putParcelable(BUNDLE_KEY_SELECT_PLAY_MODEL, mBrowseFileActivityViewModel?.mCurrentPlayRecordData?.value)
        PlaybackFragmentAction.onSaveInstanceState(mBrowsePanelController?.getPlaybackContainerFragment(), outState)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        savedInstanceState.getParcelable<StartPlayModel>(BUNDLE_KEY_SELECT_PLAY_MODEL)?.let {
            if (it.mediaId == mBrowseFileActivityViewModel?.mCurrentPlayRecordData?.value?.mediaId) {
                // 数据一致,为正常重建，不用处理
                return
            }

            // 低内存、关闭权限后的重建进入
            mBrowsePanelController?.lowMemoryReCreate = true

            if (mBrowseFileActivityViewModel?.isSmallWindow() == true) { // 低内存小屏矫正view位置
                mBrowsePanelController?.updatePercentHasChildInSmallWindow()
            }
            mBrowseFileActivityViewModel?.mCurrentPlayRecordData?.value = it
        }
        PlaybackFragmentAction.onRestoreInstanceState(mBrowsePanelController?.getPlaybackContainerFragment(), savedInstanceState)
    }

    override fun onBackPressed() {
        if (BaseUtil.isAndroidSOrLater) {
            if (!processBackPress()) {
                DebugUtil.d(TAG, "onBackPressed, !processBackPress")
                //adapter android S,back click when process , need to finish activity, because new AndroidS feature backpress
                finish()
            }
        } else {
            super.onBackPressed()
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        showTipsSame()
        checkWindowTypeChanged()
    }

    private fun checkWindowTypeChanged() {
        if (originWindowType == WindowType.MIDDLE && isFromSettingCallRecording && isFlexibleWindow(this)
        ) { //由于设置app不支持浮窗模式，导致使用singleTop启动的BrowseFile在切换浮窗时仍然获取的configuration是中屏，此时会有UI异常。此场景需要强设为WindowType.SMALL去展示UI。
            DebugUtil.d(
                TAG,
                "checkWindowTypeChanged FromSettingCallRecording force to set WindowType.SMALL!"
            )
            ItemBrowseRecordViewModel.liveEditMode[taskId]?.also {
                if (it.value == true) {
                    it.postValueSafe(true)
                }
            }
            WindowType.SMALL
        } else {
            ScreenUtil.getWindowType(resources.configuration)
        }.run {
            if (mBrowseFileActivityViewModel?.windowType?.value != this) {
                DebugUtil.i(TAG, "checkWindowTypeChanged,windowType = $this ")
                mBrowseFileActivityViewModel?.windowType?.value = this
            }
        }
    }

    private fun showTipsSame() {
        val fragments = supportFragmentManager.fragments
        for (fragment in fragments) {
            if (fragment is BrowseFragment) {
                DebugUtil.i(TAG, "fragment == $fragment")
                fragment.showTipsSame()
                return
            }
        }
    }

    /**
     * 首页搜索状态以及搜索内容发生变化
     * @param inSearch 首页是否处于搜索中，true：是； false：否
     * @param hasInputSearchValue 搜索输入框是否有内容 true：内容不为空；false：内容为null
     * @param outSearchFunc 退出首页搜索function
     * 首页搜索：未输入关键词时，点击蒙层或右侧任何区域，响应退出搜索流程
     */
    override fun onRecordSearchStateChanged(inSearch: Boolean, hasInputSearchValue: Boolean, outSearchFunc: (() -> Unit)) {
        if ((mBrowseFileActivityViewModel?.isSmallWindow() == false) && inSearch && !hasInputSearchValue) {
            if (childMaskView == null) {
                childMaskView = View(this)
                addBlankMaskView(childMaskView!!, binding.flRightContainer, outSearchFunc)
            }
        } else {
            removeBlankMastView(childMaskView, binding.flRightContainer)
            childMaskView = null
        }
    }

    /**
     * 播放转文本搜索状态以及搜索内容发生变化
     * @param inSearch 是否处于转文本内容搜索中，true：是； false：否
     * @param hasInputSearchValue 搜索输入框是否有内容 true：内容不为空；false：内容为null
     * @param outSearchFunc 退出转文本搜索function
     * 转文本内容搜索：未输入关键词时，点击蒙层或左侧任何区域，响应退出搜索流程
     */
    override fun onConvertSearchStateChanged(inSearch: Boolean, hasInputSearchValue: Boolean, outSearchFunc: (() -> Unit)) {
        if ((mBrowseFileActivityViewModel?.isSmallWindow() == false) && inSearch && !hasInputSearchValue) {
            if (parentMaskView == null) {
                parentMaskView = View(this)
                addBlankMaskView(parentMaskView!!, binding.flLeftContainer, outSearchFunc)
            }
        } else {
            removeBlankMastView(parentMaskView, binding.flLeftContainer)
            parentMaskView = null
        }
    }

    private fun addBlankMaskView(maskView: View, rootView: ViewGroup, clickMaskViewListener: () -> Unit) {
        maskView.setOnClickListener {
            clickMaskViewListener.invoke()
        }
        rootView.addView(maskView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
    }

    private fun removeBlankMastView(maskView: View?, rootView: ViewGroup) {
        maskView?.let {
            DebugUtil.i(TAG, "removeBlankMaskView ")
            rootView.removeView(it)
        }
    }

    override fun navigationBarColor(): Int {
        return com.soundrecorder.common.R.color.navigation_bar_transparent_color
    }

    /**
     * 中大屏下是否有选中音频播放
     */
    fun hasPlayBackRecord(): Boolean = mBrowseFileActivityViewModel?.hasPlayPageData() ?: false

    /**
     * 是否有音频在快捷播放
     */
    fun isFastPlaying(): Boolean = mBrowsePanelController?.getBrowseFragment()?.isFastPlaying() == true

    /**
     * 暂停播放详情
     * @param clearNotification 是否清除播放通知卡片, true:要清除
     */
    fun pausePlayDetail(clearNotification: Boolean) {
        mBrowsePanelController?.pausePlayDetail(clearNotification)
    }

    /**
     * 处理从小步建议卡片，点击查看文本 跳转到播放详情
     */
    private fun parsePlayIntentParam(intent: Intent?) {
        if (intent?.extras?.containsKey("recordId") != true) {
            return
        }
        val mediaId = intent.getLongValue("recordId", -1)
        if (mediaId < 0) {
            DebugUtil.w(TAG, "parsePlayIntentParam, mediaId < 0")
            return
        }
        val duration = intent.getLongValue("duration", -1)
        val playPath = intent.getStringValue("playPath")
        val autoPlay = intent.getBooleanValue("autoPlay", false)
        val selectPos = intent.getIntValue("selectPos", 0)
        if (mBrowseFileActivityViewModel?.mCurrentPlayRecordData?.value?.mediaId != mediaId) {
            mBrowseFileActivityViewModel?.mCurrentPlayRecordData?.value = StartPlayModel(mediaId, playPath).apply {
                this.duration = duration
                this.autoPlay = autoPlay
                this.selectPosInPlayback = selectPos
            }
        }
    }

    private fun readOSharePath(needSyncOShareDB: Boolean) {
        DebugUtil.d(TAG, "readOSharePath")
        lifecycleScope.launch(Dispatchers.IO) {
            if (PermissionUtils.getNextAction() == PermissionUtils.SHOULD_SHOW_USER_NOTICE) {
                DebugUtil.d(TAG, "readOSharePath , The user notice was not agreed")
                return@launch
            }
            if (needSyncOShareDB) {
                OShareConvertUtil.syncOShareDB()
            }
            //互传传入了录音文件路径、转文本文件路径
            val oShareRecordFilePath = intent?.getStringExtra(OShareConvertUtil.EXTRA_OSHARE_RECORD_PATH)
            val oShareConvertTextPath = intent?.getStringExtra(OShareConvertUtil.EXTRA_OSHARE_CONVERT_TEXT_PATH)
            if (oShareRecordFilePath?.isNotEmpty() == true) {
                //同步互传录音文件到录音数据库
                OShareConvertUtil.syncOShareFile(this@BrowseFile, oShareRecordFilePath, oShareConvertTextPath)
                withContext(Dispatchers.Main) {
                    mBrowsePanelController?.getBrowseFragment()?.refreshData()
                }
            } else {
                DebugUtil.i(TAG, "readOSharePath oShareFilePath is null or empty")
            }
        }
    }
}
