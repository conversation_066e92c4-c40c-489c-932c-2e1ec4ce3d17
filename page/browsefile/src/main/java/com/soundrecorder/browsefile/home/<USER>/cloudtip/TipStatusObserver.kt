package com.soundrecorder.browsefile.home.view.cloudtip

import android.app.Activity
import android.app.Dialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.refresh.BounceLayout
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.home.load.BrowseViewModel
import com.soundrecorder.browsefile.home.view.SubTitleLayout
import com.soundrecorder.common.buryingpoint.BuryingPoint.addSyncSwitch
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.databean.GroupInfo.Companion.INT_DEFAULT_CALLING
import com.soundrecorder.common.permission.PermissionDialogUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.sync.RecordDataSyncHelper
import com.soundrecorder.modulerouter.cloudkit.CloudGlobalAction
import com.soundrecorder.modulerouter.cloudkit.CloudSyncAction
import com.soundrecorder.modulerouter.cloudkit.ICloudGlobalStateCallBack
import com.soundrecorder.modulerouter.cloudkit.dialog.CloudSyncStatusDialogAction
import com.soundrecorder.modulerouter.cloudkit.dialog.ICloudSyncStatusDialog
import com.soundrecorder.modulerouter.cloudkit.dialog.ICloudUpgradeHelper
import com.soundrecorder.modulerouter.cloudkit.tipstatus.CloudErrorCode
import com.soundrecorder.modulerouter.cloudkit.tipstatus.CloudTipManagerAction
import com.soundrecorder.modulerouter.cloudkit.tipstatus.ICloudSwitchChangeListener
import com.soundrecorder.modulerouter.cloudkit.tipstatus.ITipStatus
import com.soundrecorder.modulerouter.cloudkit.tipstatus.OnLinkTextClickListener
import com.soundrecorder.modulerouter.cloudkit.utils.CloudPermissionUtilsAction

/**
 * 云同步 副标题相关显示交互
 */
class TipStatusObserver(
    private val fragment: Fragment?,
    val tipView: SubTitleLayout,
    private val refreshView: BounceLayout?,
    private val viewModel: BrowseViewModel
) :
    DefaultLifecycleObserver {
    var mShowRecordCount: Int = 0
    var mCurrentGroup: GroupInfo? = null
    private var mCloudUpgradeHelper: ICloudUpgradeHelper? = null
    private var mInitCloud = false

    companion object {
        const val TAG = "TipStatusObserver"
    }

    private var mCloudListener: ICloudSwitchChangeListener? = null
    private var mNeedCheckLastEnterTime: Boolean = false
    private var mNeedCheckAllFileAccess: Boolean = false
    private var mAllFileAccessDialog: Dialog? = null
    private var mCloudSyncStatusDialog: ICloudSyncStatusDialog? = null
    private var mOwner: LifecycleOwner? = null
    private var mLinkTextOnClickListener: OnLinkTextClickListener? =
        object : OnLinkTextClickListener {
            override fun onClick(tipStatus: Int) {
                when (tipStatus) {
                    // 没有权限
                    ITipStatus.STATE_NO_ALL_ACCESS_PERMISSION -> applyPermission()
                    // 同步完成
                    ITipStatus.STATE_COMPLETED -> CloudSyncStatusDialogAction.launchCloudApp(tipView.context)
                    // 云空间不足
                    ITipStatus.STATE_NO_CLOUD_SPACE -> upgradeCloudSpace()
                    // 同步失败
                    ITipStatus.STATE_FAILURE -> {
                        when (CloudTipManagerAction.getCloudSyncResultCode()) {
                            CloudErrorCode.RESULT_AUTH_ERROR -> reqAccountLogin()
                            CloudErrorCode.RESULT_NOT_LOGIN -> reqAccountLogin()
                            CloudErrorCode.RESULT_SWITCH_CLOSE -> CloudTipManagerAction.launchCloudSettingPage(tipView.context)
                            else -> showCloudStatusDialog()
                        }
                    }
                    else -> DebugUtil.e(TAG, "OnLinkTextClick $tipStatus")
                }
            }
        }

    override fun onCreate(owner: LifecycleOwner) {
        DebugUtil.i(TAG, "onCreate")
        mOwner = owner
        mNeedCheckLastEnterTime = true
        registerCloudLister()
        updateTipStatus(owner)
    }

    override fun onResume(owner: LifecycleOwner) {
        DebugUtil.i(TAG, "onResume")
        tipView.context?.let {
            if (CloudPermissionUtilsAction.isNetWorkGranted(it)) {
                initCloudState()
                if (mCloudSyncStatusDialog?.isShow() == true) {
                    mCloudSyncStatusDialog?.onResume()
                } else {
                    var isTrig = false
                    if (mNeedCheckLastEnterTime) {
                        isTrig = RecordDataSyncHelper.chekLastRecordSyncOverOneHour()
                    }
                    if (isTrig) return
                    isTrig = CloudTipManagerAction.checkSyncAbnormalStop(tipView.context)
                    if (isTrig) return
                    checkCloudRequirePermission()
                }
            }
        }
    }

    override fun onPause(owner: LifecycleOwner) {
        mNeedCheckLastEnterTime = false
    }

    override fun onDestroy(owner: LifecycleOwner) {
        DebugUtil.i(TAG, "onDestroy")

        if (mCloudListener != null) {
            CloudTipManagerAction.unregisterCloudSwitchChangeListener(mCloudListener)
            mCloudListener = null
        }
        releaseDialog()

        tipView.handler?.removeCallbacksAndMessages(null)
        tipView.setOnLinkTextClickListener(null, null)
        mLinkTextOnClickListener = null

        owner.lifecycle.removeObserver(this)
    }

    fun releaseDialog() {
        if (mAllFileAccessDialog?.isShowing == true) {
            mAllFileAccessDialog?.dismiss()
        }
        mAllFileAccessDialog = null
        // 释放查看弹窗
        mCloudSyncStatusDialog?.release()
        mCloudSyncStatusDialog = null
        // 释放云空间升级弹窗
        mCloudUpgradeHelper?.releaseDialog()
        mCloudUpgradeHelper = null
    }

    /**
     * 初始化云服务开关状态以及监听，该方法中checkAccount涉及联网操作
     * 由于在用户须知页面不能有联网操作，所以增加限制条件，延迟初始化，目前2个入口：
     * 1. 清除数据第一次进入，会在permissionGranted回调中调用
     * 2.非第一次进入，再onResume中调用
     */
    fun initCloudState() {
        /*用户须知弹窗过程中不执行云同步初始化，checkAccount逻辑涉及联网操作，需进入录音首页后调用;
        * 后续若再申请权限用户处理之后才能联网的话，可以再此处再增加动态权限判断*/
        if (!mInitCloud && (PermissionUtils.getNextAction() > PermissionUtils.SHOULD_SHOW_USER_NOTICE)) {
            mInitCloud = true
            CloudTipManagerAction.initCloudState()
        } else if (mInitCloud) {
            /*上面initCloudState 已包含此逻辑；每次onResume是处理录音已启动切后台登录，需获取一体化配置信息*/
            CloudGlobalAction.initCloudGlobalState(object : ICloudGlobalStateCallBack {
                override fun onSuccess(changed: Boolean, support: Boolean, state: String?) {
                    if (changed) {
                        DebugUtil.i(TAG, "call refresh, from CloudGlobalAction.initCloudGlobalState", true)
                        viewModel.refresh()
                    }
                }
            })
        }
    }

    private fun updateTipStatus(owner: LifecycleOwner) {
        CloudTipManagerAction.getCloudStatusLiveData()?.observe(owner, Observer { tipStatus: ITipStatus? ->
            DebugUtil.d(TAG, "updateTipStatus, tipStatus:${tipStatus?.state}")
            val leftResId = if (isRecycleRecordFilter()) {
                0
            } else {
                tipStatus?.tipIconResId ?: 0
            }
            tipView.setCompoundStartDrawables(leftResId)

            if ((tipStatus?.state == ITipStatus.STATE_NONE) || (tipStatus?.state == ITipStatus.STATE_CLOUD_OFF)) {
                val text = getSubTitleText()
                tipView.setTitleText(text)
            } else {
                val tipText = tipStatus?.tipText() ?: Pair("", "")
                tipView.setTitleText(tipText.first, tipText.second)
                tipView.setOnLinkTextClickListener(tipStatus?.state, mLinkTextOnClickListener)
            }
            // 收起下拉刷新
            if (refreshView?.isRefreshing() == true) {
                refreshView.setRefreshCompleted()
            }

            if ((tipStatus?.state == ITipStatus.STATE_SYNCING) || (tipStatus?.state == ITipStatus.STATE_QUERY)) {
                //同步状态需要禁止再次下拉刷新,同步完成后才能继续刷新
                setRefreshViewEnable(false)
            } else {
                //判断可刷新
                if (refreshView != null) {
                    viewModel.refreshEnable(refreshView)
                }
                //同步结束后不管是否成功，都刷新一下首页的分组列表
                viewModel.queryAllGroups(BaseApplication.getAppContext())
            }

            if (CloudTipManagerAction.isSyncing()) {
                if (mCloudSyncStatusDialog?.isShow() == true) {
                    mCloudSyncStatusDialog?.dismiss()
                }
            }
        })
    }

    private fun getSubTitleText(): String {
        val textResId = when (mCurrentGroup?.mGroupType) {
            INT_DEFAULT_CALLING -> com.soundrecorder.common.R.plurals.count_call_record
            else -> com.soundrecorder.common.R.plurals.count_record
        }
        val res = BaseApplication.getAppContext().resources
        var text = res?.getQuantityString(textResId, mShowRecordCount, mShowRecordCount) ?: ""
        if (mShowRecordCount == 0) {
            text = ""
        }
        return text
    }

    /**
     * 判断是否处于回收站
     */
    private fun isRecycleRecordFilter(): Boolean {
        return mCurrentGroup?.isRecentlyDeleteGroup() ?: false
    }

    /**
     * 注册监听云同步开关变化、是否支持云同步
     */
    private fun registerCloudLister() {
        if (mCloudListener == null) {
            mCloudListener = object : ICloudSwitchChangeListener {
                override fun onToggle(isInitValue: Boolean, open: Boolean) {
                    DebugUtil.i(TAG, "TipStatusObserver onToggle $open .mCloudSwitchInit $isInitValue  mShowRecordCount: $mShowRecordCount")
                    // 首页下拉刷新enable
                    setRefreshViewEnable(if (mShowRecordCount > 0) open else false)
                    // 埋点
                    if (!isInitValue) {
                        addSyncSwitch(if (open) RecorderUserAction.VALUE_SYNC_SWITCH_ON else RecorderUserAction.VALUE_SYNC_SWITCH_OFF)
                    }
                }
            }
        }
        CloudTipManagerAction.registerCloudSwitchChangeListener(mCloudListener)
    }

    private fun setRefreshViewEnable(enable: Boolean) {
        refreshView?.setRefreshEnable(enable)
    }

    /**
     * 升级云空间引导
     */
    private fun upgradeCloudSpace() {
        if (mCloudUpgradeHelper == null) {
            mCloudUpgradeHelper = CloudSyncStatusDialogAction.newCloudUpgradeHelper()
        }
        mCloudUpgradeHelper?.upgradeCloudSpace(fragment)
        CloudStaticsUtil.addCloudTipsClickUpgradeSpaceEvent()
    }

    private fun checkCloudRequirePermission(): Boolean {
        var isTrig = false
        if (mNeedCheckAllFileAccess && CloudPermissionUtilsAction.hasCloudRequiredPermissions()) {
            mNeedCheckAllFileAccess = false
            CloudSyncAction.trigCloudSync(tipView.context, CloudSyncAction.SYNC_TYPE_RECOVERY_START_APP)
            isTrig = true
        }
        return isTrig
    }

    private fun applyPermission() {
        val activity = tipView.context as? Activity ?: return
        if (activity.isFinishing || (mAllFileAccessDialog?.isShowing == true)) {
            return
        }
        val listener = object : PermissionDialogUtils.PermissionDialogListener {
            override fun onClick(alertType: Int, isOk: Boolean, permissions: ArrayList<String>?) {
                if (isOk) {
                    /*now this page is not support by Settings*/
                    if (BaseUtil.isAndroidROrLater) {
                        PermissionUtils.goToAppAllFileAccessConfigurePermissions(activity)
                    } else {
                        PermissionUtils.goToAppSettingConfigurePermissions(activity, permissions)
                    }
                    mNeedCheckAllFileAccess = true
                }
            }
        }
        mAllFileAccessDialog = if (BaseUtil.isAndroidROrLater) {
            PermissionDialogUtils.showPermissionAllFileAccessDialog(activity, listener)
        } else {
            PermissionDialogUtils.showReadStoragePermissionDialog(activity, listener,
                permissions = PermissionUtils.STORAGE_PERMISSIONS_Q.toCollection(ArrayList()))
        }
    }

    /**
     * token过期，请求登录
     */
    private fun reqAccountLogin() {
        tipView.context?.let {
            if (CloudPermissionUtilsAction.isNetWorkGranted(it)) {
                CloudSyncStatusDialogAction.reqLogin(it)
            }
        }
    }

    /**
     * 显示查看弹窗
     */
    private fun showCloudStatusDialog() {
        val activity = tipView.context as? Activity ?: return
        if (activity.isFinishing || (mCloudSyncStatusDialog?.isShow() == true)) {
            return
        }
        if (mCloudSyncStatusDialog == null) {
            mCloudSyncStatusDialog = CloudSyncStatusDialogAction.newCloudStatusDialog(activity, mOwner)
        }
        mCloudSyncStatusDialog?.show()
    }

    fun getCloudStatusDialog(): ICloudSyncStatusDialog? {
        return mCloudSyncStatusDialog
    }
}