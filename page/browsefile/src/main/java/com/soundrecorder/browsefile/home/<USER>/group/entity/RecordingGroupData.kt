/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2025/01/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9017070 2025/01/20      1.0     create file
 ****************************************************************/
package com.soundrecorder.browsefile.home.view.group.entity

object RecordingGroupData {


    const val IC_COVER_PURE_RED = "ic_group_cover_red" // 红
    const val IC_COVER_PURE_YELLOW = "ic_group_cover_yellow" // 黄
    const val IC_COVER_PURE_ORANGE = "ic_group_cover_orange" // 橙
    const val IC_COVER_PURE_GREEN = "ic_group_cover_green" // 绿
    const val IC_COVER_PURE_AZURE = "ic_group_cover_azure" // 蓝
    const val IC_COVER_PURE_GREY = "ic_group_cover_grey" // 灰
    const val IC_COVER_PURE_BROWN = "ic_group_cover_brown" // 棕

    private const val NUM_GROUP_COLOR_RED = 0 // 数据库对应红色
    private const val NUM_GROUP_COLOR_YELLOW = 1 // 数据库对应黄色
    private const val NUM_GROUP_COLOR_ORANGE = 2 // 数据库对应橙色
    private const val NUM_GROUP_COLOR_GREEN = 3 // 数据库对应绿色
    private const val NUM_GROUP_COLOR_AZURE = 4 // 数据库对应蓝色
    private const val NUM_GROUP_COLOR_GREY = 5 // 数据库对应灰色
    private const val NUM_GROUP_COLOR_BROWN = 6 // 数据库对应棕色

    fun getDefaultPureCover(): String {
        return IC_COVER_PURE_RED
    }

    fun getDefaultPureCoverNum(): Int {
        return NUM_GROUP_COLOR_RED
    }

    /**
     * 用于分组颜色信息（String）和数据库颜色（Int）对应(类型不一样，所以需要转换下)。
     */
    @JvmStatic
    fun pureCover2GroupColorInDb(cover: String): Int {
        return when (cover) {
            IC_COVER_PURE_RED -> NUM_GROUP_COLOR_RED
            IC_COVER_PURE_YELLOW -> NUM_GROUP_COLOR_YELLOW
            IC_COVER_PURE_ORANGE -> NUM_GROUP_COLOR_ORANGE
            IC_COVER_PURE_GREEN -> NUM_GROUP_COLOR_GREEN
            IC_COVER_PURE_AZURE -> NUM_GROUP_COLOR_AZURE
            IC_COVER_PURE_GREY -> NUM_GROUP_COLOR_GREY
            IC_COVER_PURE_BROWN -> NUM_GROUP_COLOR_BROWN
            else -> NUM_GROUP_COLOR_RED //默认红色
        }
    }

    @JvmStatic
    fun groupColorInDb2PureCover(numColor: Int): String {
        return when (numColor) {
            NUM_GROUP_COLOR_RED -> IC_COVER_PURE_RED
            NUM_GROUP_COLOR_YELLOW -> IC_COVER_PURE_YELLOW
            NUM_GROUP_COLOR_ORANGE -> IC_COVER_PURE_ORANGE
            NUM_GROUP_COLOR_GREEN -> IC_COVER_PURE_GREEN
            NUM_GROUP_COLOR_AZURE -> IC_COVER_PURE_AZURE
            NUM_GROUP_COLOR_GREY -> IC_COVER_PURE_GREY
            NUM_GROUP_COLOR_BROWN -> IC_COVER_PURE_BROWN
            else -> IC_COVER_PURE_RED //默认红色
        }
    }
}