/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: GroupCacheManager.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2025/02/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9017070 2025/02/10      1.0     create file
 ****************************************************************/
package com.soundrecorder.browsefile.home.view.group.util

import android.content.Context
import com.soundrecorder.base.utils.PrefUtil
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 处理当前选中组信息缓存业务
 */
class GroupCacheManager(private val dispatcher: CoroutineDispatcher = Dispatchers.IO) {

    companion object {
        private const val TAG = "GroupCacheManager"
    }

    /**
     * update selected group cache
     */
    suspend fun update(context: Context, groupId: Int, uuid: String): Boolean {
        val ctx = context.applicationContext

        return withContext(dispatcher) {
            PrefUtil.putInt(
                ctx,
                PrefUtil.KEY_RECORD_GROUP_ID, groupId
            )
            PrefUtil.putString(ctx, PrefUtil.KEY_RECORD_GROUP_UUID, uuid)
            return@withContext true
        }
    }
}