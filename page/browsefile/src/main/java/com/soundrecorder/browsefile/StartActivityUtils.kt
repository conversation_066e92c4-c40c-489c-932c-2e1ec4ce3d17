package com.soundrecorder.browsefile

import android.app.Activity
import android.provider.MediaStore
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.utils.RecordModeUtil.isFromOther
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel
import com.soundrecorder.modulerouter.playback.PlaybackAction
import com.soundrecorder.modulerouter.recorder.RecordAction
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant

object StartActivityUtils {
    private const val TAG = "StartActivityUtils"

    /**
     * 启动录制页面
     * @param context
     * @param model
     * @param brenoFront
     * @param overrideNoActivityAnim
     */
    @JvmStatic
    fun startRecordActivity(
        context: Activity?,
        model: StartRecordModel?,
        brenoFront: Boolean,
        overrideNoActivityAnim: Boolean = true,
        checkIsCall: Boolean
    ): Boolean {
        DebugUtil.i(TAG, "onRecordButton click")
        context ?: return false
        val comeFrom =
            if (model?.isFromCall == true) {
                RecorderDataConstant.PAGE_FROM_CALL
            } else if (brenoFront) {
                RecorderDataConstant.PAGE_FROM_BREENO_FRONT
            } else RecorderDataConstant.PAGE_FROM_LAUNCHER
        if (RecordAction.canStartRecord(checkIsCall, comeFrom) != true) {
            return false
        }

        RecordAction.createRecorderIntent(context, false)?.also {
            if (model != null) {
                it.putExtra(RecorderDataConstant.IS_NEED_RESULT, model.isNeedResult)
                it.putExtra(RecorderDataConstant.MAX_SIZE, model.limitSize)
                it.putExtra(MediaStore.Audio.Media.DURATION, model.limitDuration)
                it.putExtra(RecorderDataConstant.FROM_GESTURE, model.isFromGesture)
                it.putExtra(RecorderDataConstant.FROM_CALL_UI, model.isFromCall)
                it.putExtra(RecorderDataConstant.INTENT_EXTRA_NO_ENTER_ANIMATION, model.noEnterAnimation)
            }
            it.putExtra(RecorderDataConstant.PAGE_FROM_NAME, comeFrom)
            if (model?.isNeedResult == true) {
                context.startActivityForResult(it, RecorderDataConstant.REQUEST_CODE_TO_RECORDER)
            } else {
                context.startActivityForResult(it, RecorderDataConstant.REQUEST_SHOW_ADD_ANIMATOR)
            }
            //mFragmentController.toRecording()
            if (overrideNoActivityAnim) {
                context.overridePendingTransition(0, 0)
            }
            return true
        }

        return false
    }

    @JvmStatic
    fun startPlayback(
        record: ItemBrowseRecordViewModel?,
        activity: Activity?,
        playCurrentTime: Long? = 0,
        selectTab: Int? = null,
        searchWord: String? = null
    ) {
        activity?.apply {
            PlaybackAction.createPlaybackIntent(this)?.also {
                it.putExtra("playPath", record?.data)
                it.putExtra("playName", record?.displayName)
                it.putExtra("recordType", record?.recordType())
                it.putExtra("recordId", record?.mediaId?.toInt())
                it.putExtra("duration", record?.mDuration)
                it.putExtra("isFromOtherApp", intent.isFromOther())
                if (selectTab != null) {
                    it.putExtra("selectPos", selectTab)
                }
                if (searchWord != null) {
                    it.putExtra("searchWord", searchWord)
                }
                if ((playCurrentTime != null) && (playCurrentTime > 0)) {
                    DebugUtil.i(TAG, "startPlayback seekTo : $playCurrentTime")
                    it.putExtra("seekto", playCurrentTime)
                }
                startActivityForResult(it, Constants.REQUEST_CODE_PLAY)
                BuryingPoint.addPlayFromAndDuration(
                    RecorderUserAction.VALUE_PLAY_FROM_PLAYBACK,
                    record?.recordType().toString(),
                    record?.mDuration ?: 0
                )
            }
        }
    }
}