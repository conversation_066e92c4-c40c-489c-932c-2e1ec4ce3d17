/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: IBrowseFileActivityListener
 * Description:
 * Version: 1.0
 * Date: 2022/11/28
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/11/28 1.0 create
 */

package com.soundrecorder.browsefile.parentchild.listener

interface IBrowseFileActivityListener {

    /**
     * 列表搜索状态发生变化
     * @param inSearch 是否处于列表搜索，true：是； false：否
     * @param hasInputSearchValue 搜索关键词是否不为null； true：不为空 false：为空
     * @param outSearchFunc 退出列表搜索方法
     */
    fun onRecordSearchStateChanged(inSearch: <PERSON><PERSON><PERSON>, hasInputSearchValue: <PERSON>olean, outSearchFunc: (() -> Unit))

    /**
     * 转文本搜索状态发生变化
     * @param inSearch 是否处于转文本搜索，true：是； false：否
     * @param hasInputSearchValue 搜索关键词是否不为null； true：不为空 false：为空
     * @param outSearchFunc 退出转文本搜索方法
     */
    fun onConvertSearchStateChanged(inSearch: Boolean, hasInputSearchValue: Boolean, outSearchFunc: (() -> Unit))
}