/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: LoadRecordDataHelper
 * Description:
 * Version: 1.0
 * Date: 2024/1/15
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/1/15 1.0 create
 */

package com.soundrecorder.browsefile.home.load

import android.database.Cursor
import android.database.MatrixCursor
import android.provider.MediaStore
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.GroupInfoDbUtil
import com.soundrecorder.common.db.GroupInfoManager
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.RecordModeUtil.cleanRelativePath
import kotlinx.coroutines.Job
import kotlinx.coroutines.ensureActive
import java.io.File

object LoadRecordDataHelper {

    const val TAG = "LoadRecordDataHelper"


    private fun getRecordsGroupTypeMap(recordsList: List<Record>?): Map<String, Int>? {
        if (!recordsList.isNullOrEmpty()) {
            val map = mutableMapOf<String, Int>()
            recordsList.forEach {
                if (it.groupUuid.isNullOrEmpty()) {
                    GroupInfoManager.getInstance(BaseApplication.getAppContext())
                        .resetGroupInfoForRecord(it)
                }
                map[it.data] = GroupInfoDbUtil.getGroupTypeByGroupUUID(it.groupUuid)
            }
            return map
        }
        return null
    }

    private fun processDuplicateItemsMap(recordsList: List<Record>?): Map<String, Record>? {
        val removedDuplicateItemsMap: HashMap<String, Record> = HashMap()
        recordsList?.let {
            val groupByList = recordsList.groupBy { it.data }
            val duplicates = groupByList.filter { it.value.size > 1 }
            DebugUtil.d(
                TAG,
                "processDuplicateItemsMap,recordsList: ${recordsList.size},groupByList:${groupByList.size}, duplicates:${duplicates.size}"
            )
            duplicates.forEach { (key, value) ->
                value.forEach {
                    if (it.groupUuid.isNullOrEmpty()) {
                        GroupInfoManager.getInstance(BaseApplication.getAppContext())
                            .resetGroupInfoForRecord(it)
                    }
                    if (removedDuplicateItemsMap.containsKey(it.data)) {
                        if (GroupInfoDbUtil.isCustomGroup(it.groupUuid)) {
                            removedDuplicateItemsMap[it.data] = it
                        }
                    } else {
                        removedDuplicateItemsMap[it.data] = it
                    }
                }
            }
        }
        return removedDuplicateItemsMap
    }
    @JvmStatic
    private fun processDuplicateRecordList(recordsList: List<Record>?): List<Record> {
        //数据库里可能存在重复的数据，这里需要去重处理，否则界面上显示的数据重复，引发计数异常或编辑时被同时选中
        val resultList = mutableListOf<Record>()
        recordsList?.let {
            val nonDuplicateItemsMap = processDuplicateItemsMap(recordsList)
            if (nonDuplicateItemsMap == null) {
                resultList.addAll(recordsList)
            } else {
                val nonDuplicatesList = recordsList.filter { record -> !nonDuplicateItemsMap.containsKey(record.data) }
                resultList.addAll(nonDuplicatesList)
                resultList.addAll(nonDuplicateItemsMap.values.toList())
            }
        }
        return resultList
    }
    /**
     * 处理媒体库cursor, 该方法仅针对 默认分组（全部录音、通话录音、普通录音）切换时调用
     */
    @JvmStatic
    fun addCompleteFlag(cursor: Cursor, groupType: Int, job: Job? = null): BrowseListCount {
        val newColumns = arrayOfNulls<String>(cursor.columnNames.size + 1)
        System.arraycopy(cursor.columnNames, 0, newColumns, 0, cursor.columnNames.size)
        newColumns[cursor.columnNames.size] = DatabaseConstant.ConvertColumn.COMPLETE_STATUS
        val completes = getConvertDbMap()
        val listCount = BrowseListCount()
        val newCursor = MatrixCursor(newColumns)
        //先查一遍records表，获取Records表的真实分组，以备判断跟媒体库默认的分组信息是否匹配
        val recordsList = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).queryAllRecordsWithoutRecycleBin()
        val resultList = processDuplicateRecordList(recordsList)
        val recordsGroupTypeMap = getRecordsGroupTypeMap(processDuplicateRecordList(recordsList))
        cursor.use {
            if (!cursor.moveToFirst()) {
                return@use
            }
            var isNeedAddRow: Boolean
            var row: Array<Any?>
            do {
                job?.ensureActive()
                // cursor是来自媒体库的查询结果，获取文件路径，查询记录的分组类型
                val columnDataIndex = cursor.getColumnIndex(MediaStore.Audio.Media.DATA)
                var filePath: String? = null
                if (columnDataIndex >= 0) {
                    filePath = cursor.getString(columnDataIndex)
                    if (!filePath.isNullOrEmpty() && !File(filePath).exists()) {
                        continue
                    }
                }
                val realGroupType = recordsGroupTypeMap?.get(filePath)
                row = arrayOfNulls(newColumns.size)
                isNeedAddRow = false
                for (i in cursor.columnNames.indices) {
                    val columnIndex = cursor.getColumnIndex(cursor.columnNames[i])
                    val columnName = cursor.getColumnName(columnIndex)
                    when (cursor.getType(columnIndex)) {
                        Cursor.FIELD_TYPE_NULL -> row[i] = null
                        Cursor.FIELD_TYPE_INTEGER -> {
                            row[i] = cursor.getLong(columnIndex)
                            if ((MediaStore.Audio.Media._ID == columnName) && (completes[row[i]] != null)) {
                                row[cursor.columnNames.size] = completes[row[i]]
                            }
                        }
                        Cursor.FIELD_TYPE_STRING -> {
                            row[i] = cursor.getString(columnIndex)
                            if (parseStringColumn(row[i].toString(), columnName, groupType, realGroupType, listCount)) {
                                isNeedAddRow = true
                            }
                        }
                        Cursor.FIELD_TYPE_BLOB -> row[i] = cursor.getBlob(columnIndex)
                        Cursor.FIELD_TYPE_FLOAT -> row[i] = cursor.getFloat(columnIndex)
                    }
                }
                if (isNeedAddRow) {
                    newCursor.addRow(row)
                }
            } while (cursor.moveToNext())
        }
        listCount.cursor = newCursor
        listCount.dbRecordsList = resultList
        return listCount
    }

    /**
     * 该方法仅针对默认分组的数据。
     *
     * 解析字符串类型的列：根据文件路径判断是否需要添加到列表中并计算分组文件数量
     * 此方法会在切换默认分组的几个tab时内部调用，都会把计算结果重新赋值给listCount对象。
     * 且相关联的媒体库cursor是来自录音目录下所有音频文件
     * 所以 每次都要重新计算“ 全部录音 普通录音 通话录音 ”的数量
     * @param rowValue 列值
     * @param rowName 列名
     * @param tabGroupType 首页当前tab的groupType
     * @param realGroupType 真实分组的groupType
     * @param listCount
     */
    @JvmStatic
    private fun parseStringColumn(
        rowValue: String,
        rowName: String,
        tabGroupType: Int,
        realGroupType: Int?,
        listCount: BrowseListCount
    ): Boolean {
        var isNeedAddRow = false
        if (MediaStore.Audio.Media.RELATIVE_PATH == rowName) {
            when (rowValue.cleanRelativePath()) {
                RecordModeConstant.RELATIVE_PATH_STANDARD,
                RecordModeConstant.RELATIVE_PATH_BASE,
                RecordModeConstant.RELATIVE_PATH_MEETING,
                RecordModeConstant.RELATIVE_PATH_INTERVIEW,
                RecordModeConstant.RELATIVE_PATH_OPPO_SHARE -> {
                    if (realGroupType != null) {
                        //当前tab选中的分组为“普通录音”和record表真实分组一致，则可显示且计数
                        if (tabGroupType == GroupInfo.INT_DEFAULT_COMMON && realGroupType == tabGroupType) {
                            isNeedAddRow = true
                        }
                        if (realGroupType == GroupInfo.INT_DEFAULT_COMMON) {
                            listCount.addCommonCount()
                        }
                    }  else {
                        //当前tab选中的分组为“普通录音”，则可显示且计数；否则仅计数不显示
                        if (tabGroupType == GroupInfo.INT_DEFAULT_COMMON) {
                            isNeedAddRow = true
                        }
                        listCount.addCommonCount()
                    }
                }
                RecordModeConstant.RELATIVE_PATH_CALL -> {
                    if (realGroupType != null) {
                        if ((tabGroupType == GroupInfo.INT_DEFAULT_CALLING || tabGroupType == GroupInfo.INT_DEFAULT_COMMON)
                            && realGroupType == tabGroupType
                        ) {
                            isNeedAddRow = true
                        }
                        //当前tab选中的分组为“通话录音”和record表真实分组一致，则可显示且计数
                        if (realGroupType == GroupInfo.INT_DEFAULT_CALLING) {
                            listCount.addCallCount()
                        }
                        //record表真实分组为“普通录音”，则仅计数不可显示
                        if (realGroupType == GroupInfo.INT_DEFAULT_COMMON) {
                            listCount.addCommonCount()
                        }
                    }  else {
                        //当前tab选中的分组为“通话录音”，则可显示且计数；否则仅计数不显示
                        if (tabGroupType == GroupInfo.INT_DEFAULT_CALLING) {
                            isNeedAddRow = true
                        }
                        listCount.addCallCount()
                    }
                }
                else -> {}
            }
            if (tabGroupType == GroupInfo.INT_DEFAULT_ALL) {
                isNeedAddRow = true
            }
            listCount.addAllCount()
        }
        return isNeedAddRow
    }

    @JvmStatic
    fun getConvertDbMap(): HashMap<Long, Int> {
        val convertVads = ConvertDbUtil.selectAll()
        val completes = HashMap<Long, Int>()
        for (record in convertVads) {
            var id = record.recordId
            if (id == -1L) {
                id = MediaDBUtils.queryIdByData(record.mediaPath)
                DebugUtil.d("LoadRecordDataHelper", "id is -1 after update the id is $id")
                ConvertDbUtil.updateRecordIdByMediaPath(record.mediaPath, id)
            }
            completes[id] = record.completeStatus
        }
        return completes
    }
}