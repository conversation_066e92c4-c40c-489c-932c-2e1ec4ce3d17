/************************************************************
 * Copyright 2000-2021 OPlus Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : AbsViewModel.kt
 * Version Number: 1.0
 * Description   :
 * Author        : tianjun
 * Date          : 2021.06.04
 * History       :(ID,  2021.06.04, tianjun, Description)
 */
package com.soundrecorder.browsefile.home.load

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.home.item.BaseItemRecordViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap
import kotlin.coroutines.cancellation.CancellationException

abstract class AbsViewModel<M : AbsModel<D, T>, D : BaseItemRecordViewModel, T> : ViewModel(),
    OnDataReadyCompletedListener<D, T> {

    @Volatile
    var model: M? = null
    val liveDataList = MutableLiveData<List<D>>()
    open val logTag = "AbsViewModel"
    private var mCachedRefreshJobMap = ConcurrentHashMap<String, Job>()

    abstract fun createModel(): M

    /**
     * 抽象方法，再子类中重载，根据传入的group参数，获取分组的名称作为key
     * @param group 重载时传入的group
     */
    abstract fun getCacheKey(group: T): String

    private fun cacheCurrentRefreshingJob(key: String, job: Job?) {
        if (job != null) {
            DebugUtil.i(logTag, "refresh, cacheCurrentRefreshingJob, key = $key, job = $job")
            mCachedRefreshJobMap[key] = job
        }
    }

    private fun clearCurrentRefreshingJob(key: String) {
        if (mCachedRefreshJobMap.containsKey(key)) {
            DebugUtil.i(logTag, "refresh, cacheCurrentRefreshingJob,remove key = $key")
            mCachedRefreshJobMap.remove(key)
        }
    }

    private fun getCacheRefreshJob(group: T): Job? {
        return mCachedRefreshJobMap[getCacheKey(group)]
    }

    fun refresh(group: T, isGroupingByContact: Boolean, name: String? = null) {
        viewModelScope.launch(Dispatchers.IO) {
            val currentJobKey = getCacheKey(group)
            // 先取消之前的任务
            for (otherKey in mCachedRefreshJobMap.keys) {
                if (otherKey != currentJobKey) {
                    val startTime = System.currentTimeMillis()
                    val otherJob = mCachedRefreshJobMap[otherKey]
                    otherJob?.cancelAndJoin()
                    DebugUtil.i(logTag, "refresh, try to cancel $otherKey job = $otherJob, cost = ${System.currentTimeMillis() - startTime}")
                    mCachedRefreshJobMap.remove(otherKey)
                }
            }
            if (mCachedRefreshJobMap.containsKey(currentJobKey)) {
                DebugUtil.i(logTag, "refresh, skipped for same group querying!! key = $currentJobKey")
                return@launch
            }

            DebugUtil.i(logTag, "refresh...")
            val tmpDataJob = viewModelScope.launch(Dispatchers.IO) {
                try {
                    ensureActive()
                    ensureModelCreate()
                    model?.refresh(group, isGroupingByContact, name, getCacheRefreshJob(group))
                    //当前任务成功结束后，清理所有缓存的job
                    mCachedRefreshJobMap.clear()
                } catch (e: CancellationException) {
                    DebugUtil.i(logTag, "refresh, there is a cached job cancelled")
                } finally {
                    DebugUtil.i(logTag, "refresh, dataJob over: ${getCacheRefreshJob(group)}")
                    clearCurrentRefreshingJob(getCacheKey(group))
                }
            }
            cacheCurrentRefreshingJob(currentJobKey, tmpDataJob)
        }
    }

    private fun ensureModelCreate() {
        if (model == null) {
            model = createModel()
            model?.register(this)
        }
    }

    override fun onCleared() {
        super.onCleared()
        if (mCachedRefreshJobMap.isNotEmpty()) {
            for (job in mCachedRefreshJobMap.values) {
                job.cancel()
            }
            mCachedRefreshJobMap.clear()
        }
        model = null
    }
}