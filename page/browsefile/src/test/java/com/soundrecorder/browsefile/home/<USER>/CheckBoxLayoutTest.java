/*********************************************************************
 * * Copyright (C), 2022, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/4/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home.view;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.content.Context;
import android.os.Build;
import android.view.View;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.coui.appcompat.checkbox.COUICheckBox;
import com.soundrecorder.browsefile.R;
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption;
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class CheckBoxLayoutTest {

    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @After
    public void tearDown() {
        mContext = null;
    }

    @Test
    public void should_return_not_null_when_onFinishInflate() {
        CheckboxLayout layout = new CheckboxLayout(mContext);
        COUICheckBox mockCheckBox = new COUICheckBox(mContext);
        mockCheckBox.setId(R.id.checkbox);
        layout.addView(mockCheckBox);
        layout.onFinishInflate();
        COUICheckBox checkBox = Whitebox.getInternalState(layout, "mCheckBox");
        Assert.assertNotNull(checkBox);
    }

    @Test
    public void should_return_checked_when_setChecked() {
        CheckboxLayout layout = new CheckboxLayout(mContext);
        COUICheckBox mockCheckBox = new COUICheckBox(mContext);
        Whitebox.setInternalState(layout, "mCheckBox", mockCheckBox);
        Whitebox.setInternalState(mockCheckBox, "mPendingState", -1);
        layout.setChecked(true);
        Assert.assertEquals(mockCheckBox.getState(), COUICheckBox.SELECT_ALL);
    }

    @Test
    public void should_return_not_equals_when_toggle() {
        CheckboxLayout layout = new CheckboxLayout(mContext);
        COUICheckBox mockCheckBox = new COUICheckBox(mContext);
        Whitebox.setInternalState(layout, "mCheckBox", mockCheckBox);
        Whitebox.setInternalState(mockCheckBox, "mPendingState", -1);
        int originState = mockCheckBox.getState();
        layout.toggle();
        Assert.assertNotEquals(originState, mockCheckBox.getState());
    }

    @Test
    public void should_return_1_when_setEditModeState() {
        CheckboxLayout layout = new CheckboxLayout(mContext);
        COUICheckBox mockCheckBox = new COUICheckBox(mContext);
        Whitebox.setInternalState(layout, "mCheckBox", mockCheckBox);
        layout.setEditModeState();
        Assert.assertEquals(1, mockCheckBox.getAlpha(), 0);
    }

    @Test
    public void should_return_0_when_setNormalModeState() {
        CheckboxLayout layout = new CheckboxLayout(mContext);
        COUICheckBox mockCheckBox = new COUICheckBox(mContext);
        Whitebox.setInternalState(layout, "mCheckBox", mockCheckBox);
        layout.setNormalModeState();
        Assert.assertEquals(0, mockCheckBox.getAlpha(), 0);
    }

    @Test
    public void should_return_visible_when_startOrContinueEditModeEnterAnimation() {
        CheckboxLayout layout = new CheckboxLayout(mContext);
        COUICheckBox mockCheckBox = new COUICheckBox(mContext);
        Whitebox.setInternalState(layout, "mCheckBox", mockCheckBox);
        mockCheckBox.setVisibility(View.INVISIBLE);
        layout.startOrContinueEditModeEnterAnimation(null, null);
        Assert.assertEquals(View.VISIBLE, mockCheckBox.getVisibility());
    }

    @Test
    public void should_return_gone_when_startOrContinueEditModeExitAnimation() {
        CheckboxLayout layout = new CheckboxLayout(mContext);
        COUICheckBox mockCheckBox = new COUICheckBox(mContext);
        Whitebox.setInternalState(layout, "mCheckBox", mockCheckBox);
        mockCheckBox.setVisibility(View.VISIBLE);
        layout.startOrContinueEditModeExitAnimation(null, new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                Assert.assertEquals(View.INVISIBLE, mockCheckBox.getVisibility());
            }
        });
    }

    @Test
    public void should_return_null_when_cancelAnimation() {
        CheckboxLayout layout = new CheckboxLayout(mContext);
        Whitebox.setInternalState(layout, "enterAnim", ValueAnimator.ofInt(0, 1));
        layout.cancelAnimation();
        Assert.assertNull(Whitebox.getInternalState(layout, "enterAnim"));
    }

    @Test
    public void should_return_null_when_onRelease() {
        CheckboxLayout layout = new CheckboxLayout(mContext);
        Whitebox.setInternalState(layout, "enterAnim", ValueAnimator.ofInt(0, 1));
        layout.onRelease();
        Assert.assertNull(Whitebox.getInternalState(layout, "enterAnim"));
    }
}
