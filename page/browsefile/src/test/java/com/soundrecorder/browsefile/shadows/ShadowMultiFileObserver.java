package com.soundrecorder.browsefile.shadows;

import android.content.Context;

import com.soundrecorder.common.fileobserve.MultiFileObserver;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

/*********************************************************************
 * * Copyright (C), 2022, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/6/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
@Implements(MultiFileObserver.class)
public class ShadowMultiFileObserver {

    @Implementation
    public void addFolders(Context context) {

    }
}
