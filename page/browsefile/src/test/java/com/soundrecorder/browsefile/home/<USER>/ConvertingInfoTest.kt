/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ConvertingInfoTest
 * Description:
 * Version: 1.0
 * Date: 2023/12/26
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/12/26 1.0 create
 */

package com.soundrecorder.browsefile.home.load

import android.content.Intent
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.modulerouter.playback.PlaybackAction
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class ConvertingInfoTest {

    @Test
    fun should_correct_when_onConvertStatusChanged() {
        ConvertingInfo.onConvertStatusChanged(null)

        // addId
        val intent = Intent().apply {
            putExtra(PlaybackAction.KEY_NOTIFY_RECORD_ID, 1L)
            putExtra(PlaybackAction.KEY_NOTIFY_CONVERT_STATUS, true)
        }
        ConvertingInfo.onConvertStatusChanged(intent)
        Assert.assertTrue(ConvertingInfo.cacheConvertingIds.contains(1L))

        //remove Id
        intent.putExtra(PlaybackAction.KEY_NOTIFY_CONVERT_STATUS, false)
        ConvertingInfo.onConvertStatusChanged(intent)
        Assert.assertFalse(ConvertingInfo.cacheConvertingIds.contains(1L))
    }

    @Test
    fun should_correct_when_clearConvertingInfo() {
        ConvertingInfo.cacheConvertingIds.add(1)
        ConvertingInfo.clearConvertingInfo()
        Assert.assertEquals(0, ConvertingInfo.cacheConvertingIds.size)
    }
}