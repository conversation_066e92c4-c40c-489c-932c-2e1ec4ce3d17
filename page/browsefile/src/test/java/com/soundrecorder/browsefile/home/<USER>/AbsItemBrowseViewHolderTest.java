/*********************************************************************
 * * Copyright (C), 2022, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        : AbsItemBrowseViewHolderTest
 * * Version     : 1.0
 * * Date        : 2022/4/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home.item;

import android.content.Context;
import android.os.Build;
import android.widget.TextView;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.browsefile.home.view.ItemBrowsePlayInfoLayout;
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption;
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;

import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class AbsItemBrowseViewHolderTest {
    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @After
    public void tearDown() {
        mContext = null;
    }

    @Test
    public void should_returnFalse_when_setTextToMaxH3() {
        AbsItemBrowseViewHolder absItemBrowseView = Mockito.mock(AbsItemBrowseViewHolder.class);
        TextView textView = new TextView(mContext);
        absItemBrowseView.setTextToMaxH3(textView, 1.2f);
        Assert.assertNotEquals(textView.getTextSize(), 1.2f);
    }

    @Test
    public void should_returnFalse_when_initBasicView() throws Exception {
        AbsItemBrowseViewHolder absItemBrowseView = Mockito.mock(AbsItemBrowseViewHolder.class);
        Whitebox.invokeMethod(absItemBrowseView, "initBasicView");
        ItemBrowsePlayInfoLayout mItemPlayInfo = Whitebox.getInternalState(absItemBrowseView, "mItemPlayInfo");
        Assert.assertNull(mItemPlayInfo);
    }
}

