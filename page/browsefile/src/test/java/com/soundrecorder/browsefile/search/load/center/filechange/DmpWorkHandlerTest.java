package com.soundrecorder.browsefile.search.load.center.filechange;

import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Looper;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.StorageManager;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

import com.soundrecorder.browsefile.search.load.center.CenterDbManager;
import com.soundrecorder.browsefile.search.load.center.CenterDbUtils;
import com.soundrecorder.browsefile.search.load.center.databean.SearchInsertBean;
import com.soundrecorder.browsefile.shadows.ShadowBaseUtils;
import com.soundrecorder.browsefile.shadows.ShadowCenterDbUtils;
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption;
import com.soundrecorder.browsefile.shadows.ShadowRecorderLogger;
import com.soundrecorder.browsefile.shadows.ShadowStorageManager;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S,
        shadows = {ShadowCenterDbUtils.class, ShadowStorageManager.class,
                ShadowBaseUtils.class, ShadowFeatureOption.class, ShadowRecorderLogger.class})
public class DmpWorkHandlerTest {

    String phoneDir = "";
    String separator = "";
    String test_file_path1 = "";
    String test_file_path2 = "";
    String test_file_path3 = "";

    String FIELD_LOCAL_ITEM_LIST = "mLocalDataMap";
    String FIELD_UPDATE_IDS_SET = "mUpdateIdsList";
    String FIELD_DELETE_IDS_SET = "mDeleteMediaIdsList";
    String FIELD_MOVE_TO_Dirty_SET = "mMoveToDirtyPathSet";
    // 中子兜底
    int MSG_WHAT_DMP_FULL_COMPARE = 0X7;

    private DmpWorkHandler mDmpWorkHandler;
    private Context mContext = null;
    private MockedStatic<BaseApplication> mMockApplication;
    private MockedStatic<CenterDbManager> mMockCenterDbManager;
    private MockedStatic<CenterDbUtils> mMockCenterDbUtils;

    @Before
    public void setUp() {
        mDmpWorkHandler = new DmpWorkHandler(Looper.getMainLooper());
        mContext = Mockito.spy(ApplicationProvider.getApplicationContext());

        mMockApplication = Mockito.mockStatic(BaseApplication.class);
        mMockApplication.when(() -> BaseApplication.getAppContext()).thenReturn(mContext);
        mMockApplication.when(() -> mContext.getApplicationContext()).thenReturn(mContext);

        mMockCenterDbManager = Mockito.mockStatic(CenterDbManager.class);
        mMockCenterDbUtils = Mockito.mockStatic(CenterDbUtils.class);
        Mockito.when(CenterDbUtils.isCenterSearchUsable()).thenReturn(true);

        separator = File.separator;
        phoneDir = StorageManager.getInstance(mContext).getStoragePrefix() + separator;
        String relativePath = "Music/Recordings/Standard Recordings" + separator;
        test_file_path1 = phoneDir + relativePath + "1.mp3";
        test_file_path2 = phoneDir + relativePath + "2.mp3";
        test_file_path3 = phoneDir + relativePath + "3.mp3";
    }

    @After
    public void release() {
        mDmpWorkHandler.release();
        mDmpWorkHandler = null;
        mContext = null;
        mMockApplication.close();
        mMockCenterDbManager.close();
        mMockCenterDbUtils.close();
        mMockCenterDbUtils = null;
    }

    @Test
    public void should_hasMessage_handleMessage() {
        mDmpWorkHandler.sendEmptyMessageDelayed(999, 500);
        Assert.assertTrue(mDmpWorkHandler.hasMessages(999) == true);
    }

    @Test
    public void should_when_syncDirtyDataToDmp() {
        CopyOnWriteArrayList<String> dirtyList = Whitebox.getInternalState(mDmpWorkHandler, FIELD_MOVE_TO_Dirty_SET);
        SafeArrayMap<String, Long> localMap = Whitebox.getInternalState(mDmpWorkHandler, FIELD_LOCAL_ITEM_LIST);
        localMap.put(test_file_path1.toLowerCase(), 1L);
        dirtyList.add(test_file_path1);

        mDmpWorkHandler.syncDirtyDataToDmp();
        Assert.assertTrue((mDmpWorkHandler.hasMessages(DmpWorkHandler.MSG_WHAT_SYNC_DMP) == true));
    }

    @Ignore
    @Test
    public void should_when_fileUpdateSuccess() {
        mMockCenterDbManager.when(() -> CenterDbManager.insertOrUpdateDmp((List<SearchInsertBean>) any(), (Boolean) any())).thenReturn(true);

        mDmpWorkHandler.fileUpdateChangeSuccess(1, test_file_path1);
    }

    @Test
    public void should_when_release() {
        mDmpWorkHandler.release();
        SafeArrayMap<String, Long> mLocalDataMap = Whitebox.getInternalState(mDmpWorkHandler, FIELD_LOCAL_ITEM_LIST);
        CopyOnWriteArrayList<String> dirtyList =
                Whitebox.getInternalState(mDmpWorkHandler, FIELD_MOVE_TO_Dirty_SET);
        Assert.assertTrue(mLocalDataMap.isEmpty());
        Assert.assertTrue(dirtyList.isEmpty());
    }

    @Test
    public void should_when_doInitDmpService() throws Exception {
        mMockCenterDbManager.when(() -> CenterDbManager.getDmpConfigInfo()).thenReturn(true);
        Whitebox.invokeMethod(mDmpWorkHandler, "doInitDmpService");
    }

    @Ignore
    @Test
    public void should_when_doRecorderFullCompare() throws Exception {
        Whitebox.invokeMethod(mDmpWorkHandler, "doRecorderFullCompare", true);
    }

    @Test
    public void should_when_queryMidaCursor() throws Exception {
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        Cursor mockCursor = mock(Cursor.class);
        when(mContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.query((Uri) any(), (String[]) any(), (String) any(), (String[]) any(), (String) any())).thenReturn(mockCursor);
        Cursor cursor = Whitebox.invokeMethod(mDmpWorkHandler, "queryMidaCursor");
        Assert.assertNotNull(cursor);
    }

    @Test
    public void should_when_onFileUpdated() throws Exception {
        SafeArrayMap<String, Long> localData = Whitebox.getInternalState(mDmpWorkHandler, FIELD_LOCAL_ITEM_LIST);
        localData.put(test_file_path2.toLowerCase(), 2L);
        ArrayList<Long> updateList = Whitebox.getInternalState(mDmpWorkHandler, FIELD_UPDATE_IDS_SET);
        Whitebox.invokeMethod(mDmpWorkHandler, "onFileUpdated", test_file_path2);
        Assert.assertTrue(updateList.contains(2L));
    }

    @Test
    public void should_when_onFileBeDeleted() throws Exception {
        SafeArrayMap<String, Long> localData = Whitebox.getInternalState(mDmpWorkHandler, FIELD_LOCAL_ITEM_LIST);
        localData.put(test_file_path3.toLowerCase(), 3L);
        ArrayList<Long> deleteList = Whitebox.getInternalState(mDmpWorkHandler, FIELD_DELETE_IDS_SET);
        Whitebox.invokeMethod(mDmpWorkHandler, "onFileBeDeleted", test_file_path3);

        Assert.assertTrue(deleteList.contains(3L));
    }

    @Test
    public void should_when_onFileDirBeMoved() throws Exception {
        SafeArrayMap<String, Long> localData = Whitebox.getInternalState(mDmpWorkHandler, FIELD_LOCAL_ITEM_LIST);
        localData.put(test_file_path2.toLowerCase(), 2L);
        localData.put(test_file_path1.toLowerCase(), 1L);
        localData.put(test_file_path3.toLowerCase(), 3L);
        Whitebox.invokeMethod(mDmpWorkHandler, "onFileDirBeMoved",
                phoneDir + "Music/Recordings/Standard Recordings$separator");
        Assert.assertTrue(mDmpWorkHandler.hasMessages(DmpWorkHandler.MSG_WHAT_RECORDER_FULL_COMPARE) == true);
    }

    @Test
    public void should_when_doSycnChangedFileToDmp() throws Exception {
        Whitebox.invokeMethod(mDmpWorkHandler, "doSycnChangedFileToDmp");
        ArrayList<Long> updateList = Whitebox.getInternalState(mDmpWorkHandler, FIELD_UPDATE_IDS_SET);
        ArrayList<Long> deleteList = Whitebox.getInternalState(mDmpWorkHandler, FIELD_DELETE_IDS_SET);

        Assert.assertTrue(updateList.isEmpty());
        Assert.assertTrue(deleteList.isEmpty());
    }

    @Test
    public void should_when_checkMoveToDirtyPathMediaId() throws Exception {
        SafeArrayMap<String, Long> localData =
                Whitebox.getInternalState(mDmpWorkHandler, FIELD_LOCAL_ITEM_LIST);
        List<String> dirtySet = Whitebox.getInternalState(mDmpWorkHandler, FIELD_MOVE_TO_Dirty_SET);

        String dirtyPath = phoneDir + "Music/Recording/Standard Recordings$separator dirty.mp3";
        dirtySet.add(dirtyPath);
        Assert.assertEquals(dirtySet.size(), 1);

        localData.put(dirtyPath.toLowerCase(), 4L);
        Whitebox.invokeMethod(mDmpWorkHandler, "checkMoveToDirtyPathMediaId");
        Assert.assertEquals(dirtySet.size(), 0);
    }

    @Test
    public void should_correct_when_queryMediaIdByPath() throws Exception {
        SafeArrayMap<String, Long> localData = Whitebox.getInternalState(mDmpWorkHandler, FIELD_LOCAL_ITEM_LIST);
        localData.put(test_file_path1.toLowerCase(), 1L);
        Long mediaId =
                Whitebox.invokeMethod(mDmpWorkHandler, "queryMediaIdByPath", test_file_path1, true);
        Assert.assertTrue(1L == mediaId);
    }

    @Test
    public void should_correct_when_getRelativePathFromAbsolutePath() throws Exception {
        String relativePath =
                Whitebox.invokeMethod(mDmpWorkHandler, "getRelativePathFromAbsolutePath", test_file_path1);
        Assert.assertEquals("Music/Recordings/Standard Recordings", relativePath);
    }

    @Test
    public void should_correct_when_doNotifyDmpFullCompare() {
        mMockCenterDbManager.when(() -> CenterDbManager.notifyDmpAllSync()).thenReturn(true);
        mDmpWorkHandler.sendEmptyMessage(MSG_WHAT_DMP_FULL_COMPARE);
    }
}
