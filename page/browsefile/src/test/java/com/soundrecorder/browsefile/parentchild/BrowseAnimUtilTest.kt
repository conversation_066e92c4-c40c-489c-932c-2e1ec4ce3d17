/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: BrowseAnimUtilTest
 * Description:
 * Version: 1.0
 * Date: 2023/1/11
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/1/11 1.0 create
 */

package com.soundrecorder.browsefile.parentchild

import android.content.Context
import android.os.Build
import android.view.View
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Guideline
import androidx.core.view.isVisible
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class BrowseAnimUtilTest {
    private var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun release() {
        context = null
    }

    @Test
    fun should_when_runShowDetailAnim() {
        val mockParentStartLine = Mockito.mock(Guideline::class.java)
        val mockChildStartLine = Mockito.mock(Guideline::class.java)
        val parentView = FrameLayout(context!!)
        val mockChildView = Mockito.mock(FrameLayout::class.java)
        Mockito.`when`(mockChildView.parent).thenReturn(Mockito.mock(ConstraintLayout::class.java))
        val animUtil = BrowseAnimUtil(
            mockParentStartLine,
            Mockito.mock(Guideline::class.java),
            mockChildStartLine,
            Mockito.mock(Guideline::class.java),
            parentView,
            mockChildView)
        animUtil.runShowDetailAnim {}

        val maskView = Whitebox.getInternalState<View>(animUtil, "mParentMaskView")
        Assert.assertNotNull(maskView)

        animUtil.hideParentMaskView()
        Assert.assertFalse(maskView.isVisible)

        animUtil.release()
    }

    @Test
    fun should_when_runRemoveDetailAnim() {
        val mockParentStartLine = Mockito.mock(Guideline::class.java)
        val mockChildStartLine = Mockito.mock(Guideline::class.java)
        val mockParentView = Mockito.mock(FrameLayout::class.java)
        val mockChildView = Mockito.mock(FrameLayout::class.java)
        Mockito.`when`(mockParentView.context).thenReturn(context)
        Mockito.`when`(mockChildView.parent).thenReturn(Mockito.mock(ConstraintLayout::class.java))
        val animUtil = BrowseAnimUtil(
            mockParentStartLine,
            Mockito.mock(Guideline::class.java),
            mockChildStartLine,
            Mockito.mock(Guideline::class.java),
            mockParentView,
            mockChildView)

        animUtil.runRemoveDetailAnim() {
            val maskView = Whitebox.getInternalState<View>(animUtil, "mParentMaskView")
            Assert.assertNull(maskView.parent)
        }

        animUtil.release()
        val maskView = Whitebox.getInternalState<View>(animUtil, "mParentMaskView")
        Assert.assertNull(maskView)
    }
}