/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ItemAnimationUtilTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/11/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home

import android.animation.Animator
import android.animation.AnimatorSet
import android.content.Context
import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import androidx.recyclerview.widget.RecyclerView
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class ItemAnimationUtilTest {
    private var context: Context? = null
    private var mockedBaseApplication: MockedStatic<BaseApplication>? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        mockedBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mockedBaseApplication?.`when`<Any> { BaseApplication.getAppContext() }?.thenReturn(context)
    }

    @After
    fun tearDown() {
        if (mockedBaseApplication != null) {
            mockedBaseApplication?.close()
            mockedBaseApplication = null
        }
        context = null
    }

    @Test
    fun should_returnAnimatorSet_when_recordLayoutAppearAnimator() {
        val recorderLayout = Mockito.mock(RelativeLayout::class.java)
        val recordGradienImg = Mockito.mock(View::class.java)
        val animatorSet: AnimatorSet =
            ItemAnimationUtil.recordLayoutAppearAnimator(
                recorderLayout,
                recordGradienImg,
                context!!,
                false
            )
        val childAnimations = animatorSet.childAnimations
        Assert.assertNotNull(childAnimations)
        Assert.assertEquals(3, childAnimations.size.toLong())
    }

    @Test
    fun should_returnAnimatorSet_when_recordLayoutDismissAnimator() {
        val recorderLayout = Mockito.mock(RelativeLayout::class.java)
        val recordGradienImg = Mockito.mock(View::class.java)
        val animatorSet: AnimatorSet =
            ItemAnimationUtil.recordLayoutDismissAnimator(
                recorderLayout,
                recordGradienImg,
                context!!
            )
        val childAnimations = animatorSet.childAnimations
        Assert.assertNotNull(childAnimations)
        Assert.assertEquals(3, childAnimations.size.toLong())
    }

    @Test
    fun should_returnAnimatorSet_when_animWithListener() {
        val view = Mockito.mock(View::class.java)
        val animator = ItemAnimationUtil.animWithListener(
            view,
            0,
            100,
            200,
            object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                }

                override fun onAnimationEnd(animation: Animator) {
                }

                override fun onAnimationCancel(animation: Animator) {
                }

                override fun onAnimationRepeat(animation: Animator) {
                }
            }
        )
        Assert.assertNotNull(view)
        Assert.assertNotNull(animator)
        Assert.assertEquals(200, animator.duration)

        view.layoutParams = RecyclerView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        ItemAnimationUtil.animWithListener(
            view,
            0,
            100,
            200,
            object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                }

                override fun onAnimationEnd(animation: Animator) {
                }

                override fun onAnimationCancel(animation: Animator) {
                }

                override fun onAnimationRepeat(animation: Animator) {
                }
            }
        )
    }

    @Test
    fun should_returnAnimatorSet_when_transitionAnimator() {
        val view = Mockito.mock(View::class.java)
        val animator = ItemAnimationUtil.transitionAnimator(
            view,
            100,
            object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                }

                override fun onAnimationEnd(animation: Animator) {
                }

                override fun onAnimationCancel(animation: Animator) {
                }

                override fun onAnimationRepeat(animation: Animator) {
                }
            }
        )
        Assert.assertNotNull(view)
        Assert.assertNotNull(animator)
    }

    @Test
    fun should_returnAnimatorSet_when_transAlphaTopHeaderAnimator() {
        val view = Mockito.mock(ViewGroup::class.java)
        val imageView = Mockito.mock(ImageView::class.java)
        imageView.id = androidx.core.R.id.icon
        val animator = ItemAnimationUtil.transAlphaTopHeaderAnimator(
            view,
            0,
            100,
            object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                }

                override fun onAnimationEnd(animation: Animator) {
                }

                override fun onAnimationCancel(animation: Animator) {
                }

                override fun onAnimationRepeat(animation: Animator) {
                }
            }
        )
        Assert.assertNotNull(view)
        Assert.assertNotNull(animator)

        view.addView(imageView)
        ItemAnimationUtil.transAlphaTopHeaderAnimator(
            view,
            0,
            100,
            object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                }

                override fun onAnimationEnd(animation: Animator) {
                }

                override fun onAnimationCancel(animation: Animator) {
                }

                override fun onAnimationRepeat(animation: Animator) {
                }
            }
        )
        ItemAnimationUtil.transAlphaTopHeaderAnimator(
            null,
            0,
            100,
            object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                }

                override fun onAnimationEnd(animation: Animator) {
                }

                override fun onAnimationCancel(animation: Animator) {
                }

                override fun onAnimationRepeat(animation: Animator) {
                }
            }
        )
    }
}