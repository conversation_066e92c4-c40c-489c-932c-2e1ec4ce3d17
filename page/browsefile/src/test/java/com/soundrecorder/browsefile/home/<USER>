package com.soundrecorder.browsefile.home;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.robolectric.Shadows.shadowOf;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import static org.mockito.Mockito.mock;

import com.coui.appcompat.checkbox.COUICheckBox;
import com.soundrecorder.browsefile.BrowseFile;
import com.soundrecorder.browsefile.R;
import com.soundrecorder.browsefile.home.load.ViewStatus;
import com.soundrecorder.browsefile.shadows.ShadowCOUIVersionUtil;
import com.soundrecorder.browsefile.shadows.ShadowCursorHelper;
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption;
import com.soundrecorder.browsefile.shadows.ShadowMultiFileObserver;
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.browsefile.shadows.ShadowOplusCompactUtil;
import com.soundrecorder.browsefile.shadows.ShadowStorageManager;
import com.soundrecorder.common.databean.Record;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;

import com.soundrecorder.browsefile.home.dialog.navigation.NavigationViewManager;
import com.soundrecorder.browsefile.home.dialog.navigation.NavigationViewManager.OnOptionCompletedListener;
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel;
import com.soundrecorder.browsefile.home.load.BrowseViewModel;
import com.soundrecorder.common.fileoperator.CheckOperate;

import org.robolectric.shadows.ShadowDialog;
import org.robolectric.util.Scheduler;

import java.util.concurrent.ConcurrentHashMap;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S,
        shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class,
                ShadowMultiFileObserver.class, ShadowOplusCompactUtil.class,
                ShadowStorageManager.class, ShadowCOUIVersionUtil.class})
public class BrowseFragmentTest {
    private String mInternalStateBrowseViewModel = "mBrowseViewModel";
    private ActivityController<BrowseFile> mController;
    private BrowseFile mActivity;
    private BrowseFragment mFragment;
    private Context mContext;

    @Before
    public void setUp() throws Exception {
        mContext = ApplicationProvider.getApplicationContext();
        mController = Robolectric.buildActivity(BrowseFile.class);
        mActivity = mController.create().resume().get();
        mFragment = (BrowseFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
        Whitebox.invokeMethod(mFragment, "createViewModel");
    }

    @After
    public void tearDown() {
        mFragment = null;
        mActivity = null;
        mController = null;
        mContext = null;
    }

    @Test
    public void should_returnTrue_when_onOptionCompleted() throws Exception {
        Whitebox.invokeMethod(mFragment, "ensureNavigationViewManager");
        NavigationViewManager mNavigationViewManager = Whitebox.getInternalState(mFragment, "mNavigationViewManager");
        Assert.assertNotNull(mNavigationViewManager);
        OnOptionCompletedListener onOptionCompletedListener = Whitebox.getInternalState(mNavigationViewManager, "onOptionCompletedListener");
        onOptionCompletedListener.onOptionCompleted(CheckOperate.OPERATE_RENAME, null, null);
        Assert.assertFalse(Whitebox.getInternalState(mFragment, "isDeviceSecureDelete"));
    }

    @Test
    public void should_dialog_when_showSingle() throws Exception {
        Whitebox.invokeMethod(mFragment, "showLimitDialog");
        ShadowDialog dialog = shadowOf(RuntimeEnvironment.application).getLatestDialog();
        Assert.assertNotNull(dialog);
    }

    @Test
    public void should_dialog_when_onCreateOptionsMenuCheckRestoreWindow() throws Exception {
        BrowseViewModel mBrowseViewModel = Whitebox.getInternalState(mFragment, mInternalStateBrowseViewModel);
        Whitebox.setInternalState(mBrowseViewModel, "isDeleteDialogShowing", true, BrowseViewModel.class);
        Whitebox.setInternalState(mBrowseViewModel, "isRenameDialogShowing", true, BrowseViewModel.class);
        Whitebox.invokeMethod(mFragment, "onCreateOptionsMenuCheckRestoreWindow");
    }

    @Test
    public void should_dialog_when_checkRestoreShowLimitDialog() throws Exception {
        BrowseViewModel mBrowseViewModel = Whitebox.getInternalState(mFragment, mInternalStateBrowseViewModel);
        Whitebox.setInternalState(mBrowseViewModel, "isLimitDialogShowing", true, BrowseViewModel.class);
        Whitebox.invokeMethod(mFragment, "checkRestoreShowLimitDialog");
        ShadowDialog dialog = shadowOf(RuntimeEnvironment.application).getLatestDialog();
        Assert.assertNotNull(dialog);
    }

    @Test
    public void should_returnFalse_when_isSmallWindowHeight() throws Exception {
        boolean isflag = Whitebox.invokeMethod(mFragment, "isSmallWindowHeight");
        Assert.assertFalse(isflag);
    }


    @Test
    public void should_returnTrue_when_responseDelete() throws Exception {
        Whitebox.invokeMethod(mFragment, "responseDelete", -1);
        Assert.assertTrue(Whitebox.getInternalState(mFragment, "isDeviceSecureDelete"));
    }

    @Test
    public void should_returnTrue_when_responseMms() throws Exception {
        Whitebox.invokeMethod(mFragment, "responseMms", -1, new Intent());
        assertTrue(mActivity.isFinishing());
    }

    @Test
    public void should_testState() throws Exception {
        BrowseViewModel mBrowseViewModel = Whitebox.getInternalState(mFragment, mInternalStateBrowseViewModel);
        Assert.assertFalse(Whitebox.invokeMethod(mFragment, "checkRestoreDelete"));
        mBrowseViewModel.setClickDelete(true);
        Assert.assertTrue(Whitebox.invokeMethod(mFragment, "checkRestoreDelete"));
        Assert.assertFalse(Whitebox.invokeMethod(mFragment, "checkRestoreRename"));
        mBrowseViewModel.setClickRename(true);
        Assert.assertTrue(Whitebox.invokeMethod(mFragment, "checkRestoreRename"));
    }

    /* BottomNavigationView's click event. */
    @Test
    @Config(shadows = ShadowCursorHelper.class)
    public void should_startActivity_when_menuItemSelectedSend() throws Exception {
        mFragment.onCreateView(LayoutInflater.from(mActivity), mActivity.findViewById(R.id.fl_left_container), null);
        Whitebox.invokeMethod(mFragment, "ensureNavigationView");
        Whitebox.invokeMethod(mFragment, "ensureLiveDataParams");
        MenuItem item = mock(MenuItem.class);
        doReturn(R.id.item_send).when(item).getItemId();
        ConcurrentHashMap<Long, Record> mockMap = new ConcurrentHashMap<>();
        mockMap.put(1001L, mock(Record.class));
        ItemBrowseRecordViewModel.Companion.getLiveSelectedMap().get(mActivity.getTaskId()).setValue(mockMap);
        Whitebox.invokeMethod(mFragment, "onItemSelected", item);
        Intent intent = shadowOf(RuntimeEnvironment.application).getNextStartedActivity();
        Assert.assertEquals(Intent.ACTION_CHOOSER, intent.getAction());
    }

    @Test
    @Config(shadows = ShadowCursorHelper.class)
    public void should_popDialog_when_menuItemSelectedRename() throws Exception {
        mFragment.onCreateView(LayoutInflater.from(mActivity), mActivity.findViewById(R.id.fl_left_container), null);
        Whitebox.invokeMethod(mFragment, "ensureNavigationView");
        Whitebox.invokeMethod(mFragment, "ensureLiveDataParams");
        MenuItem item = mock(MenuItem.class);
        doReturn(R.id.item_rename).when(item).getItemId();
        ConcurrentHashMap<Long, Record> mockMap = new ConcurrentHashMap<>();
        Record record = new Record();
        record.setDisplayName("test.mp3");
        mockMap.put(1001L, record);
        ItemBrowseRecordViewModel.Companion.getLiveSelectedMap().get(mActivity.getTaskId()).setValue(mockMap);
        Whitebox.invokeMethod(mFragment, "onItemSelected", item);
        ShadowDialog dialog = shadowOf(RuntimeEnvironment.application).getLatestDialog();
        Assert.assertNotNull(dialog);
    }

    @Test
    @Config(shadows = ShadowCursorHelper.class)
    public void should_popDialog_when_menuItemSelectedDelete() throws Exception {
        mFragment.onCreateView(LayoutInflater.from(mActivity), mActivity.findViewById(R.id.fl_left_container), null);
        Whitebox.invokeMethod(mFragment, "ensureNavigationView");
        Whitebox.invokeMethod(mFragment, "ensureLiveDataParams");
        MenuItem item = mock(MenuItem.class);
        doReturn(R.id.item_delete).when(item).getItemId();
        ConcurrentHashMap<Long, Record> mockMap = new ConcurrentHashMap<>();
        Record record = new Record();
        record.setDisplayName("test.mp3");
        mockMap.put(1001L, record);
        ItemBrowseRecordViewModel.Companion.getLiveSelectedMap().get(mActivity.getTaskId()).setValue(mockMap);
        Whitebox.invokeMethod(mFragment, "onItemSelected", item);
        Scheduler scheduler = Robolectric.getForegroundThreadScheduler();
        if (!scheduler.areAnyRunnable()) {
            Thread.sleep(500);
        }
        scheduler.runOneTask();
        Dialog latestDialog = ShadowDialog.getLatestDialog();
        assertNotNull(latestDialog);
    }

    @Test
    @Config(shadows = ShadowCursorHelper.class)
    public void should_startedActivity_when_menuItemSelectedSetAs() throws Exception {
        mFragment.onCreateView(LayoutInflater.from(mActivity), mActivity.findViewById(R.id.fl_left_container), null);
        Whitebox.invokeMethod(mFragment, "ensureNavigationView");
        Whitebox.invokeMethod(mFragment, "ensureLiveDataParams");
        MenuItem item = mock(MenuItem.class);
        doReturn(R.id.set_as).when(item).getItemId();
        ConcurrentHashMap<Long, Record> mockMap = new ConcurrentHashMap<>();
        Record record = new Record();
        record.setDisplayName("test.mp3");
        mockMap.put(1001L, record);
        ItemBrowseRecordViewModel.Companion.getLiveSelectedMap().get(mActivity.getTaskId()).setValue(mockMap);
        Whitebox.invokeMethod(mFragment, "onItemSelected", item);
        Intent nextStartedActivity = shadowOf(RuntimeEnvironment.application).getNextStartedActivity();
        assertNotNull(nextStartedActivity);
        assertEquals("com.android.settings", nextStartedActivity.getPackage());
    }

    @Test
    public void should_correct_when_updateViewStatus() throws Exception {
        String methodUpdateViewStatus = "updateViewStatus";
        String internalStateLoadingView = "mLoadingViewLayout";
        mFragment.onCreateView(LayoutInflater.from(mActivity), mActivity.findViewById(R.id.fl_left_container), null);
        Whitebox.invokeMethod(mFragment, methodUpdateViewStatus, ViewStatus.QUERYING);
        View loadingView = Whitebox.getInternalState(mFragment, internalStateLoadingView);
        assertNotNull(loadingView);
        assertEquals(View.VISIBLE, loadingView.getVisibility());
        // loading to empty
        Whitebox.invokeMethod(mFragment, methodUpdateViewStatus, ViewStatus.EMPTY);
        View emptyView = Whitebox.getInternalState(mFragment, "mEmptyScrollview");
        assertNotNull(emptyView);
        assertEquals(View.VISIBLE, emptyView.getVisibility());

        // empty to loading
        Whitebox.invokeMethod(mFragment, methodUpdateViewStatus, ViewStatus.QUERYING);
        loadingView = Whitebox.getInternalState(mFragment, internalStateLoadingView);
        assertEquals(View.GONE, loadingView.getVisibility());

        //to permission
        Whitebox.invokeMethod(mFragment, methodUpdateViewStatus, ViewStatus.NO_PERMISSION);
        View permissionView = Whitebox.getInternalState(mFragment, "mPermissionScrollview");
        assertEquals(View.VISIBLE, permissionView.getVisibility());

        // permission to loading
        Whitebox.invokeMethod(mFragment, methodUpdateViewStatus, ViewStatus.QUERYING);
        loadingView = Whitebox.getInternalState(mFragment, internalStateLoadingView);
        assertEquals(View.VISIBLE, loadingView.getVisibility());

        // loading to content
        Whitebox.invokeMethod(mFragment, methodUpdateViewStatus, ViewStatus.SHOW_CONTENT);
        loadingView = Whitebox.getInternalState(mFragment, internalStateLoadingView);
        assertEquals(View.GONE, loadingView.getVisibility());
    }

    @Test
    public void should_correct_when_isFastPlaying() {
        mFragment.onCreateView(LayoutInflater.from(mActivity), mActivity.findViewById(R.id.fl_left_container), null);
        assertFalse(mFragment.isFastPlaying());

        BrowseViewModel browseViewModel = Whitebox.getInternalState(mFragment, mInternalStateBrowseViewModel);
        assertNotNull(browseViewModel);
        browseViewModel.getFastPlayHelper().setPlayRecordItem(new ItemBrowseRecordViewModel());
        browseViewModel.getFastPlayHelper().getMediaPlayerManager().getPlayerState().setValue(2);

        assertTrue(mFragment.isFastPlaying());
    }
}
