/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  TipStatusGuideObserverTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/1/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home.view.cloudtip

import android.os.Build
import android.view.View
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.MutableLiveData
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.browsefile.home.item.BrowseAdapter
import com.soundrecorder.browsefile.home.item.IBrowseViewHolderListener
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.modulerouter.cloudkit.tipstatus.ICloudSwitchChangeListener
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class TipStatusGuideObserverTest {

    private var activity: FragmentActivity? = null

    @Before
    fun init() {
        activity = Robolectric.buildActivity(FragmentActivity::class.java).create(null).resume().get()
    }

    @Test
    fun should_notnull_when_init() {
        val act = activity ?: return
        val browseAdapter = BrowseAdapter(act, act, object : IBrowseViewHolderListener {
            override fun getWindowType(): MutableLiveData<WindowType> {
                return MutableLiveData()
            }

            override fun getPlayLiveData(): MutableLiveData<StartPlayModel?> {
                return MutableLiveData()
            }
        })
        val observer = TipStatusGuideObserver(act, browseAdapter)
        val cloudListener = Whitebox.getInternalState<ICloudSwitchChangeListener>(observer, "mCloudListener")
        Assert.assertNotNull(cloudListener)
    }

    @Test
    fun should_null_when_onDestroy() {
        val act = activity ?: return
        val browseAdapter = BrowseAdapter(act, act, object : IBrowseViewHolderListener {
            override fun getWindowType(): MutableLiveData<WindowType> {
                return MutableLiveData()
            }

            override fun getPlayLiveData(): MutableLiveData<StartPlayModel?> {
                return MutableLiveData()
            }
        })
        val observer = TipStatusGuideObserver(act, browseAdapter)
        observer.onDestroy(activity!!)
        val cloudListener = Whitebox.getInternalState<ICloudSwitchChangeListener>(observer, "mCloudListener")
        Assert.assertNull(cloudListener)
    }

    @Test
    fun should_null_when_removeGuideView() {
        val act = activity ?: return
        val browseAdapter = BrowseAdapter(act, act, object : IBrowseViewHolderListener {
            override fun getWindowType(): MutableLiveData<WindowType> {
                return MutableLiveData()
            }

            override fun getPlayLiveData(): MutableLiveData<StartPlayModel?> {
                return MutableLiveData()
            }
        })
        browseAdapter.setHeader(BrowseAdapter.TYPE_HEADER_CLOUD, View(activity))
        Assert.assertNotNull(browseAdapter.getHeadeView(BrowseAdapter.TYPE_HEADER_CLOUD))

        val observer = TipStatusGuideObserver(act, browseAdapter)
        Whitebox.invokeMethod<Void>(observer, "removeGuideView")
        Assert.assertNull(browseAdapter.getHeadeView(BrowseAdapter.TYPE_HEADER_CLOUD))
    }
}