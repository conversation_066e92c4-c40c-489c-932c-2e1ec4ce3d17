package com.soundrecorder.playback.aisummary

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.lifecycle.MutableLiveData
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.utils.DebugUtil
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.MockitoAnnotations

/**
 * 摘要版本切换功能测试
 * 
 * 测试场景：
 * 1. 多个摘要版本的加载和显示
 * 2. 版本切换功能（上一个、下一个、最新）
 * 3. 选中状态的持久化
 * 4. UI状态的正确更新
 */
@ExperimentalCoroutinesApi
@RunWith(AndroidJUnit4::class)
class SummaryVersionSwitchTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    @Mock
    private lateinit var mockDataManager: AISummaryDataManager

    @Mock
    private lateinit var mockSummaryViewModel: AISummaryViewModel

    private lateinit var summaryHistoryLiveData: MutableLiveData<List<AISummaryDataManager.SummaryEntity>>
    private lateinit var currentIndexLiveData: MutableLiveData<Int>
    private lateinit var hasMultipleVersionsLiveData: MutableLiveData<Boolean>

    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        
        // 初始化 LiveData
        summaryHistoryLiveData = MutableLiveData()
        currentIndexLiveData = MutableLiveData()
        hasMultipleVersionsLiveData = MutableLiveData()

        // 配置 mock ViewModel
        `when`(mockSummaryViewModel.summaryHistory).thenReturn(summaryHistoryLiveData)
        `when`(mockSummaryViewModel.currentSummaryIndex).thenReturn(currentIndexLiveData)
        `when`(mockSummaryViewModel.hasMultipleVersions).thenReturn(hasMultipleVersionsLiveData)
    }

    @Test
    fun testMultipleSummaryVersionsLoading() = runTest {
        // 测试多个摘要版本的加载
        
        val testRecordId = 12345L
        val testSummaries = createTestSummaryHistory(testRecordId, 3)
        
        DebugUtil.d("Test", "=== 测试多个摘要版本加载 ===")
        
        // 模拟加载摘要历史
        summaryHistoryLiveData.value = testSummaries
        hasMultipleVersionsLiveData.value = true
        currentIndexLiveData.value = 0 // 默认选中最新版本
        
        // 验证数据状态
        assert(summaryHistoryLiveData.value?.size == 3)
        assert(hasMultipleVersionsLiveData.value == true)
        assert(currentIndexLiveData.value == 0)
        
        DebugUtil.d("Test", "=== 多个摘要版本加载测试通过 ===")
    }

    @Test
    fun testVersionSwitching() = runTest {
        // 测试版本切换功能
        
        val testRecordId = 67890L
        val testSummaries = createTestSummaryHistory(testRecordId, 5)
        
        DebugUtil.d("Test", "=== 测试版本切换功能 ===")
        
        // 初始状态：最新版本（索引0）
        summaryHistoryLiveData.value = testSummaries
        currentIndexLiveData.value = 0
        
        // 测试切换到上一个版本
        currentIndexLiveData.value = 1
        assert(currentIndexLiveData.value == 1)
        
        // 测试切换到下一个版本
        currentIndexLiveData.value = 0
        assert(currentIndexLiveData.value == 0)
        
        // 测试切换到最旧版本
        currentIndexLiveData.value = 4
        assert(currentIndexLiveData.value == 4)
        
        // 测试切换回最新版本
        currentIndexLiveData.value = 0
        assert(currentIndexLiveData.value == 0)
        
        DebugUtil.d("Test", "=== 版本切换功能测试通过 ===")
    }

    @Test
    fun testSingleVersionBehavior() = runTest {
        // 测试只有一个版本时的行为
        
        val testRecordId = 11111L
        val testSummaries = createTestSummaryHistory(testRecordId, 1)
        
        DebugUtil.d("Test", "=== 测试单版本行为 ===")
        
        // 模拟只有一个摘要版本
        summaryHistoryLiveData.value = testSummaries
        hasMultipleVersionsLiveData.value = false
        currentIndexLiveData.value = 0
        
        // 验证状态
        assert(summaryHistoryLiveData.value?.size == 1)
        assert(hasMultipleVersionsLiveData.value == false)
        assert(currentIndexLiveData.value == 0)
        
        DebugUtil.d("Test", "=== 单版本行为测试通过 ===")
    }

    @Test
    fun testSelectedStatePersistence() = runTest {
        // 测试选中状态的持久化
        
        val testRecordId = 22222L
        val testSummaries = createTestSummaryHistory(testRecordId, 3)
        
        DebugUtil.d("Test", "=== 测试选中状态持久化 ===")
        
        // 模拟选中第二个版本
        testSummaries[1] = testSummaries[1].copy(isSelected = true)
        testSummaries[0] = testSummaries[0].copy(isSelected = false)
        testSummaries[2] = testSummaries[2].copy(isSelected = false)
        
        summaryHistoryLiveData.value = testSummaries
        currentIndexLiveData.value = 1 // 选中第二个版本
        
        // 验证选中状态
        assert(currentIndexLiveData.value == 1)
        assert(testSummaries[1].isSelected)
        assert(!testSummaries[0].isSelected)
        assert(!testSummaries[2].isSelected)
        
        DebugUtil.d("Test", "=== 选中状态持久化测试通过 ===")
    }

    @Test
    fun testVersionInfoDisplay() = runTest {
        // 测试版本信息显示
        
        val testRecordId = 33333L
        val testSummaries = createTestSummaryHistory(testRecordId, 4)
        
        DebugUtil.d("Test", "=== 测试版本信息显示 ===")
        
        // 测试不同版本的信息显示
        for (i in 0 until testSummaries.size) {
            currentIndexLiveData.value = i
            val expectedVersionInfo = "${i + 1}/${testSummaries.size}"
            
            // 验证版本信息格式
            assert(currentIndexLiveData.value == i)
            DebugUtil.d("Test", "Version info: $expectedVersionInfo")
        }
        
        DebugUtil.d("Test", "=== 版本信息显示测试通过 ===")
    }

    /**
     * 创建测试用的摘要历史数据
     */
    private fun createTestSummaryHistory(recordId: Long, count: Int): List<AISummaryDataManager.SummaryEntity> {
        val summaries = mutableListOf<AISummaryDataManager.SummaryEntity>()
        val currentTime = System.currentTimeMillis()
        
        for (i in 0 until count) {
            val summary = AISummaryDataManager.SummaryEntity(
                id = i.toLong(),
                recordId = recordId,
                filePath = "/test/path/audio_$recordId.mp3",
                summaryContent = "测试摘要内容 $i：\n1. 重要信息 ${i + 1}\n2. 关键要点 ${i + 1}",
                summaryStyle = -1,
                recordType = 0,
                createTime = currentTime - (i * 60000), // 每个版本间隔1分钟
                updateTime = currentTime - (i * 60000),
                isSelected = i == 0 // 默认选中最新版本
            )
            summaries.add(summary)
        }
        
        return summaries
    }
}
