/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.search

import android.app.Activity
import android.os.Build
import android.text.SpannableStringBuilder
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.playback.PlaybackActivity
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.playback.convert.search.ConvertSearchBean
import com.soundrecorder.playback.newconvert.ui.TextImageItemAdapter
import com.soundrecorder.playback.newconvert.view.BackgroundTextView
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.playback.shadows.ShadowOplusUsbEnvironment
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowLog::class, ShadowOplusUsbEnvironment::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class ConvertSearchAdapterTest {

    private var context: Activity? = null
    private var adapter: ConvertSearchAdapter? = null

    @Before
    fun setUp() {
        context = Robolectric.buildActivity(PlaybackActivity::class.java).get()
        adapter = ConvertSearchAdapter(context)
    }

    @After
    fun tearDown() {
        context = null
        adapter = null
    }

    private fun createSimpleVH(): RecyclerView.ViewHolder {
        return object : RecyclerView.ViewHolder(View(context!!)) {}
    }

    private fun getCurrentFocus(adapter: ConvertSearchAdapter): Int {
        var field = Whitebox.getField(TextImageItemAdapter::class.java, "mConvertSearchCurrentFocus")
        field.isAccessible = true
        return field.getInt(adapter)
    }

    @Test
    fun should_when_onBindViewHolder() {
        adapter?.let {
            val vh = createSimpleVH()
            it.data = null

            it.onBindViewHolder(vh, 0)

            it.data = emptyList()
            it.onBindViewHolder(vh, 0)

            val list = mutableListOf<ConvertContentItem.ItemMetaData>()
            list.add(ConvertContentItem.TextItemMetaData())
            it.data = list
            it.onBindViewHolder(vh, 0)
        }
    }

    @Test
    fun should_when_setConvertSearchFocus() {
        adapter?.let {
            it.setConvertSearchFocus(0, 0)
            var focus = getCurrentFocus(it)
            Assert.assertEquals(0, focus)
        }
    }

    @Test
    fun should_when_highlightSearchFocus() {
        adapter?.let {
            var textView = BackgroundTextView(context!!)
            var searchBean = ConvertSearchBean("测试", 0, 0, true)
            var builder: SpannableStringBuilder = SpannableStringBuilder("测试")
            it.highlightSearchFocus(builder, searchBean, textView)

            Assert.assertTrue(textView.getHighLight())
        }
    }
}