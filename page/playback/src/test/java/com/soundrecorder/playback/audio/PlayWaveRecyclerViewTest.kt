/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PlayWaveControllerTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.audio

import android.content.Context
import android.os.Build
import android.view.MotionEvent
import android.widget.LinearLayout
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.playback.PlaybackActivity
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.wavemark.wave.load.SoundFile
import com.soundrecorder.wavemark.wave.view.WaveItemView
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class PlayWaveRecyclerViewTest {

    private var mContext: Context? = null
    private var mController: ActivityController<PlaybackActivity>? = null

    @Before
    fun setUp() {
        mContext = BaseApplication.getAppContext()
        mController = Robolectric.buildActivity(PlaybackActivity::class.java)
    }

    @After
    fun tearDown() {
        mController = null
        mContext = null
    }

    @Test
    fun should_return_true_when_onInterceptTouchEvent() {
        mController?.get()?.let {
            val recyclerView = PlayWaveRecyclerView(it, null)
            recyclerView.setIsCanScrollTimeRuler(false)
            val event =
                MotionEvent.obtain(
                    System.currentTimeMillis(),
                    200,
                    MotionEvent.ACTION_DOWN,
                    200f,
                    200f,
                    0
                )
            val result = recyclerView.onInterceptTouchEvent(event)
            Assert.assertTrue(result)
        }
    }

    @Test
    fun should_returnFalse_when_onTouchEvent() {
        mController?.get()?.let {
            val recyclerView = PlayWaveRecyclerView(it, null)
            val motionEvent =
                MotionEvent.obtain(
                    System.currentTimeMillis(),
                    200,
                    MotionEvent.ACTION_DOWN,
                    200f,
                    200f,
                    0
                )
            Whitebox.setInternalState(recyclerView, "mParent", LinearLayout(it))
            //模拟mCanScrollHorizontally = true, ACTION_DOWN
            recyclerView.setIsCanScrollTimeRuler(true)
            recyclerView.onTouchEvent(motionEvent)
            Assert.assertEquals(200, Whitebox.getInternalState(recyclerView, "mTouchDownX"))

            //mCanScrollHorizontally = false, ACTION_DOWN
            recyclerView.setIsCanScrollTimeRuler(false)
            recyclerView.onTouchEvent(motionEvent)
            Assert.assertTrue(recyclerView.onTouchEvent(motionEvent))
        }
    }

    @Test
    fun should_equals_when_createNewItemView() {
        mController?.get()?.let {
            val recyclerView = PlayWaveRecyclerView(it, null)
            val view = recyclerView.createNewItemView(it, LinearLayout(it))
            Assert.assertTrue(view is WaveItemView)
        }
    }

    @Test
    fun should_equals_when_fixItemCount() {
        mController?.get()?.let {
            val recyclerView = PlayWaveRecyclerView(it, null)
            val totalCount = 2
            val fixItemCount = recyclerView.fixItemCount(totalCount)
            Assert.assertEquals(totalCount, fixItemCount)
        }
    }

    @Test
    fun should_equals_when_onBindItemView() {
        mController?.get()?.let {
            val recyclerView = PlayWaveRecyclerView(it, null)
            val amplitude = mutableListOf(1, 2, 3, 4)
            Whitebox.setInternalState(recyclerView, "mAmplitudeValue", amplitude)
            val rulerView = WaveItemView(it)
            recyclerView.onBindItemView(rulerView, 1)
            Assert.assertEquals(amplitude, rulerView.amplitudes)
        }
    }

    @Test
    fun should_equals_when_setSoundFile() {
        mController?.get()?.let {
            val recyclerView = PlayWaveRecyclerView(it, null)
            val amplitude = mutableListOf(1, 2, 3, 4)
            val soundFile = Mockito.spy(SoundFile())
            Whitebox.setInternalState(soundFile, "mAmplitudeList", amplitude)
            recyclerView.setSoundFile(soundFile)
            Assert.assertEquals(
                amplitude,
                Whitebox.getInternalState(recyclerView, "mDecodedAmplitudeList")
            )
        }
    }
}