/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.search

import android.app.Activity
import android.os.Build
import android.view.ViewGroup
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.playback.PlaybackActivity
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.playback.convert.search.ConvertSearchBean
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.playback.newconvert.view.CustomLinearLayoutManager
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.playback.shadows.ShadowOplusUsbEnvironment
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog
import org.junit.*

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowLog::class, ShadowOplusUsbEnvironment::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class SearchScrollHelperTest {

    private var context: Activity? = null
    private var helper: SearchScrollHelper? = null
    private var adapter: ConvertSearchAdapter? = null
    private var layoutManager: LinearLayoutManager? = null
    private var recyclerView: COUIRecyclerView? = null

    @Before
    fun setUp() {
        context = Robolectric.buildActivity(PlaybackActivity::class.java).get()
        adapter = ConvertSearchAdapter(context)
        layoutManager = CustomLinearLayoutManager(context!!)
        var rootView = context!!.window.decorView.rootView as ViewGroup
        recyclerView = COUIRecyclerView(context!!)
        recyclerView?.adapter = adapter
        recyclerView?.layoutManager = layoutManager
        var model = PlaybackConvertViewModel(context!!.application)
        helper = SearchScrollHelper(rootView, model, recyclerView!!)
    }

    @After
    fun tearDown() {
        context = null
        helper = null
    }

    private fun getFocusSearchBean(helper: SearchScrollHelper): ConvertSearchBean? {
        val field = Whitebox.getField(SearchScrollHelper::class.java, "mFocusSearchBean")
        field.isAccessible = true
        return field.get(helper) as? ConvertSearchBean?
    }

    private fun getScrollPosition(layoutManager: LinearLayoutManager): Int {
        val field = Whitebox.getField(LinearLayoutManager::class.java, "mPendingScrollPosition")
        field.isAccessible = true
        return field.getInt(layoutManager)
    }

    private fun getScrollListeners(recyclerView: RecyclerView): List<RecyclerView.OnScrollListener> {
        var field = Whitebox.getField(RecyclerView::class.java, "mScrollListeners")
        field.isAccessible = true
        return field.get(recyclerView) as List<RecyclerView.OnScrollListener>
    }

    private fun createConvertContentList(count: Int): List<ConvertContentItem.ItemMetaData> {
        var list = mutableListOf<ConvertContentItem.ItemMetaData>()
        for (i in 0 until count) {
            list.add(ConvertContentItem.TextItemMetaData())
        }
        return list
    }

    @Test
    fun should_when_setFocusSearchBean() {
        helper?.let {
            it.setFocusSearchBean(null)
            var focusSearch = getFocusSearchBean(it)
            Assert.assertNull(focusSearch)

            val searchBean = ConvertSearchBean("测试", 1, 1, true)
            it.setFocusSearchBean(searchBean)
            focusSearch = getFocusSearchBean(it)
            Assert.assertEquals(searchBean, focusSearch)
        }
    }

    @Test
    fun should_when_updateByKeyWord() {
        helper?.let {
            it.updateByKeyWord()

            val searchBean = ConvertSearchBean("测试", 1, 1, true)
            it.setFocusSearchBean(searchBean)
            it.updateByKeyWord()
        }
    }

    @Test
    fun should_when_getKeyWordOffset() {
        helper?.let {
            var inputString = ""
            var startSeq = 0
            var offset = Whitebox.invokeMethod<Int>(it, "getKeyWordOffset", inputString, startSeq)
            Assert.assertEquals(0, offset)
        }
    }

    @Test
    @Ignore
    fun should_when_scrollToKeyWord() {
        helper?.let {
            var focusParagraph = 0
            val offset = 0
            Whitebox.invokeMethod<Void>(it, "scrollToKeyWord", focusParagraph, offset)
            var scrollPosition = getScrollPosition(layoutManager!!)
            Assert.assertEquals(1, scrollPosition)


            focusParagraph = 2
            Whitebox.invokeMethod<Void>(it, "scrollToKeyWord", focusParagraph, offset)
            scrollPosition = getScrollPosition(layoutManager!!)
            Assert.assertEquals(1, scrollPosition)

            adapter?.data = createConvertContentList(3)

            Whitebox.invokeMethod<Void>(it, "scrollToKeyWord", focusParagraph, offset)
            scrollPosition = getScrollPosition(layoutManager!!)
            Assert.assertEquals(3, scrollPosition)
        }
    }

    @Test
    fun should_when_addOnScrollListener() {
        helper?.let {

            it.addOnScrollListener()
            var listeners = getScrollListeners(recyclerView!!)
            Assert.assertEquals(1, listeners.size)

            it.addOnScrollListener()
            Assert.assertEquals(2, listeners.size)
        }
    }

    @Test
    fun should_when_removeOnScrollListener() {
        helper?.let {
            it.addOnScrollListener()
            var listeners = getScrollListeners(recyclerView!!)
            Assert.assertEquals(1, listeners.size)

            it.removeOnScrollListener()
            Assert.assertEquals(0, listeners.size)

            it.removeOnScrollListener()
            Assert.assertEquals(0, listeners.size)
        }
    }

    @Test
    fun should_when_hideSoftInput() {
        helper?.let {
            Whitebox.invokeMethod<Void>(it, "hideSoftInput")
        }
    }
}