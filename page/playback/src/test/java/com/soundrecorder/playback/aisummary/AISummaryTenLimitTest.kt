package com.soundrecorder.playback.aisummary

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.convertservice.aisummary.database.SummaryCacheDBHelper
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

/**
 * AI摘要10条限制功能测试
 * 验证单个录音文件最多保存10次摘要数据，第11条数据生成后替换第1条数据
 */
@ExperimentalCoroutinesApi
@RunWith(AndroidJUnit4::class)
class AISummaryTenLimitTest {

    private lateinit var context: Context
    private lateinit var dbManager: AISummaryDataManager
    private lateinit var dbHelper: SummaryCacheDBHelper
    private val testRecordId = 99999L
    private val testFilePath = "/storage/emulated/0/SoundRecorder/test_ten_limit.m4a"

    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
        
        // Mock BaseApplication
        mockkObject(BaseApplication)
        every { BaseApplication.getAppContext() } returns context
        
        // Mock MediaDBUtils
        mockkObject(MediaDBUtils)
        every { MediaDBUtils.queryRecordById(testRecordId) } returns mockk<Record> {
            every { data } returns testFilePath
            every { recordType } returns 0
        }
        every { MediaDBUtils.queryIdByData(testFilePath) } returns testRecordId
        
        dbHelper = SummaryCacheDBHelper(context)
        dbManager = AISummaryDataManager()
        
        // 清理测试数据
        cleanupTestData()
    }

    @After
    fun tearDown() {
        cleanupTestData()
        unmockkAll()
    }

    private fun cleanupTestData() {
        try {
            dbHelper.deleteSummaryCacheByFilePath(testFilePath)
        } catch (e: Exception) {
            // 忽略清理错误
        }
    }

    /**
     * 测试1：验证单个录音文件可以保存10条摘要
     */
    @Test
    fun testSaveTenSummaries() = runTest {
        // 保存10条摘要
        for (i in 1..10) {
            val summaryContent = "第${i}条摘要内容 - ${System.currentTimeMillis()}"
            val success = dbManager.saveSummary(testRecordId, summaryContent, i)
            assertTrue("第${i}条摘要应该保存成功", success)
            
            // 稍微延迟以确保时间戳不同
            Thread.sleep(10)
        }

        // 验证保存了10条摘要
        val summaryHistory = dbManager.getSummaryHistory(testRecordId)
        assertEquals("应该有10条摘要", 10, summaryHistory.size)

        // 验证摘要按时间排序（最新的在前）
        for (i in 0 until summaryHistory.size - 1) {
            assertTrue("摘要应该按时间降序排列", 
                summaryHistory[i].createTime >= summaryHistory[i + 1].createTime)
        }

        // 验证最新的摘要是第10条
        assertTrue("最新摘要应该包含'第10条'", 
            summaryHistory.first().summaryContent.contains("第10条"))

        println("✅ 保存10条摘要测试通过")
    }

    /**
     * 测试2：验证第11条摘要替换第1条摘要
     */
    @Test
    fun testEleventhSummaryReplacesFirst() = runTest {
        // 先保存10条摘要
        val originalSummaries = mutableListOf<String>()
        for (i in 1..10) {
            val summaryContent = "原始第${i}条摘要 - ${System.currentTimeMillis()}"
            originalSummaries.add(summaryContent)
            dbManager.saveSummary(testRecordId, summaryContent, i)
            Thread.sleep(10)
        }

        // 验证有10条摘要
        val beforeHistory = dbManager.getSummaryHistory(testRecordId)
        assertEquals("保存前应该有10条摘要", 10, beforeHistory.size)

        // 保存第11条摘要
        val eleventhSummary = "第11条摘要 - 应该替换第1条 - ${System.currentTimeMillis()}"
        val success = dbManager.saveSummary(testRecordId, eleventhSummary, 11)
        assertTrue("第11条摘要应该保存成功", success)

        // 验证仍然只有10条摘要
        val afterHistory = dbManager.getSummaryHistory(testRecordId)
        assertEquals("保存后仍然应该只有10条摘要", 10, afterHistory.size)

        // 验证最新的摘要是第11条
        assertTrue("最新摘要应该是第11条", 
            afterHistory.first().summaryContent.contains("第11条"))

        // 验证第1条摘要已被删除
        val hasFirstSummary = afterHistory.any { it.summaryContent.contains("原始第1条") }
        assertFalse("第1条摘要应该已被删除", hasFirstSummary)

        // 验证第2-10条摘要仍然存在
        for (i in 2..10) {
            val hasOriginalSummary = afterHistory.any { it.summaryContent.contains("原始第${i}条") }
            assertTrue("原始第${i}条摘要应该仍然存在", hasOriginalSummary)
        }

        println("✅ 第11条摘要替换第1条摘要测试通过")
    }

    /**
     * 测试3：验证连续添加多条摘要的替换逻辑
     */
    @Test
    fun testContinuousReplacementLogic() = runTest {
        // 保存15条摘要
        for (i in 1..15) {
            val summaryContent = "连续第${i}条摘要 - ${System.currentTimeMillis()}"
            val success = dbManager.saveSummary(testRecordId, summaryContent, i)
            assertTrue("第${i}条摘要应该保存成功", success)
            Thread.sleep(10)
        }

        // 验证只保留了最新的10条摘要
        val finalHistory = dbManager.getSummaryHistory(testRecordId)
        assertEquals("最终应该只有10条摘要", 10, finalHistory.size)

        // 验证保留的是第6-15条摘要
        for (i in 6..15) {
            val hasSummary = finalHistory.any { it.summaryContent.contains("连续第${i}条") }
            assertTrue("应该保留第${i}条摘要", hasSummary)
        }

        // 验证第1-5条摘要已被删除
        for (i in 1..5) {
            val hasSummary = finalHistory.any { it.summaryContent.contains("连续第${i}条") }
            assertFalse("第${i}条摘要应该已被删除", hasSummary)
        }

        // 验证最新的摘要是第15条
        assertTrue("最新摘要应该是第15条", 
            finalHistory.first().summaryContent.contains("连续第15条"))

        println("✅ 连续替换逻辑测试通过")
    }

    /**
     * 测试4：验证getSummary返回最新摘要
     */
    @Test
    fun testGetSummaryReturnsLatest() = runTest {
        // 保存多条摘要
        for (i in 1..12) {
            val summaryContent = "测试摘要${i} - ${System.currentTimeMillis()}"
            dbManager.saveSummary(testRecordId, summaryContent, i)
            Thread.sleep(10)
        }

        // 获取最新摘要
        val latestSummary = dbManager.getSummary(testRecordId)
        assertNotNull("应该能获取到最新摘要", latestSummary)
        assertTrue("最新摘要应该是第12条", 
            latestSummary!!.summaryContent.contains("测试摘要12"))

        // 验证摘要历史中有10条记录
        val history = dbManager.getSummaryHistory(testRecordId)
        assertEquals("历史记录应该有10条", 10, history.size)

        // 验证getSummary返回的是历史记录中的第一条（最新的）
        assertEquals("getSummary应该返回历史记录中的最新一条", 
            history.first().summaryContent, latestSummary.summaryContent)

        println("✅ getSummary返回最新摘要测试通过")
    }

    /**
     * 测试5：验证数据库层面的限制逻辑
     */
    @Test
    fun testDatabaseLevelLimitation() = runTest {
        // 直接使用DBHelper测试
        val currentCount = dbHelper.getSummaryCacheCountByFilePath(testFilePath)
        assertEquals("初始应该没有摘要", 0L, currentCount)

        // 添加12条记录
        for (i in 1..12) {
            val rowContent = com.soundrecorder.convertservice.aisummary.database.SummaryCacheRowContent(
                filePath = testFilePath,
                timeStamp = System.currentTimeMillis() + i,
                chooseState = -1,
                recordType = 0,
                summaryStyle = i
            )
            dbHelper.addSummaryCache(rowContent)
        }

        // 验证添加了12条记录
        val afterAddCount = dbHelper.getSummaryCacheCountByFilePath(testFilePath)
        assertEquals("应该添加了12条记录", 12L, afterAddCount)

        // 执行维护操作
        val deletedCount = dbHelper.maintainMaxSummaryCount(testFilePath, 10)
        assertEquals("应该删除了2条记录", 2, deletedCount)

        // 验证最终只有10条记录
        val finalCount = dbHelper.getSummaryCacheCountByFilePath(testFilePath)
        assertEquals("最终应该只有10条记录", 10L, finalCount)

        // 验证保留的是最新的10条记录
        val remainingEntities = dbHelper.getSummaryCacheByFilePathOrderByTime(testFilePath)
        assertEquals("查询结果应该有10条", 10, remainingEntities.size)

        // 验证时间戳是最新的10个
        val expectedTimestamps = (3..12).map { System.currentTimeMillis() + it }.toSet()
        val actualTimestamps = remainingEntities.map { it.timeStamp }.toSet()
        
        // 由于时间戳可能有微小差异，我们检查summaryStyle字段
        val actualStyles = remainingEntities.map { it.summaryStyle }.sorted()
        val expectedStyles = (3..12).toList()
        assertEquals("保留的应该是最新的10条记录", expectedStyles, actualStyles)

        println("✅ 数据库层面限制逻辑测试通过")
    }

    /**
     * 测试6：验证多个录音文件的独立性
     */
    @Test
    fun testMultipleFilesIndependence() = runTest {
        val testRecordId2 = 88888L
        val testFilePath2 = "/storage/emulated/0/SoundRecorder/test_ten_limit_2.m4a"

        // Mock第二个文件
        every { MediaDBUtils.queryRecordById(testRecordId2) } returns mockk<Record> {
            every { data } returns testFilePath2
            every { recordType } returns 0
        }
        every { MediaDBUtils.queryIdByData(testFilePath2) } returns testRecordId2

        try {
            // 为第一个文件保存12条摘要
            for (i in 1..12) {
                dbManager.saveSummary(testRecordId, "文件1摘要${i}", i)
                Thread.sleep(5)
            }

            // 为第二个文件保存8条摘要
            for (i in 1..8) {
                dbManager.saveSummary(testRecordId2, "文件2摘要${i}", i)
                Thread.sleep(5)
            }

            // 验证第一个文件有10条摘要
            val history1 = dbManager.getSummaryHistory(testRecordId)
            assertEquals("文件1应该有10条摘要", 10, history1.size)

            // 验证第二个文件有8条摘要
            val history2 = dbManager.getSummaryHistory(testRecordId2)
            assertEquals("文件2应该有8条摘要", 8, history2.size)

            // 验证两个文件的摘要内容不会混淆
            val file1Contents = history1.map { it.summaryContent }
            val file2Contents = history2.map { it.summaryContent }

            assertTrue("文件1的摘要应该都包含'文件1'", 
                file1Contents.all { it.contains("文件1") })
            assertTrue("文件2的摘要应该都包含'文件2'", 
                file2Contents.all { it.contains("文件2") })

            println("✅ 多个录音文件独立性测试通过")
        } finally {
            // 清理第二个文件的测试数据
            dbHelper.deleteSummaryCacheByFilePath(testFilePath2)
        }
    }

    /**
     * 测试7：验证异常情况处理
     */
    @Test
    fun testExceptionHandling() = runTest {
        // 测试无效recordId
        val invalidRecordId = -1L
        every { MediaDBUtils.queryRecordById(invalidRecordId) } returns null

        val result = dbManager.saveSummary(invalidRecordId, "测试摘要", 1)
        assertFalse("无效recordId应该保存失败", result)

        val summary = dbManager.getSummary(invalidRecordId)
        assertNull("无效recordId应该获取不到摘要", summary)

        val history = dbManager.getSummaryHistory(invalidRecordId)
        assertTrue("无效recordId应该返回空历史", history.isEmpty())

        println("✅ 异常情况处理测试通过")
    }

    /**
     * 测试8：验证性能表现
     */
    @Test
    fun testPerformance() = runTest {
        val startTime = System.currentTimeMillis()

        // 快速保存20条摘要
        for (i in 1..20) {
            dbManager.saveSummary(testRecordId, "性能测试摘要${i}", i)
        }

        val endTime = System.currentTimeMillis()
        val duration = endTime - startTime

        // 验证最终只有10条摘要
        val finalHistory = dbManager.getSummaryHistory(testRecordId)
        assertEquals("最终应该只有10条摘要", 10, finalHistory.size)

        // 验证性能（20条摘要操作应该在合理时间内完成）
        assertTrue("20条摘要操作应该在5秒内完成", duration < 5000)

        println("✅ 性能测试通过，耗时: ${duration}ms")
    }

    /**
     * 测试9：验证边界条件
     */
    @Test
    fun testBoundaryConditions() = runTest {
        // 测试空摘要内容
        val emptyResult = dbManager.saveSummary(testRecordId, "", 1)
        assertTrue("空摘要内容应该可以保存", emptyResult)

        // 测试很长的摘要内容
        val longContent = "很长的摘要内容".repeat(1000)
        val longResult = dbManager.saveSummary(testRecordId, longContent, 2)
        assertTrue("很长的摘要内容应该可以保存", longResult)

        // 测试负数样式
        val negativeStyleResult = dbManager.saveSummary(testRecordId, "负数样式测试", -999)
        assertTrue("负数样式应该可以保存", negativeStyleResult)

        // 验证所有摘要都被保存
        val history = dbManager.getSummaryHistory(testRecordId)
        assertEquals("应该有3条摘要", 3, history.size)

        println("✅ 边界条件测试通过")
    }

    /**
     * 测试10：验证完整的业务流程
     */
    @Test
    fun testCompleteBusinessFlow() = runTest {
        // 模拟完整的业务流程：用户多次生成摘要
        
        // 第一次生成摘要
        var success = dbManager.saveSummary(testRecordId, "第一次生成的摘要", 1)
        assertTrue("第一次生成应该成功", success)
        
        var currentSummary = dbManager.getSummary(testRecordId)
        assertNotNull("应该能获取到当前摘要", currentSummary)
        assertTrue("当前摘要应该是第一次生成的", currentSummary!!.summaryContent.contains("第一次"))

        // 用户不满意，重新生成多次
        for (i in 2..15) {
            success = dbManager.saveSummary(testRecordId, "第${i}次重新生成的摘要", i)
            assertTrue("第${i}次重新生成应该成功", success)
        }

        // 验证最终状态
        val finalSummary = dbManager.getSummary(testRecordId)
        assertNotNull("应该能获取到最终摘要", finalSummary)
        assertTrue("最终摘要应该是第15次生成的", finalSummary!!.summaryContent.contains("第15次"))

        val finalHistory = dbManager.getSummaryHistory(testRecordId)
        assertEquals("最终历史应该有10条记录", 10, finalHistory.size)

        // 验证历史记录是最近的10次生成
        for (i in 6..15) {
            val hasRecord = finalHistory.any { it.summaryContent.contains("第${i}次") }
            assertTrue("应该包含第${i}次生成的记录", hasRecord)
        }

        // 验证早期的记录已被清理
        for (i in 1..5) {
            val hasRecord = finalHistory.any { it.summaryContent.contains("第${i}次") }
            assertFalse("第${i}次生成的记录应该已被清理", hasRecord)
        }

        println("✅ 完整业务流程测试通过")
        println("🎉 所有10条摘要限制测试通过！")
    }
}
