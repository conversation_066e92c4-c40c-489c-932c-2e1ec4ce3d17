/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.convert;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.res.Configuration;
import android.os.Build;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.Fragment;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.coui.appcompat.darkmode.COUIDarkModeUtil;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.common.permission.PermissionUtils;
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate;
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant;
import com.soundrecorder.playback.MarkReadReadyCallback;
import com.soundrecorder.playback.PlaybackActivity;
import com.soundrecorder.playback.PlaybackActivityViewModel;
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel;
import com.soundrecorder.playback.shadows.ShadowCOUIVersionUtil;
import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowDialog;
import org.robolectric.shadows.ShadowLog;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class,
        ShadowFeatureOption.class, ShadowCOUIVersionUtil.class})
public class ConvertManagerImplTest {

    private static final long TEST_RECORD_ID = 1L;

    private Context mContext;
    private ConvertManagerImpl mConvertManager;
    private PlaybackActivity mActivity;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = BaseApplication.getAppContext();
        mConvertManager = Mockito.spy(new ConvertManagerImpl());
        mActivity = Robolectric.buildActivity(PlaybackActivity.class).create().resume().get();
    }

    @After
    public void tearDown() {
        mActivity = null;
        mConvertManager = null;
        mContext = null;
    }

    @Test
    @Ignore
    public void should_correct_when_register() {
        PlaybackActivity spyActivity = Mockito.spy(mActivity);
        View view = Mockito.mock(View.class);
        Mockito.doReturn(spyActivity).when(view).getContext();
        ConvertViewController convertViewController = Mockito.mock(ConvertViewController.class);
        Whitebox.setInternalState(mConvertManager, "mConvertViewController", convertViewController);
        MockedStatic<COUIDarkModeUtil> mocked = Mockito.mockStatic(COUIDarkModeUtil.class);
        mocked.when(() -> COUIDarkModeUtil.isNightMode(spyActivity)).thenReturn(true);
        Mockito.doReturn(true).when(spyActivity).bindService(any(Intent.class), any(ServiceConnection.class), anyInt());
        mConvertManager.register(spyActivity.getSupportFragmentManager().getFragments().get(0), view, TEST_RECORD_ID, true);
        mocked.close();
        Assert.assertNotNull(mConvertManager.getPlaybackConvertViewModel());
        Assert.assertNotNull(Whitebox.getInternalState(mConvertManager, "convertServiceManager"));
    }

    @Test
    public void should_not_null_when_setExportMenuItem() {
        mConvertManager.setExportMenuItem();
        Assert.assertNotNull(Whitebox.getInternalState(mConvertManager, "mExportHelper"));
    }

    @Test
    public void should_show_dialog_when_showStatementWithFirstUseConvert_with_first_use() throws Exception {
        mConvertManager.setActivity(mActivity);
        Whitebox.setInternalState(mActivity, "mIPrivacyPolicyDelegate", new FakePrivacyPolicyDelegate(mActivity));
        if (mActivity.getPrivacyPolicyDelegate() != null) {
            mActivity.getPrivacyPolicyDelegate().resumeShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH, true);
        }
        Dialog actual = ShadowDialog.getLatestDialog();
        Assert.assertNotNull(actual);
    }

    @Test
    public void should_show_dialog_when_showUserTimeOutDialog() throws Exception {
        mConvertManager.setActivity(mActivity);
        AlertDialog actualDialog = Whitebox.getInternalState(mConvertManager, "mUserTimeOutDialog");
        Assert.assertNull(actualDialog);
        Whitebox.invokeMethod(mConvertManager, "showUserTimeOutDialog");
        actualDialog = Whitebox.getInternalState(mConvertManager, "mUserTimeOutDialog");
        Assert.assertNotNull(actualDialog);
        Assert.assertTrue(actualDialog.isShowing());
    }

    @Test
    public void should_when_initViewModel() throws Exception{
        mConvertManager.setActivity(mActivity);
        Whitebox.invokeMethod(mConvertManager,"initViewModel", mActivity.getSupportFragmentManager().getFragments().get(0), TEST_RECORD_ID);
        Assert.assertEquals(TEST_RECORD_ID,mConvertManager.getPlaybackConvertViewModel().getMediaRecordId());
    }

    @Test
    public void should_when_initServiceManager() throws Exception {
        PlaybackConvertViewModel viewModel = new PlaybackConvertViewModel(BaseApplication.getApplication());
        mConvertManager.setPlaybackConvertViewModel(viewModel);
        Whitebox.invokeMethod(mConvertManager,"initServiceManager");
        Assert.assertNotNull(Whitebox.getInternalState(mConvertManager,"convertServiceManager"));
        Assert.assertNotNull(Whitebox.getInternalState(mConvertManager,"extractKeyWordManager"));
    }

    @Test
    public void should_when_initViewModelObeservers() throws Exception {
        PlaybackConvertViewModel viewModel = new PlaybackConvertViewModel(BaseApplication.getApplication());
        mConvertManager.setPlaybackConvertViewModel(viewModel);

        Fragment fragment = mActivity.getSupportFragmentManager().getFragments().get(0);
        Whitebox.invokeMethod(mConvertManager,"initViewModelObeservers", fragment, TEST_RECORD_ID);
        boolean hasObserver = mConvertManager.getPlaybackConvertViewModel().getMConvertStatus().hasObservers();
        Assert.assertFalse(hasObserver);

        mConvertManager.setActivity(mActivity);
        Whitebox.invokeMethod(mConvertManager,"initViewModelObeservers", fragment, TEST_RECORD_ID);
        hasObserver = mConvertManager.getPlaybackConvertViewModel().getMConvertStatus().hasObservers();
        Assert.assertTrue(hasObserver);
    }

    @Test
    public void should_when_setViewModel() {
        ConvertViewController convertViewController = Mockito.mock(ConvertViewController.class);
        Whitebox.setInternalState(mConvertManager,"mConvertViewController",convertViewController);
        mConvertManager.setViewModel(null);
        Assert.assertNull(mConvertManager.getConvertViewController().getViewModel());

        PlaybackActivityViewModel viewModel = new PlaybackActivityViewModel();
        mConvertManager.setViewModel(viewModel);
        MarkReadReadyCallback callback = viewModel.getMarkReadReadyCallback();
        Assert.assertEquals(mConvertManager.getPlaybackConvertViewModel(), callback);

    }

    @Test
    public void should_when_showStatementWithFirstUseExtract() throws Exception{

        Whitebox.invokeMethod(mConvertManager,"showStatementWithFirstUseExtract",false);

        mConvertManager.setActivity(mActivity); // activity没有生命周期，因此结果为空
        Whitebox.invokeMethod(mConvertManager,"showStatementWithFirstUseExtract",true);
    }

    @Test
    public void should_when_showStatementWithFirstUseConvert() throws Exception{
        Whitebox.invokeMethod(mConvertManager,"showStatementWithFirstUseConvert",false);

        mConvertManager.setActivity(mActivity);
        Whitebox.invokeMethod(mConvertManager,"showStatementWithFirstUseConvert",true);
    }

    @Test
    public void should_when_doClickPermissionConvertOK() {
        boolean granted = PermissionUtils.isStatementConvertGranted(mContext);
        Assert.assertFalse(granted);

        mConvertManager.doClickPermissionConvertOK();
        granted = PermissionUtils.isStatementConvertGranted(mContext);
        Assert.assertTrue(granted);
    }

    @Test
    public void should_when_doClickPermissionConvertSearchOK() {
        mConvertManager.doClickPermissionConvertSearchOK();

        PlaybackConvertViewModel viewModel = new PlaybackConvertViewModel(BaseApplication.getApplication());
        mConvertManager.setPlaybackConvertViewModel(viewModel);
        mConvertManager.doClickPermissionConvertSearchOK();

        viewModel.setMediaRecordId(TEST_RECORD_ID);
        mConvertManager.doClickPermissionConvertSearchOK();
    }

    @Test
    public void should_show_dialog_when_showStatementWithFirstUseConvert_with_convert_start_click() throws Exception {
        mConvertManager.setActivity(mActivity);
        Whitebox.setInternalState(mActivity, "mIPrivacyPolicyDelegate", new FakePrivacyPolicyDelegate(mActivity));
        if (mActivity.getPrivacyPolicyDelegate() != null) {
            mActivity.getPrivacyPolicyDelegate().resumeShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH, true);
        }
        Dialog actual = ShadowDialog.getLatestDialog();
        Assert.assertNotNull(actual);
    }

    private static class FakePrivacyPolicyDelegate implements IPrivacyPolicyDelegate {

        private final Activity activity;

        public FakePrivacyPolicyDelegate(Activity activity) {
            this.activity = activity;
        }

        @Override
        public void onSaveInstanceState(@NonNull Bundle outState) {}

        @Override
        public void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {}

        @Override
        public void onConfigurationChanged(@NonNull Configuration newConfig) {}

        @Override
        public void checkAndDismissDialog() {}

        @Override
        public void resumeShowDialog(int type, boolean forceShow) {
            new Dialog(activity).show();
        }

        @Override
        public boolean hasConvertPermission() {
            return true;
        }

        @Override
        public boolean canShowWithdrawnPermissionConvert() {
            return false;
        }

        @Override
        public void onDestroy() {}

        @Nullable
        @Override
        public Integer getLastShowPrivacyDialogType() {
            return null;
        }
    }
}
