/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.search.anim

import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.app.Activity
import android.os.Build
import android.view.ViewGroup
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.playback.PlaybackActivity
import com.soundrecorder.playback.newconvert.keyword.KeyWordChipGroup
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.playback.shadows.ShadowOplusUsbEnvironment
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowLog::class, ShadowOplusUsbEnvironment::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class ConvertSearchAnimTest {

    private var context: Activity? = null
    private var anim: ConvertSearchAnim? = null

    @Before
    fun setUp() {
        context = Robolectric.buildActivity(PlaybackActivity::class.java).get()
        val group = KeyWordChipGroup(context!!)
        group.layoutParams = ViewGroup.MarginLayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        anim = ConvertSearchAnim(group, null)
    }

    @After
    fun tearDown() {
        ConvertSearchAnim.reset()
        context = null
        anim = null
    }

    private fun getInAnimSet(anim: ConvertSearchAnim): AnimatorSet {
        val animField = Whitebox.getField(ConvertSearchAnim::class.java, "inAnimSet")
        animField.isAccessible = true
        return animField.get(anim) as AnimatorSet
    }

    private fun getOutAnimSet(anim: ConvertSearchAnim): AnimatorSet {
        val animField = Whitebox.getField(ConvertSearchAnim::class.java, "outAnimSet")
        animField.isAccessible = true
        return animField.get(anim) as AnimatorSet
    }


    @Test
    fun should_not_null_when_animateSearchIn() {
        anim?.let {
            it.animateSearchIn()
            val set = getInAnimSet(it)

            Assert.assertNotNull(set)
            Assert.assertEquals(3, set.childAnimations.size)
        }
    }

    @Test
    fun should_not_null_when_animateSearchOut() {
        anim?.let {
            it.animateSearchOut()
            val set = getOutAnimSet(it)

            Assert.assertNotNull(set)
            Assert.assertEquals(3, set.childAnimations.size)
        }
    }

    @Test
    fun should_cancel_when_release() {
        anim?.let {
            it.animateSearchIn()
            val set = getInAnimSet(it)

            Assert.assertTrue(set.isRunning)

            it.release()
            Assert.assertFalse(set.isRunning)
        }
    }

    @Test
    fun should_return_int_when_getAnimHeight() {
        anim?.let {
            ConvertSearchAnim.animHeight = 0
            var height = Whitebox.invokeMethod<Int>(it, "getAnimHeight")
            Assert.assertTrue(height > 0)

            ConvertSearchAnim.animHeight = 200
            height = Whitebox.invokeMethod<Int>(it, "getAnimHeight")
            Assert.assertEquals(200, height)
        }
    }

    @Test
    fun should_not_null_when_createMarginTopAnim() {
        anim?.let {
            var valueAnim = Whitebox.invokeMethod<ValueAnimator>(it, "createMarginTopAnim", 54, 0)
            var size = valueAnim.values.size

            Assert.assertNotNull(valueAnim)
            Assert.assertEquals(1, size)

            valueAnim = Whitebox.invokeMethod<ValueAnimator>(it, "createMarginTopAnim", 0, 54)
            size = valueAnim.values.size

            Assert.assertNotNull(valueAnim)
            Assert.assertEquals(1, size)
        }
    }

    @Test
    fun should_not_null_when_createHeightAnim() {
        anim?.let {
            var valueAnim = Whitebox.invokeMethod<ValueAnimator>(it, "createHeightAnim", 200, 0, true)
            var size = valueAnim.values.size

            Assert.assertNotNull(valueAnim)
            Assert.assertEquals(1, size)

            valueAnim = Whitebox.invokeMethod<ValueAnimator>(it, "createHeightAnim", 0, 200, false)
            size = valueAnim.values.size

            Assert.assertNotNull(valueAnim)
            Assert.assertEquals(1, size)
        }
    }

    @Test
    fun should_not_null_when_createAlphaAnim() {
        anim?.let {
            var valueAnim = Whitebox.invokeMethod<ValueAnimator>(it, "createAlphaAnim", 1.0f, 0f)
            var size = valueAnim.values.size

            Assert.assertNotNull(valueAnim)
            Assert.assertEquals(1, size)

            valueAnim = Whitebox.invokeMethod<ValueAnimator>(it, "createAlphaAnim", 0f, 1.0f)
            size = valueAnim.values.size

            Assert.assertNotNull(valueAnim)
            Assert.assertEquals(1, size)
        }
    }

    @Test
    fun should_when_reset() {
        ConvertSearchAnim.animHeight = 10
        Assert.assertEquals(10, ConvertSearchAnim.animHeight)

        ConvertSearchAnim.reset()
        Assert.assertEquals(0, ConvertSearchAnim.animHeight)
    }

    @Test
    fun should_return_boolean_when_isRunning() {
        anim?.let {
            var isRunning = it.isRunning()
            Assert.assertFalse(isRunning)

            it.animateSearchIn()
            isRunning = it.isRunning()
            Assert.assertTrue(isRunning)

            it.animateSearchOut()
            isRunning = it.isRunning()
            Assert.assertTrue(isRunning)
        }
    }
}