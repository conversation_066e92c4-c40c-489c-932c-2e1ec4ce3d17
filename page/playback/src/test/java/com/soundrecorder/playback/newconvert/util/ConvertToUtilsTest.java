/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/01/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.playback.newconvert.util;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.databean.BeanConvertText;
import com.soundrecorder.convertservice.convert.NewConvertResultUtil;
import com.soundrecorder.common.databean.ConvertContentItem;
import com.soundrecorder.playback.shadows.ShadowExternalStorage;
import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class,
        ShadowExternalStorage.class})
public class ConvertToUtilsTest {

    private Context mContext;
    private static final String TEST_FILE_NAME = "test_file";

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @Test
    public void should_empty_when_toConvertContentItem() {
        BeanConvertText.SubItem subItem = new BeanConvertText.SubItem(0, 1, "测试一下",
                "1000", "10000", "", "");
        ConvertContentItem item = com.soundrecorder.convertservice.util.ConvertToUtils.toConvertContentItem(subItem, false);
        Assert.assertNotNull(item);
    }

    @Test
    public void should_empty_when_readConvertContent() {
        try {
            List<ConvertContentItem> items = new ArrayList<>();
            File file = new File(NewConvertResultUtil.getConvertSavePath(mContext), TEST_FILE_NAME);
            Mockito.when(file.exists()).thenReturn(true);
            com.soundrecorder.convertservice.util.ConvertToUtils.readConvertContent(mContext, TEST_FILE_NAME);
            com.soundrecorder.convertservice.util.ConvertToUtils.reWriteConvertFile(NewConvertResultUtil.getConvertSavePath(mContext) + TEST_FILE_NAME, items);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void should_empty_when_combineContents() {
        ConvertContentItem item = new ConvertContentItem();
        item.setStartTime(1000);
        item.setEndTime(10000);
        item.setTextContent("虎式工哈哈哈哈哈哈卡盟民京哈空间很大空间撒谎哈口水都快摩卡金");
        item.setRoleName("wo ce shi");
        item.setRoleId(100);
        try {
            String buff = Whitebox.invokeMethod(com.soundrecorder.convertservice.util.ConvertToUtils.INSTANCE, "combineContents", item);
            Assert.assertNotNull(buff);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void should_empty_when_genListToString() {
        try {
            List<String> stringList = new ArrayList<>();
            String str1 = Whitebox.invokeMethod(com.soundrecorder.convertservice.util.ConvertToUtils.INSTANCE, "genListToString", stringList);
            Assert.assertEquals("", str1);
            stringList.add("测试1");
            stringList.add("车市2");
            stringList.add("车市3");
            stringList.add("测试4");
            String str2 = Whitebox.invokeMethod(com.soundrecorder.convertservice.util.ConvertToUtils.INSTANCE, "genListToString", stringList);
            Assert.assertNotEquals("", str2);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
