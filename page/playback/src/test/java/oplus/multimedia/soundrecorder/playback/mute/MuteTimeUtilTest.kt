package oplus.multimedia.soundrecorder.playback.mute

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Assert.*

import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class MuteTimeUtilTest {

    @Test
    fun should_success_when_isMuteInfoValid() {
        val muteList = mutableListOf(
            MuteItem(100, 600),
            MuteItem(1000, 3000),
            MuteItem(8000, 9000),
            MuteItem(12000, 20000)
        )
        var muteInfo = MuteInfo(
            lastItem = null,
            curItem = muteList[0]
        )

        var result = MuteTimeUtil.isMuteInfoValid(700, muteInfo, muteList)
        Assert.assertFalse(result)

        result = MuteTimeUtil.isMuteInfoValid(300, muteInfo, muteList)
        Assert.assertTrue(result)

        muteInfo = MuteInfo(
            lastItem = muteList[1],
            curItem = muteList[2]
        )
        result = MuteTimeUtil.isMuteInfoValid(2000, muteInfo, muteList)
        Assert.assertTrue(result)

        result = MuteTimeUtil.isMuteInfoValid(300, muteInfo, muteList)
        Assert.assertTrue(result)
    }

    @Test
    fun getSeekTimeTest() {
        val muteItem0 = MuteItem(3000, 5000)
        val muteItem1 = MuteItem(7000, 9000)
        val muteItem2 = MuteItem(12000, 14000)
        val muteItem3 = MuteItem(18000, 20000)
        val muteItem4 = MuteItem(24000, 30000)

        val skipTime1 = MuteTimeUtil.getSeekTime(
            4000,
            MuteInfo(null, muteItem0)
        )
        val skipTime2 = MuteTimeUtil.getSeekTime(
            25000,
            MuteInfo(muteItem3, muteItem4)
        )
        val skipTime3 = MuteTimeUtil.getSeekTime(
            11000,
            MuteInfo(muteItem1, muteItem2)
        )
        assertEquals(-1, skipTime1)
        assertEquals(30000L - 500L, skipTime2)
        assertEquals(-1, skipTime3)
    }

    /**
     * useCase:
     * 1.target位于MuteItem区间内
     * 2.target位于MuteItem区间外
     * 3.边界值：0；last
     */
    @Test
    fun binarySearchTest() {
        val muteItem0 = MuteItem(3, 5)
        val muteItem1 = MuteItem(7, 9)
        val muteItem2 = MuteItem(12, 14)
        val muteItem3 = MuteItem(18, 20)
        val muteItem4 = MuteItem(24, 30)
        val list = listOf(muteItem0, muteItem1, muteItem2, muteItem3, muteItem4)

        //1.target位于MuteItem区间内
        val muteInfo1 = MuteTimeUtil.getMuteData(list, 13)
        val muteInfo2 = MuteTimeUtil.getMuteData(list, 4)
        assertEquals(MuteInfo(muteItem1, muteItem2), muteInfo1)
        assertEquals(MuteInfo(null, muteItem0), muteInfo2)

        //2.target位于MuteItem区间外
        val muteInfo4 = MuteTimeUtil.getMuteData(list, 11)
        assertEquals(MuteInfo(muteItem1, muteItem2), muteInfo4)

        //3.边界：target位于第一条 MuteItem 之前
        val muteInfo5 = MuteTimeUtil.getMuteData(list, 0)
        val muteInfo6 = MuteTimeUtil.getMuteData(list, 2)
        assertEquals(MuteInfo(null, muteItem0), muteInfo5)
        assertEquals(MuteInfo(null, muteItem0), muteInfo6)

        //4.边界：target位于最后一条 MuteItem 之后
        val muteInfo7 = MuteTimeUtil.getMuteData(list, 30)
        val muteInfo8 = MuteTimeUtil.getMuteData(list, 31)
        assertEquals(MuteInfo(muteItem3, muteItem4), muteInfo7)
        assertNull(muteInfo8)
    }
}