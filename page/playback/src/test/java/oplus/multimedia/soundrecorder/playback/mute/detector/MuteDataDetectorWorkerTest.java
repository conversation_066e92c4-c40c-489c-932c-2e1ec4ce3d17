package oplus.multimedia.soundrecorder.playback.mute.detector;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;

import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;

import org.robolectric.annotation.Config;

/*********************************************************************
 * * Copyright (C), 2022, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/5/24
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class MuteDataDetectorWorkerTest {

    @Test
    @Ignore
    public void should_returnNotNull_when_startMuteDetectIfNecessary() {
        MuteDataDetectorWorker.Companion.startMuteDetectIfNecessary("123");
        MuteDataDetectorWorker worker = Whitebox.getInternalState(MuteDataDetectorWorker.class, "sCurWorker");
        Assert.assertNotNull(worker);
    }

    @Test
    public void should_returnNull_when_cancel() {
        MuteDataDetectorWorker.Companion.cancel();
        MuteDataDetectorWorker sCurWorker = Whitebox.getInternalState(MuteDataDetectorWorker.class, "sCurWorker");
        Assert.assertNull(sCurWorker);
    }
}
