<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/m_constraint_layout"
        android:background="@color/coui_color_white"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout
            android:id="@+id/wave_gradient_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintHeight_percent="@dimen/play_wave_view_height_percent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="@dimen/common_wave_view_margin_top"
            app:backgroundWhole="@color/wave_recycler_background">

            <com.soundrecorder.playback.audio.PlayWaveRecyclerView
                android:id="@+id/wave_recyclerview"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:importantForAccessibility="no"
                android:layoutDirection="ltr"/>
        </com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout>

        <androidx.recyclerview.widget.COUIRecyclerView
            android:id="@+id/mark_list_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintWidth_percent="@dimen/screen_width_percent_parentchild"
            android:paddingHorizontal="@dimen/play_mark_list_padding_horizontal"
            android:divider="@null"
            android:scrollbars="none"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/space_bottom_view_top"
            app:layout_constraintTop_toBottomOf="@id/miniRecordTop"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:listitem="@layout/item_mark_list" />

        <LinearLayout
            android:id="@+id/layout_time"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingVertical="@dimen/play_mark_list_padding_top"
            android:layout_marginHorizontal="@dimen/play_time_area_margin_horizontal"
            android:clickable="true"
            android:importantForAccessibility="no"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/wave_gradient_view">

            <TextView
                android:id="@+id/tv_current_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textAppearance="@style/couiTextAppearanceDisplayM"
                android:textColor="@color/coui_color_primary_neutral"
                android:textSize="@dimen/common_max_time_text_size"
                android:fontFamily="sys-sans-en"
                android:fontFeatureSettings="tnum"
                android:textStyle="bold"
                tools:text="00:33" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <com.soundrecorder.common.widget.COUIAnimateTextView
                    android:id="@+id/tv_play_name"
                    style="@style/couiTextAppearanceHeadline6"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textSize="@dimen/common_name_text_size"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:includeFontPadding="false"
                    android:textColor="@color/coui_color_primary_neutral"
                    tools:text="标准录音 1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:couiAnimateStyle="2"
                    app:couiAnimateTextDuration="420"
                    app:couiAnimateTextDelay="30"
                    app:couiAnimateTextType="7"
                    app:couiAnimateTextOffset="10"/>

                <com.oplus.anim.EffectiveAnimationView
                    android:id="@+id/smart_name_loading"
                    android:layout_width="@dimen/dp28"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:forceDarkAllowed="false"
                    android:visibility="gone"
                    app:anim_loop="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tv_play_name"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_play_name"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>

        <Space
            android:id="@+id/miniRecordTop"
            android:layout_width="1px"
            android:layout_height="1px"
            android:layout_marginTop="@dimen/common_max_time_layout_height"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/wave_gradient_view" />
        <include
            layout="@layout/include_audio_fragment_bottom_button"
            android:visibility="gone"/>

        <LinearLayout
            android:id="@+id/layout_empty"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toTopOf="@id/space_bottom_view_top"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/miniRecordTop">

            <ScrollView
                android:id="@+id/layout_empty_scroller"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical">

                    <com.soundrecorder.common.widget.OSImageView
                        android:id="@+id/iv_mark_empty"
                        android:layout_width="@dimen/os_image_def_width"
                        android:layout_height="@dimen/os_image_def_height"
                        app:anim_raw_json="@raw/ic_mark_list_empty"
                        app:anim_raw_json_night="@raw/ic_mark_list_empty_night"
                        app:img_draw="@drawable/ic_mark_list_empty" />

                    <TextView
                        android:id="@+id/tv_mark_empty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:text="@string/no_mark"
                        android:textColor="@color/coui_color_label_secondary"
                        android:textSize="@dimen/no_mark_empty_text_size" />
                </LinearLayout>
            </ScrollView>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>