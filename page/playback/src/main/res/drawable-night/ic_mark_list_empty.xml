<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="281dp"
    android:height="200dp"
    android:viewportWidth="281"
    android:viewportHeight="200">
  <group>
    <clip-path
        android:pathData="M55.93,25h165.75v149h-165.75z"/>
    <path
        android:pathData="M58.4,27.41C58.4,26.77 58.65,26.16 59.11,25.71C59.56,25.25 60.17,25 60.81,25H103.82C104.46,25 105.07,25.25 105.53,25.71C105.98,26.16 106.23,26.77 106.23,27.41V93.56C106.23,94.02 106.11,94.46 105.86,94.85C105.62,95.23 105.28,95.54 104.87,95.74C104.46,95.94 104,96.01 103.55,95.96C103.1,95.91 102.67,95.73 102.31,95.45C95.68,90.13 89.91,84.86 83.87,79.51C83.43,79.13 82.87,78.92 82.29,78.92C81.71,78.92 81.14,79.13 80.71,79.51C74.72,84.71 68.42,89.9 62.36,95.1C62.01,95.41 61.58,95.61 61.12,95.67C60.66,95.74 60.19,95.67 59.77,95.48C59.35,95.28 58.99,94.97 58.74,94.58C58.49,94.19 58.36,93.73 58.37,93.27L58.4,27.41Z"
        android:fillColor="#8D65AC"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M71.73,163.66H119.31C118.27,161.97 117.72,160.01 117.72,158.02C117.72,156.03 118.27,154.08 119.31,152.38H71.73C70.68,154.08 70.13,156.03 70.13,158.02C70.13,160.01 70.68,161.97 71.73,163.66Z"
        android:fillColor="#DEDEDE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M119.31,163.66C118.27,161.97 117.72,160.01 117.72,158.02C117.72,156.03 118.27,154.08 119.31,152.38"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M119.31,164.66H71.73C71.56,164.66 71.39,164.62 71.24,164.53C71.09,164.45 70.97,164.33 70.88,164.18C69.73,162.33 69.13,160.2 69.13,158.02C69.13,155.85 69.73,153.71 70.88,151.86C70.97,151.72 71.09,151.6 71.24,151.51C71.39,151.43 71.56,151.38 71.73,151.38H119.31C119.57,151.38 119.83,151.49 120.02,151.68C120.2,151.86 120.31,152.12 120.31,152.38C120.31,152.65 120.2,152.9 120.02,153.09C119.83,153.27 119.57,153.38 119.31,153.38H72.3C71.53,154.81 71.13,156.4 71.13,158.02C71.13,159.65 71.53,161.24 72.3,162.67H119.31C119.57,162.67 119.83,162.77 120.02,162.96C120.2,163.15 120.31,163.4 120.31,163.66C120.31,163.93 120.2,164.18 120.02,164.37C119.83,164.56 119.57,164.66 119.31,164.66Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#C9C9C9"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.97,150.35H121.02C122.51,148.85 123.35,146.82 123.35,144.71C123.35,142.6 122.51,140.57 121.02,139.07H83.97C85.46,140.57 86.3,142.6 86.3,144.71C86.3,146.82 85.46,148.85 83.97,150.35Z"
        android:fillColor="#DEDEDE"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M121.02,151.35H83.97C83.7,151.35 83.45,151.24 83.26,151.05C83.07,150.87 82.97,150.61 82.97,150.35C82.97,150.08 83.07,149.83 83.26,149.64C83.45,149.46 83.7,149.35 83.97,149.35H120.59C121.72,148.07 122.35,146.42 122.35,144.71C122.35,143 121.72,141.35 120.59,140.07H83.97C83.7,140.07 83.45,139.97 83.26,139.78C83.07,139.59 82.97,139.34 82.97,139.07C82.97,138.81 83.07,138.56 83.26,138.37C83.45,138.18 83.7,138.08 83.97,138.08H121.02C121.28,138.08 121.53,138.18 121.72,138.37C123.4,140.05 124.34,142.33 124.34,144.71C124.34,147.09 123.4,149.37 121.72,151.06C121.53,151.24 121.28,151.34 121.02,151.35Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#C9C9C9"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M84.86,140.08C85.83,141.43 86.35,143.05 86.35,144.72C86.35,146.38 85.83,148 84.86,149.35"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M134.27,173.81H56.13V168.14C56.13,167.18 56.52,166.25 57.2,165.56C57.89,164.88 58.82,164.49 59.79,164.49H130.61C131.09,164.49 131.57,164.59 132.01,164.77C132.46,164.95 132.86,165.22 133.2,165.56C133.54,165.9 133.81,166.3 134,166.74C134.18,167.19 134.27,167.66 134.27,168.14V173.81Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#C9C9C9"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M187.97,84.94C188.19,84.65 188.43,84.39 188.69,84.14L189.45,87.58C189.47,87.65 189.51,87.72 189.56,87.77C189.62,87.82 189.69,87.86 189.76,87.87C189.84,87.89 189.91,87.88 189.98,87.85C190.05,87.83 190.12,87.78 190.16,87.72C191.65,85.49 192.52,81.82 191.75,74.58C191.15,69.09 190.52,64.07 189.62,56.08C189.24,52.67 188.27,50.09 184.63,50.42C180.99,50.75 179.95,53.67 180.49,56.96C181.33,61.95 179.74,67.14 179.15,72.05C178.2,80.03 179.87,84.22 180.84,86.52C181.72,88.81 183.37,90.72 185.51,91.92C185.6,91.97 185.7,91.99 185.79,91.98C185.89,91.97 185.98,91.94 186.06,91.88C186.14,91.83 186.2,91.75 186.24,91.66C186.28,91.57 186.29,91.47 186.27,91.37C186.1,90.24 186.16,89.08 186.45,87.98C186.74,86.87 187.26,85.84 187.97,84.94Z"
        android:fillColor="#CC8A52"/>
    <path
        android:pathData="M164.64,57.81C172.12,57.81 178.19,51.75 178.19,44.26C178.19,36.78 172.12,30.72 164.64,30.72C157.16,30.72 151.1,36.78 151.1,44.26C151.1,51.75 157.16,57.81 164.64,57.81Z"
        android:fillColor="#CC8A52"/>
    <path
        android:pathData="M130.21,50.35C129.34,50.43 128.51,50.77 127.83,51.33C127.15,51.88 126.65,52.63 126.39,53.47C126.28,53.85 126.24,54.24 126.28,54.63C126.32,55.1 126.5,55.54 126.79,55.91C127.08,56.27 127.48,56.54 127.93,56.67C127.07,58.09 126.66,59.74 126.76,61.4C127.07,63.73 130.23,64.8 130.23,64.8L137.52,63.97L141.63,55.44L140.63,53.53C140.01,52.32 139.04,51.32 137.84,50.68C136.64,50.04 135.27,49.78 133.92,49.94L130.21,50.35Z"
        android:fillColor="#DEDEDE"/>
    <path
        android:pathData="M167.81,67.48C159.24,66.91 152.85,57.92 153.39,48.68L153.53,46.22C154.08,36.97 161.32,30.99 169.83,31.6L172.3,31.74C180.81,32.18 187.27,38.97 186.76,48.18C186.14,58.78 177.64,68.18 167.81,67.48Z"
        android:fillColor="#CC8A52"/>
    <path
        android:pathData="M184.87,90.11C186.19,94.29 187.78,98.36 189.42,102.41C191.07,106.46 192.84,110.39 194.69,114.38C195.62,116.38 196.55,118.37 197.52,120.28C198.49,122.18 199.52,124.18 200.51,126.09C202.69,129.89 203.51,129.73 206.55,133.14C208.76,135.6 213.18,136.36 215.02,133.61C216.12,131.4 216.71,128.96 216.73,126.5C216.36,121.86 215.22,117.31 213.34,113.05C212.52,111.05 206.88,99.27 204.93,95.37C202.99,91.47 200.94,87.59 198.69,83.84C197.72,82.24 196.19,81.05 194.4,80.5C192.62,79.94 190.69,80.07 188.98,80.84C187.28,81.61 185.91,82.98 185.15,84.69C184.38,86.4 184.27,88.33 184.83,90.11H184.87Z"
        android:fillColor="#DEDEDE"/>
    <path
        android:pathData="M143.62,81.03C141.24,82.26 139.07,83.84 137.17,85.72C136.17,86.63 135.32,87.59 134.48,88.58C133.63,89.56 132.77,90.57 131.91,91.57C130.24,93.56 128.6,95.66 127.05,97.8C125.5,99.94 124.07,102.17 122.77,104.48V104.54C121.92,106.1 121.63,107.91 121.96,109.65C122.29,111.4 123.23,112.98 124.6,114.11C125.97,115.25 127.69,115.86 129.47,115.86C131.25,115.86 132.98,115.24 134.35,114.11C136.42,112.36 138.37,110.48 140.19,108.47C142.01,106.47 143.72,104.42 145.38,102.27C146.19,101.21 147,100.12 147.78,99.01C148.59,97.91 149.33,96.75 150,95.56C151.46,93.04 152.48,90.28 152.99,87.41C153.18,86.28 153.05,85.13 152.62,84.07C152.2,83.01 151.49,82.09 150.57,81.41C149.66,80.73 148.57,80.31 147.44,80.2C146.3,80.09 145.16,80.3 144.13,80.8L143.62,81.03Z"
        android:fillColor="#DEDEDE"/>
    <path
        android:pathData="M164.16,60.63L163.09,78.93L176.33,79.71L177.41,61.41L164.16,60.63Z"
        android:fillColor="#DEDEDE"/>
    <group>
      <clip-path
          android:pathData="M177.25,64.51L164,63.73L162.92,82.04L176.17,82.82L177.25,64.51Z"/>
      <path
          android:pathData="M170.28,66.96C169.87,68.48 169.01,69.85 167.82,70.89C166.63,71.93 165.16,72.59 163.59,72.79C162.02,72.99 160.43,72.72 159.02,72.02C157.6,71.32 156.43,70.21 155.64,68.84C154.86,67.46 154.5,65.89 154.61,64.31C154.72,62.74 155.3,61.23 156.27,59.98C157.24,58.73 158.56,57.8 160.05,57.3C161.55,56.81 163.17,56.76 164.69,57.18C166.73,57.74 168.46,59.09 169.5,60.92C170.55,62.75 170.83,64.92 170.28,66.96Z"
          android:fillColor="#808080"/>
    </group>
    <path
        android:pathData="M161.68,36.73C153.28,38.8 154.13,54.97 154.29,56.68C154.96,64.15 159.43,70.42 164.61,69.83C168.35,69.34 171.84,67.68 174.58,65.08C177.31,62.49 179.15,59.09 179.84,55.38C180.07,54.02 181.83,51.25 180.69,47.49C178.64,40.51 169.57,34.79 161.68,36.73Z"
        android:fillColor="#DEDEDE"/>
    <path
        android:pathData="M160.88,59.24C161.65,59.18 162.39,58.88 162.98,58.38"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M156.41,51.77C156.41,52.51 156.8,53.13 157.41,53.15C158.02,53.17 158.41,52.59 158.47,51.86C158.52,51.54 158.44,51.22 158.25,50.96C158.07,50.7 157.78,50.52 157.47,50.47C156.92,50.45 156.44,51.03 156.41,51.77Z"
        android:fillColor="#323739"/>
    <path
        android:pathData="M167.16,52.29C167.1,52.62 167.18,52.95 167.37,53.22C167.55,53.49 167.83,53.68 168.15,53.75C168.74,53.75 169.24,53.16 169.27,52.38C169.33,52.06 169.25,51.73 169.07,51.45C168.88,51.18 168.6,50.99 168.27,50.93C167.66,50.94 167.2,51.52 167.16,52.29Z"
        android:fillColor="#323739"/>
    <path
        android:pathData="M155.9,50.52C156.1,50.16 156.4,49.88 156.77,49.7C157.13,49.52 157.55,49.46 157.95,49.52C158.31,49.57 158.66,49.7 158.97,49.89C159.29,50.09 159.55,50.35 159.75,50.66"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M171.19,51.11C170.91,50.75 170.55,50.47 170.15,50.26C169.74,50.06 169.3,49.95 168.85,49.94C168.45,49.91 168.05,49.98 167.68,50.16C167.32,50.33 167.01,50.6 166.79,50.94"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M135.54,92.84C135.56,93.84 135.68,94.85 135.9,95.83C136.12,96.82 136.57,97.74 137.2,98.53C137.83,99.31 138.63,99.95 139.55,100.38C140.46,100.8 141.47,101.02 142.48,101C143.49,100.98 144.48,100.73 145.38,100.27C145.64,100.13 145.94,100.05 146.24,100.04C149.58,100.04 153.97,103.78 153.98,107.13C162.82,106.8 168.55,101.71 168.55,92.84C168.45,88.53 166.66,84.43 163.58,81.43C160.49,78.42 156.35,76.73 152.05,76.73C147.74,76.73 143.6,78.42 140.52,81.43C137.43,84.43 135.65,88.53 135.54,92.84Z"
        android:fillColor="#DEDEDE"/>
    <path
        android:pathData="M188.87,76.18L183.71,75.71C181.87,75.55 180.07,75.05 178.4,74.25L177.59,73.86C177.33,73.74 177.06,73.16 176.77,73.18C176.49,73.19 176.2,73.81 175.96,73.96C175.71,74.11 175.52,74.32 175.38,74.57C175.24,74.82 175.17,75.1 175.17,75.39V79.25H188.86L188.87,76.18Z"
        android:fillColor="#DEDEDE"/>
    <path
        android:pathData="M152.04,76.35L157.19,75.88C159.03,75.72 160.75,74.83 162.42,74.03L163.54,73.29C163.8,73.16 163.86,73.85 164.14,73.87C164.43,73.89 164.7,73.97 164.94,74.13C165.18,74.28 165.38,74.49 165.52,74.74C165.66,74.99 165.73,75.27 165.73,75.56V79.42H152.04V76.35Z"
        android:fillColor="#DEDEDE"/>
    <path
        android:pathData="M178.64,47.76L177.38,62.56C177.38,62.56 183.73,57.38 184.02,52.74C184.39,46.48 178.64,47.76 178.64,47.76Z"
        android:fillColor="#CC8A52"/>
    <path
        android:pathData="M148.09,140.91H184.52C186.79,126.26 190.51,116.26 193.67,99.4C195.94,87.56 184.13,76.74 174.21,75.86L168.12,75.31C158.2,74.42 144.66,82.97 144.79,95.02C144.86,112.16 150.34,125.07 148.09,140.91Z"
        android:fillColor="#DEDEDE"/>
    <path
        android:pathData="M177.97,50.87C178.69,50.51 179.5,50.38 180.3,50.5C181.1,50.63 181.83,50.99 182.41,51.56C183.79,53.23 182.26,56.27 180.64,57.82C180.43,58.02 178.33,60.02 177.04,59.39C175.24,58.5 174.94,52.4 177.97,50.87Z"
        android:fillColor="#DEDEDE"/>
    <path
        android:pathData="M181.53,53.68C181.35,53.48 181.11,53.33 180.86,53.25C180.6,53.16 180.32,53.14 180.05,53.18C179.51,53.28 179.02,53.54 178.64,53.93"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M180.88,48.12C179.88,50.35 179.3,52.02 177.82,52.84C177.72,52.9 177.61,52.92 177.5,52.91C177.38,52.9 177.28,52.86 177.19,52.8C177.1,52.73 177.02,52.64 176.98,52.53C176.94,52.43 176.93,52.31 176.95,52.2C177.14,50.72 176.89,49.22 176.23,47.87C169.9,47.62 165.76,44.36 161.89,39C158.44,41.26 155.73,44.47 154.07,48.24C153.68,43.1 156.56,37.27 161.13,35.52C177.88,29.3 183.48,42.22 180.88,48.12Z"
        android:fillColor="#CC8A52"/>
    <path
        android:pathData="M126.04,59.84L125.05,57.84C125.02,57.78 124.98,57.73 124.93,57.68C124.88,57.64 124.82,57.61 124.75,57.59C124.69,57.57 124.62,57.56 124.56,57.57C124.49,57.58 124.43,57.6 124.37,57.64L121.38,59.56C121.3,59.62 121.25,59.7 121.24,59.8C121.23,59.89 121.25,59.99 121.3,60.07L122.06,61.54C122.09,61.63 122.15,61.7 122.24,61.75C122.32,61.79 122.42,61.8 122.51,61.78L125.77,60.5C125.83,60.48 125.89,60.45 125.94,60.4C125.99,60.35 126.03,60.3 126.06,60.23C126.08,60.17 126.1,60.1 126.1,60.03C126.09,59.96 126.08,59.9 126.04,59.84Z"
        android:fillColor="#323739"/>
    <path
        android:pathData="M121.78,61.21L121.4,60.46C121.38,60.44 121.36,60.41 121.34,60.39C121.32,60.37 121.29,60.36 121.26,60.35C121.23,60.35 121.2,60.34 121.17,60.35C121.14,60.36 121.11,60.37 121.09,60.38L119.48,61.59C119.42,61.63 119.38,61.7 119.36,61.77C119.34,61.84 119.35,61.92 119.38,61.98C119.41,62.05 119.47,62.1 119.54,62.13C119.6,62.16 119.68,62.16 119.75,62.14L121.75,61.51C121.79,61.48 121.81,61.42 121.82,61.37C121.82,61.31 121.81,61.26 121.78,61.21Z"
        android:fillColor="#323739"/>
    <path
        android:pathData="M122.18,61.04L121.79,60.28C121.69,60.09 121.46,60.01 121.26,60.11C121.07,60.21 121,60.44 121.1,60.63L121.49,61.4C121.58,61.59 121.82,61.66 122.01,61.57C122.2,61.47 122.28,61.23 122.18,61.04Z"
        android:fillColor="#323739"/>
    <path
        android:pathData="M144.82,46.82L124.83,57.05C124.56,57.19 124.46,57.52 124.6,57.79L125.85,60.25C125.99,60.52 126.32,60.62 126.59,60.49L146.57,50.26C146.84,50.12 146.95,49.79 146.81,49.52L145.55,47.06C145.42,46.79 145.09,46.69 144.82,46.82Z"
        android:fillColor="#DEDEDE"/>
    <path
        android:pathData="M127.37,63.39C126.86,67.16 126.26,70.89 125.71,74.68C125.15,78.47 124.49,82.2 123.82,85.95C123.15,89.7 122.53,93.46 122.07,97.25C121.83,99.13 121.63,101.02 121.5,102.91C121.37,104.81 121.29,106.71 121.35,108.63C121.42,110.38 122.12,112.06 123.33,113.33C124.54,114.61 126.17,115.4 127.92,115.56C129.67,115.73 131.42,115.25 132.84,114.22C134.27,113.19 135.27,111.68 135.66,109.96C136.07,108.1 136.37,106.22 136.58,104.33C136.8,102.44 136.95,100.55 137.07,98.65C137.32,94.85 137.38,91.04 137.44,87.23C137.5,83.42 137.56,79.61 137.69,75.81C137.82,72.01 137.95,68.2 138.15,64.4C138.2,63.02 137.73,61.66 136.81,60.61C135.9,59.57 134.62,58.91 133.23,58.78C131.85,58.66 130.47,59.06 129.38,59.92C128.29,60.79 127.57,62.03 127.37,63.41V63.39Z"
        android:fillColor="#DEDEDE"/>
    <path
        android:pathData="M165.47,46.63C167.1,46.36 168.76,46.45 170.36,46.87C171.65,47.21 172.87,47.77 173.98,48.52"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M161.38,62.52C161.38,62.52 161.03,61.86 162.28,61.34C163.75,60.96 165.27,60.77 166.79,60.77C166.69,61.3 166.48,61.79 166.16,62.22C165.85,62.65 165.44,63.01 164.97,63.26C162.2,64.45 161.38,62.52 161.38,62.52Z"
        android:fillColor="#808080"/>
    <path
        android:pathData="M137.05,65.96L142.83,57.37C143.17,56.83 143.28,56.18 143.15,55.56C143.02,54.94 142.66,54.39 142.14,54.03C140.58,53.15 134.21,53.72 134.21,53.72C134.21,53.72 133.93,55.04 135.03,55.39C136.24,55.78 137.48,56.09 138.73,56.3L135.09,57.76C134.08,58.12 133.21,58.81 132.63,59.71C132.04,60.61 131.76,61.68 131.84,62.75L132.02,65.64L137.05,65.96Z"
        android:fillColor="#DEDEDE"/>
    <path
        android:pathData="M138.97,49.88L136.42,46.81C136.24,46.65 136.02,46.54 135.79,46.48C135.56,46.42 135.32,46.41 135.09,46.45C134.85,46.5 134.63,46.59 134.43,46.72C134.24,46.86 134.07,47.04 133.96,47.25L130.8,52.93C130.74,53.05 130.72,53.19 130.74,53.32C130.75,53.45 130.8,53.58 130.89,53.68C130.97,53.79 131.08,53.87 131.21,53.91C131.34,53.95 131.47,53.96 131.6,53.93C132,53.84 132.39,53.69 132.75,53.5C133.95,52.56 134.96,51.41 135.74,50.11L136.24,51.26L138.97,49.88Z"
        android:fillColor="#DEDEDE"/>
    <path
        android:pathData="M132.38,50.1L130.8,52.93C130.74,53.05 130.72,53.19 130.74,53.32C130.75,53.45 130.8,53.58 130.89,53.68C130.97,53.79 131.08,53.87 131.21,53.91C131.34,53.95 131.47,53.96 131.6,53.93C132,53.84 132.39,53.69 132.75,53.5C134.21,52.64 135.92,49.66 135.92,49.66"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M135.35,50.58L135.83,51.42"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M161.16,62.46C163.18,62.6 165.18,61.95 166.74,60.64"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M141.47,53.14L142.18,53.9"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M154.18,53.28C154.18,54.99 154.24,56.27 154.28,56.66C154.96,64.13 159.4,69.98 164.6,69.81C167.52,69.71 170.76,67.66 173.31,65.82"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M152.03,75.59C154.37,75.54 156.71,75.32 159.01,74.93C162.32,74.31 163.49,73.25 163.49,73.25L163.67,69.89"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M177.64,59.77L176.92,73.42C178.23,74.17 179.65,74.69 181.13,74.95C182.95,75.36 184.81,75.55 186.68,75.49"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M127.24,63.8C127.24,64.89 127.57,65.97 128.18,66.88C128.8,67.79 129.67,68.5 130.68,68.92C131.7,69.33 132.82,69.44 133.89,69.22C134.97,69 135.96,68.47 136.73,67.69C137.5,66.91 138.03,65.92 138.24,64.84C138.44,63.76 138.33,62.65 137.9,61.63C137.48,60.62 136.76,59.76 135.84,59.15C134.92,58.55 133.85,58.23 132.75,58.24C131.28,58.25 129.88,58.84 128.85,59.88C127.82,60.92 127.24,62.33 127.24,63.8Z"
        android:fillColor="#DEDEDE"/>
    <path
        android:pathData="M129.16,116.1C133.69,116.1 137.37,112.45 137.37,107.95C137.37,103.45 133.69,99.8 129.16,99.8C124.62,99.8 120.95,103.45 120.95,107.95C120.95,112.45 124.62,116.1 129.16,116.1Z"
        android:fillColor="#DEDEDE"/>
    <path
        android:pathData="M134.27,114.11C136.35,112.36 138.3,110.48 140.12,108.47C141.94,106.47 143.65,104.41 145.31,102.28"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M127.44,62.98C127.44,63.11 127.39,63.25 127.37,63.4C126.87,67.16 126.26,70.92 125.71,74.68C125.15,78.44 124.49,82.2 123.82,85.95C123.15,89.7 122.53,93.47 122.07,97.25C121.83,99.13 121.63,101.02 121.5,102.91C121.35,104.81 121.3,106.72 121.36,108.63C121.41,110.16 121.95,111.64 122.9,112.85C123.85,114.05 125.16,114.92 126.64,115.33C127.91,115.82 129.28,115.97 130.63,115.75C131.97,115.54 133.23,114.97 134.27,114.11"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M126.67,60.52C126.65,60.82 126.65,61.11 126.67,61.4C126.75,61.99 127.01,62.54 127.4,62.98"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M138.89,49.77L136.42,46.78C136.24,46.62 136.02,46.51 135.79,46.45C135.56,46.39 135.32,46.38 135.09,46.42C134.85,46.47 134.63,46.56 134.43,46.7C134.24,46.83 134.07,47.01 133.96,47.22L132.39,50.07L130.21,50.32C129.34,50.4 128.51,50.74 127.83,51.29C127.15,51.85 126.65,52.59 126.39,53.43C126.28,53.81 126.24,54.21 126.28,54.6C126.32,55.08 126.49,55.54 126.76,55.93"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M138.15,64.4C137.95,68.2 137.84,72.01 137.69,75.81C137.58,79.01 137.19,98.32 137.14,101.54"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M118.07,61.27C113.14,61.27 113.14,64.73 108.19,64.73C103.24,64.73 103.2,61.27 98.3,61.27C93.4,61.27 93.36,64.73 88.43,64.73C83.49,64.73 83.44,61.27 78.53,61.27C73.62,61.27 73.54,64.73 68.64,64.73"
        android:strokeLineJoin="round"
        android:strokeWidth="1.54"
        android:fillColor="#00000000"
        android:strokeColor="#B3B0CF"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M131.03,53.93L124.77,57.09C124.71,57.12 124.66,57.16 124.61,57.21C124.57,57.26 124.54,57.32 124.52,57.38C124.5,57.44 124.49,57.51 124.49,57.57C124.5,57.64 124.52,57.7 124.55,57.76L125.86,60.31C125.92,60.43 126.02,60.52 126.14,60.56C126.27,60.6 126.41,60.59 126.52,60.53L135.74,55.84"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M140.02,53.63L146.63,50.26C146.68,50.23 146.74,50.19 146.78,50.14C146.82,50.09 146.85,50.03 146.87,49.97C146.89,49.91 146.9,49.84 146.89,49.78C146.88,49.71 146.87,49.65 146.84,49.59L145.53,47.04C145.47,46.92 145.37,46.83 145.24,46.79C145.11,46.75 144.98,46.76 144.86,46.82L134.02,52.37"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M132.3,63.35C132.3,63.35 131.68,60.7 133.14,59.06C134.43,57.57 138.66,56.29 138.66,56.29L134.68,55.55C134.37,55.49 134.03,54.96 134.03,54.96C133.99,54.83 133.98,54.69 134,54.55C134.03,54.42 134.08,54.29 134.16,54.18C134.24,54.08 134.35,53.99 134.47,53.92C134.59,53.86 134.73,53.83 134.86,53.83C137.29,53.76 139.57,53.43 142.16,54.03C143.44,54.33 143.55,56.25 142.85,57.37L138.17,64.35V64.43"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M130.35,50.36C129.27,51.63 128.73,53.27 128.86,54.93"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M128.31,51C127.41,52.36 127.08,54.01 127.37,55.62"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M169.13,59.13C172.28,59.13 174.84,56.57 174.84,53.42C174.84,50.27 172.28,47.72 169.13,47.72C165.98,47.72 163.43,50.27 163.43,53.42C163.43,56.57 165.98,59.13 169.13,59.13Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M156.02,58.92C159.17,58.92 161.73,56.36 161.73,53.21C161.73,50.06 159.17,47.51 156.02,47.51C152.87,47.51 150.32,50.06 150.32,53.21C150.32,56.36 152.87,58.92 156.02,58.92Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M163.43,53.11L161.73,53.01"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M155.1,46.71C156.93,46.07 158.92,46.07 160.74,46.71"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M179.65,64.07C181.56,64.07 183.12,62.51 183.12,60.59C183.12,58.68 181.56,57.12 179.65,57.12C177.73,57.12 176.17,58.68 176.17,60.59C176.17,62.51 177.73,64.07 179.65,64.07Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#C9C9C9"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M155.13,76.05C155.13,76.05 157.28,82.14 167.97,82.98C180.83,83.98 184.52,76.2 184.52,76.2"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M144.79,95.02C144.84,107.61 150.4,126.18 148.09,140.91"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M184.81,139.07C186.02,127.48 191.55,110.91 193.67,99.4"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M153.51,129.04C155.69,112.84 161.79,73.88 161.79,73.88C158.85,73.91 155.91,74.09 152.99,74.43C150.05,74.86 147.24,75.96 144.8,77.66C142.35,79.35 140.33,81.58 138.89,84.19C138.97,80.2 139.23,72.95 139.23,72.95C139.23,72.95 121.43,75.23 118.35,75.58C118.35,75.58 115.99,110.55 123.68,115.61C134.4,122.68 142.85,108.57 142.85,108.57L140.27,128.65L153.51,129.04Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#C9C9C9"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M138.92,83.95L138.16,101.54"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M127.16,88.3H116.72V82.39H127.16C127.96,82.39 128.72,82.71 129.28,83.27C129.84,83.83 130.15,84.59 130.15,85.38C130.15,86.18 129.84,86.94 129.28,87.5C128.72,88.06 127.96,88.38 127.16,88.38V88.3Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#DEDEDE"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M146.17,87.16C144.67,92.02 143.35,102.05 142.84,108.57"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M208.79,137.08C218.61,138.61 226.74,133.8 217.29,112.33C212.8,102.14 209.25,80.17 198.05,76.05C191.94,73.87 185.4,73.13 178.96,73.88L175.18,128.75L194.58,129.34L195.18,87.16"
        android:fillColor="#C9C9C9"/>
    <path
        android:pathData="M208.79,137.08C218.61,138.61 226.74,133.8 217.29,112.33C212.8,102.14 209.25,80.17 198.05,76.05C191.94,73.87 185.4,73.13 178.96,73.88L175.18,128.75L194.58,129.34L195.18,87.16"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M178.96,73.88C178.96,73.88 184.34,80.65 186.37,86.05C188.4,91.46 188.37,93.87 188.37,93.87L176.28,108.48L178.96,73.88Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#DEDEDE"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M175.59,84.63L171.76,123.04C171.68,123.88 172.29,124.63 173.13,124.71L202.89,127.68C203.73,127.77 204.47,127.15 204.56,126.32L208.39,87.9C208.48,87.06 207.86,86.31 207.02,86.23L177.26,83.26C176.43,83.18 175.68,83.79 175.59,84.63Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#DEDEDE"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M193.8,85.2L192.85,94.73C192.85,94.8 192.85,94.86 192.88,94.92C192.9,94.98 192.94,95.04 192.99,95.08C193.04,95.12 193.1,95.15 193.16,95.16C193.22,95.18 193.29,95.18 193.35,95.16L195.62,94.28L196.45,93.96C196.57,93.91 196.71,93.89 196.84,93.9C196.98,93.91 197.11,93.95 197.22,94.02L199.22,95.19L200.38,95.89C200.44,95.91 200.51,95.93 200.57,95.92C200.63,95.92 200.7,95.9 200.75,95.87C200.8,95.84 200.85,95.79 200.88,95.74C200.92,95.68 200.94,95.62 200.94,95.56L201.94,86C201.96,85.83 201.9,85.66 201.79,85.53C201.68,85.4 201.52,85.31 201.35,85.29L194.57,84.62C194.48,84.6 194.39,84.61 194.3,84.63C194.21,84.65 194.13,84.69 194.06,84.75C193.99,84.8 193.93,84.87 193.88,84.95C193.84,85.03 193.81,85.11 193.8,85.2Z"
        android:fillColor="#CC8A52"/>
    <path
        android:pathData="M177.13,113.13C179.93,114.82 182.68,116.67 185.45,118.4C188.23,120.14 190.92,122.03 193.63,123.84C196.35,125.66 198.78,127.68 201.61,129.34C203.02,130.17 204.44,130.97 205.88,131.71C207.36,132.4 208.88,133 210.42,133.51C213.94,134.84 218.21,133.42 219.11,129.77C219.77,127.06 218.7,123.41 216.81,121.92C215.5,120.93 214.13,120.04 212.7,119.25C211.29,118.43 209.85,117.68 208.4,116.95C205.49,115.47 202.5,114.14 199.52,112.83C196.54,111.51 193.53,110.19 190.54,108.84C187.55,107.48 184.62,106.08 181.7,104.65C181.1,104.35 180.45,104.18 179.79,104.14C179.12,104.09 178.45,104.18 177.82,104.4C177.19,104.61 176.61,104.95 176.11,105.39C175.61,105.84 175.2,106.37 174.91,106.97C173.74,109.32 174.92,111.79 177.13,113.13Z"
        android:fillColor="#DEDEDE"/>
    <path
        android:pathData="M178.37,114.39L174.25,112.03C173.15,111.34 172.28,110.34 171.75,109.16C171.21,107.98 171.04,106.67 171.26,105.39L172.6,97.41C172.85,95.95 174.92,95.61 176.88,97.02L178.88,98.49C178.75,97.99 178.56,97.51 178.3,97.07C177.23,96.57 175.84,95.89 175.68,94.7C175.68,94.65 175.68,94.59 175.7,94.53C175.71,94.48 175.74,94.43 175.78,94.39C175.81,94.34 175.86,94.31 175.91,94.29C175.96,94.26 176.02,94.25 176.07,94.25C177,94.25 179.4,94.25 180.32,95.03C181.45,96.04 182.45,97.17 183.32,98.4C187.53,103.46 186.9,107.76 186.9,107.76L182.55,113.64C182.07,114.26 181.38,114.68 180.61,114.82C179.83,114.96 179.04,114.81 178.37,114.39Z"
        android:fillColor="#DEDEDE"/>
    <path
        android:pathData="M179.85,114.78C179.93,114.79 180,114.79 180.07,114.78C185.02,117.86 189.74,121.27 194.21,125.01"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M186.94,106.7C186.68,103.61 185.41,100.68 183.33,98.37C182.46,97.14 181.45,96.01 180.33,95C179.41,94.24 177.01,94.2 176.08,94.22C176.03,94.23 175.97,94.24 175.92,94.26C175.87,94.29 175.83,94.32 175.79,94.36C175.76,94.41 175.73,94.45 175.71,94.51C175.7,94.56 175.69,94.62 175.7,94.67C175.85,95.86 177.24,96.54 178.31,97.04C178.56,97.48 178.76,97.96 178.88,98.46L176.88,97C174.89,95.58 172.83,95.92 172.59,97.38L171.25,105.36C171.03,106.64 171.2,107.95 171.74,109.14C172.27,110.32 173.14,111.32 174.24,112.01C174.24,112.01 178.7,114.43 179.85,114.79"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M205.28,116.78L191.32,109.17L186.94,106.71"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M187.85,105.78L191.22,108.38L184.06,117.66C183.95,117.8 183.79,117.89 183.61,117.92C183.43,117.94 183.25,117.89 183.11,117.78L180.81,116C180.66,115.89 180.57,115.73 180.55,115.55C180.53,115.38 180.58,115.2 180.69,115.05L187.85,105.78Z"
        android:fillColor="#B89374"/>
    <path
        android:pathData="M191.71,107.41L188.48,104.91C188.03,104.56 187.38,104.64 187.04,105.09L183.18,110.07C182.83,110.52 182.91,111.17 183.36,111.52L186.59,114.02C187.04,114.37 187.69,114.29 188.04,113.84L191.9,108.85C192.24,108.4 192.16,107.76 191.71,107.41Z"
        android:fillColor="#595050"/>
    <path
        android:pathData="M178.94,98.46L180.83,99.8"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M161.79,73.88C161.79,73.88 155.8,79.57 152.81,84.54C149.82,89.5 149.49,91.84 149.49,91.84L156.47,108.53L161.79,73.88Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#DEDEDE"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M209.16,137.13C209.16,137.13 205.62,136.6 197.77,133.44C192.98,131.34 188.32,128.96 183.81,126.32L192.94,108.49L214.55,117.59"
        android:fillColor="#C9C9C9"/>
    <path
        android:pathData="M209.16,137.13C209.16,137.13 205.62,136.6 197.77,133.44C192.98,131.34 188.32,128.96 183.81,126.32L192.94,108.49L214.55,117.59"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M213.09,127.14m-7.54,0a7.54,7.54 0,1 1,15.09 0a7.54,7.54 0,1 1,-15.09 0"
        android:fillColor="#C9C9C9"/>
    <path
        android:pathData="M198.97,124.84L194.49,134.27L189.15,131.74L193.63,122.3C193.97,121.59 194.58,121.04 195.33,120.77C196.08,120.5 196.9,120.54 197.62,120.89C198.34,121.23 198.89,121.84 199.15,122.58C199.42,123.33 199.38,124.16 199.04,124.87L198.97,124.84Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#DEDEDE"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M141.83,173.64H192.85C192.57,163.66 191.32,150.39 184.81,139.11H148.44C148.44,139.11 144.51,151.35 141.83,173.64Z"
        android:fillColor="#B89374"/>
  </group>
</vector>
