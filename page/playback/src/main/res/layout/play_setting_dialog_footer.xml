<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:paddingHorizontal="@dimen/dp32"
    android:orientation="vertical">
    <TextView
        android:id="@+id/tv_play_setting_play_speed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/tv_play_setting_restore_all"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/tv_play_setting_restore_all"
        android:text="@string/play_speed"
        android:fontFamily="sans-serif-medium"
        android:textSize="@dimen/sp12"
        android:textColor="?attr/couiColorLabelSecondary" />
    <TextView
        android:id="@+id/tv_play_setting_restore_all"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:clickable="true"
        android:focusable="true"
        android:text="@string/dialog_play_setting_btn_reset"
        android:fontFamily="sans-serif-medium"
        android:textSize="@dimen/sp12"
        android:textColor="?attr/couiColorPrimaryText" />
</androidx.constraintlayout.widget.ConstraintLayout>
