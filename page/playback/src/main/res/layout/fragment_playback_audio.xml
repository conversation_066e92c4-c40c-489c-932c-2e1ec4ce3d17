<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/m_constraint_layout"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@color/coui_color_white"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout
            android:id="@+id/wave_gradient_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintHeight_percent="@dimen/play_wave_view_height_percent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/dp22"
            app:backgroundWhole="@color/wave_recycler_background">

            <com.soundrecorder.playback.audio.PlayWaveRecyclerView
                android:id="@+id/wave_recyclerview"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:importantForAccessibility="no"
                android:layoutDirection="ltr"/>
        </com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout>

        <LinearLayout
            android:id="@+id/layout_time"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/play_time_area_margin_horizontal"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/play_mark_list_padding_top"
            android:clickable="true"
            android:importantForAccessibility="no"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/wave_gradient_view"
            app:layout_constraintEnd_toEndOf="parent">

            <!--只有在H>840只显示layout_time，不显示标记下，字体为最大号-->
            <TextView
                android:id="@+id/tv_current_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="@dimen/common_max_time_text_size"
                android:gravity="center"
                android:textAppearance="@style/couiTextAppearanceDisplayM"
                android:fontFamily="sys-sans-en"
                android:fontFeatureSettings="tnum"
                android:textStyle="bold"
                android:textColor="@color/coui_color_primary_neutral"
                tools:text="00:33" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_transfer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/play_time_area_margin_horizontal"
            android:background="@drawable/bg_turn_on_transcription"
            android:drawableStart="@drawable/speech_to_text"
            android:ellipsize="end"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/transfer_text"
            android:textColor="@color/coui_color_primary_neutral"
            android:textSize="@dimen/common_name_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/wave_gradient_view"
            tools:text="@string/transfer_text" />

        <Space
            android:id="@+id/miniRecordTop"
            android:layout_width="1px"
            android:layout_height="1px"
            android:layout_marginTop="@dimen/common_max_time_layout_height"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/wave_gradient_view" />

        <include
            android:visibility="gone"
            layout="@layout/include_audio_fragment_bottom_button"/>

        <LinearLayout
            android:id="@+id/layout_empty"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/space_bottom_view_top"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/miniRecordTop">

            <ScrollView
                android:id="@+id/layout_empty_scroller"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical">

                    <com.soundrecorder.common.widget.OSImageView
                        android:id="@+id/iv_mark_empty"
                        android:layout_width="@dimen/os_image_def_width"
                        android:layout_height="@dimen/os_image_def_height"
                        app:anim_raw_json="@raw/ic_mark_list_empty"
                        app:img_draw="@drawable/ic_mark_list_empty"
                        app:anim_raw_json_night="@raw/ic_mark_list_empty_night"/>

                    <TextView
                        android:id="@+id/tv_mark_empty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:textFontWeight="500"
                        android:fontFamily="sans-serif-medium"
                        android:orientation="vertical"
                        android:text="@string/no_mark"
                        android:textColor="@color/coui_color_label_primary"
                        android:textSize="@dimen/sp16" />
                </LinearLayout>
            </ScrollView>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>