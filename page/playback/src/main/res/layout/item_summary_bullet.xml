<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingVertical="@dimen/dp6"
    android:paddingStart="@dimen/dp16"
    android:gravity="top">

    <TextView
        android:id="@+id/text_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp14"
        android:textColor="@color/coui_color_label_primary"
        android:layout_marginEnd="@dimen/dp8"
        android:layout_marginTop="@dimen/dp2" />

    <TextView
        android:id="@+id/text_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textSize="@dimen/sp14"
        android:textColor="@color/coui_color_label_primary"
        android:lineSpacingExtra="@dimen/dp2" />

</LinearLayout>
