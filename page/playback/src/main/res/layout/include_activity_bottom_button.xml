<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_marginEnd="@dimen/play_layout_mark_marginEnd_activity"
        android:layout_marginStart="@dimen/play_layout_mark_marginHorizontal"
        android:id="@+id/markLayout"
        android:visibility="gone"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:weightSum="5"
        app:layout_constraintEnd_toStartOf="@id/tool_center_guide_line"
        app:layout_constraintBottom_toBottomOf="@id/middle_control"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintTop_toTopOf="@id/middle_control">

        <!--生成摘要-->
        <FrameLayout
            android:id="@+id/layout_summary_activity"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <com.soundrecorder.playback.view.RepeatAnimationTextView
                android:id="@+id/tv_summary_activity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:contentDescription="@string/generate_summary"
                android:gravity="center"
                android:paddingHorizontal="@dimen/play_btn_control_paddingHorizontal"
                android:paddingTop="@dimen/dp8"
                app:animViewHeight="@dimen/playback_bottom_tool_image_height"
                app:animViewWidth="@dimen/playback_bottom_tool_image_width"
                app:anim_rawRes_light="@raw/summary_generation_animation_new"
                app:anim_rawRes_night="@raw/summary_generation_animation_new"
                app:default_image_res="@drawable/ic_summary_icon"
                app:bottom_text="@string/generate_summary"
                app:bottom_text_margin_top="@dimen/dp6"
                app:bottom_text_minWidthStyle="false"
                app:bottom_text_paddingBottom="@dimen/dp8"/>
        </FrameLayout>

        <!--转文本-->
        <FrameLayout
            android:id="@+id/layout_transfer_text_activity"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <com.soundrecorder.playback.newconvert.view.TransferAnimationTextView
                android:id="@+id/view_transfer_text_activity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:contentDescription="@string/transfer_text"
                android:gravity="center"
                android:paddingHorizontal="@dimen/play_btn_control_paddingHorizontal"
                android:paddingTop="@dimen/dp8"
                app:animFrameRate="60"
                app:animRepeatRateStart="19"
                app:animRepeatRateEnd="103"
                app:animViewHeight="@dimen/playback_bottom_tool_image_height"
                app:animViewWidth="@dimen/playback_bottom_tool_image_width"
                app:anim_rawRes_light="@raw/ic_transferring_light"
                app:anim_rawRes_night="@raw/ic_transferring_night"
                app:default_image_res="@drawable/ic_convert_t"
                app:bottom_text="@string/transfer_text"
                app:bottom_text_margin_top="@dimen/dp6"
                app:bottom_text_minWidthStyle="false"
                app:bottom_text_paddingBottom="@dimen/dp8"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/layout_mark_photo"
                app:layout_constraintStart_toStartOf="parent" />
        </FrameLayout>

        <!--标记-->
        <FrameLayout
            android:id="@+id/layout_mark_activity"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">
            <TextView
                android:id="@+id/tv_mark_activity"
                style="@style/textView_playBack_bottomTool_base"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:contentDescription="@string/talkback_flag"
                android:drawableTop="@drawable/selector_playback_button_mark"
                android:textColor="@drawable/selector_playback_button_tv_color"
                android:gravity="center"
                android:paddingHorizontal="@dimen/play_btn_control_paddingHorizontal"
                android:paddingVertical="@dimen/dp8"
                android:text="@string/talkback_flag" />
        </FrameLayout>

        <!--图片标记-->
        <TextView
            android:id="@+id/layout_mark_photo_activity"
            style="@style/textView_playBack_bottomTool"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:contentDescription="@string/picture_mark"
            android:drawableTop="@drawable/selector_playback_button_photo_mark"
            android:textColor="@drawable/selector_playback_button_tv_color"
            android:gravity="center"
            android:paddingHorizontal="@dimen/play_btn_control_paddingHorizontal"
            android:paddingVertical="@dimen/dp8"
            android:text="@string/picture_mark" />

        <!--标记列表-->
        <FrameLayout
            android:id="@+id/layout_mark_list_activity"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <com.soundrecorder.common.widget.AnimateColorTextView
                android:id="@+id/tv_mark_list_activity"
                style="@style/textView_playBack_bottomTool_base"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:contentDescription="@string/mark_list"
                android:drawableTop="@drawable/ic_mark_list_selected"
                android:gravity="center"
                android:paddingHorizontal="@dimen/play_btn_control_paddingHorizontal"
                android:paddingVertical="@dimen/dp8"
                android:text="@string/mark_list"
                app:animate_selected="false"
                app:selected_image_color="?attr/couiColorPrimary"
                app:selected_text_color="?attr/couiColorPrimary"
                app:unselected_image_color="@color/icon_mark_list_color"
                app:unselected_text_color="@color/coui_color_secondary_neutral" />
        </FrameLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/convertLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginEnd="@dimen/play_layout_mark_marginEnd_activity"
        android:layout_marginStart="@dimen/play_layout_mark_marginHorizontal"
        android:weightSum="4"
        android:gravity="start"
        app:layout_constraintBottom_toBottomOf="@id/middle_control"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tool_center_guide_line"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintTop_toTopOf="@id/middle_control">

        <!--生成摘要-->
        <FrameLayout
            android:id="@+id/layout_convert_summary_activity"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <com.soundrecorder.playback.view.RepeatAnimationTextView
                android:id="@+id/tv_convert_summary_activity"
                style="@style/textView_playBack_bottomTool_base"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:contentDescription="@string/generate_summary"
                android:gravity="center"
                android:paddingHorizontal="@dimen/play_btn_control_paddingHorizontal"
                android:paddingTop="@dimen/dp8"
                android:visibility="visible"
                app:animViewHeight="@dimen/playback_bottom_tool_image_height"
                app:animViewWidth="@dimen/playback_bottom_tool_image_width"
                app:anim_rawRes_light="@raw/summary_generation_animation_new"
                app:anim_rawRes_night="@raw/summary_generation_animation_new"
                app:default_image_res="@drawable/ic_summary_icon"
                app:bottom_text="@string/generate_summary"
                app:bottom_text_margin_top="@dimen/dp6"
                app:bottom_text_minWidthStyle="false"
                app:bottom_text_paddingBottom="@dimen/dp8"
                tools:visibility="visible"/>
        </FrameLayout>

        <!--讲话人-->
        <FrameLayout
            android:id="@+id/layout_convert_role_activity"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <com.soundrecorder.common.widget.AnimateColorTextView
                android:id="@+id/tv_convert_role_activity"
                style="@style/textView_playBack_bottomTool_base"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:contentDescription="@string/convert_speaker_description"
                android:drawableTop="@drawable/ic_convert_role_selected"
                android:gravity="center"
                android:paddingHorizontal="@dimen/play_btn_control_paddingHorizontal"
                android:paddingVertical="@dimen/dp8"
                android:text="@string/convert_speaker_description"
                android:visibility="visible"
                app:animate_selected="false"
                app:selected_image_color="?attr/couiColorPrimary"
                app:selected_text_color="?attr/couiColorPrimary"
                app:unselected_image_color="@color/icon_black"
                app:unselected_text_color="@color/coui_color_secondary_neutral" />
        </FrameLayout>

        <!--內容搜索-->
        <FrameLayout
            android:id="@+id/layout_convert_search_activity"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tv_convert_search_activity"
                style="@style/textView_playBack_bottomTool_base"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableTop="@drawable/ic_convert_search"
                android:gravity="center"
                android:paddingHorizontal="@dimen/play_btn_control_paddingHorizontal"
                android:paddingVertical="@dimen/dp8"
                android:text="@string/content_search"
                tools:visibility="visible" />
        </FrameLayout>

        <!--分享文本-->
        <TextView
            android:id="@+id/layout_convert_export_activity"
            style="@style/textView_playBack_bottomTool"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableTop="@drawable/ic_convert_share"
            android:gravity="center_horizontal"
            android:paddingHorizontal="@dimen/play_btn_control_paddingHorizontal"
            android:paddingVertical="@dimen/dp8"
            android:text="@string/share_convert"
            android:visibility="visible" />
    </LinearLayout>

</merge>