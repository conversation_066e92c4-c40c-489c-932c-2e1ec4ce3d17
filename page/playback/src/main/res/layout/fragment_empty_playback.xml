<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/sv_empty_playback"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:scrollbars="none">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_root_empty_play"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/common_background_color">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_empty_playback"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.45" />

        <LinearLayout
            android:id="@+id/ll_empty_playback"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@id/guide_empty_playback"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/guide_empty_playback">

            <com.soundrecorder.common.widget.OSImageView
                android:id="@+id/iv_empty_playback"
                android:layout_width="@dimen/os_image_def_width"
                android:layout_height="@dimen/os_image_def_height"
                app:img_draw="@drawable/ic_empty_playback" />

            <TextView
                android:id="@+id/tv_empty_playback"
                style="@style/Widget.COUI.COUINoContentStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/detail_no_record_selected"
                android:textColor="@color/coui_color_label_secondary"
                android:textSize="@dimen/sp16"
                android:visibility="visible" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>