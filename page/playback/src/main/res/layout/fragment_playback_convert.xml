<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/fragmentConvertRootView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/convert_view_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@id/convert_flow_layout"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <com.soundrecorder.playback.view.RepeatAnimationTextView
            android:id="@+id/layout_convert_summary"
            style="@style/textView_playBack_bottomTool"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@string/generate_summary"
            android:gravity="center"
            android:paddingTop="@dimen/play_btn_marginBottom"
            android:visibility="gone"
            app:animViewHeight="@dimen/playback_bottom_tool_image_height"
            app:animViewWidth="@dimen/playback_bottom_tool_image_width"
            app:anim_rawRes_light="@raw/summary_generation_animation_new"
            app:anim_rawRes_night="@raw/summary_generation_animation_new"
            app:default_image_res="@drawable/ic_summary_icon"
            app:bottom_text="@string/generate_summary"
            app:bottom_text_margin_top="@dimen/dp6"
            app:bottom_text_minWidthStyle="false"
            app:bottom_text_paddingBottom="@dimen/play_btn_marginBottom"
            app:layout_constraintStart_toStartOf="parent"
            app:bottom_text_paddingHorizontal="@dimen/play_btn_control_paddingHorizontal"
            tools:visibility="visible"/>

        <com.soundrecorder.common.widget.AnimateColorTextView
            android:id="@+id/layout_convert_role"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/textView_playBack_bottomTool_base"
            android:contentDescription="@string/convert_speaker_description"
            android:visibility="gone"
            android:drawableTop="@drawable/ic_convert_role_selected"
            android:gravity="center"
            android:maxWidth="@dimen/botton_max_width"
            android:paddingHorizontal="@dimen/play_btn_control_paddingHorizontal"
            android:paddingVertical="@dimen/play_btn_marginBottom"
            android:text="@string/convert_speaker_description"
            app:animate_selected="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/layout_convert_summary"
            tools:visibility="visible"
            app:selected_text_color="?attr/couiColorPrimary"
            app:unselected_text_color="@color/coui_color_secondary_neutral" />

        <TextView
            android:id="@+id/layout_convert_search"
            style="@style/textView_playBack_bottomTool"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableTop="@drawable/ic_convert_search"
            android:gravity="center"
            android:minWidth="@dimen/botton_min_width"
            android:maxWidth="@dimen/botton_max_width"
            android:paddingHorizontal="@dimen/play_btn_control_paddingHorizontal"
            android:paddingVertical="@dimen/play_btn_marginBottom"
            android:text="@string/content_search"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/layout_convert_export"
            app:layout_constraintStart_toEndOf="@+id/layout_convert_role"
            tools:visibility="visible" />


        <TextView
            android:id="@+id/layout_convert_export"
            style="@style/textView_playBack_bottomTool"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableTop="@drawable/ic_convert_share"
            android:visibility="gone"
            android:gravity="center_horizontal"
            android:minWidth="@dimen/botton_min_width"
            android:maxWidth="@dimen/botton_max_width"
            android:paddingHorizontal="@dimen/play_btn_control_paddingHorizontal"
            android:paddingVertical="@dimen/play_btn_marginBottom"
            android:text="@string/share_convert"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:visibility="visible"
            />

        <!--spread_inside 分散对齐，两头贴边-->
        <androidx.constraintlayout.helper.widget.Flow
            android:id="@+id/convert_flow_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="layout_convert_summary,layout_convert_role,layout_convert_search,layout_convert_export"
            app:flow_firstHorizontalStyle="spread_inside"
            app:flow_wrapMode="chain"
            android:layout_marginHorizontal="@dimen/play_layout_mark_marginHorizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>