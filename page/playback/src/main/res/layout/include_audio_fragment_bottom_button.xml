<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <View
        android:id="@+id/space_bottom_view_top"
        android:layout_width="1px"
        android:layout_height="@dimen/play_fragment_bottom_space"
        app:layout_constraintBottom_toTopOf="@id/layout_mark_list" />

    <View
        android:id="@+id/view_line_mark_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/toolbar_divider_height"
        android:background="?attr/couiColorDivider"
        android:forceDarkAllowed="false"
        android:visibility="invisible"
        app:layout_constraintTop_toTopOf="@id/space_bottom_view_top" />

    <FrameLayout
        android:id="@+id/layout_summary"
        android:minWidth="@dimen/botton_min_width"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:forceDarkAllowed="false"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/layout_transfer_text"
        app:layout_constraintStart_toStartOf="parent">

        <com.soundrecorder.playback.view.RepeatAnimationTextView
            android:id="@+id/tv_summary"
            android:layout_gravity="start"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@string/generate_summary"
            android:textColor="@drawable/selector_playback_button_tv_color"
            android:gravity="center"
            android:paddingTop="@dimen/play_btn_marginBottom"
            app:animViewHeight="@dimen/playback_bottom_tool_image_height"
            app:animViewWidth="@dimen/playback_bottom_tool_image_width"
            app:anim_rawRes_light="@raw/summary_generation_animation_new"
            app:default_image_res="@drawable/ic_summary_icon"
            app:bottom_text="@string/generate_summary"
            app:bottom_text_margin_top="@dimen/dp6"
            app:bottom_text_minWidthStyle="false"
            app:bottom_text_paddingBottom="@dimen/play_btn_marginBottom"
            app:bottom_text_paddingHorizontal="@dimen/play_btn_control_paddingHorizontal"/>

    </FrameLayout>
    <FrameLayout
        android:id="@+id/layout_transfer_text"
        android:minWidth="@dimen/botton_min_width"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:forceDarkAllowed="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/layout_mark_photo"
        app:layout_constraintStart_toStartOf="@id/layout_summary">
        <com.soundrecorder.playback.newconvert.view.TransferAnimationTextView
            android:id="@+id/view_transfer_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:contentDescription="@string/transfer_text"
            android:paddingTop="@dimen/play_btn_marginBottom"
            app:animFrameRate="60"
            app:animRepeatRateStart="19"
            app:animRepeatRateEnd="103"
            app:animViewHeight="@dimen/playback_bottom_tool_image_height"
            app:animViewWidth="@dimen/playback_bottom_tool_image_width"
            app:anim_rawRes_light="@raw/ic_transferring_light"
            app:anim_rawRes_night="@raw/ic_transferring_night"
            app:default_image_res="@drawable/ic_convert_t"
            app:bottom_text="@string/transfer_text"
            app:bottom_text_margin_top="@dimen/dp6"
            app:bottom_text_paddingBottom="@dimen/play_btn_marginBottom"
            app:bottom_text_paddingHorizontal="@dimen/play_btn_control_paddingHorizontal" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/layout_mark"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:forceDarkAllowed="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/layout_mark_photo"
        app:layout_constraintStart_toEndOf="@id/layout_transfer_text"
        android:gravity="center"
        android:minWidth="@dimen/botton_min_width">

        <TextView
            android:id="@+id/tv_mark"
            style="@style/textView_playBack_bottomTool_base"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:contentDescription="@string/talkback_flag"
            android:drawableTop="@drawable/selector_playback_button_mark"
            android:drawablePadding="@dimen/dp6"
            android:textColor="@drawable/selector_playback_button_tv_color"
            android:minWidth="0dp"
            android:paddingHorizontal="@dimen/play_btn_control_paddingHorizontal"
            android:paddingVertical="@dimen/play_btn_marginBottom"
            android:text="@string/talkback_flag" />
    </FrameLayout>

    <TextView
        android:id="@+id/layout_mark_photo"
        style="@style/textView_playBack_bottomTool"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:contentDescription="@string/picture_mark"
        android:drawableTop="@drawable/selector_playback_button_photo_mark"
        android:drawablePadding="@dimen/dp6"
        android:paddingHorizontal="@dimen/play_btn_control_paddingHorizontal"
        android:paddingVertical="@dimen/play_btn_marginBottom"
        android:text="@string/picture_mark"
        android:textColor="@drawable/selector_playback_button_tv_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/layout_mark_list"
        app:layout_constraintStart_toEndOf="@id/layout_mark" />

    <FrameLayout
        android:id="@+id/layout_mark_list"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:forceDarkAllowed="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:minWidth="@dimen/botton_min_width">

        <com.soundrecorder.common.widget.AnimateColorTextView
            android:id="@+id/tv_mark_list"
            android:layout_gravity="end"
            style="@style/textView_playBack_bottomTool_base"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@string/mark_list"
            android:drawableTop="@drawable/ic_mark_list_selected"
            android:paddingHorizontal="@dimen/play_btn_control_paddingHorizontal"
            android:paddingVertical="@dimen/play_btn_marginBottom"
            android:text="@string/mark_list"
            app:animate_selected="false"
            app:selected_image_color="?attr/couiColorPrimary"
            app:selected_text_color="?attr/couiColorPrimary"
            app:unselected_image_color="@color/icon_mark_list_color"
            app:unselected_text_color="@color/coui_color_secondary_neutral" />
    </FrameLayout>

    <androidx.constraintlayout.helper.widget.Flow
        android:id="@+id/flow_mark_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/play_layout_mark_marginHorizontal"
        app:constraint_referenced_ids="layout_summary,layout_transfer_text,layout_mark,layout_mark_photo,layout_mark_list"
        app:flow_firstHorizontalStyle="spread_inside"
        app:flow_wrapMode="chain"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</merge>