<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.soundrecorder.playback.view.SegmentSeekBar
        android:id="@+id/seek_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:focusable="true"
        android:visibility="gone"
        android:layoutDirection="locale"
        app:couiSeekBarMaxWidth="@dimen/max_seekbar_width"
        app:couiSeekBarProgressColor="@color/seek_bar_progress"
        app:couiSeekBarThumbColor="@color/seek_bar_thumb"
        app:couiSeekBarBackgroundHeight="@dimen/dp4"
        app:couiSeekBarProgressHeight="@dimen/dp4"
        app:couiSeekBarBackGroundEnlargeScale="6" />

    <com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout
        android:id="@+id/wave_gradient_view_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp36"
        android:visibility="gone"
        app:backgroundWhole="@color/wave_recycler_background">

        <com.soundrecorder.playback.audio.PlayWaveRecyclerView
            android:id="@+id/wave_recyclerview_bottom"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:importantForAccessibility="no"
            android:layoutDirection="ltr"/>
    </com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout>
</FrameLayout>