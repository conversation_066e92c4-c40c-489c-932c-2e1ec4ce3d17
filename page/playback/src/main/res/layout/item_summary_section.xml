<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingVertical="@dimen/dp12"
    android:gravity="top">

    <TextView
        android:id="@+id/text_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp16"
        android:textColor="@color/coui_color_label_primary"
        android:textStyle="bold"
        android:layout_marginEnd="@dimen/dp8"
        android:minWidth="@dimen/dp24" />

    <TextView
        android:id="@+id/text_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textSize="@dimen/sp16"
        android:textColor="@color/coui_color_label_primary"
        android:textStyle="bold"
        android:lineSpacingExtra="@dimen/dp2" />

</LinearLayout>
