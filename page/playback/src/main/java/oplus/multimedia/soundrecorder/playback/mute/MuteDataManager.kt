/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/12/3
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package oplus.multimedia.soundrecorder.playback.mute

import androidx.lifecycle.*
import com.soundrecorder.playback.R
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.db.MediaDBUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import oplus.multimedia.soundrecorder.playback.mute.detector.IMuteDataDetector
import oplus.multimedia.soundrecorder.playback.mute.detector.MuteDataDetectorFactory

class MuteDataManager(private val coroutineScope: CoroutineScope, private val taskId: Int) {

    companion object {
        private const val TAG = "MuteDataManager"
    }

    var mediaId: Long = -1
    private var lastModify: Long = -1
    private var fullPath: String? = null

    private var muteDataDetector: IMuteDataDetector? = null
    private var muteData: List<MuteItem>? = null

    /**
     * 两段相邻的静音片段，用来判断某时间内最近的静音片段是否有效
     */
    private var muteInfo: MuteInfo? = null

    var muteEnable = MutableLiveData(false)
    /**
     * 为了防止播放时拖动到静音片段后，又因为跳过静音直接跳转回去的死循环
     * 此时要求播放器接着播放一段时间，直到nextMutePosition再检测
     */
    var nextMutePosition: Long = -1
    var loadMuteJob: Job? = null

    init {
        MuteDataState.addLoadState(taskId)
    }

    fun setBasicInfo(fullPath: String?, lastModify: Long) {
        if (!fullPath.isNullOrEmpty() && fullPath != this.fullPath) {
            this.fullPath = fullPath
        }

        if (lastModify != -1L && lastModify != this.lastModify) {
            this.lastModify = lastModify
        }
    }

    fun loadMuteData(mediaId: Long) {
        loadMuteJob = coroutineScope.launch(Dispatchers.IO) {
            loadMuteDataInner(mediaId)
        }
    }

    private suspend fun loadMuteDataInner(mediaId: Long) {
        this.mediaId = mediaId

        if (MuteDataState.needLoadData(taskId)) {
            DebugUtil.d(TAG, "loadMuteData(taskId=$taskId) is loading or complete")
            return
        }

        MuteDataState.setLoadDataState(taskId, MuteDataState.MUTE_LOAD_STATE_PREPARING)
        //根据mediaId获取基本数据
        if (!checkBasicInfoValid()) {
            DebugUtil.d(TAG, "checkBasicInfoInvalid basic info is invalid")
            MuteDataState.setLoadDataState(taskId, MuteDataState.MUTE_LOAD_STATE_INIT)
            return
        }

        if (loadMuteDataFromFile()) {
            DebugUtil.d(TAG, "loadMuteDataFromFile success")
            return
        }

        if (MuteDataState.canLoadDataFromOrigin()) {
            loadMuteDataFromOrigin()
        }
    }

    /**
     * 根据mediaId获取基本信息并过滤不合法参数
     */
    private fun checkBasicInfoValid(): Boolean {
        if (mediaId == -1L) {
            DebugUtil.d(TAG, "loadMuteData mediaId is invalid")
            return false
        }

        if (lastModify != -1L && !fullPath.isNullOrEmpty()) {
            DebugUtil.d(TAG, "loadMuteData basicInfo has loaded and valid")
            return true
        }

        MediaDBUtils.queryRecordById(mediaId)?.let {
            lastModify = it.dateModied
            fullPath = it.data
            DebugUtil.d(TAG, "checkBasicInfoInvalid: lastModify=$lastModify, fullPath=$fullPath")
        }

        return lastModify != -1L && !fullPath.isNullOrEmpty()
    }

    /**
     * 从文件缓存中获取静音数据
     */
    private fun loadMuteDataFromFile(): Boolean {
        MuteDataState.setLoadDataState(taskId, MuteDataState.MUTE_LOAD_STATE_LOADING_FROM_CACHE)
        val data = MuteCacheManager.load(mediaId, lastModify)

        if (data == null) {
            MuteDataState.setLoadDataState(taskId, MuteDataState.MUTE_LOAD_STATE_INIT)
            return false
        }

        muteData = data
        MuteDataState.setLoadDataState(taskId, MuteDataState.MUTE_LOAD_STATE_COMPLETED)
        MuteCacheManager.clearDirtyData(BaseApplication.getAppContext())
        return true
    }

    /**
     * 从原始文件中获取静音数据
     */
    private suspend fun loadMuteDataFromOrigin() {
        MuteDataState.setLoadDataState(taskId, MuteDataState.MUTE_LOAD_STATE_LOADING_FROM_ORIGIN)
        if (muteDataDetector == null) {
            muteDataDetector = MuteDataDetectorFactory.createDetector()
        }
        val data = muteDataDetector!!.load(BaseApplication.getAppContext(), mediaId,
            object : IExtractFormatCallback {
                override fun onError(errorCode: Int) {
                    DebugUtil.e(TAG, "loadMuteDataFromOrigin error is $errorCode")
                }
            })
        if (data == null) {
            MuteDataState.setLoadDataState(taskId, MuteDataState.MUTE_LOAD_STATE_INIT)
            return
        }

        muteData = data
        MuteDataState.setLoadDataState(taskId, MuteDataState.MUTE_LOAD_STATE_COMPLETED)
        MuteCacheManager.save(fullPath!!, mediaId, lastModify, data)
        MuteCacheManager.clearDirtyData(BaseApplication.getAppContext())

        releaseMuteDetector()
    }

    private fun releaseMuteDetector() {
        if (MuteDataState.needCancel(taskId)) {
            muteDataDetector?.cancel()
            muteData = null
        }

        muteDataDetector?.release()
        muteDataDetector = null
    }

    fun release() {
        loadMuteJob?.cancel()
        loadMuteJob = null
        releaseMuteDetector()
        MuteDataState.removeLoadState(taskId)
    }

    /**
     * 打开静音按钮
     */
    fun enableMuteButton() {
        val pageState = MuteDataState.getPageMuteState(taskId)
        DebugUtil.d(TAG, "enableMuteButton, state is $pageState")
        when (pageState) {
            MuteDataState.MUTE_LOAD_STATE_PREPARING,
            MuteDataState.MUTE_LOAD_STATE_LOADING_FROM_CACHE,
            MuteDataState.MUTE_LOAD_STATE_LOADING_FROM_ORIGIN -> {
                ToastManager.showShortToast(
                    BaseApplication.getAppContext(),
                    com.soundrecorder.common.R.string.parsing_audio_toast
                )
            }
            MuteDataState.MUTE_LOAD_STATE_COMPLETED -> {
                muteEnable.value = true
            }
            MuteDataState.MUTE_LOAD_STATE_INIT -> {
                ToastManager.showShortToast(
                    BaseApplication.getAppContext(),
                    com.soundrecorder.common.R.string.parsing_audio_toast
                )
                loadMuteData(mediaId)
            }
        }
    }

    fun getLoadingState(): MutableLiveData<Int>? {
        return MuteDataState.getLoadingState(taskId)
    }

    /**
     * 关闭静音按钮
     */
    fun disableMuteEnable() {
        muteEnable.value = false
    }

    fun getSeekTimeAfterJumpMute(currentTimeMillis: Long): Long {
        if (muteData.isNullOrEmpty()) {
            return -1
        }

        if (muteInfo == null) {
            muteInfo = MuteTimeUtil.getMuteData(muteData!!, currentTimeMillis)
        } else {
            if (!MuteTimeUtil.isMuteInfoValid(currentTimeMillis, muteInfo!!, muteData!!)) {
                muteInfo = MuteTimeUtil.getMuteData(muteData!!, currentTimeMillis)
            }
        }

        DebugUtil.d(TAG, "getSeekTimeAfterJumpMute: curTime is $currentTimeMillis, MuteInfo is (${muteInfo?.lastItem},${muteInfo?.curItem})")
        if (muteInfo == null) {
            return -1
        }

        return MuteTimeUtil.getSeekTime(currentTimeMillis, muteInfo!!)
    }
}