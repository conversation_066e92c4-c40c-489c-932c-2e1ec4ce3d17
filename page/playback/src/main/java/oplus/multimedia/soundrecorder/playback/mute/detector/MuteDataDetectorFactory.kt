package oplus.multimedia.soundrecorder.playback.mute.detector

import android.content.Context
import oplus.multimedia.soundrecorder.playback.mute.IExtractFormatCallback
import oplus.multimedia.soundrecorder.playback.mute.MuteItem
import oplus.multimedia.soundrecorder.playback.mute.detector.media.MuteDataDetectorMedia

/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/12/3
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
object MuteDataDetectorFactory {

    const val MUTE_DETECTOR_TYPE_MEDIA = 0

    fun createDetector(): IMuteDataDetector {
        return MuteDataDetectorMedia()
    }
}

interface IMuteDataDetector {
    suspend fun load(context: Context, mediaId: Long, callback: IExtractFormatCallback?): List<MuteItem>?
    fun cancel()
    fun release()
}