/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        : PlaybackSummaryFragment
 * * Description : AI摘要Fragment
 * * Version     : 2.0
 * * Date        : 2025/06/03
 * * Author      : Assistant
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 * *  Assistant                2025/06/03    2.0      重构架构分层
 ***********************************************************************/
package com.soundrecorder.playback.audio

import android.app.Activity
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.BrowseFileAction
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.modulerouter.aisummary.AISummaryAction
import com.soundrecorder.modulerouter.aisummary.IAISummaryManager
import com.soundrecorder.playback.PlaybackActivityViewModel
import com.soundrecorder.playback.R
import com.soundrecorder.playback.aisummary.AISummaryDataManager
import com.soundrecorder.playback.aisummary.AISummaryManagerImpl
import com.soundrecorder.playback.aisummary.AISummaryViewModel
import com.soundrecorder.playback.aisummary.DocumentStyleSummaryAdapter
import com.soundrecorder.playback.aisummary.DocumentSummaryItem
import com.soundrecorder.playback.databinding.FragmentPlaybackSummaryBinding
import com.soundrecorder.summary.RecordSummaryManager.SUMMARY_STATE_CLIENT_END
import com.soundrecorder.summary.RecordSummaryManager.SUMMARY_STATE_CLIENT_GENERATING
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * AI摘要Fragment
 * 专注于UI展示和用户交互，业务逻辑委托给ViewModel处理
 * 支持多版本摘要切换显示功能
 */
class PlaybackSummaryFragment : Fragment(), IAISummaryManager {

    companion object {
        private const val TAG = "PlaybackSummaryFragment"
    }

    private var _binding: FragmentPlaybackSummaryBinding? = null
    private val binding get() = _binding!!

    private lateinit var mViewModel: PlaybackActivityViewModel
    private val summaryViewModel: AISummaryViewModel by viewModels()
    private var documentAdapter: DocumentStyleSummaryAdapter? = null
    private var isShowingOriginalText = false

    // UI状态管理
    private var currentMediaId: Long = -1
    private var isPageSelected = false
    private var currentSummaryContent: String? = null
    private var delayedCheckRunnable: Runnable? = null
    private val handler = Handler(Looper.getMainLooper())

    private var mAISummaryManagerImpl: AISummaryManagerImpl? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPlaybackSummaryBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        mViewModel = ViewModelProvider(requireActivity())[PlaybackActivityViewModel::class.java]

        initViews()
        initObservers()
        initAISummaryManagerImpl()
    }

    private fun initViews() {
        // 初始化RecyclerView
        documentAdapter = DocumentStyleSummaryAdapter()
        binding.recyclerSummary.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = documentAdapter
        }

        // 原文/摘要切换标签点击事件
        binding.tabOriginal.setOnClickListener {
            switchToOriginalText()
        }

        binding.tabSummary.setOnClickListener {
            switchToSummaryText()
        }

        // 生成摘要按钮点击事件
        binding.btnGenerateSummary.setOnClickListener {
            summaryViewModel.generateSummaryWithManager(activity, mAISummaryManagerImpl)
        }

        // 重新生成按钮点击事件
        binding.btnRegenerateSummary.setOnClickListener {
            summaryViewModel.regenerateSummary()
        }

        // 版本切换按钮点击事件
        binding.btnPreviousSummary.setOnClickListener {
            summaryViewModel.switchToPreviousSummary()
        }

        binding.btnNextSummary.setOnClickListener {
            summaryViewModel.switchToNextSummary()
        }

        binding.btnLatestSummary.setOnClickListener {
            summaryViewModel.switchToLatestSummary()
        }
    }

    /**
     * 切换到原文显示
     */
    private fun switchToOriginalText() {
        if (isShowingOriginalText) return

        isShowingOriginalText = true
        updateTabSelection()

        // TODO: 显示原文内容
        DebugUtil.d(TAG, "switchToOriginalText: showing original text")
    }

    /**
     * 切换到摘要显示
     */
    private fun switchToSummaryText() {
        if (!isShowingOriginalText) return

        isShowingOriginalText = false
        updateTabSelection()

        // 显示摘要内容
        DebugUtil.d(TAG, "switchToSummaryText: showing summary")
        displayCurrentSummary()
    }

    /**
     * 更新标签选中状态
     */
    private fun updateTabSelection() {
        safeUpdateUI {
            if (isShowingOriginalText) {
                binding.tabOriginal.setBackgroundResource(R.drawable.bg_tab_left_selected)
                binding.tabOriginal.setTextColor(resources.getColor(R.color.role_color_3, null))
                binding.tabSummary.setBackgroundResource(R.drawable.bg_tab_right_normal)
                binding.tabSummary.setTextColor(resources.getColor(R.color.role_color_1, null))
            } else {
                binding.tabOriginal.setBackgroundResource(R.drawable.bg_tab_left_normal)
                binding.tabOriginal.setTextColor(resources.getColor(R.color.role_color_3, null))
                binding.tabSummary.setBackgroundResource(R.drawable.bg_tab_right_selected)
                binding.tabSummary.setTextColor(resources.getColor(R.color.role_color_1, null))
            }
        }
    }

    /**
     * 显示当前摘要
     */
    private fun displayCurrentSummary() {
        val currentContent = summaryViewModel.summaryContent.value
        if (!currentContent.isNullOrEmpty()) {
            displaySummaryContent(currentContent)
        }
    }

    /**
     * 初始化AISummaryManagerImpl
     */
    private fun initAISummaryManagerImpl() {
        if (mAISummaryManagerImpl == null) {
            mAISummaryManagerImpl = AISummaryManagerImpl()
        }
        mViewModel.let {
            mAISummaryManagerImpl?.registerForAISummary(this, !it.mIsFromOtherApp)
        }
    }

    private fun initObservers() {
        DebugUtil.d(TAG, "initObservers: start")

        // 1. 观察SummaryViewModel的数据变化
        initSummaryViewModelObserver()

        // 2. 观察BrowseFileActivityViewModel的数据变化（主要数据源）
        initBrowseActivityViewModelObserver()

        // 3. 观察PlaybackActivityViewModel的摘要状态变化
        initViewModelObserver()

        // 4. 检查当前是否已经有有效的recordId
        checkCurrentRecordId()

        DebugUtil.d(TAG, "initObservers: completed")
    }

    /**
     * 观察SummaryViewModel的数据变化
     */
    private fun initSummaryViewModelObserver() {
        // 观察摘要状态
        summaryViewModel.summaryState.observe(viewLifecycleOwner) { state ->
            DebugUtil.d(TAG, "SummaryViewModel state changed: $state")
            updateSummaryState(state)
        }

        // 观察摘要项列表
        summaryViewModel.summaryItems.observe(viewLifecycleOwner) { items ->
            DebugUtil.d(TAG, "SummaryViewModel items changed: ${items.size}")
            // 转换为文档风格的摘要项
            val documentItems = items.flatMap { item ->
                DocumentSummaryItem.parseContent(item.content)
            }
            documentAdapter?.submitList(documentItems)
        }

        // 观察错误信息
        summaryViewModel.errorMessage.observe(viewLifecycleOwner) { errorMsg ->
            if (errorMsg.isNotEmpty()) {
                DebugUtil.e(TAG, "SummaryViewModel error: $errorMsg")
                showErrorMessage(errorMsg)
            }
        }

        // 观察摘要内容
        summaryViewModel.summaryContent.observe(viewLifecycleOwner) { content ->
            if (content != null) {
                if (content.isNotEmpty()) {
                    DebugUtil.d(TAG, "SummaryViewModel content updated: ${content.length} chars")
                    updateSummaryInfo(content)
                }
            }
        }

        // 观察摘要历史版本
        summaryViewModel.summaryHistory.observe(viewLifecycleOwner) { history ->
            DebugUtil.d(TAG, "SummaryViewModel history updated: ${history.size} versions")
            updateVersionInfo(history)
        }

        // 观察当前版本索引
        summaryViewModel.currentSummaryIndex.observe(viewLifecycleOwner) { index ->
            DebugUtil.d(TAG, "SummaryViewModel current index updated: $index")
            updateCurrentVersionIndex(index)
        }

        // 观察是否有多个版本
        summaryViewModel.hasMultipleVersions.observe(viewLifecycleOwner) { hasMultiple ->
            DebugUtil.d(TAG, "SummaryViewModel multiple versions: $hasMultiple")
            updateVersionControlVisibility(hasMultiple)
        }
    }

    /**
     * 观察BrowseFileActivityViewModel的数据变化
     */
    private fun initBrowseActivityViewModelObserver() {
        try {
            val browseFileActivityViewModel = BrowseFileAction.getBrowseActivityViewModel(requireActivity() as AppCompatActivity)
            BrowseFileAction.getViewModelPlayData<StartPlayModel>(browseFileActivityViewModel)?.observe(viewLifecycleOwner) { startPlayModel ->
                DebugUtil.d(TAG, "BrowseFileActivityViewModel data changed: $startPlayModel")

                if (startPlayModel == null) {
                    DebugUtil.i(TAG, "play data to be null")
                    return@observe
                }

                if (startPlayModel.mediaId == currentMediaId) {
                    DebugUtil.i(TAG, "play data id not change, id=${startPlayModel.mediaId}")
                    return@observe
                }

                DebugUtil.i(TAG, "play record data changed to $startPlayModel")

                // 音频发生变化，处理recordId变化
                val newRecordId = startPlayModel.mediaId
                if (newRecordId > 0) {
                    handleRecordIdChange(newRecordId)
                }
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "Failed to observe BrowseFileActivityViewModel: ${e.message}")
        }
    }

    /**
     * 观察PlaybackActivityViewModel的相关状态
     */
    private fun initViewModelObserver() {
        // 观察摘要状态变化
        mViewModel.mSummaryStatus?.observe(viewLifecycleOwner) { summaryStatus ->
            DebugUtil.d(TAG, "Summary status changed: $summaryStatus")
            handleSummaryStatusChange(summaryStatus)
        }
    }

    /**
     * 检查当前是否已经有有效的recordId
     */
    private fun checkCurrentRecordId() {
        // 检查startPlayModel
        mViewModel.startPlayModel?.let { startPlayModel ->
            val recordId = startPlayModel.mediaId
            if (recordId > 0 && recordId != currentMediaId) {
                DebugUtil.d(TAG, "checkCurrentRecordId: startPlayModel recordId=$recordId")
                handleRecordIdChange(recordId)
                return
            }
        }

        // 检查直接的recordId
        if (mViewModel.recordId > 0 && mViewModel.recordId != currentMediaId) {
            DebugUtil.d(TAG, "checkCurrentRecordId: direct recordId=${mViewModel.recordId}")
            handleRecordIdChange(mViewModel.recordId)
            return
        }
    }

    override fun onResume() {
        super.onResume()
        DebugUtil.d(TAG, "onResume")

        // 如果页面当前被选中，刷新摘要数据
        if (isPageSelected) {
            DebugUtil.d(TAG, "onResume: page is selected, refreshing data")
            if (currentMediaId > 0) {
                refreshSummaryData()
            } else {
                checkExistingSummary()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()

        // 清理延迟任务
        delayedCheckRunnable?.let {
            handler.removeCallbacks(it)
            delayedCheckRunnable = null
        }

        // 注销回调
        mAISummaryManagerImpl?.unregisterForAISummary()

        _binding = null
    }

    /**
     * 页面选中状态变化回调
     * 由PlaybackContainerFragment调用
     */
    fun onPageSelectedStateChange(isSelected: Boolean) {
        val wasSelected = isPageSelected
        isPageSelected = isSelected

        DebugUtil.d(TAG, "onPageSelectedStateChange: $wasSelected -> $isSelected")

        if (isSelected && !wasSelected) {
            // 页面从未选中变为选中时
            DebugUtil.d(TAG, "Summary tab selected, refreshing data")

            // 如果recordId还没有准备好，先尝试延迟检查
            if (currentMediaId <= 0) {
                DebugUtil.d(TAG, "Summary tab selected but recordId not ready, scheduling check")
                scheduleDelayedRecordIdCheck()
            } else {
                // recordId已准备好，立即刷新数据
                refreshSummaryData()
            }
        } else if (!isSelected && wasSelected) {
            // 页面从选中变为未选中时
            DebugUtil.d(TAG, "Summary tab deselected")
            // 可以在这里进行一些清理工作，但不注销回调，因为可能还会回到这个页面
        }
    }

    /**
     * 刷新摘要数据 - Tab切换时调用
     */
    private fun refreshSummaryData() {
        DebugUtil.d(TAG, "refreshSummaryData: currentMediaId=$currentMediaId")

        if (currentMediaId <= 0) {
            DebugUtil.w(TAG, "refreshSummaryData: invalid recordId")
            return
        }

        // 1. 首先检查ViewModel中是否有当前数据
        val currentContent = summaryViewModel.summaryContent.value
        val currentState = summaryViewModel.summaryState.value

        DebugUtil.d(TAG, "refreshSummaryData: currentState=$currentState, hasContent=${!currentContent.isNullOrEmpty()}")

        // 2. 如果ViewModel中有完整的摘要数据，直接显示
        if (currentState == AISummaryViewModel.SummaryState.COMPLETED && !currentContent.isNullOrEmpty()) {
            DebugUtil.d(TAG, "refreshSummaryData: displaying existing ViewModel data")
            displaySummaryContent(currentContent)
            return
        }

        // 3. 如果ViewModel中没有数据或状态不正确，强制重新检查
        DebugUtil.d(TAG, "refreshSummaryData: force refreshing summary data")
        summaryViewModel.forceRefreshSummary()

        // 4. 如果有缓存的摘要内容，作为备用显示
        currentSummaryContent?.let { content ->
            if (content.isNotEmpty() && currentState != AISummaryViewModel.SummaryState.COMPLETED) {
                DebugUtil.d(TAG, "refreshSummaryData: displaying cached content as fallback")
                displaySummaryContent(content)
            }
        }
    }

    /**
     * 处理recordId变化
     */
    private fun handleRecordIdChange(newRecordId: Long) {
        DebugUtil.d(TAG, "handleRecordIdChange: $currentMediaId -> $newRecordId")

        if (newRecordId == currentMediaId) {
            DebugUtil.d(TAG, "handleRecordIdChange: recordId not changed")
            return
        }

        currentMediaId = newRecordId

        // 通知SummaryViewModel recordId变化
        summaryViewModel.setRecordId(newRecordId)

        // 如果页面当前被选中，立即检查摘要
        if (isPageSelected) {
            checkExistingSummary()
        }
    }

    /**
     * 检查现有摘要 - 委托给ViewModel处理
     */
    private fun checkExistingSummary() {
        val recordId = if (currentMediaId > 0) currentMediaId else mViewModel.recordId

        if (recordId <= 0) {
            DebugUtil.w(TAG, "checkExistingSummary: Invalid recordId: $recordId")
            return
        }

        DebugUtil.d(TAG, "checkExistingSummary: delegating to ViewModel for recordId=$recordId")
        summaryViewModel.checkExistingSummary()
    }

    /**
     * 延迟检查recordId
     */
    private fun scheduleDelayedRecordIdCheck() {
        // 清除之前的延迟任务
        delayedCheckRunnable?.let {
            handler.removeCallbacks(it)
        }

        // 创建新的延迟任务
        delayedCheckRunnable = Runnable {
            DebugUtil.d(TAG, "scheduleDelayedRecordIdCheck: executing delayed check")

            // 重新检查recordId
            val recordId = if (currentMediaId > 0) currentMediaId else mViewModel.recordId
            if (recordId > 0) {
                handleRecordIdChange(recordId)
            } else {
                DebugUtil.w(TAG, "scheduleDelayedRecordIdCheck: still no valid recordId")
            }
        }

        // 延迟500ms执行
        handler.postDelayed(delayedCheckRunnable!!, 500)
    }

    /**
     * 处理摘要状态变化
     */
    private fun handleSummaryStatusChange(summaryStatus: Int) {
        when (summaryStatus) {
            SUMMARY_STATE_CLIENT_GENERATING -> {
                DebugUtil.d(TAG, "Summary generating")
                updateSummaryState(AISummaryViewModel.SummaryState.GENERATING)
            }
            SUMMARY_STATE_CLIENT_END -> {
                DebugUtil.d(TAG, "Summary generation ended")
                // 状态结束后，重新检查摘要
                if (isPageSelected && currentMediaId > 0) {
                    checkExistingSummary()
                }
            }
        }
    }

    // ========== UI更新方法 ==========

    /**
     * 更新摘要状态
     */
    private fun updateSummaryState(state: AISummaryViewModel.SummaryState) {
        safeUpdateUI {
            when (state) {
                AISummaryViewModel.SummaryState.EMPTY -> {
                    binding.layoutEmpty.visibility = View.VISIBLE
                    binding.layoutContent.visibility = View.GONE
                    binding.layoutGenerating.visibility = View.GONE
                    binding.layoutError.visibility = View.GONE
                }
                AISummaryViewModel.SummaryState.GENERATING -> {
                    binding.layoutEmpty.visibility = View.GONE
                    binding.layoutContent.visibility = View.GONE
                    binding.layoutGenerating.visibility = View.VISIBLE
                    binding.layoutError.visibility = View.GONE
                }
                AISummaryViewModel.SummaryState.COMPLETED -> {
                    binding.layoutEmpty.visibility = View.GONE
                    binding.layoutContent.visibility = View.VISIBLE
                    binding.layoutGenerating.visibility = View.GONE
                    binding.layoutError.visibility = View.GONE
                }
                AISummaryViewModel.SummaryState.ERROR -> {
                    binding.layoutEmpty.visibility = View.GONE
                    binding.layoutContent.visibility = View.GONE
                    binding.layoutGenerating.visibility = View.GONE
                    binding.layoutError.visibility = View.VISIBLE
                }

                AISummaryViewModel.SummaryState.STOPPED -> TODO()
            }
        }
    }

    /**
     * 显示摘要结果 - 委托给ViewModel处理
     */
    private fun displaySummaryResult(jsonResult: String) {
        DebugUtil.d(TAG, "displaySummaryResult: delegating to ViewModel")
        summaryViewModel.processSummaryResult(jsonResult)
    }

    /**
     * 显示摘要内容 - 仅处理UI显示
     */
    private fun displaySummaryContent(content: String) {
        safeUpdateUI {
            currentSummaryContent = content

            // 解析内容为文档项
            val documentItems = DocumentSummaryItem.parseContent(content)
            documentAdapter?.submitList(documentItems)

            // 更新时间显示
            binding.textSummaryTime.text = "生成时间：${getCurrentTimeString()}"

            // 更新会议总结内容（如果有的话）
            updateMeetingSummary(content)
        }
    }

    /**
     * 更新会议总结内容
     */
    private fun updateMeetingSummary(content: String) {
        safeUpdateUI {
            // 从内容中提取会议总结部分
            val summarySection = extractMeetingSummary(content)
            if (summarySection.isNotEmpty()) {
                binding.textMeetingSummary.text = summarySection
                binding.textMeetingSummary.visibility = View.VISIBLE
            } else {
                binding.textMeetingSummary.visibility = View.GONE
            }
        }
    }

    /**
     * 从内容中提取会议总结
     */
    private fun extractMeetingSummary(content: String): String {
        // 查找会议总结相关的内容
        val lines = content.split("\n")
        val summaryLines = mutableListOf<String>()
        var inSummarySection = false

        for (line in lines) {
            val trimmedLine = line.trim()
            if (trimmedLine.contains("会议总结") || trimmedLine.contains("总结")) {
                inSummarySection = true
                continue
            }

            if (inSummarySection) {
                if (trimmedLine.isNotEmpty() && !trimmedLine.matches(Regex("^\\d+\\..*"))) {
                    summaryLines.add(trimmedLine)
                } else if (trimmedLine.matches(Regex("^\\d+\\..*"))) {
                    // 遇到新的章节，停止收集
                    break
                }
            }
        }

        return summaryLines.joinToString("")
    }

    /**
     * 显示错误信息 - 专注于UI显示
     */
    private fun showErrorMessage(errorMsg: String) {
        safeUpdateUI {
            binding.textErrorMessage.text = errorMsg
            binding.btnRetry.setOnClickListener {
                summaryViewModel.generateSummaryWithManager(activity, mAISummaryManagerImpl)
            }
        }
    }

    /**
     * 显示错误信息 - 专注于UI显示
     */
    private fun showErrorMessage(errorCode: Int, errorMsg: String?) {
        safeUpdateUI {
            val message = when (errorCode) {
                AISummaryAction.NETWORK_ERROR -> "网络连接失败，请检查网络设置"
                AISummaryAction.CONTENT_LESS_ERROR -> "录音内容较少，无法生成摘要"
                AISummaryAction.SERVER_ERROR -> "服务异常，摘要生成失败"
                AISummaryAction.REQUEST_TIMEOUT -> "请求超时，请重试"
                else -> errorMsg ?: "生成摘要失败，错误码：$errorCode"
            }

            binding.textErrorMessage.text = message
            binding.btnRetry.setOnClickListener {
                summaryViewModel.generateSummaryWithManager(activity, mAISummaryManagerImpl)
            }
        }
    }

    /**
     * 更新摘要信息
     */
    private fun updateSummaryInfo(content: String) {
        safeUpdateUI {
            // 解析文档项并显示
            val documentItems = DocumentSummaryItem.parseContent(content)
            documentAdapter?.submitList(documentItems)

            // 统计信息
            val sectionCount = documentItems.count { it.type == DocumentSummaryItem.Type.SECTION_HEADER }
            val bulletCount = documentItems.count { it.type == DocumentSummaryItem.Type.BULLET_POINT }

            val infoText = "共${sectionCount}个章节，${bulletCount}个要点"
            DebugUtil.d(TAG, "Summary info: $infoText")
        }
    }

    /**
     * 更新版本信息
     */
    private fun updateVersionInfo(history: List<AISummaryDataManager.SummaryEntity>) {
        safeUpdateUI {
            val currentIndex = summaryViewModel.currentSummaryIndex.value ?: 0
            val totalCount = history.size

            // 更新版本信息显示
            binding.textVersionInfo.text = "${currentIndex + 1}/$totalCount"

            // 更新时间显示（显示当前版本的生成时间）
            if (currentIndex < history.size) {
                val currentSummary = history[currentIndex]
                val timeText = "生成时间：${formatTime(currentSummary.createTime)}"
                binding.textSummaryTime.text = timeText
            }

            DebugUtil.d(TAG, "updateVersionInfo: ${currentIndex + 1}/$totalCount")
        }
    }

    /**
     * 更新当前版本索引
     */
    private fun updateCurrentVersionIndex(index: Int) {
        safeUpdateUI {
            val history = summaryViewModel.summaryHistory.value ?: emptyList()
            val totalCount = history.size

            if (totalCount > 0) {
                // 更新版本信息显示
                binding.textVersionInfo.text = "${index + 1}/$totalCount"

                // 更新按钮状态
                binding.btnPreviousSummary.isEnabled = index < totalCount - 1
                binding.btnNextSummary.isEnabled = index > 0
                binding.btnLatestSummary.isEnabled = index > 0

                // 更新时间显示
                if (index < history.size) {
                    val currentSummary = history[index]
                    val timeText = "生成时间：${formatTime(currentSummary.createTime)}"
                    binding.textSummaryTime.text = timeText
                }
            }

            DebugUtil.d(TAG, "updateCurrentVersionIndex: index=$index")
        }
    }

    /**
     * 更新版本控制的可见性
     */
    private fun updateVersionControlVisibility(hasMultiple: Boolean) {
        safeUpdateUI {
            binding.layoutVersionControl.visibility = if (hasMultiple) {
                View.VISIBLE
            } else {
                View.GONE
            }

            DebugUtil.d(TAG, "updateVersionControlVisibility: hasMultiple=$hasMultiple")
        }
    }

    // ========== 辅助方法 ==========

    /**
     * 安全的UI更新
     */
    private fun safeUpdateUI(action: () -> Unit) {
        if (isAdded && !isDetached && view != null) {
            try {
                action()
            } catch (e: Exception) {
                DebugUtil.e(TAG, "safeUpdateUI error: ${e.message}")
            }
        }
    }

    /**
     * 清理摘要数据 - UI操作
     */
    private fun clearSummaryData() {
        safeUpdateUI {
            documentAdapter?.submitList(emptyList())
            currentSummaryContent = null
        }
    }

    /**
     * 获取当前时间字符串
     */
    private fun getCurrentTimeString(): String {
        return formatTime(System.currentTimeMillis())
    }

    /**
     * 格式化时间显示
     */
    private fun formatTime(timestamp: Long): String {
        return java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
            .format(java.util.Date(timestamp))
    }

    // ========== IAISummaryManager接口实现 ==========

    override fun registerForAISummary(viewModelStoreOwner: Fragment, fromMainProcess: Boolean) {
        // 这个方法由AISummaryManagerImpl调用，不需要在Fragment中实现
    }

    override fun startAISummaryClickHandle(
        activity: Activity?,
        selectedMediaIdList: MutableList<Long>?,
        pageFrom: Int?,
        isOpenSwitch: Boolean
    ) {
        // 这个方法由AISummaryManagerImpl调用，不需要在Fragment中实现
    }

    override fun checkPluginsDownload(activity: Activity?, callback: ((Boolean) -> Unit)?, isOpenSwitch: Boolean) {
        // 这个方法由AISummaryManagerImpl调用，不需要在Fragment中实现
    }

    override fun startOrResumeAISummary(
        activity: Activity?,
        selectedMediaIdList: MutableList<Long>?,
        isOpenSwitch: Boolean
    ) {
        // 这个方法由AISummaryManagerImpl调用，不需要在Fragment中实现
    }

    override fun doClickPermissionAISummaryOK(activity: Activity?, pageFrom: Int?, isOpenSwitch: Boolean) {
        DebugUtil.d(TAG, "doClickPermissionAISummaryOK: pageFrom=$pageFrom, isOpenSwitch=$isOpenSwitch")
        // 权限确认后，可以在这里处理UI更新或其他逻辑
    }

    override fun release() {
        DebugUtil.d(TAG, "release")
        // Fragment的资源释放在onDestroyView中处理
    }

    override fun releaseAll() {
        DebugUtil.d(TAG, "releaseAll")
        release()
    }

    /**
     * AI摘要结果回调
     */
    fun onAISummaryResult(recordId: Long, jsonResult: String) {
        DebugUtil.d(TAG, "onAISummaryResult: recordId=$recordId")

        if (recordId != currentMediaId) {
            DebugUtil.w(TAG, "onAISummaryResult: recordId mismatch, expected=$currentMediaId, actual=$recordId")
            return
        }

        lifecycleScope.launch {
            try {
                withContext(Dispatchers.Main) {
                    displaySummaryResult(jsonResult)
                }
            } catch (e: Exception) {
                DebugUtil.e(TAG, "onAISummaryResult error: ${e.message}")
            }
        }
    }

    /**
     * AI摘要错误回调
     */
    fun onAISummaryError(recordId: Long, errorCode: Int, errorMsg: String?) {
        DebugUtil.e(TAG, "onAISummaryError: recordId=$recordId, errorCode=$errorCode, errorMsg=$errorMsg")

        if (recordId != currentMediaId) {
            DebugUtil.w(TAG, "onAISummaryError: recordId mismatch, expected=$currentMediaId, actual=$recordId")
            return
        }

        lifecycleScope.launch {
            try {
                withContext(Dispatchers.Main) {
                    showErrorMessage(errorCode, errorMsg)
                    updateSummaryState(AISummaryViewModel.SummaryState.ERROR)
                }
            } catch (e: Exception) {
                DebugUtil.e(TAG, "onAISummaryError handling error: ${e.message}")
            }
        }
    }
}