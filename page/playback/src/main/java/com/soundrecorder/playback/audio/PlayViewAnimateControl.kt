/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlayViewAnimateControl
 * Description:
 * Version: 1.0
 * Date: 2023/7/17
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/7/17 1.0 create
 */

package com.soundrecorder.playback.audio

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.os.Build
import android.view.Gravity
import android.view.View
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.addListener
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.core.view.marginEnd
import androidx.core.view.marginTop
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.FragmentActivity
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.getFloatValue
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.StatusBarUtil
import com.soundrecorder.common.utils.ViewUtils.dp2px
import com.soundrecorder.common.utils.ViewUtils.updateConstraintHeight
import com.soundrecorder.playback.R
import com.soundrecorder.playback.databinding.FragmentPlaybackAudioBinding
import com.soundrecorder.playback.databinding.IncludeAudioFragmentBottomButtonBinding
import kotlin.math.roundToInt

class PlayViewAnimateControl(fragmentActivity: FragmentActivity?) {
    private val logTag = "PlayViewAnimateControl"
    private val pathInterpolator = COUIMoveEaseInterpolator()
    private val wavePercentShowMark: Float = BaseApplication.getAppContext()
        .getFloatValue(R.dimen.play_wave_view_height_percent_show_mark)
    private val wavePercentDefault: Float = BaseApplication.getAppContext()
        .getFloatValue(R.dimen.play_wave_view_height_percent)

    private var maxTimeLayoutHeight: Int = 0
    private var showMarkAnimation: AnimatorSet? = null
    private var hideMarkAnimation: AnimatorSet? = null
    private var mFragmentActivity = fragmentActivity
    companion object {
        private const val BOTTOM_BTN_SPACE = 5
    }
    init {
        val resource = BaseApplication.getAppContext().resources
        maxTimeLayoutHeight =
            resource.getDimensionPixelSize(com.soundrecorder.common.R.dimen.common_max_time_layout_height)
    }

    /**
     * 获取波形高度
     */
    fun getWaveHeight(showMarkArea: Boolean): Int {
        var screenHeight = ScreenUtil.screenHeight
        mFragmentActivity?.let {
            val systemUiHeight = StatusBarUtil.getNavigationBarHeight(it) + StatusBarUtil.getStatusBarHeight(it)
            screenHeight = ScreenUtil.screenHeight - systemUiHeight
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                it.windowManager?.let { windowManager ->
                    windowManager.currentWindowMetrics.let { metrics ->
                        screenHeight = metrics.bounds.height() - systemUiHeight
                    }
                }
            }
        }
        return (screenHeight * getWavePercent(showMarkArea)).roundToInt()
    }

    /**
     * 获取当前波形区域占屏幕的百分占比
     */
    private fun getWavePercent(showMarkArea: Boolean): Float {
        /*H>840，波形高度在显示标记情况下，不调整;
        * 增加平板&& H》840，避免22001设备显示大小改为小，读取到的H为846，波形高度不发生变化*/
        if (FeatureOption.IS_PAD && BaseApplication.getAppContext().resources.getBoolean(com.soundrecorder.common.R.bool.height_over_840)) {
            return wavePercentDefault
        }
        return if (showMarkArea) {
            wavePercentShowMark
        } else {
            wavePercentDefault
        }
    }

    /**
     * 标记选中状态发生改变
     */
    fun showMarkViewAnim(
        audioBinding: FragmentPlaybackAudioBinding,
        bottomButtonBinding: IncludeAudioFragmentBottomButtonBinding,
        noMarkData: Boolean,
        doAnim: Boolean = true,
        endCallBack: (() -> Unit)? = null
    ) {
        DebugUtil.i(logTag, "showMarkViewAnim: noMarkData=$noMarkData, doAnim=$doAnim")
        cancelAllAnimation()
        if (doAnim) {
            addShowMarkAnimation(
                audioBinding,
                bottomButtonBinding.spaceBottomViewTop,
                !noMarkData,
                mutableListOf()
            ).run {
                if (this.isNotEmpty()) {
                    showMarkAnimation = AnimatorSet()
                    showMarkAnimation?.addListener(onEnd = { endCallBack?.invoke() })
                    showMarkAnimation?.playTogether(this)
                    showMarkAnimation?.start()
                }
            }
        } else {
            doTitleViewToTop(audioBinding, bottomButtonBinding.spaceBottomViewTop, noMarkData)
            endCallBack?.invoke()
        }
    }

    fun hideMarkViewAnim(
        audioBinding: FragmentPlaybackAudioBinding,
        bottomButtonBinding: IncludeAudioFragmentBottomButtonBinding,
        doAnim: Boolean = true,
    ) {
        DebugUtil.i(logTag, "hideMarkViewAnim: doAnim=$doAnim")
        cancelAllAnimation()
        if (doAnim) {
            addHideMarkAnimation(audioBinding, bottomButtonBinding, mutableListOf()).run {
                if (this.isNotEmpty()) {
                    hideMarkAnimation = AnimatorSet()
                    hideMarkAnimation?.playTogether(this)
                    hideMarkAnimation?.start()
                }
            }
        } else {
            doTitleViewToCenter(audioBinding, bottomButtonBinding)
        }
    }

    /**
     * 设置底部按钮最大宽度，让其能在一行显示完
     */
    fun setBottomViewMaxWidth(
        rootView: View?,
        bottomView: IncludeAudioFragmentBottomButtonBinding?,
        hasSummary: Boolean,
        hasSpeechToTextFeature: Boolean
    ) {
        val binding = bottomView ?: return
        val contentView = rootView ?: return
        if (!bottomView.layoutMarkList.isVisible) {
            DebugUtil.i(logTag, "setBottomViewMaxWidth return by not visible")
            return
        }
        val validWidth =
            contentView.width - 2 * contentView.resources.getDimensionPixelSize(
                R.dimen.play_layout_mark_marginHorizontal
            )
        // 支持转文本和摘要，底部就有5个按钮，都不支持3个按钮，支持一个4个按钮
        val showCount = if (hasSpeechToTextFeature && hasSummary) {
            NumberConstant.NUM_5
        } else if (!hasSpeechToTextFeature && !hasSummary) {
            NumberConstant.NUM_3
        } else {
            NumberConstant.NUM_4
        }
        val minWidth = binding.layoutMarkList.minimumWidth
        val maxWidth = validWidth / showCount - BOTTOM_BTN_SPACE
        DebugUtil.i(logTag, "setBottomViewMaxWidth maxWidth =$maxWidth  ,minWidth =$minWidth,showCount=$showCount")
        if (minWidth > maxWidth) {
            binding.layoutTransferText.minimumWidth = maxWidth
            binding.layoutMark.minimumWidth = maxWidth
            binding.layoutMarkList.minimumWidth = maxWidth
            binding.layoutSummary.minimumWidth = maxWidth
            binding.layoutMarkPhoto.minWidth = maxWidth
        }
        if (maxWidth != binding.tvMarkList.maxWidth) {
            binding.viewTransferText.setMaxWidth(maxWidth)
            binding.tvSummary.setMaxWidth(maxWidth)
            binding.tvMark.maxWidth = maxWidth
            binding.layoutMarkPhoto.maxWidth = maxWidth
            binding.tvMarkList.maxWidth = maxWidth
        }
        when (showCount) {
            NumberConstant.NUM_5 -> {
                (binding.tvMark.layoutParams as? FrameLayout.LayoutParams)?.gravity = Gravity.CENTER
                (binding.viewTransferText.layoutParams as? FrameLayout.LayoutParams)?.gravity =
                    Gravity.CENTER
            }

            NumberConstant.NUM_4 -> {
                (binding.tvMark.layoutParams as? FrameLayout.LayoutParams)?.gravity = Gravity.CENTER
                if (hasSpeechToTextFeature) {
                    (binding.viewTransferText.layoutParams as? FrameLayout.LayoutParams)?.gravity =
                        Gravity.START
                    correctBottomButtonOffset(binding.viewTransferText, binding.tvMark, binding.layoutMarkPhoto.minWidth)
                } else if (hasSummary) {
                    correctBottomButtonOffset(binding.tvSummary, binding.tvMark, binding.layoutMarkPhoto.minWidth)
                }
            }

            NumberConstant.NUM_3 -> (binding.tvMark.layoutParams as? FrameLayout.LayoutParams)?.gravity = Gravity.START
        }
    }

    /**
     * 转文本图标or摘要没有到达最小宽度，往左边偏移了，标记按钮左侧间距看起来比较大；
     * 处理下标记按钮的偏移量
     */
    private fun correctBottomButtonOffset(startChildView: View, correctChildView: View, itemMinWidth: Int) {
        val startTextWidth = startChildView.width
        val markMarginEnd = correctChildView.marginEnd
        if (startTextWidth < itemMinWidth) {
            if (markMarginEnd == 0) {
                correctChildView.updateLayoutParams<FrameLayout.LayoutParams> {
                    marginEnd = (itemMinWidth - startTextWidth) / NumberConstant.NUM_2
                }
            }
        } else if (markMarginEnd != 0) {
            correctChildView.updateLayoutParams<FrameLayout.LayoutParams> {
                marginEnd = 0
            }
        }
    }

    /**
     * 无标记，时间名称居中
     */
    private fun doTitleViewToCenter(
        dataBinding: FragmentPlaybackAudioBinding,
        bottomBinding: IncludeAudioFragmentBottomButtonBinding
    ) {
        val waveHeight = getWaveHeight(false)
        val layoutTimeMarginTop = calTimeLayoutMarginTop(
            dataBinding,
            bottomBinding.spaceBottomViewTop,
            false,
            waveHeight
        )
        dataBinding.layoutTime.updateLayoutParams<ConstraintLayout.LayoutParams> {
            topMargin = layoutTimeMarginTop
        }
        bottomBinding.viewLineMarkBottom.isInvisible = true
        bottomBinding.viewLineMarkBottom.alpha = NumberConstant.NUM_F1_0
        dataBinding.layoutEmpty.isVisible = false
        dataBinding.waveGradientView.updateConstraintHeight(waveHeight)
    }

    /**
     * 有标记，时间名称位于标记上方
     */
    private fun doTitleViewToTop(
        dataBinding: FragmentPlaybackAudioBinding,
        bottomView: View,
        noMarkData: Boolean
    ) {
        val waveHeight = getWaveHeight(true)
        val layoutTimeMarginTop = calTimeLayoutMarginTop(dataBinding, bottomView, true, waveHeight)
        dataBinding.layoutTime.updateLayoutParams<ConstraintLayout.LayoutParams> {
            topMargin = layoutTimeMarginTop
        }
        dataBinding.layoutEmpty.isVisible = noMarkData
        dataBinding.waveGradientView.updateConstraintHeight(waveHeight)
    }

    private fun addShowMarkAnimation(
        dataBinding: FragmentPlaybackAudioBinding,
        bottomView: View,
        hasMarkData: Boolean,
        animList: MutableList<Animator>
    ): MutableList<Animator> {
        val waveDuration = NumberConstant.NUMBER_L400
        // 波形高度变化
        val endWaveHeight = getWaveHeight(true)
        genHeightAnimator(
            view = dataBinding.waveGradientView,
            startValue = dataBinding.waveGradientView.height, endValue = endWaveHeight,
            durationMill = waveDuration
        )?.run {
            animList.add(this)
        }
        val startTimeLayoutMarginTop = dataBinding.layoutTime.marginTop
        val endTimeLayoutMarginTop =
            calTimeLayoutMarginTop(dataBinding, bottomView, true, endWaveHeight)
        // 时间区域的marginTop
        genTopMarginAnimator(
            view = dataBinding.layoutTime,
            startValue = startTimeLayoutMarginTop, endValue = endTimeLayoutMarginTop,
            durationMill = waveDuration
        )?.run {
            animList.add(this)
        }

        getMarkAreaAnimator(dataBinding, showMarkArea = true, hasMarkData = hasMarkData).run {
            animList.addAll(this)
        }
        return animList
    }

    private fun addHideMarkAnimation(
        dataBinding: FragmentPlaybackAudioBinding,
        bottomView: IncludeAudioFragmentBottomButtonBinding,
        animList: MutableList<Animator>
    ): MutableList<Animator> {
        val waveDelay = NumberConstant.NUM_50.toLong()
        val waveDuration = NumberConstant.NUM_500.toLong()
        // 波形高度变化
        val endWaveHeight = getWaveHeight(false)
        if (dataBinding.waveGradientView.measuredHeight != endWaveHeight) {
            genHeightAnimator(
                view = dataBinding.waveGradientView,
                startValue = dataBinding.waveGradientView.height, endValue = endWaveHeight,
                delayMill = waveDelay, durationMill = waveDuration
            )?.run {
                animList.add(this)
            }
        }
        // 时间区域marginTop
        val timeLayoutMarginTop =
            calTimeLayoutMarginTop(dataBinding, bottomView.spaceBottomViewTop, false, endWaveHeight)
        genTopMarginAnimator(
            view = dataBinding.layoutTime,
            startValue = dataBinding.layoutTime.marginTop, endValue = timeLayoutMarginTop,
            delayMill = waveDelay, durationMill = waveDuration
        )?.run {
            animList.add(this)
        }
        getMarkAreaAnimator(dataBinding, showMarkArea = false, hasMarkData = false).run {
            animList.addAll(this)
        }

        if (bottomView.viewLineMarkBottom.isVisible) {
            genAlphaAnimator(
                view = bottomView.viewLineMarkBottom,
                startValue = bottomView.viewLineMarkBottom.alpha,
                endValue = NumberConstant.NUM_F0_0,
                delayMill = waveDelay,
                durationMill = NumberConstant.NUM_180.toLong()
            )?.run {
                addListener(onEnd = {
                    bottomView.viewLineMarkBottom.isInvisible = true
                    // 这里alpha需要设置回来，显示出来没有alpha动效
                    bottomView.viewLineMarkBottom.alpha = NumberConstant.NUM_F1_0
                }, onCancel = {
                    bottomView.viewLineMarkBottom.alpha = NumberConstant.NUM_F1_0
                })
                animList.add(this)
            }
        }
        return animList
    }


    private fun getMarkAreaAnimator(
        dataBinding: FragmentPlaybackAudioBinding,
        showMarkArea: Boolean,
        hasMarkData: Boolean
    ): List<ValueAnimator> {
        val markAnimList = mutableListOf<ValueAnimator>()

        val showTargetView = if (showMarkArea) {
            if (hasMarkData) {
                dataBinding.markListView
            } else {
                dataBinding.layoutEmpty
            }
        } else {
            null
        }

        if (showTargetView != null && (!showTargetView.isVisible || showTargetView.alpha != NumberConstant.NUM_F1_0)) {
            showTargetView.alpha = NumberConstant.NUM_F0_0
            showTargetView.isVisible = true
            // 标记alpha动效
            genAlphaAnimator(
                view = showTargetView,
                startValue = showTargetView.alpha,
                endValue = NumberConstant.NUM_F1_0,
                delayMill = NumberConstant.NUM_200.toLong(),
                durationMill = NumberConstant.NUM_500.toLong()
            )?.run {
                markAnimList.add(this)
            }
        }
        val hideTargetView = if (showMarkArea) {
            if (hasMarkData) {
                dataBinding.layoutEmpty
            } else {
                dataBinding.markListView
            }
        } else if (dataBinding.layoutEmpty.isVisible) {
            dataBinding.layoutEmpty
        } else {
            null
        }
        if (hideTargetView != null && hideTargetView.isVisible) {
            genAlphaAnimator(
                view = hideTargetView,
                startValue = hideTargetView.alpha,
                endValue = NumberConstant.NUM_F0_0,
                delayMill = 0,
                durationMill = NumberConstant.NUM_200.toLong()
            )?.run {
                addListener(onEnd = {
                    hideTargetView.isVisible = false
                })
                markAnimList.add(this)
            }
        }
        return markAnimList
    }

    /**
     * 计算时间区域marginTop
     */
    private fun calTimeLayoutMarginTop(
        dataBinding: FragmentPlaybackAudioBinding,
        bottomView: View,
        showMark: Boolean,
        waveHeight: Int = getWaveHeight(showMark)
    ): Int {
        return if (!showMark) {
            // 无标记
            dp2px(NumberConstant.NUM_F36_0).toInt()
        } else {
            dp2px(NumberConstant.NUM_F24_0).toInt()
        }
    }

    private fun genHeightAnimator(
        view: View,
        startValue: Int,
        endValue: Int,
        delayMill: Long = 0,
        durationMill: Long
    ): ValueAnimator? {
        if (startValue == endValue) {
            return null
        }
        return ValueAnimator.ofInt(startValue, endValue).apply {
            startDelay = delayMill
            duration = durationMill
            interpolator = pathInterpolator
            addUpdateListener {
                view.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    height = it.animatedValue as Int
                }
            }
        }
    }

    private fun genTopMarginAnimator(
        view: View,
        startValue: Int,
        endValue: Int,
        delayMill: Long = 0,
        durationMill: Long
    ): ValueAnimator? {
        if (startValue == endValue) {
            return null
        }
        return ValueAnimator.ofInt(startValue, endValue).apply {
            startDelay = delayMill
            duration = durationMill
            interpolator = pathInterpolator
            addUpdateListener {
                view.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    topMargin = it.animatedValue as Int
                }
            }
        }
    }

    private fun genScaleAnimator(
        view: View,
        startValue: Float,
        endValue: Float,
        delayMill: Long = 0,
        durationMill: Long
    ): ValueAnimator? {
        if (startValue == endValue) {
            return null
        }
        return ValueAnimator.ofFloat(startValue, endValue).apply {
            startDelay = delayMill
            duration = durationMill
            interpolator = pathInterpolator
            addUpdateListener {
                view.scaleX = it.animatedValue as Float
                view.scaleY = it.animatedValue as Float
            }
        }
    }

    private fun genAlphaAnimator(
        view: View,
        startValue: Float,
        endValue: Float,
        delayMill: Long = 0,
        durationMill: Long
    ): ValueAnimator? {
        if (startValue == endValue) {
            return null
        }
        return ValueAnimator.ofFloat(startValue, endValue).apply {
            startDelay = delayMill
            duration = durationMill
            interpolator = pathInterpolator
            addUpdateListener {
                view.alpha = it.animatedValue as Float
            }
        }
    }

    private fun genTransitionYAnimator(
        view: View,
        startValue: Float,
        endValue: Float,
        delayMill: Long = 0,
        durationMill: Long
    ): ValueAnimator? {
        if (startValue == endValue) {
            return null
        }
        return ValueAnimator.ofFloat(startValue, endValue).apply {
            startDelay = delayMill
            duration = durationMill
            interpolator = pathInterpolator
            addUpdateListener {
                view.translationY = it.animatedValue as Float
            }
        }
    }

    fun cancelAllAnimation() {
        showMarkAnimation?.cancel()
        hideMarkAnimation?.cancel()
    }

    fun release() {
        cancelAllAnimation()
        showMarkAnimation = null
        hideMarkAnimation = null
    }
}