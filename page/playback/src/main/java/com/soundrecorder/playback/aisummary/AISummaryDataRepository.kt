/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryDataRepository
 * Description: AI摘要数据仓库，负责数据层操作
 * Version: 1.0
 * Date: 2025/5/14
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/14      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.playback.aisummary

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.convertservice.aisummary.database.SummaryCacheDBHelper
import com.soundrecorder.convertservice.aisummary.database.SummaryCacheEntity
import com.soundrecorder.convertservice.aisummary.database.SummaryCacheRowContent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * AI摘要数据仓库
 * 负责所有与摘要数据相关的数据库操作
 * 遵循Repository模式，为上层提供统一的数据访问接口
 */
class AISummaryDataRepository() {

    companion object {
        private const val TAG = "AISummaryDataRepository"
        private const val MAX_SUMMARY_COUNT = 10
        private const val SELECTED_STATE = 0
        private const val UNSELECTED_STATE = -1

        @Volatile
        private var INSTANCE: AISummaryDataRepository? = null

        fun getInstance(): AISummaryDataRepository {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AISummaryDataRepository().also { INSTANCE = it }
            }
        }
    }

    private val summaryCacheDBHelper by lazy {
        SummaryCacheDBHelper(BaseApplication.getAppContext())
    }

    /**
     * 保存摘要到数据库
     * 新生成的摘要自动设置为选中状态，最多保存10条摘要记录
     * @param recordId 录音ID
     * @param summaryContent 摘要内容
     * @param summaryStyle 摘要风格
     * @return 保存是否成功
     */
    suspend fun saveSummary(recordId: Long, summaryContent: String, summaryStyle: Int = -1): Boolean {
        return withContext(Dispatchers.IO) {
            val filePath = getFilePathByRecordId(recordId)
            if (filePath.isNullOrEmpty()) {
                DebugUtil.e(TAG, "saveSummary failed: cannot find filePath for recordId=$recordId")
                return@withContext false
            }

            val currentTime = System.currentTimeMillis()
            val record = getRecordByRecordId(recordId)
            val recordType = record?.recordType ?: 0

            // 清除所有现有摘要的选中状态
            clearAllSelectedStates(filePath)

            // 创建新的摘要记录（设置为选中状态）
            val newRowContent = SummaryCacheRowContent(
                filePath = filePath,
                timeStamp = currentTime,
                chooseState = SELECTED_STATE,
                recordType = recordType,
                summaryStyle = summaryStyle
            )

            // 保存新摘要记录
            val success = addSummaryRecord(newRowContent, summaryContent)
            if (!success) {
                DebugUtil.e(TAG, "saveSummary failed: cannot add new summary for recordId=$recordId")
                return@withContext false
            }

            // 维护最大摘要数量限制
            maintainMaxSummaryCount(filePath)

            DebugUtil.d(TAG, "saveSummary success: recordId=$recordId")
            true
        }
    }

    /**
     * 清除所有摘要的选中状态
     */
    private fun clearAllSelectedStates(filePath: String) {
        val existingEntities = summaryCacheDBHelper.getSummaryCacheByFilePathOrderByTime(filePath)
        existingEntities.forEach { entity ->
            entity.chooseState = UNSELECTED_STATE
        }
        if (existingEntities.isNotEmpty()) {
            summaryCacheDBHelper.updateSummaryCache(existingEntities)
        }
    }

    /**
     * 添加摘要记录并更新内容
     */
    private fun addSummaryRecord(rowContent: SummaryCacheRowContent, summaryContent: String): Boolean {
        return try {
            val addSuccess = summaryCacheDBHelper.addSummaryCache(rowContent)
            if (!addSuccess) return false

            val newEntities = summaryCacheDBHelper.getSummaryCacheByFilePathOrderByTime(rowContent.filePath)
            if (newEntities.isNotEmpty()) {
                val newEntity = newEntities.first()
                newEntity.summaryContent = summaryContent
                return summaryCacheDBHelper.updateSummaryCache(newEntity)
            }
            false
        } catch (e: Exception) {
            DebugUtil.e(TAG, "addSummaryRecord failed: ${e.message}")
            false
        }
    }

    /**
     * 维护最大摘要数量限制
     */
    private fun maintainMaxSummaryCount(filePath: String) {
        val deletedCount = summaryCacheDBHelper.maintainMaxSummaryCount(filePath, MAX_SUMMARY_COUNT)
        if (deletedCount > 0) {
            DebugUtil.d(TAG, "maintainMaxSummaryCount: deleted $deletedCount old summaries")
        }
    }

    /**
     * 获取摘要历史版本列表
     * @param recordId 录音ID
     * @return 摘要历史列表，按时间排序（最新的在前）
     */
    suspend fun getSummaryHistory(recordId: Long): List<SummaryEntity> {
        return withContext(Dispatchers.IO) {
            val filePath = getFilePathByRecordId(recordId) ?: return@withContext emptyList()
            val entities = getSummaryEntitiesByFilePath(filePath)

            val summaries = entities
                .filter { !it.summaryContent.isNullOrEmpty() }
                .map { SummaryEntity.fromSummaryCacheEntity(it, recordId) }

            DebugUtil.d(TAG, "getSummaryHistory: found ${summaries.size} summaries for recordId=$recordId")
            summaries
        }
    }

    /**
     * 获取文件路径对应的摘要实体列表
     */
    private fun getSummaryEntitiesByFilePath(filePath: String): List<SummaryCacheEntity> {
        val entities = summaryCacheDBHelper.getSummaryCacheByFilePathOrderByTime(filePath)
        DebugUtil.d(TAG, "getSummaryEntitiesByFilePath: filePath=$filePath, found ${entities.size} entities")
        entities.forEach { entity ->
            DebugUtil.d(TAG, "  Entity: id=${entity.id}, hasContent=${!entity.summaryContent.isNullOrEmpty()}, chooseState=${entity.chooseState}")
        }
        return entities
    }

    /**
     * 设置摘要的选中状态
     * @param recordId 录音ID
     * @param summaryIndex 摘要索引（0表示最新）
     * @return 设置是否成功
     */
    suspend fun setSelectedSummary(recordId: Long, summaryIndex: Int): Boolean {
        return withContext(Dispatchers.IO) {
            val filePath = getFilePathByRecordId(recordId) ?: return@withContext false
            val entities = getSummaryEntitiesByFilePath(filePath)
            val validEntities = entities.filter { !it.summaryContent.isNullOrEmpty() }

            if (!isValidIndex(summaryIndex, validEntities.size)) {
                DebugUtil.e(TAG, "setSelectedSummary: invalid index $summaryIndex for recordId=$recordId")
                return@withContext false
            }

            // 更新选中状态
            updateSelectionStates(validEntities, summaryIndex)

            // 批量更新数据库
            val success = summaryCacheDBHelper.updateSummaryCache(validEntities)
            DebugUtil.d(TAG, "setSelectedSummary: recordId=$recordId, index=$summaryIndex, success=$success")

            success
        }
    }

    /**
     * 验证索引是否有效
     */
    private fun isValidIndex(index: Int, size: Int): Boolean {
        return index in 0 until size
    }

    /**
     * 更新选中状态
     */
    private fun updateSelectionStates(entities: List<SummaryCacheEntity>, selectedIndex: Int) {
        entities.forEachIndexed { index, entity ->
            entity.chooseState = if (index == selectedIndex) SELECTED_STATE else UNSELECTED_STATE
        }
    }

    /**
     * 通过recordId获取文件路径
     */
    private fun getFilePathByRecordId(recordId: Long): String? {
        return MediaDBUtils.queryRecordById(recordId)?.data
    }

    /**
     * 通过recordId获取录音记录
     */
    private fun getRecordByRecordId(recordId: Long): Record? {
        return MediaDBUtils.queryRecordById(recordId)
    }

    /**
     * 摘要数据实体
     * 封装摘要相关的所有信息
     */
    data class SummaryEntity(
        val id: Long = -1,
        val recordId: Long,
        val filePath: String,
        val summaryContent: String,
        val summaryStyle: Int = -1,
        val recordType: Int = 0,
        val createTime: Long = System.currentTimeMillis(),
        val updateTime: Long = System.currentTimeMillis(),
        val isSelected: Boolean = false
    ) {
        companion object {
            /**
             * 从数据库实体转换为业务实体
             */
            fun fromSummaryCacheEntity(entity: SummaryCacheEntity, recordId: Long): SummaryEntity {
                return SummaryEntity(
                    id = entity.id,
                    recordId = recordId,
                    filePath = entity.filePath,
                    summaryContent = entity.summaryContent ?: "",
                    summaryStyle = entity.summaryStyle,
                    recordType = entity.recordType,
                    createTime = entity.timeStamp,
                    updateTime = entity.timeStamp,
                    isSelected = entity.chooseState == SELECTED_STATE
                )
            }
        }
    }
}
