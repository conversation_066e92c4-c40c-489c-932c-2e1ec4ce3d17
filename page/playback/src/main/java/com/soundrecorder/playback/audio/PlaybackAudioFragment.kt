/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.audio

import android.annotation.SuppressLint
import android.graphics.Rect
import android.os.Bundle
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.currentInMsFormatTimeExclusive
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.splitwindow.WindowLayoutChangeListener
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.px2dp
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.buryingpoint.SummaryStaticUtil
import com.soundrecorder.common.databean.ConvertStatus.Companion.CONVERT_STATUS_INIT
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.ViewUtils.setAnimatePressBackground
import com.soundrecorder.common.utils.ViewUtils.updateConstraintHeight
import com.soundrecorder.common.utils.cancelAnimationExt
import com.soundrecorder.common.utils.gone
import com.soundrecorder.common.utils.invisible
import com.soundrecorder.common.utils.isGone
import com.soundrecorder.common.utils.playAnimationExt
import com.soundrecorder.common.utils.visible
import com.soundrecorder.modulerouter.BrowseFileAction
import com.soundrecorder.playback.PlaybackActivityViewModel
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.MARK_ACTION_ADD
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.MARK_ACTION_DELETE
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.MARK_ACTION_INIT
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.MARK_ACTION_RENAME
import com.soundrecorder.playback.PlaybackContainerFragment
import com.soundrecorder.playback.R
import com.soundrecorder.playback.databinding.FragmentPlaybackAudioBinding
import com.soundrecorder.playback.databinding.IncludeAudioFragmentBottomButtonBinding
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.player.TimerTickCallback
import com.soundrecorder.player.WavePlayerController
import com.soundrecorder.player.status.PlayStatus
import com.soundrecorder.summary.RecordSummaryManager
import com.soundrecorder.summary.util.SummarySupportManager
import com.soundrecorder.wavemark.mark.MarkListAdapter
import java.lang.Long.min

/**
 * 录音播放
 */
class PlaybackAudioFragment : Fragment(),
    View.OnCreateContextMenuListener,
    View.OnClickListener {

    companion object {
        const val TAG = "PlaybackAudioFragment"
        const val READ_MARK_TIMEOUT = 20000
    }

    var mBinding: FragmentPlaybackAudioBinding? = null
    var bottomViewBinding: IncludeAudioFragmentBottomButtonBinding? = null
    private var mMarkListAdapter: MarkListAdapter? = null
    private lateinit var mViewModel: PlaybackActivityViewModel
    private var mBrowseViewModel: ViewModel? = null
    private var isNeedRefresh = MutableLiveData(true)
    private var mPlaybackConvertViewModel: PlaybackConvertViewModel? = null
    private var isSelected = false
    var waveRecyclerviewBottom: PlayWaveRecyclerView? = null

    private val viewAnimateControl: PlayViewAnimateControl by lazy {
        PlayViewAnimateControl(requireActivity())
    }

    private val mPlayStateChangeObserver = Observer<Int> {
        DebugUtil.i(TAG, "mPlayerState changed $it")
        when (it) {
            PlayStatus.PLAYER_STATE_PLAYING,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> {
                if (mViewModel.playerController.mIsTouchSeekbar.value != true) {
                    waveStartMove()
                }
            }
            PlayStatus.PLAYER_STATE_PAUSE,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE -> waveStopMove()
            PlayStatus.PLAYER_STATE_HALTON -> {
                //可能最后smoothScrollBy后70ms的动画才走到最后，状态变为0后立马stopScroll导致没有滚动到最后
                waveStopMoveForEnd()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        DebugUtil.i(TAG, "onCreate $this , savedInstanceState $savedInstanceState")
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        mBinding = FragmentPlaybackAudioBinding.bind(
            inflater.inflate(
                R.layout.fragment_playback_audio,
                container,
                false
            )
        ).apply {
            bottomViewBinding = IncludeAudioFragmentBottomButtonBinding.bind(root)
        }

        mViewModel = ViewModelProvider(requireParentFragment())[PlaybackActivityViewModel::class.java]
        mPlaybackConvertViewModel =
            ViewModelProvider(this.requireParentFragment())[PlaybackConvertViewModel::class.java]
        mBrowseViewModel = BrowseFileAction.getBrowseActivityViewModel(activity as? AppCompatActivity)
        isNeedRefresh = BrowseFileAction.getBrowseFileActivityViewModel(activity as? AppCompatActivity)
        initView(savedInstanceState)
        initViewModelObserver()
        checkDialogShowStatus(savedInstanceState != null)
        return mBinding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        checkShowStartGuideAnim()
        (parentFragment as? PlaybackContainerFragment)?.checkIsRestoreMarkPictureDialog()
    }

    private fun initView(savedInstanceState: Bundle?) {
        if (savedInstanceState == null) {
            //重建不需要刷新波形
            mViewModel.needSyncRulerView = true
        }
        //waveRecyclerviewBottom = (requireParentFragment() as? PlaybackContainerFragment)?.waveRecyclerviewBottom
        mBinding?.waveRecyclerview?.let { handleWaveRecyclerview(it) }
        waveRecyclerviewBottom?.let { handleWaveRecyclerview(it) }
        mBinding?.tvTransfer?.isVisible = !recordFilterIsRecycle()
        mBinding?.tvTransfer?.setOnClickListener(this)
        bottomViewBinding?.layoutTransferText?.setOnClickListener(this)
        bottomViewBinding?.layoutMarkList?.setOnClickListener(this)
        bottomViewBinding?.layoutMark?.setOnClickListener(this)
        bottomViewBinding?.layoutMarkPhoto?.setOnClickListener(this)
        bottomViewBinding?.layoutSummary?.setOnClickListener(this)
        bottomViewBinding?.layoutTransferText?.setAnimatePressBackground()
        bottomViewBinding?.layoutMarkList?.setAnimatePressBackground()
        bottomViewBinding?.layoutMark?.setAnimatePressBackground()
        bottomViewBinding?.layoutMarkPhoto?.setAnimatePressBackground()
        bottomViewBinding?.layoutSummary?.setAnimatePressBackground()
        mBinding?.mConstraintLayout?.addOnLayoutChangeListener(mLayoutChangeListener)
        mBinding?.layoutEmpty?.addOnLayoutChangeListener(mMarkEmptyChangeListener)
    }

    private fun handleWaveRecyclerview(it: PlayWaveRecyclerView) {
        // 波形load成功之前禁止波形左右滚动
        it.setIsCanScrollTimeRuler(mViewModel.loadAmpSuccess())
        it.setAmplitudeList(mViewModel.ampList.value)
        it.setMaxAmplitudeSource(mViewModel.maxAmplitudeSource)

        it.duration = mViewModel.duration
        it.isDirectOn = mViewModel.isDirectOn
        it.directTime = mViewModel.directTime

        it.setDragListener(mViewModel.mDragListener)
        it.invalidate()
        it.addOnScrollListener(object : RecyclerView.OnScrollListener() {

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                when (newState) {
                    RecyclerView.SCROLL_STATE_DRAGGING -> {
                        mViewModel.playerController.stopTimerNow()
                        mViewModel.playerController.scrollFromType =
                            WavePlayerController.SCROLL_FROM_DRAG
                        //手拖动占据控制权后，设置preTimeMillis = -1,恢复后startSmoothScrollBy当次不处理
                        it.setPreTimeMillis(-1)
                    }

                    RecyclerView.SCROLL_STATE_IDLE -> {
                        if (mViewModel.playerController.scrollFromType == WavePlayerController.SCROLL_FROM_DRAG) {
                            val curTime = it.getSlideTime("onScrolledChanged idle")
                            if (curTime >= 0) {
                                mViewModel.needSyncRulerView = false
                                mViewModel.seekTime(curTime)
                                mViewModel.playerController.scrollFromType =
                                    WavePlayerController.SCROLL_FROM_DEFAULT
                                if ((curTime < mViewModel.playerController.getDuration())
                                    && mViewModel.playerController.isWholePlaying()
                                ) {
                                    mViewModel.playerController.startTimerAsync("onScrollStateChanged")
                                }
                            }
                        }
                    }
                }
            }

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                if (mViewModel.playerController.scrollFromType != WavePlayerController.SCROLL_FROM_DRAG) {
                    //onScrolled, is auto scrolling, just return
                    return
                }

                if (dx != 0) {
                    val curTime = it.getSlideTime("onScrolled")
                    if (curTime >= 0) {
                        mViewModel.needSyncRulerView = false
                        mViewModel.setCurrentTime(curTime)
                    }
                }
            }
        })
    }

    private fun setMarkViewEnable(isMarkEnable: Boolean) {
        bottomViewBinding?.tvMark?.isEnabled = isMarkEnable
        bottomViewBinding?.layoutMarkPhoto?.isEnabled = isMarkEnable
    }

    private fun checkShowStartGuideAnim() {
        val animRunningLiveData = BrowseFileAction.getViewModelAnimRunning(mBrowseViewModel)
        if (animRunningLiveData?.value == true) {
            // 动效执行，波形不绘制，等动效完成后，再绘制，避免进入动效卡顿
            mBinding?.waveRecyclerview?.suppressLayout(true)
            waveRecyclerviewBottom?.suppressLayout(true)
            animRunningLiveData.observe(viewLifecycleOwner) {
                if (!it) { // 小屏执行完动效再显示新手引导
                    mBinding?.waveRecyclerview?.suppressLayout(false)
                    waveRecyclerviewBottom?.suppressLayout(false)
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initViewModelObserver() {
        // 每次进入初始页面时，不需要做动效
        var disableAnimFirstTime = true
        mViewModel.isShowMarkList.observe(viewLifecycleOwner) { isShowing ->
            DebugUtil.i(TAG, "isShowing = $isShowing")
            if (isShowing) {
                BuryingPoint.addPlayMarkListButton(RecorderUserAction.VALUE_PLAY_MARK_LIST_SHOWING)
            } else {
                BuryingPoint.addPlayMarkListButton(RecorderUserAction.VALUE_PLAY_MARK_LIST_UNSHOWING)
            }
            bottomViewBinding?.tvMarkList?.setAnimateSelected(isShowing)
            mBinding?.mConstraintLayout?.post {
                setShowOrHideWaveViewAndMarkListView(!disableAnimFirstTime)
            }
            disableAnimFirstTime = false
            dismissMenuPop()
        }

        mViewModel.playName.observe(viewLifecycleOwner) { name ->
            DebugUtil.i(TAG, "the play name changes to $name")
            mBinding?.tvPlayName?.setText(name.title())
        }
        mViewModel.isPrepareAmplitudeAndMark.observe(viewLifecycleOwner) {
            if (it) {
                mBinding?.waveRecyclerview?.setIsCanScrollTimeRuler(true) // 拿到波形数据标记可滚动
                waveRecyclerviewBottom?.setIsCanScrollTimeRuler(true)
                onAmpLoadingFinish()
            }
        }
        mViewModel.mIsDecodeReady.observe(viewLifecycleOwner) { isReady ->
            if (isReady) {
                mBinding?.waveRecyclerview?.setIsCanScrollTimeRuler(true) // 拿到波形数据标记可滚动
                waveRecyclerviewBottom?.setIsCanScrollTimeRuler(true)
                mBinding?.waveRecyclerview?.let { decodeReadyRecyclerView(it) }
                waveRecyclerviewBottom?.let { decodeReadyRecyclerView(it) }
            }
        }
        mViewModel.playerController.currentTimeMillis.observe(viewLifecycleOwner) { currentTime ->
            refreshCurPlayTime(currentTime)
            refreshWaveInfo(currentTime)
        }
        mViewModel.playerController.playerState.observeForever(mPlayStateChangeObserver)
        mViewModel.playerController.isReplay.observe(viewLifecycleOwner) { isReplay ->
            if (isReplay) {
                waveStopMove()
            }
        }

        mViewModel.markEnable.observe(viewLifecycleOwner) {
            setMarkViewEnable(it)
        }
        mViewModel.lastMarkAction.observe(viewLifecycleOwner) {
            DebugUtil.i(TAG, "lastMarkAction changed $it")
            when (it) {
                MARK_ACTION_INIT -> {
                    notifyMarkListByData()
                }
                MARK_ACTION_DELETE -> {
                    notifyMarkListByData()
                    BuryingPoint.addMarkDelete(mViewModel.recordType.value ?: 0)
                }
                MARK_ACTION_RENAME -> {
                    notifyMarkListByData()
                    BuryingPoint.addMarkRename(mViewModel.recordType.value ?: 0)
                }
                MARK_ACTION_ADD -> {
                    try {
                        val index = mViewModel.addMarkIndex
                        mMarkListAdapter?.setShowAnimatorPos(index)
                        if (mViewModel.playerController.playerState.value in arrayOf(
                                PlayStatus.PLAYER_STATE_PAUSE,
                                PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE,
                                PlayStatus.PLAYER_STATE_HALTON
                            )
                        ) {
                            BuryingPoint.addMarkWhenUnplay()
                        }
                        BuryingPoint.addMarkAdd(mViewModel.recordType.value ?: 0)
                    } catch (e: Exception) {
                        DebugUtil.e(TAG, "ADD MARK ERROR", e)
                    }
                }
            }
        }

        mViewModel.playerController.timerTickListener = object : TimerTickCallback {
            override fun onTimerTick(timeTickMillis: Long) {
                if (!mViewModel.needSyncRulerView) {
                    DebugUtil.d(TAG, "refreshTimerTick: newTime = $timeTickMillis, needSyncRulerView = true")
                    val realTime = min(timeTickMillis, mViewModel.playerController.getDuration())
                    mBinding?.waveRecyclerview?.startSmoothScroll(realTime)
                    waveRecyclerviewBottom?.startSmoothScroll(realTime)
                    mViewModel.onTimerRefreshTime(timeTickMillis)
                }
            }
        }

        if (PermissionUtils.hasReadAudioPermission()) {
            mViewModel.readMarkTag()
        }

        mPlaybackConvertViewModel?.mConvertStatus?.observe(viewLifecycleOwner) {
            val canPlayAnim = isSelected && (bottomViewBinding?.viewTransferText?.isVisible ?: false)
            DebugUtil.i(TAG, "convertViewModelChange mConvertStatus changed: $it, isSelected = $isSelected, $canPlayAnim")
            bottomViewBinding?.viewTransferText?.updateTransferState(it, canPlayAnim)
        }
        mViewModel.mSummaryStatus.observe(viewLifecycleOwner) {
            val context = BaseApplication.getAppContext()
            when (it) {
                RecordSummaryManager.SUMMARY_STATE_CLIENT_END -> {
                    bottomViewBinding?.tvSummary?.let { view ->
                        view.setBottomText(context.resources.getString(com.soundrecorder.common.R.string.view_summary))
                        view.showDefaultImage()
                    }
                }

                RecordSummaryManager.SUMMARY_STATE_CLIENT_GENERATING -> {
                    bottomViewBinding?.tvSummary?.let { view ->
                        view.setBottomText(context.resources.getString(com.soundrecorder.common.R.string.summary_generating))
                        view.startRunAnim()
                    }
                }

                else -> {
                    bottomViewBinding?.tvSummary?.let { view ->
                        view.setBottomText(context.resources.getString(com.soundrecorder.common.R.string.generate_summary))
                        view.showDefaultImage()
                    }
                }
            }
            mViewModel.addShowSummaryEvent(SummaryStaticUtil.EVENT_FROM_AUDIO,
                SummarySupportManager.supportRecordSummary.value,
                it == RecordSummaryManager.SUMMARY_STATE_CLIENT_END)
        }
    }

    private fun decodeReadyRecyclerView(it: PlayWaveRecyclerView) {
        it.setSoundFile(mViewModel.mSoundFile)
        it.totalTime = mViewModel.playerController.getDuration()
        it.setSelectTime(mViewModel.playerController.getCurrentPosition())

        it.duration = mViewModel.duration
        it.isDirectOn = mViewModel.isDirectOn
        it.directTime = mViewModel.directTime
        DebugUtil.d(
            TAG, "initViewModelObserver isDirectOn:${mViewModel.isDirectOn}, " +
                    "directTime:${mViewModel.directTime}"
        )
        it.notifyDataSetChanged()
    }

    private fun recordFilterIsRecycle(): Boolean {
        return mViewModel.isRecycle
    }

    private fun notifyMarkListByData(commitCallback: Runnable? = null) {
        mViewModel.getMarkList()?.value?.let {
            mMarkListAdapter?.setData(it, commitCallback)
            mBinding?.waveRecyclerview?.setMarkTimeList(it)
            waveRecyclerviewBottom?.setMarkTimeList(it)
        }
    }

    private fun refreshCurPlayTime(seekToTime: Long) {
        mBinding?.tvCurrentTime?.let {
            it.contentDescription = TimeUtils.getContentDescriptionForTimeDuration(seekToTime)
            it.text = seekToTime.currentInMsFormatTimeExclusive(mViewModel.playerController.getDuration())
        }
    }

    private fun refreshWaveInfo(seekToTime: Long) {
        //快进快退或者点击标记的时候，需要更新波形
        if (mViewModel.needSyncRulerView) {
            mBinding?.waveRecyclerview?.let {
                mViewModel.needSyncRulerView = false
                it.setSelectTime(seekToTime)
            }
            waveRecyclerviewBottom?.let {
                mViewModel.needSyncRulerView = false
                it.setSelectTime(seekToTime)
            }
            DebugUtil.d(
                TAG,
                "refreshWaveInfo: seekToTime = $seekToTime, needSyncRulerView = true"
            )
        }
    }

    private fun checkDialogShowStatus(isFromRestore: Boolean) {
        if (!isFromRestore) {
            DebugUtil.i(TAG, "checkDialogShowStatus not from restore, no need to check Dialog Show")
            return
        }
        if (mViewModel.mNeedShowMarkRenameDialog.value == true) {
            DebugUtil.i(TAG, "checkDialogShowStatus mMarkRenameDialogShow")
            mMarkListAdapter?.showRenameMark(
                activity,
                mViewModel.mMarkRenameData,
                mViewModel.mRenameMarkEditText
            )
        }
        if (mViewModel.mNeedShowMarkDeleteDialog) {
            mMarkListAdapter?.showDeleteMark(activity, mViewModel.mMarkDeleteData)
        }
    }

    private fun waveStartMove() {
        mBinding?.waveRecyclerview?.let {
            it.setSelectTime(mViewModel.getCurrentTime())
            it.setIsCanScrollTimeRuler(true)
        }
        waveRecyclerviewBottom?.let {
            it.setSelectTime(mViewModel.getCurrentTime())
            it.setIsCanScrollTimeRuler(true)
        }
    }

    fun waveStopMove() {
        mBinding?.waveRecyclerview?.let {
            it.setSelectTime(mViewModel.getCurrentTime())
            it.stopScroll()
            it.setIsCanScrollTimeRuler(true)
        }
        waveRecyclerviewBottom?.let {
            it.setSelectTime(mViewModel.getCurrentTime())
            it.stopScroll()
            it.setIsCanScrollTimeRuler(true)
        }
    }

    private fun waveStopMoveForEnd() {
        mBinding?.waveRecyclerview?.setIsCanScrollTimeRuler(true)
        waveRecyclerviewBottom?.setIsCanScrollTimeRuler(true)
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun onAmpLoadingFinish() {
        val duration = mViewModel.playerController.getDuration()
        val currentTimeMillis = mViewModel.playerController.getCurrentPosition()
        val ampList = mViewModel.ampList.value
        mBinding?.waveRecyclerview?.let {
            it.setAmplitudeList(ampList)
            it.totalTime = duration
            it.setSelectTime(currentTimeMillis)

            it.duration = duration
            it.isDirectOn = mViewModel.isDirectOn
            it.directTime = mViewModel.directTime
        }
        waveRecyclerviewBottom?.let {
            it.setAmplitudeList(ampList)
            it.totalTime = duration
            it.setSelectTime(currentTimeMillis)

            it.duration = duration
            it.isDirectOn = mViewModel.isDirectOn
            it.directTime = mViewModel.directTime
        }
        DebugUtil.i(
            TAG,
            "onAmpLoadingFinish mWaveRecyclerView set select time $currentTimeMillis + duration : $duration"
        )
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.layout_transfer_text,
            R.id.tv_transfer -> {
                DebugUtil.d(TAG, "onClick layout_transfer_text:")
                activity?.findViewById<View>(R.id.layout_transfer_text_activity)?.callOnClick()
            }
            R.id.layout_mark -> {
                DebugUtil.d(TAG, "onClick layout_mark:")
                activity?.findViewById<View>(R.id.layout_mark_activity)?.callOnClick()
            }
            R.id.layout_mark_photo -> {
                DebugUtil.d(TAG, "onClick layout_mark_photo:")
                activity?.findViewById<View>(R.id.layout_mark_photo_activity)?.callOnClick()
            }
            R.id.layout_mark_list -> {
                activity?.findViewById<View>(R.id.layout_mark_list_activity)?.callOnClick()
            }
            R.id.layout_summary -> activity?.findViewById<View>(R.id.layout_summary_activity)?.callOnClick()
        }
    }

    private fun getWidthPercentValue(): Float {
        val typedValue = TypedValue()
        resources.getValue(R.dimen.screen_width_percent_parentchild, typedValue, true)
        val widthPercentValue = typedValue.float
        return widthPercentValue
    }

    private fun stopObserver() {
        mViewModel.playerController.playerState.removeObserver(mPlayStateChangeObserver)
        //释放listener
        mViewModel.playerController.timerTickListener = null
    }

    override fun onDestroyView() {
        super.onDestroyView()
        DebugUtil.d(TAG, "onDestroyView")

        stopObserver()
        cacheDialogShowStatus()
        releaseDialogs()
        viewAnimateControl.release()
        mBinding?.waveRecyclerview?.setDragListener(null)
        waveRecyclerviewBottom?.setDragListener(null)
        mBinding?.mConstraintLayout?.removeOnLayoutChangeListener(mLayoutChangeListener)
        mBinding?.layoutEmpty?.removeOnLayoutChangeListener(mMarkEmptyChangeListener)
        mBinding?.ivMarkEmpty?.release()
    }

    override fun onDestroy() {
        super.onDestroy()
        DebugUtil.d(TAG, "onDestroy: isFinishing = ${activity?.isFinishing}, isRemoving = ${parentFragment?.isRemoving}")
        val isFinish = (activity?.isFinishing == true) || (parentFragment?.isRemoving == true)
        if (isFinish && (::mViewModel.isInitialized)) {
            mViewModel.playerController.releasePlay()
        }
    }

    private fun cacheDialogShowStatus() {
        mViewModel.mNeedShowMarkRenameDialog.value = mMarkListAdapter?.isRenameMarkDialogShowing ?: false
        mViewModel.mNeedShowMarkDeleteDialog = mMarkListAdapter?.isDeleteMarkDialogShowing ?: false
    }

    private fun releaseDialogs() {
        mMarkListAdapter?.let {
            if (it.isRenameMarkDialogShowing) {
                DebugUtil.i(TAG, "releaseDialogs mRenameMarkDialog")
                mViewModel.mRenameMarkEditText = it.renameMarkDialog?.getNewContent().toString()
                mViewModel.mMarkRenameData = it.mRenameMark
                it.dismissRenameDialog()
            }
            if (it.isDeleteMarkDialogShowing) {
                mViewModel.mMarkDeleteData = it.mDeleteMark
                it.dismissDeleteDialog()
            }
            it.setOnShouldShowMenuPopListener(null)
        }
        dismissMenuPop()
    }

    fun dismissMenuPop() {
        mMarkListAdapter?.dismissMenuPop()
    }

    override fun onResume() {
        super.onResume()
        // 临时规避#4501003,造成该问题原有是由于waveStartMove中调用setSelectTime导致最后一次波形滚动被拦截未执行
        if (mViewModel.playerController.playerState.value == PlayStatus.PLAYER_STATE_HALTON) {
            mBinding?.waveRecyclerview?.setSelectTime(mViewModel.getCurrentTime())
            waveRecyclerviewBottom?.setSelectTime(mViewModel.getCurrentTime())
        }
    }

    private val mLayoutChangeListener: WindowLayoutChangeListener =
        object : WindowLayoutChangeListener() {
            override fun onLayoutChange(v: View?, rect: Rect, oldRect: Rect) {
                activity?.let {
                    /*底部工具栏设置最大宽度，为了处理左右对齐，又不会换行*/
                    viewAnimateControl.setBottomViewMaxWidth(
                        mBinding?.mConstraintLayout,
                        bottomViewBinding,
                        mViewModel.mPanelShowStatus.value?.checkHasSummary() == true,
                        mViewModel.isSupportConvert()
                    )
                    setShowOrHideWaveViewAndMarkListView()
                }
            }
        }

    private val mMarkEmptyChangeListener: WindowLayoutChangeListener = object : WindowLayoutChangeListener() {
        override fun onLayoutChange(v: View?, rect: Rect, oldRect: Rect) {
            activity?.let {
                if (mBinding?.layoutEmpty?.visibility == View.VISIBLE) {
                    mBinding?.ivMarkEmpty?.initImageResource()
                    DebugUtil.e(TAG, "mMarkEmptyChangeListener")
                    mBinding?.ivMarkEmpty?.setScaleByEmptySize(
                        it.px2dp(rect.width()).toInt(), it.px2dp(rect.height()).toInt(),
                        "mMarkEmptyChangeListener"
                    )
                }
            }
        }
    }

    private fun setShowOrHideWaveViewAndMarkListView(doAnim: Boolean = false) {
        if (!isAdded) {
            DebugUtil.i(TAG, "setShowOrHideWaveViewAndMarkListView isAdded is false")
            return
        }
        val binding = mBinding ?: return
        val waveHeight = viewAnimateControl.getWaveHeight(false)
        binding.waveGradientView.updateConstraintHeight(waveHeight)
    }

    fun getPictureBtnView(): View? {
        return mBinding?.root?.findViewById(R.id.layout_mark_photo)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        isNeedRefresh.value = false
    }

    fun onPageSelectedStateChange(select: Boolean) {
        isSelected = select
        if (select) {
            val convertStatus = mPlaybackConvertViewModel?.mConvertStatus?.value ?: CONVERT_STATUS_INIT
            val canPlayAnim = bottomViewBinding?.layoutTransferText?.isVisible ?: false
            DebugUtil.d(TAG, "onPageSelectedStateChange: canPlayAnim = $canPlayAnim")
            bottomViewBinding?.viewTransferText?.updateTransferState(convertStatus, canPlayAnim)
        } else {
            bottomViewBinding?.viewTransferText?.cancelAnimator()
        }
    }

    fun onSmartNameStatusChange(display: Boolean, resultName: String?) {
        DebugUtil.d(TAG, "onSmartNameStatusChange, dispaly:$display")
        if (display) {
            mBinding?.tvPlayName.invisible()
            mBinding?.smartNameLoading.playAnimationExt()
        } else {
            mBinding?.tvPlayName.visible()
            mBinding?.smartNameLoading.cancelAnimationExt()
        }
        if (!resultName.isNullOrBlank()) {
            if (mBinding?.smartNameLoading?.isGone() == false) {
                mBinding?.smartNameLoading.gone()
            }
            mBinding?.tvPlayName?.setAnimateText(resultName, true)
            mBinding?.tvPlayName?.setAnimationListener {
                mViewModel.smartNameResult.postValueSafe(resultName)
            }
        }
    }
}