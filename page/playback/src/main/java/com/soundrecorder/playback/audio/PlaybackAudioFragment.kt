/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.audio

import android.annotation.SuppressLint
import android.graphics.Rect
import android.os.Bundle
import android.text.TextUtils
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ScrollView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.core.view.marginBottom
import androidx.core.view.marginTop
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.currentInMsFormatTimeExclusive
import com.soundrecorder.base.ext.isFlexibleWindow
import com.soundrecorder.base.ext.isInMultiWindowMode
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.splitwindow.WindowLayoutChangeListener
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.px2dp
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.buryingpoint.SummaryStaticUtil
import com.soundrecorder.common.databean.ConvertStatus.Companion.CONVERT_STATUS_INIT
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.TipUtil
import com.soundrecorder.common.utils.ViewUtils.addItemDecorationBottom
import com.soundrecorder.common.utils.ViewUtils.setAnimatePressBackground
import com.soundrecorder.common.utils.ViewUtils.updateConstraintHeight
import com.soundrecorder.common.utils.ViewUtils.updateConstraintPercentWidth
import com.soundrecorder.common.utils.cancelAnimationExt
import com.soundrecorder.common.utils.gone
import com.soundrecorder.common.utils.invisible
import com.soundrecorder.common.utils.isGone
import com.soundrecorder.common.utils.playAnimationExt
import com.soundrecorder.common.utils.visible
import com.soundrecorder.modulerouter.BrowseFileAction
import com.soundrecorder.playback.PlaybackActivityViewModel
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.MARK_ACTION_ADD
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.MARK_ACTION_DELETE
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.MARK_ACTION_INIT
import com.soundrecorder.playback.PlaybackActivityViewModel.Companion.MARK_ACTION_RENAME
import com.soundrecorder.playback.PlaybackContainerFragment
import com.soundrecorder.playback.R
import com.soundrecorder.playback.databinding.FragmentPlaybackAudioBinding
import com.soundrecorder.playback.databinding.IncludeAudioFragmentBottomButtonBinding
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.player.TimerTickCallback
import com.soundrecorder.player.WavePlayerController
import com.soundrecorder.player.status.PlayStatus
import com.soundrecorder.summary.RecordSummaryManager
import com.soundrecorder.summary.util.SummarySupportManager
import com.soundrecorder.wavemark.mark.MarkListAdapter
import com.soundrecorder.wavemark.mark.OnShouldShowMenuPopListener
import java.lang.Long.min

/**
 * 录音播放
 */
class PlaybackAudioFragment : Fragment(),
    View.OnCreateContextMenuListener,
    View.OnClickListener {

    companion object {
        private const val TAG = "PlaybackAudioFragment"
        const val READ_MARK_TIMEOUT = 20000
    }

    var mBinding: FragmentPlaybackAudioBinding? = null
    var bottomViewBinding: IncludeAudioFragmentBottomButtonBinding? = null
    private var mMarkListAdapter: MarkListAdapter? = null
    private lateinit var mViewModel: PlaybackActivityViewModel
    private var mBrowseViewModel: ViewModel? = null
    private var isNeedRefresh = MutableLiveData(true)
    private var mAnim: EffectiveAnimationView? = null
    private var mPlaybackConvertViewModel: PlaybackConvertViewModel? = null
    private var markBottomLineHelper: MarkListBottomLineHelper? = null
    private var isSelected = false

    private val viewAnimateControl: PlayViewAnimateControl by lazy {
        PlayViewAnimateControl(requireActivity())
    }

    private val mPlayStateChangeObserver = Observer<Int> {
        DebugUtil.i(TAG, "mPlayerState changed $it")
        when (it) {
            PlayStatus.PLAYER_STATE_PLAYING,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> {
                if (mViewModel.playerController.mIsTouchSeekbar.value != true) {
                    waveStartMove()
                }
            }
            PlayStatus.PLAYER_STATE_PAUSE,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE -> waveStopMove()
            PlayStatus.PLAYER_STATE_HALTON -> {
                //可能最后smoothScrollBy后70ms的动画才走到最后，状态变为0后立马stopScroll导致没有滚动到最后
                waveStopMoveForEnd()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        DebugUtil.i(TAG, "onCreate $this , savedInstanceState $savedInstanceState")
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        mBinding = FragmentPlaybackAudioBinding.bind(
            inflater.inflate(
                R.layout.fragment_playback_audio,
                container,
                false
            )
        ).apply {
            bottomViewBinding = IncludeAudioFragmentBottomButtonBinding.bind(root)
        }

        mViewModel = ViewModelProvider(requireParentFragment())[PlaybackActivityViewModel::class.java]
        mPlaybackConvertViewModel =
            ViewModelProvider(this.requireParentFragment())[PlaybackConvertViewModel::class.java]
        mBrowseViewModel = BrowseFileAction.getBrowseActivityViewModel(activity as? AppCompatActivity)
        isNeedRefresh = BrowseFileAction.getBrowseFileActivityViewModel(activity as? AppCompatActivity)
        initView(savedInstanceState)
        initViewModelObserver()
        checkDialogShowStatus(savedInstanceState != null)
        return mBinding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        checkShowStartGuideAnim()
        (parentFragment as? PlaybackContainerFragment)?.checkIsRestoreMarkPictureDialog()
        markBottomLineHelper = MarkListBottomLineHelper(
            bottomViewBinding?.viewLineMarkBottom,
            bottomViewBinding?.spaceBottomViewTop,
            mBinding?.markListView
        )
    }

    private fun initView(savedInstanceState: Bundle?) {
        if (savedInstanceState == null) {
            //重建不需要刷新波形
            mViewModel.needSyncRulerView = true
        }
        mBinding?.waveRecyclerview?.let {
            // 波形load成功之前禁止波形左右滚动
            it.setIsCanScrollTimeRuler(mViewModel.loadAmpSuccess())
            it.setAmplitudeList(mViewModel.ampList.value)
            it.setMaxAmplitudeSource(mViewModel.maxAmplitudeSource)

            it.duration = mViewModel.duration
            it.isDirectOn = mViewModel.isDirectOn
            it.directTime = mViewModel.directTime

            it.setDragListener(mViewModel.mDragListener)
            it.invalidate()
            it.addOnScrollListener(object : RecyclerView.OnScrollListener() {

                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    when (newState) {
                        RecyclerView.SCROLL_STATE_DRAGGING -> {
                            mViewModel.playerController.stopTimerNow()
                            mViewModel.playerController.scrollFromType =
                                WavePlayerController.SCROLL_FROM_DRAG
                            //手拖动占据控制权后，设置preTimeMillis = -1,恢复后startSmoothScrollBy当次不处理
                            it.setPreTimeMillis(-1)
                        }
                        RecyclerView.SCROLL_STATE_IDLE -> {
                            if (mViewModel.playerController.scrollFromType == WavePlayerController.SCROLL_FROM_DRAG) {
                                val curTime = it.getSlideTime("onScrolledChanged idle")
                                if (curTime >= 0) {
                                    mViewModel.needSyncRulerView = false
                                    mViewModel.seekTime(curTime)
                                    mViewModel.playerController.scrollFromType =
                                        WavePlayerController.SCROLL_FROM_DEFAULT
                                    if ((curTime < mViewModel.playerController.getDuration())
                                        && mViewModel.playerController.isWholePlaying()
                                    ) {
                                        mViewModel.playerController.startTimerAsync("onScrollStateChanged")
                                    }
                                }
                            }
                        }
                    }
                }

                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    if (mViewModel.playerController.scrollFromType != WavePlayerController.SCROLL_FROM_DRAG) {
                        //onScrolled, is auto scrolling, just return
                        return
                    }

                    if (dx != 0) {
                        val curTime = it.getSlideTime("onScrolled")
                        if (curTime >= 0) {
                            mViewModel.needSyncRulerView = false
                            mViewModel.setCurrentTime(curTime)
                        }
                    }
                }
            })
        }
        initMarkList()
        bottomViewBinding?.layoutTransferText?.setOnClickListener(this)
        bottomViewBinding?.layoutMarkList?.setOnClickListener(this)
        bottomViewBinding?.layoutMark?.setOnClickListener(this)
        bottomViewBinding?.layoutMarkPhoto?.setOnClickListener(this)
        bottomViewBinding?.layoutSummary?.setOnClickListener(this)
        bottomViewBinding?.layoutTransferText?.setAnimatePressBackground()
        bottomViewBinding?.layoutMarkList?.setAnimatePressBackground()
        bottomViewBinding?.layoutMark?.setAnimatePressBackground()
        bottomViewBinding?.layoutMarkPhoto?.setAnimatePressBackground()
        bottomViewBinding?.layoutSummary?.setAnimatePressBackground()
        mBinding?.mConstraintLayout?.addOnLayoutChangeListener(mLayoutChangeListener)
        mBinding?.layoutEmpty?.addOnLayoutChangeListener(mMarkEmptyChangeListener)
    }

    private fun startGuideAnim() {
        if (recordFilterIsRecycle()) {
            DebugUtil.d(TAG, "startGuideAnim isRecycle false return")
            return
        }
        if (!mViewModel.isSupportConvert()) {
            DebugUtil.d(TAG, "startGuideAnim loadSpeechToTextFeature false return")
            return
        }
        if (TipUtil.hasShowTip(TipUtil.TYPE_GUIDE)) {
            DebugUtil.d(TAG, "startGuideAnim hasShowTip return")
            return
        }
        //新增分屏模式拦截，分屏模式下不显示转文本提示动效
        if (!isFlexibleWindow(activity) && isInMultiWindowMode()) { //不为浮窗状态下时，再判断是否分屏
            DebugUtil.d(TAG, "startGuideAnim isInMultiWindowMode return")
            TipUtil.saveShowedTip(TipUtil.TYPE_GUIDE)
            return
        }
        mBinding?.animStub?.apply {
            if (!isInflated) {
                mAnim = this.viewStub?.inflate() as? EffectiveAnimationView
            }
        }
        mAnim?.apply {
            setGuideAnim(this)
            visibility = View.VISIBLE
            clearAnimation()
            playAnimation()
            showGuideTip()
        }
    }

    private fun setGuideAnim(animView: EffectiveAnimationView) {
        if (BaseApplication.sIsRTLanguage) {
            animView.setAnimation("guide_rtl.json")
        } else {
            animView.setAnimation("guide.json")
        }
    }

    private fun releaseGuideAnim() {
        mAnim?.apply {
            if (isAnimating) {
                cancelAnimation()
                visibility = View.GONE
                DebugUtil.d(TAG, "releaseGuideAnim")
            }
            setImageDrawable(null)
        }
    }

    private fun initMarkList() {
        mMarkListAdapter = MarkListAdapter(activity, true).apply {
            setOnShouldShowMenuPopListener(object : OnShouldShowMenuPopListener {
                override fun shouldShow(): Boolean {
                    return isOnAudioWhenViewPager2IDLEWhenShowMarkList()
                }
            })
            setOnDeleteListener {
                val index = mViewModel.getMarkList()?.value?.indexOf(it) ?: return@setOnDeleteListener
                mViewModel.removeMark(index)
                mBinding?.waveRecyclerview?.removeMarkData = it
            }
            setOnMarkClickListener { mark ->
                mBinding?.waveRecyclerview?.stopScroll()
                mViewModel.needSyncRulerView = true
                mViewModel.seekTime(mark.correctTime)
                BuryingPoint.seekToMarkTagWhenPlayback(mViewModel.recordType.value)
            }
            setOnRenameMarkListener(mViewModel.onRenameMarkListener)
        }
        mBinding?.markListView?.let {
            it.layoutManager = LinearLayoutManager(it.context)
            it.adapter = mMarkListAdapter
            it.addItemDecorationBottom(com.soundrecorder.common.R.dimen.card_margin_top_buttom)
        }
    }

    private fun setMarkViewEnable(isMarkEnable: Boolean) {
        bottomViewBinding?.tvMark?.isEnabled = isMarkEnable
        bottomViewBinding?.layoutMarkPhoto?.isEnabled = isMarkEnable
    }

    private fun checkShowStartGuideAnim() {
        val animRunningLiveData = BrowseFileAction.getViewModelAnimRunning(mBrowseViewModel)
        if (animRunningLiveData?.value != true) { // 没有执行小屏进入动画，显示新手引导
            startGuideAnim()
        } else {
            // 动效执行，波形不绘制，等动效完成后，再绘制，避免进入动效卡顿
            mBinding?.waveRecyclerview?.suppressLayout(true)
            animRunningLiveData.observe(viewLifecycleOwner) {
                if (!it) { // 小屏执行完动效再显示新手引导
                    startGuideAnim()
                    markBottomLineHelper?.handleBottomLineVisible("animRunning change to false")
                    mBinding?.waveRecyclerview?.suppressLayout(false)
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initViewModelObserver() {
        // 每次进入初始页面时，不需要做动效
        var disableAnimFirstTime = true
        mViewModel.isShowMarkList.observe(viewLifecycleOwner) { isShowing ->
            DebugUtil.i(TAG, "isShowing = $isShowing")
            if (isShowing) {
                BuryingPoint.addPlayMarkListButton(RecorderUserAction.VALUE_PLAY_MARK_LIST_SHOWING)
            } else {
                BuryingPoint.addPlayMarkListButton(RecorderUserAction.VALUE_PLAY_MARK_LIST_UNSHOWING)
            }
            bottomViewBinding?.tvMarkList?.setAnimateSelected(isShowing)
            mBinding?.mConstraintLayout?.post {
                setShowOrHideWaveViewAndMarkListView(!disableAnimFirstTime)
            }
            disableAnimFirstTime = false
            dismissMenuPop()
        }

        mViewModel.mShowActivityControlView.observe(viewLifecycleOwner) {
            DebugUtil.i(TAG, "mShowActivityControlView changed $it")
            val hasSummary = mViewModel.mPanelShowStatus.value?.checkHasSummary()
            val isRecycle = mViewModel.isRecycle
            setVisibleLayoutMarkAndMarkList(it.not(),
                hasSummary == true,
                isRecycle
            )
        }
        mViewModel.mPanelShowStatus.observe(viewLifecycleOwner) {
            val visible = mViewModel.mShowActivityControlView.value?.not()
            setVisibleLayoutMarkAndMarkList(visible == true,
                it.checkHasSummary(),
                mViewModel.isRecycle
            )
        }

        mViewModel.playName.observe(viewLifecycleOwner) { name ->
            DebugUtil.i(TAG, "the play name changes to $name")
            mBinding?.tvPlayName?.setText(name.title())
        }
        mViewModel.isPrepareAmplitudeAndMark.observe(viewLifecycleOwner) {
            if (it) {
                mBinding?.waveRecyclerview?.setIsCanScrollTimeRuler(true) // 拿到波形数据标记可滚动
                onAmpLoadingFinish()
            }
        }
        mViewModel.mIsDecodeReady.observe(viewLifecycleOwner) { isReady ->
            if (isReady) {
                mBinding?.waveRecyclerview?.setIsCanScrollTimeRuler(true) // 拿到波形数据标记可滚动
                mBinding?.waveRecyclerview?.let {
                    it.setSoundFile(mViewModel.mSoundFile)
                    it.totalTime = mViewModel.playerController.getDuration()
                    it.setSelectTime(mViewModel.playerController.getCurrentPosition())

                    it.duration = mViewModel.duration
                    it.isDirectOn = mViewModel.isDirectOn
                    it.directTime = mViewModel.directTime
                    DebugUtil.d(TAG, "initViewModelObserver isDirectOn:${mViewModel.isDirectOn}, " +
                            "directTime:${mViewModel.directTime}")
                    it.notifyDataSetChanged()
                }
            }
        }
        mViewModel.playerController.currentTimeMillis.observe(viewLifecycleOwner) { currentTime ->
            refreshCurPlayTime(currentTime)
            refreshWaveInfo(currentTime)
        }
        mViewModel.playerController.playerState.observeForever(mPlayStateChangeObserver)
        mViewModel.playerController.isReplay.observe(viewLifecycleOwner) { isReplay ->
            if (isReplay) {
                waveStopMove()
            }
        }

        mViewModel.markEnable.observe(viewLifecycleOwner) {
            setMarkViewEnable(it)
        }
        mViewModel.lastMarkAction.observe(viewLifecycleOwner) {
            DebugUtil.i(TAG, "lastMarkAction changed $it")
            when (it) {
                MARK_ACTION_INIT -> {
                    notifyMarkListByData()
                }
                MARK_ACTION_DELETE -> {
                    notifyMarkListByData()
                    BuryingPoint.addMarkDelete(mViewModel.recordType.value ?: 0)
                }
                MARK_ACTION_RENAME -> {
                    notifyMarkListByData()
                    BuryingPoint.addMarkRename(mViewModel.recordType.value ?: 0)
                }
                MARK_ACTION_ADD -> {
                    try {
                        val index = mViewModel.addMarkIndex
                        mMarkListAdapter?.setShowAnimatorPos(index)
                        notifyMarkListByData {
                            smoothScrollBy(index)
                            // 添加标记后赋值挪到次数，避免动效标记闪烁
                            if (mBinding?.markListView?.isVisible != true) {
                                mViewModel.isShowMarkList.value = true
                            }
                        }
                        if (mViewModel.playerController.playerState.value in arrayOf(
                                PlayStatus.PLAYER_STATE_PAUSE,
                                PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE,
                                PlayStatus.PLAYER_STATE_HALTON
                            )
                        ) {
                            BuryingPoint.addMarkWhenUnplay()
                        }
                        BuryingPoint.addMarkAdd(mViewModel.recordType.value ?: 0)
                    } catch (e: Exception) {
                        DebugUtil.e(TAG, "ADD MARK ERROR", e)
                    }
                }
            }
        }

        mViewModel.playerController.timerTickListener = object : TimerTickCallback {
            override fun onTimerTick(timeTickMillis: Long) {
                if (!mViewModel.needSyncRulerView) {
                    DebugUtil.d(TAG, "refreshTimerTick: newTime = $timeTickMillis, needSyncRulerView = true")
                    val realTime = min(timeTickMillis, mViewModel.playerController.getDuration())
                    mBinding?.waveRecyclerview?.startSmoothScroll(realTime)
                    mViewModel.onTimerRefreshTime(timeTickMillis)
                }
            }
        }

        if (PermissionUtils.hasReadAudioPermission()) {
            mViewModel.readMarkTag()
        }

        mPlaybackConvertViewModel?.mConvertStatus?.observe(viewLifecycleOwner) {
            val canPlayAnim = isSelected && (bottomViewBinding?.viewTransferText?.isVisible ?: false)
            DebugUtil.i(TAG, "convertViewModelChange mConvertStatus changed: $it, isSelected = $isSelected, $canPlayAnim")
            bottomViewBinding?.viewTransferText?.updateTransferState(it, canPlayAnim)
        }
        mViewModel.mSummaryStatus.observe(viewLifecycleOwner) {
            val context = BaseApplication.getAppContext()
            when (it) {
                RecordSummaryManager.SUMMARY_STATE_CLIENT_END -> {
                    bottomViewBinding?.tvSummary?.let { view ->
                        view.setBottomText(context.resources.getString(com.soundrecorder.common.R.string.view_summary))
                        view.showDefaultImage()
                    }
                }

                RecordSummaryManager.SUMMARY_STATE_CLIENT_GENERATING -> {
                    bottomViewBinding?.tvSummary?.let { view ->
                        view.setBottomText(context.resources.getString(com.soundrecorder.common.R.string.summary_generating))
                        view.startRunAnim()
                    }
                }

                else -> {
                    bottomViewBinding?.tvSummary?.let { view ->
                        view.setBottomText(context.resources.getString(com.soundrecorder.common.R.string.generate_summary))
                        view.showDefaultImage()
                    }
                }
            }
            mViewModel.addShowSummaryEvent(SummaryStaticUtil.EVENT_FROM_AUDIO,
                SummarySupportManager.supportRecordSummary.value,
                it == RecordSummaryManager.SUMMARY_STATE_CLIENT_END)
        }
    }

    private fun recordFilterIsRecycle(): Boolean {
        return mViewModel.isRecycle
    }

    private fun notifyMarkListByData(commitCallback: Runnable? = null) {
        mViewModel.getMarkList()?.value?.let {
            mMarkListAdapter?.setData(it, commitCallback)
            mBinding?.waveRecyclerview?.setMarkTimeList(it)
        }
    }

    private fun refreshCurPlayTime(seekToTime: Long) {
        mBinding?.tvCurrentTime?.let {
            it.contentDescription = TimeUtils.getContentDescriptionForTimeDuration(seekToTime)
            it.text = seekToTime.currentInMsFormatTimeExclusive(mViewModel.playerController.getDuration())
        }
    }

    private fun refreshWaveInfo(seekToTime: Long) {
        //快进快退或者点击标记的时候，需要更新波形
        if (mViewModel.needSyncRulerView) {
            mBinding?.waveRecyclerview?.let {
                mViewModel.needSyncRulerView = false
                it.setSelectTime(seekToTime)
            }
            DebugUtil.d(
                TAG,
                "refreshWaveInfo: seekToTime = $seekToTime, needSyncRulerView = true"
            )
        }
    }

    private fun smoothScrollBy(index: Int) {
        (mBinding?.markListView?.layoutManager as? LinearLayoutManager)?.scrollToPositionWithOffset(
            index,
            0
        )
    }

    private fun checkDialogShowStatus(isFromRestore: Boolean) {
        if (!isFromRestore) {
            DebugUtil.i(TAG, "checkDialogShowStatus not from restore, no need to check Dialog Show")
            return
        }
        if (mViewModel.mNeedShowMarkRenameDialog.value == true) {
            DebugUtil.i(TAG, "checkDialogShowStatus mMarkRenameDialogShow")
            mMarkListAdapter?.showRenameMark(
                activity,
                mViewModel.mMarkRenameData,
                mViewModel.mRenameMarkEditText
            )
        }
        if (mViewModel.mNeedShowMarkDeleteDialog) {
            mMarkListAdapter?.showDeleteMark(activity, mViewModel.mMarkDeleteData)
        }
    }

    private fun waveStartMove() {
        mBinding?.waveRecyclerview?.let {
            it.setSelectTime(mViewModel.getCurrentTime())
            it.setIsCanScrollTimeRuler(true)
        }
    }

    fun waveStopMove() {
        mBinding?.waveRecyclerview?.let {
            it.setSelectTime(mViewModel.getCurrentTime())
            it.stopScroll()
            it.setIsCanScrollTimeRuler(true)
        }
    }

    private fun waveStopMoveForEnd() {
        mBinding?.waveRecyclerview?.setIsCanScrollTimeRuler(true)
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun onAmpLoadingFinish() {
        val duration = mViewModel.playerController.getDuration()
        val currentTimeMillis = mViewModel.playerController.getCurrentPosition()
        val ampList = mViewModel.ampList.value
        mBinding?.waveRecyclerview?.let {
            it.setAmplitudeList(ampList)
            it.totalTime = duration
            it.setSelectTime(currentTimeMillis)

            it.duration = duration
            it.isDirectOn = mViewModel.isDirectOn
            it.directTime = mViewModel.directTime
        }
        DebugUtil.i(
            TAG,
            "onAmpLoadingFinish mWaveRecyclerView set select time $currentTimeMillis + duration : $duration"
        )
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.layout_transfer_text -> {
                activity?.findViewById<View>(R.id.layout_transfer_text_activity)?.callOnClick()
            }
            R.id.layout_mark -> {
                DebugUtil.d(TAG, "onClick layout_mark:")
                activity?.findViewById<View>(R.id.layout_mark_activity)?.callOnClick()
            }
            R.id.layout_mark_photo -> {
                DebugUtil.d(TAG, "onClick layout_mark_photo:")
                activity?.findViewById<View>(R.id.layout_mark_photo_activity)?.callOnClick()
            }
            R.id.layout_mark_list -> {
                activity?.findViewById<View>(R.id.layout_mark_list_activity)?.callOnClick()
            }
            R.id.layout_summary -> activity?.findViewById<View>(R.id.layout_summary_activity)?.callOnClick()
        }
    }


    private fun setVisibleLayoutMarkAndMarkList(visible: Boolean, hasSummary: Boolean, isRecycle: Boolean) {
        val viewBinding = mBinding ?: return
        val bottomViewBinding = bottomViewBinding ?: return
        if (isRecycle) {
            bottomViewBinding.layoutSummary.isVisible = false
            bottomViewBinding.layoutTransferText.isVisible = false
            bottomViewBinding.layoutMark.isVisible = false
            bottomViewBinding.layoutMarkPhoto.isVisible = false
            bottomViewBinding.layoutMarkList.isVisible = false
        } else {
            bottomViewBinding.layoutTransferText.isVisible =
                visible && mViewModel.isSupportConvert()
            val showSummary = visible && hasSummary
            if (bottomViewBinding.layoutSummary.isVisible != showSummary) {
                viewAnimateControl.setBottomViewMaxWidth(
                    mBinding?.mConstraintLayout, bottomViewBinding, hasSummary, mViewModel.isSupportConvert())
                bottomViewBinding.layoutSummary.isVisible = showSummary
            }

            bottomViewBinding.layoutMark.isVisible = visible
            bottomViewBinding.layoutMarkPhoto.isVisible = visible
            bottomViewBinding.layoutMarkList.isVisible = visible
            viewBinding.markListView.updateConstraintPercentWidth(getWidthPercentValue())
            val markPaddingHorizontal = resources.getDimensionPixelSize(R.dimen.play_mark_list_padding_horizontal)
            viewBinding.markListView.updatePadding(
                left = markPaddingHorizontal,
                right = markPaddingHorizontal
            )
        }
    }

    private fun getWidthPercentValue(): Float {
        val typedValue = TypedValue()
        resources.getValue(R.dimen.screen_width_percent_parentchild, typedValue, true)
        val widthPercentValue = typedValue.float
        return widthPercentValue
    }

    private fun stopObserver() {
        mViewModel.playerController.playerState.removeObserver(mPlayStateChangeObserver)
        //释放listener
        mViewModel.playerController.timerTickListener = null
    }

    override fun onDestroyView() {
        super.onDestroyView()
        DebugUtil.d(TAG, "onDestroyView")

        stopObserver()
        releaseGuideAnim()
        cacheDialogShowStatus()
        releaseDialogs()
        markBottomLineHelper?.onDestroyView()
        markBottomLineHelper = null
        viewAnimateControl.release()
        if (mBinding?.waveRecyclerview != null) {
            mBinding?.waveRecyclerview?.setDragListener(null)
        }
        mBinding?.mConstraintLayout?.removeOnLayoutChangeListener(mLayoutChangeListener)
        mBinding?.layoutEmpty?.removeOnLayoutChangeListener(mMarkEmptyChangeListener)
        mBinding?.ivMarkEmpty?.release()
    }

    override fun onDestroy() {
        super.onDestroy()
        DebugUtil.d(TAG, "onDestroy: isFinishing = ${activity?.isFinishing}, isRemoving = ${parentFragment?.isRemoving}")
        val isFinish = (activity?.isFinishing == true) || (parentFragment?.isRemoving == true)
        if (isFinish && (::mViewModel.isInitialized)) {
            mViewModel.playerController.releasePlay()
        }
    }

    private fun cacheDialogShowStatus() {
        mViewModel.mNeedShowMarkRenameDialog.value = mMarkListAdapter?.isRenameMarkDialogShowing ?: false
        mViewModel.mNeedShowMarkDeleteDialog = mMarkListAdapter?.isDeleteMarkDialogShowing ?: false
    }

    private fun releaseDialogs() {
        mMarkListAdapter?.let {
            if (it.isRenameMarkDialogShowing) {
                DebugUtil.i(TAG, "releaseDialogs mRenameMarkDialog")
                mViewModel.mRenameMarkEditText = it.renameMarkDialog?.getNewContent().toString()
                mViewModel.mMarkRenameData = it.mRenameMark
                it.dismissRenameDialog()
            }
            if (it.isDeleteMarkDialogShowing) {
                mViewModel.mMarkDeleteData = it.mDeleteMark
                it.dismissDeleteDialog()
            }
            it.setOnShouldShowMenuPopListener(null)
        }
        dismissMenuPop()
    }

    fun dismissMenuPop() {
        mMarkListAdapter?.dismissMenuPop()
    }

    override fun onResume() {
        super.onResume()
        // 临时规避#4501003,造成该问题原有是由于waveStartMove中调用setSelectTime导致最后一次波形滚动被拦截未执行
        if (mViewModel.playerController.playerState.value == PlayStatus.PLAYER_STATE_HALTON) {
            mBinding?.waveRecyclerview?.setSelectTime(mViewModel.getCurrentTime())
        }
    }

    private val mLayoutChangeListener: WindowLayoutChangeListener =
        object : WindowLayoutChangeListener() {
            override fun onLayoutChange(v: View?, rect: Rect, oldRect: Rect) {
                activity?.let {
                    /*底部工具栏设置最大宽度，为了处理左右对齐，又不会换行*/
                    viewAnimateControl.setBottomViewMaxWidth(
                        mBinding?.mConstraintLayout,
                        bottomViewBinding,
                        mViewModel.mPanelShowStatus.value?.checkHasSummary() == true,
                        mViewModel.isSupportConvert()
                    )
                    setShowOrHideWaveViewAndMarkListView()
                }
            }
        }

    private val mMarkEmptyChangeListener: WindowLayoutChangeListener = object : WindowLayoutChangeListener() {
        override fun onLayoutChange(v: View?, rect: Rect, oldRect: Rect) {
            activity?.let {
                if (mBinding?.layoutEmpty?.visibility == View.VISIBLE) {
                    mBinding?.ivMarkEmpty?.initImageResource()
                    DebugUtil.e(TAG, "mMarkEmptyChangeListener")
                    mBinding?.ivMarkEmpty?.setScaleByEmptySize(
                        it.px2dp(rect.width()).toInt(), it.px2dp(rect.height()).toInt(),
                        "mMarkEmptyChangeListener"
                    )
                }
            }
        }
    }

    private fun setShowOrHideWaveViewAndMarkListView(doAnim: Boolean = false) {
        if (!isAdded) {
            DebugUtil.i(TAG, "setShowOrHideWaveViewAndMarkListView isAdded is false")
            return
        }
        val binding = mBinding ?: return
        val bottomButtonBinding = bottomViewBinding ?: return
        getShowOrHideWaveViewAndMarkListView().run {
            val showWave = this[0]
            val showMarkArea = this[1]
            val showTimeArea = this[2]
            /*波形、时间区域占位空间赋值，用于摆放标记区域位置，处理动效过程中标记区域不会有上下的动画*/
            val wavePlaceHeight = if (showWave) {
                viewAnimateControl.getWaveHeight(true)
            } else {
                0
            }
            binding.miniRecordTop.updateConstraintHeight(wavePlaceHeight)

            if (showTimeArea) {
                binding.tvPlayName.let {
                    it.isSingleLine = true
                    it.ellipsize = TextUtils.TruncateAt.END
                }
            }
            binding.waveGradientView.isVisible = showWave
            binding.waveRecyclerview.isVisible = showWave
            binding.layoutTime.isVisible = showTimeArea
            if (showMarkArea) {
                val noMarkData = mViewModel.getMarkList()?.value.isNullOrEmpty()
                viewAnimateControl.showMarkViewAnim(
                    binding,
                    bottomButtonBinding,
                    noMarkData,
                    doAnim
                ) {
                    if (noMarkData) {
                        binding.layoutEmptyScroller.fullScroll(ScrollView.FOCUS_DOWN)
                    } else {
                        markBottomLineHelper?.handleBottomLineVisible("showMarkViewAnim")
                    }
                }
            } else {
                viewAnimateControl.hideMarkViewAnim(binding, bottomButtonBinding, doAnim)
            }
        }
    }

    /**
     * @return 一个boolean[2]数组，
     * boolean[0]->能否显示波形
     * boolean[1]->能否显示标记区域
     * boolean[2]->能否显示标题
     */
    private fun getShowOrHideWaveViewAndMarkListView(): BooleanArray {
        val bindView = mBinding ?: return booleanArrayOf(true, true, true)
        val bottomBindView = bottomViewBinding ?: return booleanArrayOf(true, true, true)
        val isShowMarkList = mViewModel.isShowMarkList.value == true
        val layoutMarkHeight = resources.getDimensionPixelOffset(R.dimen.play_fragment_bottom_space) + bottomBindView.layoutMark.run {
            if (isGone) 0 else height + marginTop + marginBottom
        }
        val contentHeight: Int =
            bindView.mConstraintLayout.height - layoutMarkHeight
        val waveViewHeight =
            viewAnimateControl.getWaveHeight(isShowMarkList) + resources.getDimensionPixelOffset(
                com.soundrecorder.common.R.dimen.common_wave_view_margin_top
            )

        /**
         * 标题和时间整个高度
         */
        val titleDefaultHeight = resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.common_max_time_layout_height)

        /**
         * 显示一个标记需要的最小高度
         */
        val minMarkViewHeight = if (isShowMarkList) {
            bindView.markListView.run {
                resources.getDimensionPixelOffset(com.soundrecorder.wavemark.R.dimen.mark_item_height) + marginTop + marginBottom
            }
        } else 0

        /*DebugUtil.i(TAG, "layoutMarkHeight=$layoutMarkHeight, contentHeight = $contentHeight, waveViewHeight = $waveViewHeight," +
                "titleDefaultHeight = $titleDefaultHeight , minMarkViewHeight = $minMarkViewHeight")*/
        val showWave: Boolean
        val showMark: Boolean
        val showTitle: Boolean


        if ((contentHeight - waveViewHeight - titleDefaultHeight - minMarkViewHeight) > 0) {
            showWave = true
            showMark = isShowMarkList
            showTitle = true
        } else {
            showWave = false
            if (isShowMarkList) {
                showMark = (contentHeight - titleDefaultHeight - minMarkViewHeight) > 0
                showTitle = if (showMark) true else (contentHeight - titleDefaultHeight > 0)
            } else {
                showMark = false
                showTitle = contentHeight - titleDefaultHeight > 0
            }
        }
        return booleanArrayOf(showWave, showMark, showTitle)
    }

    private fun isOnAudioWhenViewPager2IDLEWhenShowMarkList(): Boolean {
        val playbackFragment = parentFragment as? PlaybackContainerFragment ?: return false
        val isShowMarkList = mViewModel.isShowMarkList.value == true
        val isViewPager2IDLE = playbackFragment.isViewPager2IDLE()
        DebugUtil.d(TAG, "isOnAudioWhenViewPager2, isShowMarkList:$isShowMarkList, isViewPager2IDLE:$isViewPager2IDLE")
        val isOnAudio = playbackFragment.isOnAudio()
        DebugUtil.d(TAG, "isOnAudioWhenViewPager2, isOnAudio:$isOnAudio")
        return if (isFlexibleWindow(activity)) {
            isViewPager2IDLE && isOnAudio && isShowMarkList
        } else {
            isViewPager2IDLE && isOnAudio && isFullAudioPage() && isShowMarkList
        }
    }

    private fun isOnAudioWhenViewPager2IDLE(): Boolean {
        val playbackFragment = parentFragment as? PlaybackContainerFragment ?: return false
        val isViewPager2IDLE = playbackFragment.isViewPager2IDLE()
        val isOnAudio = playbackFragment.isOnAudio()
        DebugUtil.d(TAG, "isOnAudioWhenViewPager2IDLE, isViewPager2IDLE:$isViewPager2IDLE, isOnAudio:$isOnAudio")
        val isFullAudioPage = isFullAudioPage()
        DebugUtil.d(TAG, "isOnAudioWhenViewPager2IDLE, isFullAudioPage:$isFullAudioPage")
        return if (isFlexibleWindow(activity)) {
            isViewPager2IDLE && isOnAudio
        } else {
            isViewPager2IDLE && isOnAudio && isFullAudioPage
        }
    }

    private fun isFullAudioPage(): Boolean {
        val xy = intArrayOf(-1, -1)
        mBinding?.mConstraintLayout?.getLocationOnScreen(xy)
        DebugUtil.d(TAG, "isFullAudioPage: [${xy[0]},${xy[1]}]")
        if (xy[0] > 0 && ScreenUtil.getWindowType(resources.configuration) != WindowType.SMALL) {
            val maxWidth = resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.sub_window_parent_max_width)
            val defaultWidth = ScreenUtil.getRealScreenWidthContainSystemBars() * BrowseFileAction.getParentPercentDefault() + NumberConstant.NUM_F0_5
            val parentViewWidth = maxWidth.coerceAtMost(defaultWidth.toInt())
            DebugUtil.d(TAG, "isFullAudioPage, maxWidth=$maxWidth, defaultWidth=$defaultWidth")
            // 中大屏减去父子级左侧列表宽度
            return (xy[0] - parentViewWidth) == 0
        }
        return xy[0] == 0
    }

    private fun showGuideTip() {
        TipUtil.checkShow(
            { if (isOnAudioWhenViewPager2IDLE()) mAnim else null },
            TipUtil.TYPE_GUIDE,
            null,
            this.lifecycle,
            if (isFlexibleWindow(activity)) false else isInMultiWindowMode(),
            onFinish = {
                releaseGuideAnim()
            }
        )
    }

    fun getPictureBtnView(): View? {
        return mBinding?.root?.findViewById(R.id.layout_mark_photo)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        isNeedRefresh.value = false
    }

    fun onPageSelectedStateChange(select: Boolean) {
        isSelected = select
        if (select) {
            val convertStatus = mPlaybackConvertViewModel?.mConvertStatus?.value ?: CONVERT_STATUS_INIT
            val canPlayAnim = bottomViewBinding?.layoutTransferText?.isVisible ?: false
            DebugUtil.d(TAG, "onPageSelectedStateChange: canPlayAnim = $canPlayAnim")
            bottomViewBinding?.viewTransferText?.updateTransferState(convertStatus, canPlayAnim)
        } else {
            bottomViewBinding?.viewTransferText?.cancelAnimator()
        }
    }

    fun onSmartNameStatusChange(display: Boolean, resultName: String?) {
        DebugUtil.d(TAG, "onSmartNameStatusChange, dispaly:$display")
        if (display) {
            mBinding?.tvPlayName.invisible()
            mBinding?.smartNameLoading.playAnimationExt()
        } else {
            mBinding?.tvPlayName.visible()
            mBinding?.smartNameLoading.cancelAnimationExt()
        }
        if (!resultName.isNullOrBlank()) {
            if (mBinding?.smartNameLoading?.isGone() == false) {
                mBinding?.smartNameLoading.gone()
            }
            mBinding?.tvPlayName?.setAnimateText(resultName, true)
            mBinding?.tvPlayName?.setAnimationListener {
                mViewModel.smartNameResult.postValueSafe(resultName)
            }
        }
    }
}