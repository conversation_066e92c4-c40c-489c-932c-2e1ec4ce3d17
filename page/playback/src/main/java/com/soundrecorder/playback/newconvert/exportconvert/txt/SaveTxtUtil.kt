/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.exportconvert.txt

import android.content.Context
import android.media.MediaScannerConnection
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.playback.R
import kotlinx.coroutines.*
import com.soundrecorder.base.ext.durationInMsFormatTimeExclusive
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.base.utils.DebugUtil
import java.io.File
import java.io.FileOutputStream

class SaveTxtUtil {

    companion object {
        val instance: SaveTxtUtil by lazy {
            SaveTxtUtil()
        }
        const val TAG = "SaveTxtUtil"
        const val SAVE_SUCCESS = 1
        const val SAVE_FAILED = -1
    }

    private var mCallback: SaveToLocalCallback? = null
    private var mSaveTxtToLocalAbsPath: String = ""
    private var mSaveTxtToLocalFileName: String = ""

    //显示dialog的时间，最少存在1s再消失
    private var needShowDialog = false
    private var mShowDialogDuration = 0L

    fun saveTxtToLocal(
        viewModel: ShareWithTxtViewModel,
        folderPath: String,
        originalFileName: String,
        createDate: String,
        convertFileSize: Long,
        callback: SaveToLocalCallback?,
        viewModelScope: CoroutineScope
    ) {
        this.mCallback = callback
        if (convertFileSize >= Constants.SHOW_WAITING_DIALOG_THRESHOLD) {
            needShowDialog = true
            //开始计时
            mShowDialogDuration = System.currentTimeMillis()
            //显示waitingDialog
            mCallback?.onShowSaveFileWaitingDialog()
        }
//        viewModel.viewModelScope.launch(Dispatchers.IO) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                if (checkFolderExist(folderPath)) {
                    val tmpFileName = getTmpFileName(originalFileName, createDate)
                    if (tmpFileName.isBlank()) {
                        setSaveResult(false, "getTmpFileName isBlank")
                    } else {
                        if (writeFileToFolder(viewModel, folderPath, getRealFileName(folderPath, tmpFileName))) {
                            updateMediaDatabase(BaseApplication.getAppContext(), folderPath)
                            setSaveResult(true)
                        } else {
                            setSaveResult(false, "setSaveTxtResult failed")
                        }
                    }
                } else {
                    DebugUtil.e(TAG, "checkFolderExist false >>")
                    setSaveResult(false, "checkFolderExist false")
                }
            } catch (exception: Exception) {
                DebugUtil.e(TAG, "saveTxtToLocal error>> ${exception.message}")
                setSaveResult(false, "saveTxtToLocal error!")
            }
        }
    }

    /**
     * 统一返回保存结果
     */
    suspend fun setSaveResult(success: Boolean, message: String = "") {
        if (needShowDialog) {
            val delay = System.currentTimeMillis() - mShowDialogDuration
            if (delay < 1000) {
                delay(1000)
            }
            needShowDialog = false
        }
        withContext(Dispatchers.Main) {
            if (success) {
                if (mSaveTxtToLocalFileName.isBlank() || mSaveTxtToLocalAbsPath.isBlank()) {
                    mCallback?.onSaveFailed(message)
                } else {
                    mCallback?.onSaveSuccess(mSaveTxtToLocalFileName, mSaveTxtToLocalAbsPath)
                }
            } else {
                mCallback?.onSaveFailed(message)
            }
        }
    }

    /**
     * 检查Documents/SoundRecordDoc/文件夹是否存在
     */
    private fun checkFolderExist(path: String): Boolean {
        return try {
            DebugUtil.e(TAG, "checkFolderExist path >> $path")
            val recorderFolder = File(path)
            if (!recorderFolder.exists()) {
                recorderFolder.mkdirs()
            }
            recorderFolder.exists()
        } catch (e: Exception) {
            DebugUtil.e(TAG, "checkFolderExist error>> ${e.message}")
            false
        }
    }

    /**
     * 获取保存到本地的文件名称  “标准录音-2021-10-26 20-45-33”-转文本结果
     */
    private fun getTmpFileName(originalFileName: String, createDate: String): String {
        return try {
            DebugUtil.i(TAG, "originalFileName >> $originalFileName, createDate >> $createDate")
            val saveFileName = BaseApplication.getAppContext().resources.getString(
                com.soundrecorder.common.R.string.export_save_file_name,
                "$originalFileName-$createDate"
            )
            DebugUtil.i(TAG, "getTmpFileName >> $saveFileName, length >> ${saveFileName.length}")
            saveFileName
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getTmpFileName error >> ${e.message}")
            ""
        }
    }

    /**
     * 获取保本到本地文件真正的名称
     * 有可能saveFileName已经存在，则在后面加上"_x"，x为数字，从1开始递增
     * 例如："标准录音-2021-10-26 20-57-33"-转文本结果_1
     */
    private fun getRealFileName(folderPath: String, saveFileName: String): String {
        val realName: String
        val fileExtension = ".txt"
        var number = 1
        var file = File(folderPath + saveFileName + fileExtension)
        if (!file.exists()) {
            return saveFileName
        }
        //todo  是否合理
        while (true) {
            file = File(folderPath + saveFileName + "_" + number + fileExtension)
            if (!file.exists()) {
                realName = file.nameWithoutExtension
                break
            }
            number++
        }
        DebugUtil.i(TAG, "getRealFileName > $realName")
        return realName
    }

    /**
     * 将转文本内容写入到文件
     * @param fileName 文件名称，不包含扩展名
     */
    private fun writeFileToFolder(viewModel: ShareWithTxtViewModel, folderPath: String, fileName: String): Boolean {
        DebugUtil.i(TAG, "writeFileToFolder fullFileName> $fileName")
        if (fileName.isBlank() || folderPath.isBlank()) {
            DebugUtil.e(TAG, "writeFileToFolder fullFileName is empty or blank")
            return false
        }
        try {
            val absPath = "$folderPath$fileName.txt"
            DebugUtil.i(TAG, "writeFileToFolder absPath> $absPath")
            DebugUtil.i(TAG, "mShowLineLiveData > ${viewModel.mTitleLiveData}")
            mSaveTxtToLocalAbsPath = absPath
            mSaveTxtToLocalFileName = fileName
            mCallback?.onGetFileName(fileName, absPath)
            //获取txt文本信息
            val txtString = getTxtFileContent(viewModel)
            //写入文件
            return doWriteFile(absPath, txtString)
        } catch (exception: Exception) {
            DebugUtil.e(TAG, "writeFileToFolder exception > ${exception.message}")
        }
        return false
    }

    /**
     * 获取保存到本地文件的文本内容
     * 将标题、时间、主题、参会人、讲话内容等信息按格式组合
     */
    private fun getTxtFileContent(viewModel: ShareWithTxtViewModel): String {
        val lineSeparator = System.lineSeparator()
        val txtString = StringBuilder()
        try {
            txtString.append("${viewModel.mTitleLiveData.value}$lineSeparator$lineSeparator")
            txtString.append("${viewModel.mDateLiveData.value}$lineSeparator")
            txtString.append("${viewModel.mSubjectLiveData.value}$lineSeparator")

            val showLine = viewModel.mShowLineLiveData.value == true
            //显示分段的情况
            if (showLine) {
                val showDate = viewModel.mShowDateLiveData.value == true
                val showSpeaker = viewModel.getCanShowSpeakerRole() && viewModel.mShowSpeakerLiveData.value == true
                if (showSpeaker) {
                    //添加参会人信息
                    txtString.append("${viewModel.mRolesStringLiveData.value}$lineSeparator")
                }
                txtString.append(lineSeparator)
                //添加讲话人、时间和讲话内容
                viewModel.mItemsListLiveData.value?.forEach {
                    if (showSpeaker) {
                        txtString.append(it.roleName)
                        if (showDate) {
                            txtString.append("  ")
                        } else {
                            txtString.append(lineSeparator)
                        }
                    }
                    if (showDate) {
                        txtString.append("${it.startTime.durationInMsFormatTimeExclusive()}$lineSeparator")
                    }
                    txtString.append("${it.textContent}$lineSeparator$lineSeparator")
                }
            } else {
                //不显示分段
                txtString.append("$lineSeparator${viewModel.mContentStringLiveData.value}")
            }
            return txtString.toString()
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getTxtFileContent exception > ${e.message}")
        }
        return ""
    }

    /**
     * 写入文本到本地文件
     */
    private fun doWriteFile(absPath: String, txtString: String): Boolean {
        var fileOutputStream: FileOutputStream? = null
        try {
            fileOutputStream = FileOutputStream(absPath)
            fileOutputStream.write(txtString.toByteArray())
            return true
        } catch (exception: Exception) {
            DebugUtil.e(TAG, "fileOutputStream exception > ${exception.message}")
        } finally {
            try {
                fileOutputStream?.close()
            } catch (exception: Exception) {
                DebugUtil.e(TAG, "close fileOutputStream exception > ${exception.message}")
            }
        }
        return false
    }

    /**
     * 保存文件完成之后更新媒体库
     */
    fun updateMediaDatabase(context: Context, saveTxtFolder: String) {
        //R上更新媒体库方法,
        //todo 测试Q手机
        try {
            if (saveTxtFolder.isBlank()) {
                return
            }
            val paths = arrayOf(saveTxtFolder)
            MediaScannerConnection.scanFile(context, paths, null, null)
        } catch (ex: Exception) {
            DebugUtil.e(TAG, "internalMediaScanner :$saveTxtFolder, error: $ex")
        }
    }
}