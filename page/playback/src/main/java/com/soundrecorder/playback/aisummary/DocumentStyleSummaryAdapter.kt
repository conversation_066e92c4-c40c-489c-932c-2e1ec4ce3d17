/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        : DocumentStyleSummaryAdapter
 * * Description : 文档风格的摘要内容适配器
 * * Version     : 1.0
 * * Date        : 2025/06/03
 * * Author      : Assistant
 ***********************************************************************/
package com.soundrecorder.playback.aisummary

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.playback.R

/**
 * 文档风格的摘要内容适配器
 * 用于显示结构化的摘要内容，类似文档格式
 */
class DocumentStyleSummaryAdapter : ListAdapter<DocumentSummaryItem, DocumentStyleSummaryAdapter.ViewHolder>(DocumentSummaryDiffCallback()) {

    companion object {
        private const val TAG = "DocumentStyleSummaryAdapter"
        
        // 视图类型
        private const val TYPE_TITLE = 1
        private const val TYPE_BULLET_POINT = 2
        private const val TYPE_CONTENT = 3
        private const val TYPE_SECTION_HEADER = 4
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position).type) {
            DocumentSummaryItem.Type.TITLE -> TYPE_TITLE
            DocumentSummaryItem.Type.BULLET_POINT -> TYPE_BULLET_POINT
            DocumentSummaryItem.Type.CONTENT -> TYPE_CONTENT
            DocumentSummaryItem.Type.SECTION_HEADER -> TYPE_SECTION_HEADER
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val layoutId = when (viewType) {
            TYPE_TITLE -> R.layout.item_summary_title
            TYPE_BULLET_POINT -> R.layout.item_summary_bullet
            TYPE_CONTENT -> R.layout.item_summary_content
            TYPE_SECTION_HEADER -> R.layout.item_summary_section
            else -> R.layout.item_summary_content
        }
        
        val view = LayoutInflater.from(parent.context).inflate(layoutId, parent, false)
        return ViewHolder(view, viewType)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    /**
     * ViewHolder
     */
    class ViewHolder(itemView: View, private val viewType: Int) : RecyclerView.ViewHolder(itemView) {
        
        private val textContent: TextView = itemView.findViewById(R.id.text_content)
        private val textNumber: TextView? = itemView.findViewById(R.id.text_number)
        
        fun bind(item: DocumentSummaryItem) {
            when (viewType) {
                TYPE_TITLE -> {
                    textContent.text = item.content
                }
                TYPE_BULLET_POINT -> {
                    textContent.text = item.content
                    textNumber?.text = "•"
                }
                TYPE_CONTENT -> {
                    textContent.text = item.content
                }
                TYPE_SECTION_HEADER -> {
                    textContent.text = item.content
                    textNumber?.text = "${item.number}."
                }
            }
        }
    }
}

/**
 * DiffUtil回调
 */
class DocumentSummaryDiffCallback : DiffUtil.ItemCallback<DocumentSummaryItem>() {
    
    override fun areItemsTheSame(oldItem: DocumentSummaryItem, newItem: DocumentSummaryItem): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: DocumentSummaryItem, newItem: DocumentSummaryItem): Boolean {
        return oldItem == newItem
    }
}

/**
 * 文档摘要项数据类
 */
data class DocumentSummaryItem(
    val id: String,
    val type: Type,
    val content: String,
    val number: Int = 0,
    val level: Int = 0
) {
    enum class Type {
        TITLE,          // 标题
        SECTION_HEADER, // 章节标题（如：1. 关键建议）
        BULLET_POINT,   // 项目符号
        CONTENT         // 普通内容
    }
    
    companion object {
        /**
         * 解析摘要内容为文档项列表
         */
        fun parseContent(content: String): List<DocumentSummaryItem> {
            val items = mutableListOf<DocumentSummaryItem>()
            val lines = content.split("\n")
            
            var itemId = 0
            
            for (line in lines) {
                val trimmedLine = line.trim()
                if (trimmedLine.isEmpty()) continue
                
                when {
                    // 检测数字标题（如：1. 关键建议）
                    trimmedLine.matches(Regex("^\\d+\\.\\s*.*")) -> {
                        val number = trimmedLine.substringBefore(".").toIntOrNull() ?: 0
                        val text = trimmedLine.substringAfter(".").trim()
                        items.add(
                            DocumentSummaryItem(
                                id = "section_${itemId++}",
                                type = Type.SECTION_HEADER,
                                content = text,
                                number = number
                            )
                        )
                    }
                    // 检测项目符号（以 • 或 - 开头）
                    trimmedLine.startsWith("•") || trimmedLine.startsWith("-") -> {
                        val text = trimmedLine.substring(1).trim()
                        items.add(
                            DocumentSummaryItem(
                                id = "bullet_${itemId++}",
                                type = Type.BULLET_POINT,
                                content = text
                            )
                        )
                    }
                    // 普通内容
                    else -> {
                        items.add(
                            DocumentSummaryItem(
                                id = "content_${itemId++}",
                                type = Type.CONTENT,
                                content = trimmedLine
                            )
                        )
                    }
                }
            }
            return items
        }
    }
}
