/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ContainerBottomButtonHelper
 * Description:
 * Version: 1.0
 * Date: 2023/8/15
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/8/15 1.0 create
 */

package com.soundrecorder.playback

import android.view.Gravity
import android.view.ViewGroup.MarginLayoutParams
import android.widget.FrameLayout
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.playback.databinding.IncludeActivityBottomButtonBinding

class ContainerBottomButtonHelper {
    private val logTag = "ContainerBottomButtonHelper"
    companion object {
        const val FLOAT_5 = 5F
        const val FLOAT_4 = 4F
    }

    fun setPlaybackBottomViewPosition(bottomView: IncludeActivityBottomButtonBinding?, hasSummary: Boolean, supportConvert: Boolean) {
        val binding = bottomView ?: return
        if (!binding.layoutMarkListActivity.isVisible) {
            return
        }
        // 支持转文本，底部就有4个按钮，否则3个按钮
        val showCount = if (supportConvert && hasSummary) {
            NumberConstant.NUM_5
        } else if (!supportConvert && !hasSummary) {
            NumberConstant.NUM_3
        } else {
            NumberConstant.NUM_4
        }
        binding.markLayout.weightSum = if (showCount == NumberConstant.NUM_5) {
            FLOAT_5
        } else {
            FLOAT_4
        }
        when (showCount) {
            NumberConstant.NUM_4 -> {
                binding.viewTransferTextActivity.updateLayoutParams<FrameLayout.LayoutParams> {
                    gravity = Gravity.START
                }
                val parentWidth = binding.layoutTransferTextActivity.width
                val viewWidth = binding.viewTransferTextActivity.width
                val offsetX = if (viewWidth < parentWidth) {
                    (parentWidth - viewWidth) / NumberConstant.NUM_2
                } else 0
                binding.tvMarkActivity.updateLayoutParams<MarginLayoutParams> {
                    marginEnd = offsetX / NumberConstant.NUM_2
                }
                DebugUtil.i(logTag, "setPlaybackBottomViewPosition,offsetX=$offsetX")
            }
            NumberConstant.NUM_3 -> {
                binding.tvMarkActivity.updateLayoutParams<FrameLayout.LayoutParams> {
                    gravity = Gravity.START
                }
            }
            else -> {
                binding.tvMarkActivity.updateLayoutParams<FrameLayout.LayoutParams> {
                    gravity = Gravity.CENTER
                }
                binding.viewTransferTextActivity.updateLayoutParams<FrameLayout.LayoutParams> {
                    gravity = Gravity.CENTER
                }
            }
        }
    }

    fun setConvertBottomViewPosition(
        bottomView: IncludeActivityBottomButtonBinding?,
        showRoleSwitch: Boolean,
        hasSummary: Boolean
    ) {
        val binding = bottomView ?: return
        if (!binding.layoutConvertExportActivity.isVisible) {
            return
        }
        DebugUtil.i(logTag, "setConvertBottomViewPosition,showRoleSwitch=$showRoleSwitch")
        if (!hasSummary) {
            if (showRoleSwitch) {
                val offsetX =
                    (binding.layoutConvertRoleActivity.width - binding.tvConvertRoleActivity.width) / NumberConstant.NUM_2
                DebugUtil.i(logTag, "setConvertBottomViewPosition,offsetX=$offsetX")
                binding.tvConvertRoleActivity.updateLayoutParams<FrameLayout.LayoutParams> {
                    gravity = Gravity.START
                }
                binding.tvConvertSearchActivity.updateLayoutParams<FrameLayout.LayoutParams> {
                    gravity = Gravity.CENTER
                    marginEnd = offsetX / NumberConstant.NUM_2
                }
            } else {
                binding.tvConvertSearchActivity.updateLayoutParams<FrameLayout.LayoutParams> {
                    gravity = Gravity.START
                    marginEnd = 0
                }
            }
        } else {
            binding.tvConvertRoleActivity.updateLayoutParams<FrameLayout.LayoutParams> {
                gravity = Gravity.CENTER
            }
            binding.tvConvertSearchActivity.updateLayoutParams<FrameLayout.LayoutParams> {
                gravity = Gravity.CENTER
            }
        }
    }
}