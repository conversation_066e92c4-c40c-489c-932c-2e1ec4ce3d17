package com.soundrecorder.playback.aisummary

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.playback.R

/**
 * 摘要项类型枚举 - 极简版本，只保留显示相关功能
 */
enum class SummaryItemType(val displayName: String) {
    CONTENT("内容");

    companion object {
        /**
         * 根据文本内容判断类型 - 简化逻辑
         */
        fun fromContent(content: String): SummaryItemType {
            return CONTENT
        }

        /**
         * 解析内容，去除前缀 - 简化逻辑
         */
        fun parseContent(content: String): String {
            return when {
                content.startsWith("主题：") -> content.substring(3).trim()
                content.startsWith("标题：") -> content.substring(3).trim()
                content.startsWith("要点：") -> content.substring(3).trim()
                content.startsWith("关键点：") -> content.substring(4).trim()
                content.startsWith("总结：") -> content.substring(3).trim()
                content.startsWith("摘要：") -> content.substring(3).trim()
                else -> content.trim()
            }
        }
    }
}

/**
 * 摘要项数据类 - 极简版本
 */
data class SummaryItem(
    val type: SummaryItemType,
    val content: String
) {
    companion object {
        /**
         * 从原始文本创建SummaryItem - 简化逻辑
         */
        fun fromRawContent(rawContent: String): SummaryItem {
            val type = SummaryItemType.fromContent(rawContent)
            val content = SummaryItemType.parseContent(rawContent)
            return SummaryItem(type, content)
        }

        /**
         * 批量解析摘要内容 - 简化逻辑
         */
        fun parseContent(content: String): List<SummaryItem> {
            return content.split("\n")
                .filter { it.isNotBlank() }
                .map { line -> fromRawContent(line) }
        }
    }
}

/**
 * 摘要内容适配器
 * 专注于摘要内容的显示，遵循单一职责原则
 */
class SummaryContentAdapter : ListAdapter<SummaryItem, SummaryContentAdapter.SummaryViewHolder>(SummaryItemDiffCallback()) {

    companion object {
        private const val TAG = "SummaryContentAdapter"
    }

    /**
     * 更新数据 - 保持向后兼容
     */
    @SuppressLint("NotifyDataSetChanged")
    fun updateData(items: List<SummaryItem>) {
        DebugUtil.d(TAG, "updateData: ${items.size} items")
        submitList(items.toList())
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SummaryViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(android.R.layout.simple_list_item_2, parent, false)
        return SummaryViewHolder(view)
    }

    override fun onBindViewHolder(holder: SummaryViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    /**
     * ViewHolder - 负责单个摘要项的显示
     */
    class SummaryViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        private val titleText: TextView = itemView.findViewById(android.R.id.text1)
        private val contentText: TextView = itemView.findViewById(android.R.id.text2)

        fun bind(item: SummaryItem) {
            titleText.text = item.type.displayName
            contentText.text = item.content

            // 应用样式
            applyItemStyle(item.type)
        }

        /**
         * 应用项目样式
         */
        private fun applyItemStyle(type: SummaryItemType) {
            when (type) {
                SummaryItemType.CONTENT -> {
                    titleText.setTextColor(0xFF757575.toInt())
                    contentText.textSize = 13f
                    contentText.setTextColor(0xFF212121.toInt())
                }
            }
        }
    }
}

/**
 * DiffUtil回调 - 用于高效的列表更新
 */
class SummaryItemDiffCallback : DiffUtil.ItemCallback<SummaryItem>() {

    override fun areItemsTheSame(oldItem: SummaryItem, newItem: SummaryItem): Boolean {
        return oldItem.type == newItem.type && oldItem.content == newItem.content
    }

    override fun areContentsTheSame(oldItem: SummaryItem, newItem: SummaryItem): Boolean {
        return oldItem == newItem
    }
}
