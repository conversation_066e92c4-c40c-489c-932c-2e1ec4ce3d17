/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryDataBaseManager
 * Description: AISummaryDataBaseManager
 * Version: 1.0
 * Date: 2025/5/14
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/14      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.playback.aisummary

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.convertservice.aisummary.database.SummaryCacheDBHelper
import com.soundrecorder.convertservice.aisummary.database.SummaryCacheEntity
import com.soundrecorder.convertservice.aisummary.database.SummaryCacheRowContent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 摘要数据仓库
 * 封装对SummaryCacheDBHelper的调用
 */
class AISummaryDataManager {

    companion object {
        private const val TAG = "SummaryRepository"

        @Volatile
        private var INSTANCE: AISummaryDataManager? = null
        fun getInstance(): AISummaryDataManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AISummaryDataManager().also { INSTANCE = it }
            }
        }
    }

    private val summaryCacheDBHelper by lazy {
        SummaryCacheDBHelper(BaseApplication.getAppContext())
    }

    /**
     * 保存摘要到数据库 - 实现10条摘要限制
     * 单个录音文件最多保存10次摘要数据，第11条数据生成后替换第1条数据
     */
    suspend fun saveSummary(recordId: Long, summaryContent: String, summaryStyle: Int = -1): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 通过recordId获取文件路径
                val filePath = getFilePathByRecordId(recordId)
                if (filePath.isNullOrEmpty()) {
                    DebugUtil.e(TAG, "saveSummary: cannot find filePath, recordId=$recordId")
                    return@withContext false
                }

                val currentTime = System.currentTimeMillis()
                val record = getRecordByRecordId(recordId)
                val recordType = record?.recordType ?: 0

                // 创建新的摘要记录
                val newRowContent = SummaryCacheRowContent(
                    filePath = filePath,
                    timeStamp = currentTime,
                    chooseState = -1, // 默认非选中
                    recordType = recordType,
                    summaryStyle = summaryStyle
                )

                // 添加新摘要记录
                val addSuccess = summaryCacheDBHelper.addSummaryCache(newRowContent)
                if (!addSuccess) {
                    DebugUtil.e(TAG, "saveSummary: failed to add new summary, recordId=$recordId")
                    return@withContext false
                }

                // 查询刚插入的记录并更新摘要内容
                val newEntities = summaryCacheDBHelper.getSummaryCacheByFilePathOrderByTime(filePath)
                if (newEntities.isNotEmpty()) {
                    val newEntity = newEntities.first() // 最新的记录
                    newEntity.summaryContent = summaryContent
                    val updateSuccess = summaryCacheDBHelper.updateSummaryCache(newEntity)
                    if (!updateSuccess) {
                        DebugUtil.e(TAG, "saveSummary: failed to update summary content, recordId=$recordId")
                        return@withContext false
                    }
                }

                // 维护最大摘要数量限制
                val deletedCount = summaryCacheDBHelper.maintainMaxSummaryCount(filePath, 5)
                if (deletedCount > 0) {
                    DebugUtil.d(TAG, "saveSummary: maintained max count, deleted $deletedCount old summaries for recordId=$recordId")
                }

                DebugUtil.d(TAG, "saveSummary: successfully saved summary for recordId=$recordId, deletedOld=$deletedCount")
                true
            } catch (e: Exception) {
                DebugUtil.e(TAG, "saveSummary: failed for recordId=$recordId, error=${e.message}")
                false
            }
        }
    }

    /**
     * 从数据库获取摘要 - 返回最新的摘要
     */
    suspend fun getSummary(recordId: Long): SummaryEntity? {
        return withContext(Dispatchers.IO) {
            try {
                // 通过recordId获取文件路径
                val filePath = getFilePathByRecordId(recordId)
                if (filePath.isNullOrEmpty()) {
                    DebugUtil.d(TAG, "getSummary: cannot find filePath for recordId=$recordId")
                    return@withContext null
                }

                // 按时间排序查询摘要，获取最新的摘要
                val entities = summaryCacheDBHelper.getSummaryCacheByFilePathOrderByTime(filePath)
                if (entities.isNotEmpty()) {
                    val latestEntity = entities.first() // 最新的摘要
                    if (!latestEntity.summaryContent.isNullOrEmpty()) {
                        DebugUtil.d(TAG, "getSummary: found latest summary for recordId=$recordId")
                        return@withContext SummaryEntity.fromSummaryCacheEntity(latestEntity, recordId)
                    }
                }

                DebugUtil.d(TAG, "getSummary: no summary found for recordId=$recordId")
                null
            } catch (e: Exception) {
                DebugUtil.e(TAG, "getSummary: failed for recordId=$recordId, error=${e.message}")
                null
            }
        }
    }

    /**
     * 获取指定录音文件的所有摘要历史（按时间排序，最新的在前）
     */
    suspend fun getSummaryHistory(recordId: Long): List<SummaryEntity> {
        return withContext(Dispatchers.IO) {
            try {
                // 通过recordId获取文件路径
                val filePath = getFilePathByRecordId(recordId)
                if (filePath.isNullOrEmpty()) {
                    DebugUtil.d(
                        TAG,
                        "getSummaryHistory: cannot find filePath for recordId=$recordId"
                    )
                    return@withContext emptyList()
                }

                // 按时间排序查询所有摘要
                val entities = summaryCacheDBHelper.getSummaryCacheByFilePathOrderByTime(filePath)
                val summaries = entities.filter { !it.summaryContent.isNullOrEmpty() }
                    .map { SummaryEntity.fromSummaryCacheEntity(it, recordId) }

                DebugUtil.d(
                    TAG,
                    "getSummaryHistory: found ${summaries.size} summaries for recordId=$recordId"
                )
                summaries
            } catch (e: Exception) {
                DebugUtil.e(
                    TAG,
                    "getSummaryHistory: failed for recordId=$recordId, error=${e.message}"
                )
                emptyList()
            }
        }
    }

    /**
     * 删除摘要 - 删除指定录音文件的所有摘要
     */
    suspend fun deleteSummary(recordId: Long): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 通过recordId获取文件路径
                val filePath = getFilePathByRecordId(recordId)
                if (filePath.isNullOrEmpty()) {
                    DebugUtil.d(TAG, "deleteSummary: cannot find filePath for recordId=$recordId")
                    return@withContext false
                }

                // 删除摘要缓存 - 使用convertservice模块的新方法
                val success = summaryCacheDBHelper.deleteSummaryCacheByFilePath(filePath)
                DebugUtil.d(
                    TAG,
                    "deleteSummary: deleted all summaries for recordId=$recordId, success=$success"
                )
                success
            } catch (e: Exception) {
                DebugUtil.e(TAG, "deleteSummary: failed for recordId=$recordId, error=${e.message}")
                false
            }
        }
    }

    /**
     * 检查摘要是否存在 - 使用现有SummaryCacheDatabase
     */
    suspend fun hasSummary(recordId: Long): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val summary = getSummary(recordId)
                val hasContent = summary != null && summary.summaryContent.isNotEmpty()
                DebugUtil.d(TAG, "hasSummary: recordId=$recordId, hasContent=$hasContent")
                hasContent
            } catch (e: Exception) {
                DebugUtil.e(TAG, "hasSummary: failed for recordId=$recordId, error=${e.message}")
                false
            }
        }
    }

    /**
     * 获取所有摘要 - 使用现有SummaryCacheDatabase
     */
    suspend fun getAllSummaries(): List<SummaryEntity> {
        return withContext(Dispatchers.IO) {
            try {
                val allEntities = summaryCacheDBHelper.getAllSummaryCacheList()
                val summaries = mutableListOf<SummaryEntity>()

                allEntities.forEach { entity ->
                    if (!entity.summaryContent.isNullOrEmpty()) {
                        // 通过文件路径获取recordId
                        val recordId = getRecordIdByFilePath(entity.filePath)
                        if (recordId > 0) {
                            summaries.add(SummaryEntity.fromSummaryCacheEntity(entity, recordId))
                        }
                    }
                }

                // 按时间排序
                summaries.sortByDescending { it.updateTime }

                DebugUtil.d(TAG, "getAllSummaries: found ${summaries.size} summaries")
                summaries
            } catch (e: Exception) {
                DebugUtil.e(TAG, "getAllSummaries: failed, error=${e.message}")
                emptyList()
            }
        }
    }

    /**
     * 通过recordId获取文件路径
     */
    private suspend fun getFilePathByRecordId(recordId: Long): String? {
        return withContext(Dispatchers.IO) {
            try {
                val record = MediaDBUtils.queryRecordById(recordId)
                val filePath = record?.data
                DebugUtil.d(TAG, "getFilePathByRecordId: recordId=$recordId, filePath=$filePath")
                filePath
            } catch (e: Exception) {
                DebugUtil.e(TAG, "getFilePathByRecordId: failed for recordId=$recordId, error=${e.message}")
                null
            }
        }
    }

    /**
     * 通过文件路径获取recordId
     */
    private suspend fun getRecordIdByFilePath(filePath: String): Long {
        return withContext(Dispatchers.IO) {
            try {
                val recordId = MediaDBUtils.queryIdByData(filePath)
                DebugUtil.d(TAG, "getRecordIdByFilePath: filePath=$filePath, recordId=$recordId")
                recordId
            } catch (e: Exception) {
                DebugUtil.e(
                    TAG,
                    "getRecordIdByFilePath: failed for filePath=$filePath, error=${e.message}"
                )
                -1L
            }
        }
    }

    /**
     * 通过recordId获取录音记录
     */
    private suspend fun getRecordByRecordId(recordId: Long): Record? {
        return withContext(Dispatchers.IO) {
            try {
                MediaDBUtils.queryRecordById(recordId)
            } catch (e: Exception) {
                DebugUtil.e(TAG, "getRecordByRecordId: failed for recordId=$recordId, error=${e.message}")
                null
            }
        }
    }

    /**
     * 摘要数据实体 - 适配现有SummaryCacheEntity
     */
    data class SummaryEntity(
        val recordId: Long,
        val filePath: String,
        val summaryContent: String,
        val summaryStyle: Int = -1,
        val recordType: Int = 0,
        val createTime: Long = System.currentTimeMillis(),
        val updateTime: Long = System.currentTimeMillis()
    ) {
        companion object {
            fun fromSummaryCacheEntity(entity: SummaryCacheEntity, recordId: Long): SummaryEntity {
                return SummaryEntity(
                    recordId = recordId,
                    filePath = entity.filePath,
                    summaryContent = entity.summaryContent ?: "",
                    summaryStyle = entity.summaryStyle,
                    recordType = entity.recordType,
                    createTime = entity.timeStamp,
                    updateTime = entity.timeStamp
                )
            }
        }
    }
}
