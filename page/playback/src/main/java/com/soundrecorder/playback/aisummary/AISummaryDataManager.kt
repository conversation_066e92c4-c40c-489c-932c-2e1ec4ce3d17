/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryDataManager
 * Description: AI摘要数据仓库，负责数据层操作
 * Version: 1.0
 * Date: 2025/5/14
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/14      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.playback.aisummary

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.convertservice.aisummary.database.SummaryCacheDBHelper
import com.soundrecorder.convertservice.aisummary.database.SummaryCacheEntity
import com.soundrecorder.convertservice.aisummary.database.SummaryCacheRowContent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * AI摘要数据仓库
 * 负责所有与摘要数据相关的数据库操作
 * 遵循Repository模式，为上层提供统一的数据访问接口
 */
class AISummaryDataManager private constructor() {

    companion object {
        private const val TAG = "AISummaryDataManager"
        private const val MAX_SUMMARY_COUNT = 10
        private const val SELECTED_STATE = 0
        private const val UNSELECTED_STATE = -1

        @Volatile
        private var INSTANCE: AISummaryDataManager? = null

        fun getInstance(): AISummaryDataManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AISummaryDataManager().also { INSTANCE = it }
            }
        }
    }

    private val summaryCacheDBHelper by lazy {
        SummaryCacheDBHelper(BaseApplication.getAppContext())
    }

    /**
     * 保存摘要到数据库
     * 新生成的摘要自动设置为选中状态，最多保存5条摘要记录
     * @param recordId 录音ID
     * @param summaryContent 摘要内容
     * @param summaryStyle 摘要风格
     * @return 保存是否成功
     */
    suspend fun saveSummary(recordId: Long, summaryContent: String, summaryStyle: Int = -1): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val filePath = getFilePathByRecordId(recordId)
                if (filePath.isNullOrEmpty()) {
                    DebugUtil.e(TAG, "saveSummary failed: cannot find filePath for recordId=$recordId")
                    return@withContext false
                }

                val currentTime = System.currentTimeMillis()
                val record = getRecordByRecordId(recordId)
                val recordType = record?.recordType ?: 0

                // 清除所有现有摘要的选中状态
                clearAllSelectedStates(filePath)

                // 创建新的摘要记录（设置为选中状态）
                val newRowContent = SummaryCacheRowContent(
                    filePath = filePath,
                    timeStamp = currentTime,
                    chooseState = SELECTED_STATE,
                    recordType = recordType,
                    summaryStyle = summaryStyle
                )

                // 保存新摘要记录
                val success = addSummaryRecord(newRowContent, summaryContent)
                if (!success) {
                    DebugUtil.e(TAG, "saveSummary failed: cannot add new summary for recordId=$recordId")
                    return@withContext false
                }

                // 维护最大摘要数量限制
                maintainMaxSummaryCount(filePath)

                DebugUtil.d(TAG, "saveSummary success: recordId=$recordId")
                true
            } catch (e: Exception) {
                DebugUtil.e(TAG, "saveSummary exception: recordId=$recordId, error=${e.message}")
                false
            }
        }
    }

    /**
     * 清除所有摘要的选中状态
     */
    private fun clearAllSelectedStates(filePath: String) {
        val existingEntities = summaryCacheDBHelper.getSummaryCacheByFilePathOrderByTime(filePath)
        existingEntities.forEach { entity ->
            entity.chooseState = UNSELECTED_STATE
        }
        if (existingEntities.isNotEmpty()) {
            summaryCacheDBHelper.updateSummaryCache(existingEntities)
        }
    }

    /**
     * 添加摘要记录并更新内容
     */
    private fun addSummaryRecord(rowContent: SummaryCacheRowContent, summaryContent: String): Boolean {
        val addSuccess = summaryCacheDBHelper.addSummaryCache(rowContent)
        if (!addSuccess) return false

        val newEntities = summaryCacheDBHelper.getSummaryCacheByFilePathOrderByTime(rowContent.filePath)
        if (newEntities.isNotEmpty()) {
            val newEntity = newEntities.first()
            newEntity.summaryContent = summaryContent
            return summaryCacheDBHelper.updateSummaryCache(newEntity)
        }
        return false
    }

    /**
     * 维护最大摘要数量限制
     */
    private fun maintainMaxSummaryCount(filePath: String) {
        val deletedCount = summaryCacheDBHelper.maintainMaxSummaryCount(filePath, MAX_SUMMARY_COUNT)
        if (deletedCount > 0) {
            DebugUtil.d(TAG, "maintainMaxSummaryCount: deleted $deletedCount old summaries")
        }
    }

    /**
     * 获取最新的摘要
     *
     * @param recordId 录音ID
     * @return 最新的摘要实体，如果不存在则返回null
     */
    suspend fun getLatestSummary(recordId: Long): SummaryEntity? {
        return withContext(Dispatchers.IO) {
            try {
                val filePath = getFilePathByRecordId(recordId) ?: return@withContext null
                val entities = getSummaryEntitiesByFilePath(filePath)

                val latestEntity = entities.firstOrNull()
                if (latestEntity != null && !latestEntity.summaryContent.isNullOrEmpty()) {
                    DebugUtil.d(TAG, "getLatestSummary: found for recordId=$recordId")
                    return@withContext SummaryEntity.fromSummaryCacheEntity(latestEntity, recordId)
                }

                DebugUtil.d(TAG, "getLatestSummary: not found for recordId=$recordId")
                null
            } catch (e: Exception) {
                DebugUtil.e(TAG, "getLatestSummary exception: recordId=$recordId, error=${e.message}")
                null
            }
        }
    }

    /**
     * 获取摘要历史版本列表
     *
     * @param recordId 录音ID
     * @return 摘要历史列表，按时间排序（最新的在前）
     */
    suspend fun getSummaryHistory(recordId: Long): List<SummaryEntity> {
        return withContext(Dispatchers.IO) {
            try {
                val filePath = getFilePathByRecordId(recordId) ?: return@withContext emptyList()
                val entities = getSummaryEntitiesByFilePath(filePath)

                val summaries = entities
                    .filter { !it.summaryContent.isNullOrEmpty() }
                    .map { SummaryEntity.fromSummaryCacheEntity(it, recordId) }

                DebugUtil.d(TAG, "getSummaryHistory: found ${summaries.size} summaries for recordId=$recordId")
                summaries
            } catch (e: Exception) {
                DebugUtil.e(TAG, "getSummaryHistory exception: recordId=$recordId, error=${e.message}")
                emptyList()
            }
        }
    }

    /**
     * 获取文件路径对应的摘要实体列表
     */
    private fun getSummaryEntitiesByFilePath(filePath: String): List<SummaryCacheEntity> {
        return summaryCacheDBHelper.getSummaryCacheByFilePathOrderByTime(filePath)
    }

    /**
     * 获取当前选中的摘要
     * 如果没有选中的摘要，返回最新的摘要
     *
     * @param recordId 录音ID
     * @return 选中的摘要实体，如果不存在则返回null
     */
    suspend fun getSelectedSummary(recordId: Long): SummaryEntity? {
        return withContext(Dispatchers.IO) {
            try {
                val filePath = getFilePathByRecordId(recordId) ?: return@withContext null
                val entities = getSummaryEntitiesByFilePath(filePath)

                // 查找选中状态的摘要
                val selectedEntity = entities.find {
                    it.chooseState == SELECTED_STATE && !it.summaryContent.isNullOrEmpty()
                }

                if (selectedEntity != null) {
                    DebugUtil.d(TAG, "getSelectedSummary: found selected for recordId=$recordId")
                    return@withContext SummaryEntity.fromSummaryCacheEntity(selectedEntity, recordId)
                }

                // 如果没有选中的摘要，返回最新的摘要
                val latestEntity = entities.firstOrNull { !it.summaryContent.isNullOrEmpty() }
                if (latestEntity != null) {
                    DebugUtil.d(TAG, "getSelectedSummary: returning latest for recordId=$recordId")
                    return@withContext SummaryEntity.fromSummaryCacheEntity(latestEntity, recordId)
                }

                DebugUtil.d(TAG, "getSelectedSummary: not found for recordId=$recordId")
                null
            } catch (e: Exception) {
                DebugUtil.e(TAG, "getSelectedSummary exception: recordId=$recordId, error=${e.message}")
                null
            }
        }
    }

    /**
     * 设置摘要的选中状态
     *
     * @param recordId 录音ID
     * @param summaryIndex 摘要索引（0表示最新）
     * @return 设置是否成功
     */
    suspend fun setSelectedSummary(recordId: Long, summaryIndex: Int): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val filePath = getFilePathByRecordId(recordId) ?: return@withContext false
                val entities = getSummaryEntitiesByFilePath(filePath)
                val validEntities = entities.filter { !it.summaryContent.isNullOrEmpty() }

                if (!isValidIndex(summaryIndex, validEntities.size)) {
                    DebugUtil.e(TAG, "setSelectedSummary: invalid index $summaryIndex for recordId=$recordId")
                    return@withContext false
                }

                // 更新选中状态
                updateSelectionStates(validEntities, summaryIndex)

                // 批量更新数据库
                val success = summaryCacheDBHelper.updateSummaryCache(validEntities)
                DebugUtil.d(TAG, "setSelectedSummary: recordId=$recordId, index=$summaryIndex, success=$success")

                success
            } catch (e: Exception) {
                DebugUtil.e(TAG, "setSelectedSummary exception: recordId=$recordId, error=${e.message}")
                false
            }
        }
    }

    /**
     * 验证索引是否有效
     */
    private fun isValidIndex(index: Int, size: Int): Boolean {
        return index in 0 until size
    }

    /**
     * 更新选中状态
     */
    private fun updateSelectionStates(entities: List<SummaryCacheEntity>, selectedIndex: Int) {
        entities.forEachIndexed { index, entity ->
            entity.chooseState = if (index == selectedIndex) SELECTED_STATE else UNSELECTED_STATE
        }
    }

    /**
     * 删除指定录音的所有摘要
     *
     * @param recordId 录音ID
     * @return 删除是否成功
     */
    suspend fun deleteSummary(recordId: Long): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val filePath = getFilePathByRecordId(recordId) ?: return@withContext false
                val success = summaryCacheDBHelper.deleteSummaryCacheByFilePath(filePath)
                DebugUtil.d(TAG, "deleteSummary: recordId=$recordId, success=$success")
                success
            } catch (e: Exception) {
                DebugUtil.e(TAG, "deleteSummary exception: recordId=$recordId, error=${e.message}")
                false
            }
        }
    }

    /**
     * 检查是否存在摘要
     *
     * @param recordId 录音ID
     * @return 是否存在摘要
     */
    suspend fun hasSummary(recordId: Long): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val summary = getLatestSummary(recordId)
                val hasContent = summary != null && summary.summaryContent.isNotEmpty()
                DebugUtil.d(TAG, "hasSummary: recordId=$recordId, hasContent=$hasContent")
                hasContent
            } catch (e: Exception) {
                DebugUtil.e(TAG, "hasSummary exception: recordId=$recordId, error=${e.message}")
                false
            }
        }
    }

    /**
     * 获取所有摘要记录
     *
     * @return 所有摘要实体列表，按时间排序
     */
    suspend fun getAllSummaries(): List<SummaryEntity> {
        return withContext(Dispatchers.IO) {
            try {
                val allEntities = summaryCacheDBHelper.getAllSummaryCacheList()
                val summaries = allEntities
                    .filter { !it.summaryContent.isNullOrEmpty() }
                    .mapNotNull { entity ->
                        val recordId = getRecordIdByFilePath(entity.filePath)
                        if (recordId > 0) {
                            SummaryEntity.fromSummaryCacheEntity(entity, recordId)
                        } else null
                    }
                    .sortedByDescending { it.updateTime }

                DebugUtil.d(TAG, "getAllSummaries: found ${summaries.size} summaries")
                summaries
            } catch (e: Exception) {
                DebugUtil.e(TAG, "getAllSummaries exception: ${e.message}")
                emptyList()
            }
        }
    }

    /**
     * 通过recordId获取文件路径
     */
    private fun getFilePathByRecordId(recordId: Long): String? {
        return try {
            MediaDBUtils.queryRecordById(recordId)?.data
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getFilePathByRecordId exception: recordId=$recordId, error=${e.message}")
            null
        }
    }

    /**
     * 通过文件路径获取recordId
     */
    private fun getRecordIdByFilePath(filePath: String): Long {
        return try {
            MediaDBUtils.queryIdByData(filePath)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getRecordIdByFilePath exception: filePath=$filePath, error=${e.message}")
            -1L
        }
    }

    /**
     * 通过recordId获取录音记录
     */
    private fun getRecordByRecordId(recordId: Long): Record? {
        return try {
            MediaDBUtils.queryRecordById(recordId)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getRecordByRecordId exception: recordId=$recordId, error=${e.message}")
            null
        }
    }

    /**
     * 摘要数据实体
     * 封装摘要相关的所有信息
     */
    data class SummaryEntity(
        val id: Long = -1,
        val recordId: Long,
        val filePath: String,
        val summaryContent: String,
        val summaryStyle: Int = -1,
        val recordType: Int = 0,
        val createTime: Long = System.currentTimeMillis(),
        val updateTime: Long = System.currentTimeMillis(),
        val isSelected: Boolean = false
    ) {
        companion object {
            /**
             * 从数据库实体转换为业务实体
             */
            fun fromSummaryCacheEntity(entity: SummaryCacheEntity, recordId: Long): SummaryEntity {
                return SummaryEntity(
                    id = entity.id,
                    recordId = recordId,
                    filePath = entity.filePath,
                    summaryContent = entity.summaryContent ?: "",
                    summaryStyle = entity.summaryStyle,
                    recordType = entity.recordType,
                    createTime = entity.timeStamp,
                    updateTime = entity.timeStamp,
                    isSelected = entity.chooseState == SELECTED_STATE
                )
            }
        }
    }
}
