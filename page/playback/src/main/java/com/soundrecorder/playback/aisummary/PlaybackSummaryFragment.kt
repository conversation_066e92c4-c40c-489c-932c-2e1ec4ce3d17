/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  PlaybackSummaryFragment
 * * Description: 播放页面摘要Tab Fragment
 * * Version: 1.0
 * * Date : 2025/6/4
 * * Author: Assistant
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  Assistant    2025/6/4   1.0    build this module
 ****************************************************************/
package com.soundrecorder.playback.aisummary

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.modulerouter.BrowseFileAction
import com.soundrecorder.playback.PlaybackActivityViewModel
import com.soundrecorder.playback.databinding.FragmentPlaybackSummaryBinding
import com.soundrecorder.summary.RecordSummaryManager.SUMMARY_STATE_CLIENT_END
import com.soundrecorder.summary.RecordSummaryManager.SUMMARY_STATE_CLIENT_GENERATING

/**
 * 播放页面摘要Fragment
 * 显示AI摘要内容和状态
 * 实现IAISummaryManager接口以处理权限回调
 */
class PlaybackSummaryFragment : Fragment() {

    companion object {
        private const val TAG = "PlaybackSummaryFragment"
    }

    private var _binding: FragmentPlaybackSummaryBinding? = null
    private val binding get() = _binding!!

    private lateinit var mViewModel: PlaybackActivityViewModel
    private val summaryViewModel: AISummaryViewModel by viewModels()
    private var summaryAdapter: SummaryContentAdapter? = null
    private var isPageSelected = false
    private var currentMediaId: Long = -1
    private var currentSummaryContent: String? = null

    private var mAISummaryManagerImpl: AISummaryManagerImpl? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPlaybackSummaryBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        mViewModel = ViewModelProvider(requireActivity())[PlaybackActivityViewModel::class.java]

        initViews()
        initObservers()
        initAISummaryManagerImpl()
    }


    private fun initViews() {

        initRecyclerView()

        initGenerateButton()

        initReGenerateButton()

        initChangeButton()

    }

    private fun initRecyclerView() {
        // 初始化RecyclerView
        summaryAdapter = SummaryContentAdapter()
        binding.recyclerSummary.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = summaryAdapter
        }
    }

    private fun initGenerateButton() {
        // 生成摘要按钮点击事件 - 使用完整流程（包含权限检查、插件下载、文件检查）
        binding.btnGenerateSummary.setOnClickListener {
            summaryViewModel.generateSummaryWithManager(activity, mAISummaryManagerImpl)
        }
    }

    private fun initReGenerateButton() {
        // 重新生成按钮点击事件
        binding.btnRegenerateSummary.setOnClickListener {
            summaryViewModel.regenerateSummary()
        }
    }

    private fun initChangeButton() {
        // 版本切换按钮点击事件
        binding.btnPreviousSummary.setOnClickListener {
            summaryViewModel.switchToPreviousSummary()
        }

        binding.btnNextSummary.setOnClickListener {
            summaryViewModel.switchToNextSummary()
        }

        binding.btnLatestSummary.setOnClickListener {
            summaryViewModel.switchToLatestSummary()
        }
    }

    /**
     * 初始化AISummaryManagerImpl
     */
    private fun initAISummaryManagerImpl() {
        if (mAISummaryManagerImpl == null) {
            mAISummaryManagerImpl = AISummaryManagerImpl()
        }
        mViewModel.let {
            mAISummaryManagerImpl?.registerForAISummary(this, !it.mIsFromOtherApp)
        }
    }

    private fun initObservers() {
        DebugUtil.d(TAG, "initObservers: start")

        // 1. 观察SummaryViewModel的数据变化
        initSummaryViewModelObserver()

        // 2. 观察BrowseFileActivityViewModel的数据变化（主要数据源）
        initBrowseActivityViewModelObserver()

        // 3. 观察PlaybackActivityViewModel的摘要状态变化
        initViewModelObserver()

        // 4. 检查当前是否已经有有效的recordId
        checkCurrentRecordId()

        DebugUtil.d(TAG, "initObservers: completed")
    }

    /**
     * 观察SummaryViewModel的数据变化
     */
    private fun initSummaryViewModelObserver() {
        // 观察摘要状态
        summaryViewModel.mSummaryState.observe(viewLifecycleOwner) { state ->
            DebugUtil.d(TAG, "SummaryViewModel state changed: $state")
            updateSummaryState(state)
        }

        // 观察摘要项列表
        summaryViewModel.summaryItems.observe(viewLifecycleOwner) { items ->
            DebugUtil.d(TAG, "SummaryViewModel items changed: ${items.size}")
            summaryAdapter?.updateData(items)
        }

        // 观察错误信息
        summaryViewModel.mErrorMessage.observe(viewLifecycleOwner) { errorMsg ->
            if (errorMsg.isNotEmpty()) {
                DebugUtil.e(TAG, "SummaryViewModel error: $errorMsg")
                showErrorMessage(errorMsg)
            }
        }

        // 观察摘要内容
        summaryViewModel.summaryContent.observe(viewLifecycleOwner) { content ->
            if (content != null) {
                if (content.isNotEmpty()) {
                    DebugUtil.d(TAG, "SummaryViewModel content updated: ${content.length} chars")
                    updateSummaryInfo(content)
                }
            }
        }

        // 观察摘要历史版本
        summaryViewModel.summaryHistory.observe(viewLifecycleOwner) { history ->
            DebugUtil.d(TAG, "SummaryViewModel history updated: ${history.size} versions")
            updateVersionInfo(history)
        }

        // 观察当前版本索引
        summaryViewModel.mCurrentSummaryIndex.observe(viewLifecycleOwner) { index ->
            DebugUtil.d(TAG, "SummaryViewModel current index updated: $index")
            updateCurrentVersionIndex(index)
        }

        // 观察是否有多个版本
        summaryViewModel.mHasMultipleVersions.observe(viewLifecycleOwner) { hasMultiple ->
            DebugUtil.d(TAG, "SummaryViewModel multiple versions: $hasMultiple")
            updateVersionControlVisibility(hasMultiple)
        }
    }

    /**
     * 观察BrowseFileActivityViewModel的数据变化
     */
    private fun initBrowseActivityViewModelObserver() {
        try {
            val browseFileActivityViewModel = BrowseFileAction.getBrowseActivityViewModel(requireActivity() as AppCompatActivity)
            BrowseFileAction.getViewModelPlayData<StartPlayModel>(browseFileActivityViewModel)?.observe(viewLifecycleOwner) { startPlayModel ->
                DebugUtil.d(TAG, "BrowseFileActivityViewModel data changed: $startPlayModel")

                if (startPlayModel == null) {
                    DebugUtil.i(TAG, "play data to be null")
                    return@observe
                }

                if (startPlayModel.mediaId == currentMediaId) {
                    DebugUtil.i(TAG, "play data id not change, id=${startPlayModel.mediaId}")
                    return@observe
                }

                DebugUtil.i(TAG, "play record data changed to $startPlayModel")

                // 音频发生变化，处理recordId变化
                val newRecordId = startPlayModel.mediaId
                if (newRecordId > 0) {
                    handleRecordIdChange(newRecordId)
                }
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "Failed to observe BrowseFileActivityViewModel: ${e.message}")
        }
    }

    /**
     * 观察PlaybackActivityViewModel的相关状态
     */
    private fun initViewModelObserver() {
        // 观察摘要状态变化
        mViewModel.mSummaryStatus?.observe(viewLifecycleOwner) { summaryStatus ->
            DebugUtil.d(TAG, "Summary status changed: $summaryStatus")
            handleSummaryStatusChange(summaryStatus)
        }
    }

    /**
     * 检查当前是否已经有有效的recordId
     */
    private fun checkCurrentRecordId() {
        // 检查startPlayModel
        mViewModel.startPlayModel?.let { startPlayModel ->
            val recordId = startPlayModel.mediaId
            if (recordId > 0 && recordId != currentMediaId) {
                DebugUtil.d(TAG, "checkCurrentRecordId: startPlayModel recordId=$recordId")
                handleRecordIdChange(recordId)
                return
            }
        }

        // 检查直接的recordId
        if (mViewModel.recordId > 0 && mViewModel.recordId != currentMediaId) {
            DebugUtil.d(TAG, "checkCurrentRecordId: direct recordId=${mViewModel.recordId}")
            handleRecordIdChange(mViewModel.recordId)
            return
        }
    }

    private fun handleRecordIdChange(newRecordId: Long) {
        DebugUtil.d(TAG, "handleRecordIdChange: $currentMediaId -> $newRecordId")

        // 更新当前录音ID
        currentMediaId = newRecordId

        // 通知SummaryViewModel recordId变化
        summaryViewModel.setRecordId(newRecordId)
    }

    private fun handleSummaryStatusChange(summaryStatus: Int?) {
        when (summaryStatus) {
            SUMMARY_STATE_CLIENT_GENERATING -> {
                if (isPageSelected) {
                    updateSummaryState(AISummaryViewModel.SummaryState.GENERATING)
                }
            }
            SUMMARY_STATE_CLIENT_END -> {
                if (isPageSelected) {
                    // 摘要生成完成，重新检查摘要内容
                    checkExistingSummary()
                }
            }
        }
    }

    /**
     * 检查现有摘要
     */
    private fun checkExistingSummary() {
        val recordId = if (currentMediaId > 0) currentMediaId else mViewModel.recordId

        if (recordId <= 0) {
            DebugUtil.w(TAG, "checkExistingSummary: Invalid recordId: $recordId")
            return
        }

        DebugUtil.d(TAG, "checkExistingSummary: delegating to ViewModel for recordId=$recordId")
        summaryViewModel.checkExistingSummary()
    }

    private fun updateSummaryState(state: AISummaryViewModel.SummaryState) {
        safeUpdateUI {
            DebugUtil.d(TAG, "updateSummaryState: $state")

            when (state) {
                AISummaryViewModel.SummaryState.EMPTY -> {
                    binding.layoutEmpty.visibility = View.VISIBLE
                    binding.layoutGenerating.visibility = View.GONE
                    binding.layoutContent.visibility = View.GONE
                    binding.layoutError.visibility = View.GONE
                }
                AISummaryViewModel.SummaryState.GENERATING -> {
                    binding.layoutEmpty.visibility = View.GONE
                    binding.layoutGenerating.visibility = View.VISIBLE
                    binding.layoutContent.visibility = View.GONE
                    binding.layoutError.visibility = View.GONE

                    // 开始动画
                    binding.progressBar.visibility = View.VISIBLE
                    binding.textGenerating.text = "正在生成摘要..."
                }
                AISummaryViewModel.SummaryState.COMPLETED -> {
                    binding.layoutEmpty.visibility = View.GONE
                    binding.layoutGenerating.visibility = View.GONE
                    binding.layoutContent.visibility = View.VISIBLE
                    binding.layoutError.visibility = View.GONE
                }
                AISummaryViewModel.SummaryState.ERROR -> {
                    binding.layoutEmpty.visibility = View.GONE
                    binding.layoutGenerating.visibility = View.GONE
                    binding.layoutContent.visibility = View.GONE
                    binding.layoutError.visibility = View.VISIBLE
                }
                AISummaryViewModel.SummaryState.STOPPED -> {
                    binding.layoutEmpty.visibility = View.VISIBLE
                    binding.layoutGenerating.visibility = View.GONE
                    binding.layoutContent.visibility = View.GONE
                    binding.layoutError.visibility = View.GONE
                }
            }
        }
    }

    /**
     * 显示错误信息
     */
    private fun showErrorMessage(errorMsg: String) {
        safeUpdateUI {
            binding.textErrorMessage.text = errorMsg
            binding.btnRetry.setOnClickListener {
                summaryViewModel.generateSummaryWithManager(activity, mAISummaryManagerImpl)
            }
        }
    }

    /**
     * 更新摘要信息
     */
    private fun updateSummaryInfo(content: String) {
        safeUpdateUI {
            // 可以在这里显示摘要的统计信息，如字数、项目数等
            val items = SummaryItem.parseContent(content)
            val contentCount = items.count { it.type == SummaryItemType.CONTENT }

            val infoText = "共${items.size}项内容"
            // 如果有相应的UI组件，可以显示这个信息
            DebugUtil.d(TAG, "Summary info: $infoText")
        }
    }

    /**
     * 更新版本信息
     */
    private fun updateVersionInfo(history: List<AISummaryDataRepository.SummaryEntity>) {
        safeUpdateUI {
            val currentIndex = summaryViewModel.mCurrentSummaryIndex.value ?: 0
            val totalCount = history.size

            // 更新版本信息显示
            binding.textVersionInfo.text = "${currentIndex + 1}/$totalCount"

            // 更新时间显示（显示当前版本的生成时间）
            if (currentIndex < history.size) {
                val currentSummary = history[currentIndex]
                val timeText = "生成时间：${formatTime(currentSummary.createTime)}"
                binding.textSummaryTime.text = timeText
            }

            DebugUtil.d(TAG, "updateVersionInfo: ${currentIndex + 1}/$totalCount")
        }
    }

    /**
     * 更新当前版本索引
     */
    private fun updateCurrentVersionIndex(index: Int) {
        safeUpdateUI {
            val history = summaryViewModel.summaryHistory.value ?: emptyList()
            val totalCount = history.size

            if (totalCount > 0) {
                // 更新版本信息显示
                binding.textVersionInfo.text = "${index + 1}/$totalCount"

                // 更新按钮状态
                binding.btnPreviousSummary.isEnabled = index < totalCount - 1
                binding.btnNextSummary.isEnabled = index > 0
                binding.btnLatestSummary.isEnabled = index > 0

                // 更新时间显示
                if (index < history.size) {
                    val currentSummary = history[index]
                    val timeText = "生成时间：${formatTime(currentSummary.createTime)}"
                    binding.textSummaryTime.text = timeText
                }
            }

            DebugUtil.d(TAG, "updateCurrentVersionIndex: index=$index")
        }
    }

    /**
     * 更新版本控制的可见性
     */
    private fun updateVersionControlVisibility(hasMultiple: Boolean) {
        safeUpdateUI {
            binding.layoutVersionControl.visibility = if (hasMultiple) {
                View.VISIBLE
            } else {
                View.GONE
            }

            DebugUtil.d(TAG, "updateVersionControlVisibility: hasMultiple=$hasMultiple")
        }
    }

    /**
     * 格式化时间显示
     */
    private fun formatTime(timestamp: Long): String {
        return java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
            .format(java.util.Date(timestamp))
    }

    /**
     * 显示摘要结果 - 委托给ViewModel处理
     */
    private fun displaySummaryResult(jsonResult: String) {
        DebugUtil.d(TAG, "displaySummaryResult: delegating to ViewModel")
        summaryViewModel.processSummaryResult(jsonResult)
    }

    /**
     * 显示摘要内容 - 仅处理UI显示
     */
    private fun displaySummaryContent(content: String) {
        safeUpdateUI {
            currentSummaryContent = content
            binding.textSummaryTitle.text = "录音摘要"
            binding.textSummaryTime.text = "生成时间：${getCurrentTimeString()}"
        }
    }

    /**
     * 清理摘要数据 - UI操作
     */
    private fun clearSummaryData() {
        safeUpdateUI {
            summaryAdapter?.updateData(emptyList())
            currentSummaryContent = null
        }
    }

    /**
     * 获取当前时间字符串
     */
    private fun getCurrentTimeString(): String {
        return formatTime(System.currentTimeMillis())
    }

    /**
     * 页面选中状态变化
     */
    fun onPageSelectedStateChange(isSelected: Boolean) {
        val wasSelected = isPageSelected
        isPageSelected = isSelected
        DebugUtil.d(TAG, "onPageSelectedStateChange: $wasSelected -> $isSelected")

        if (isSelected && !wasSelected) {
            // 页面从未选中变为选中时
            DebugUtil.d(TAG, "Summary tab selected, refreshing data")

            // 如果recordId还没有准备好，先尝试延迟检查
//            if (currentMediaId <= 0) {
//                DebugUtil.d(TAG, "Summary tab selected but recordId not ready, scheduling check")
//                scheduleDelayedRecordIdCheck()
//            } else {
//                // recordId已准备好，立即刷新数据
//                refreshSummaryData()
//            }

            refreshSummaryData()

        } else if (!isSelected && wasSelected) {
            // 页面从选中变为未选中时
            DebugUtil.d(TAG, "Summary tab deselected")
            // 可以在这里进行一些清理工作，但不注销回调，因为可能还会回到这个页面
        }
    }

    /**
     * 刷新摘要数据 - Tab切换时调用
     */
    private fun refreshSummaryData() {
        DebugUtil.d(TAG, "refreshSummaryData: currentMediaId=$currentMediaId")

        if (currentMediaId <= 0) {
            DebugUtil.w(TAG, "refreshSummaryData: invalid recordId")
            return
        }

        // 1. 首先检查ViewModel中是否有当前数据
        val currentContent = summaryViewModel.summaryContent.value
        val currentState = summaryViewModel.mSummaryState.value

        DebugUtil.d(TAG, "refreshSummaryData: currentState=$currentState, hasContent=${!currentContent.isNullOrEmpty()}")

        // 2. 如果ViewModel中有完整的摘要数据，直接显示
        if (currentState == AISummaryViewModel.SummaryState.COMPLETED && !currentContent.isNullOrEmpty()) {
            DebugUtil.d(TAG, "refreshSummaryData: displaying existing ViewModel data")
            displaySummaryContent(currentContent)
            return
        }

        // 3. 如果ViewModel中没有数据或状态不正确，强制重新检查
        DebugUtil.d(TAG, "refreshSummaryData: force refreshing summary data")
        summaryViewModel.forceRefreshSummary()

        // 4. 如果有缓存的摘要内容，作为备用显示
        currentSummaryContent?.let { content ->
            if (content.isNotEmpty() && currentState != AISummaryViewModel.SummaryState.COMPLETED) {
                DebugUtil.d(TAG, "refreshSummaryData: displaying cached content as fallback")
                displaySummaryContent(content)
            }
        }
    }

    override fun onDestroyView() {
        DebugUtil.d(TAG, "onDestroyView")

        // 清理AISummaryManagerImpl
        mAISummaryManagerImpl = null

        // 清理资源
        summaryAdapter = null
        currentSummaryContent = null
        currentMediaId = -1
        isPageSelected = false

        super.onDestroyView()
        _binding = null
    }

    override fun onPause() {
        super.onPause()
        DebugUtil.d(TAG, "onPause")
    }

    override fun onResume() {
        super.onResume()
        DebugUtil.d(TAG, "onResume")

        // 如果页面当前被选中，刷新摘要数据
        if (isPageSelected) {
            DebugUtil.d(TAG, "onResume: page is selected, refreshing data")
            if (currentMediaId > 0) {
                refreshSummaryData()
            } else {
                checkExistingSummary()
            }
        }
    }

    /**
     * 检查Fragment是否处于有效状态
     */
    private fun isFragmentValid(): Boolean {
        return isAdded && !isDetached && !isRemoving && activity != null && _binding != null
    }

    /**
     * 安全地更新UI状态
     */
    private fun safeUpdateUI(action: () -> Unit) {
        if (isFragmentValid()) {
            try {
                action()
            } catch (e: Exception) {
                DebugUtil.e(TAG, "Error updating UI: ${e.message}")
            }
        }
    }
}