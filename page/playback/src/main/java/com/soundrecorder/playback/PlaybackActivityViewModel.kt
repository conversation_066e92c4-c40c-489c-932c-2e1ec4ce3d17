/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.ContentValues
import android.content.Context
import android.content.Context.RECEIVER_NOT_EXPORTED
import android.content.Intent
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import android.provider.MediaStore
import android.text.TextUtils
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.getValueWithDefault
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.ext.registerReceiverCompat
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.CustomMutableLiveData
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.base.utils.MediaDataScanner
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.base.PlayerHelperBasicCallback
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.CuttingStaticsUtil
import com.soundrecorder.common.buryingpoint.SummaryStaticUtil
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.databean.ConvertStatus
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.share.IShareListener
import com.soundrecorder.common.share.OShareConvertUtil
import com.soundrecorder.common.share.ShareAction
import com.soundrecorder.common.share.ShareType
import com.soundrecorder.common.share.ShareTypeDoc
import com.soundrecorder.common.share.ShareTypeLink
import com.soundrecorder.common.sync.db.RecordDataSync
import com.soundrecorder.common.utils.AmpFileUtil
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.ConvertDbUtil.CONVERT_COMP_STATUS_COMPLETE
import com.soundrecorder.common.utils.ConvertDbUtil.SHOW_SWITH_TRUE
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.common.utils.RecordModeUtil
import com.soundrecorder.common.utils.RecordModeUtil.recordType
import com.soundrecorder.modulerouter.SeedlingAction
import com.soundrecorder.convertservice.ConvertSupportManager
import com.soundrecorder.modulerouter.notification.NotificationAction
import com.soundrecorder.modulerouter.notification.NotificationModel
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.modulerouter.recorder.RecordAction
import com.soundrecorder.modulerouter.summary.ISummaryCallback
import com.soundrecorder.modulerouter.summary.SummaryAction
import com.soundrecorder.playback.audio.PlaybackAudioFragment
import com.soundrecorder.playback.newconvert.ConvertUtils
import com.soundrecorder.convertservice.util.ConvertToUtils
import com.soundrecorder.player.status.PlayStatus
import com.soundrecorder.summary.RecordSummaryManager
import com.soundrecorder.summary.util.SummarySupportManager
import com.soundrecorder.wavemark.mark.MarkHelper
import com.soundrecorder.wavemark.mark.MarkListAdapter
import com.soundrecorder.wavemark.model.AmpAndMarkModel
import com.soundrecorder.wavemark.wave.WaveViewUtil
import com.soundrecorder.wavemark.wave.load.AmplitudeListUtil
import com.soundrecorder.wavemark.wave.load.SoundFile
import com.soundrecorder.wavemark.wave.view.MaxAmplitudeSource
import com.soundrecorder.wavemark.wave.view.WaveRecyclerView
import java.io.File
import java.util.Collections.emptyList
import java.util.UUID
import java.util.concurrent.ExecutionException
import kotlin.math.ceil
import kotlin.math.min
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import oplus.multimedia.soundrecorder.playback.mute.MuteConstants
import oplus.multimedia.soundrecorder.playback.mute.MuteDataManager

open class PlaybackActivityViewModel : ViewModel(), PlayerHelperBasicCallback, IShareListener, DefaultLifecycleObserver {

    var playerController = PlaybackPlayerController(this).also { it.init() }
    var isShowMarkList = MutableLiveData<Boolean>()
    var hasConvertContent = MutableLiveData(false)
    var mShowSwitch = MutableLiveData(false)
    var mIsPageStopScroll = MutableLiveData(false)
    var mSpeakerNumber = MutableLiveData<Int>()
    var mControlSpeakerTip = MediatorLiveData<ControlFirstTip>()

    var mShowActivityControlView = MutableLiveData<Boolean>()
    var mPanelShowStatus = MediatorLiveData<PanelShowStatus>()
    var mNotificationBtnStatus = MediatorLiveData<NotificationBtnStatus>()
    var ampList = MutableLiveData<MutableList<Int>>(ArrayList())
    var recordType = MutableLiveData(-1)
    var isPrepareAmplitudeAndMark = MutableLiveData<Boolean>()
    var oneWaveLineTime = 0f
    var amplitudeListUtil: AmplitudeListUtil? = null
    var recordId: Long = -1
    var mPlayPath: String? = null
    var isRecycle: Boolean = false
    var mKeyId: String = ""
    var mIsFromOtherApp = false
    var mIsFromSearch = false
    var convertSupportType = ConvertSupportManager.CONVERT_DISABLE
    private var mRecord: Record? = null
    var summaryNoteId: String? = null
    var summaryCallId: String? = null
    var mSummaryStatus: MutableLiveData<Int> = MutableLiveData()
    var currentLifecycleState: Lifecycle.Event = Lifecycle.Event.ON_ANY
    val showShareLinkPanel = MutableLiveData<String?>()
    val showShareToast = MutableLiveData<Int?>()
    var oShareConvertTextPath: String? = null

    // 主页的搜索词，可能为空
    var browseSearchWord: String? = null

    //首次进入转文本界面则需要播放初始动效
    var isFirstInConvertFragment = true

    var duration: Long = 0

    var isDirectOn: Boolean = false
    var directTime: String? = null
    var isNeedSmartName = false
    var needSmartNameMediaList = mutableListOf<Long>()
    var isNeedAISummary = false
    var needAISummaryMediaList = mutableListOf<Long>()

    val smartNameResult = MutableLiveData<String?>()

    var startPlayModel: StartPlayModel? = null
        set(value) {
            if (field == null) {
                field = value
                recordId = field?.mediaId ?: -1
                mPlayPath = field?.playPath
                isRecycle = field?.isRecycle ?: false
                DebugUtil.d(TAG, "field?.playPath:${field?.playPath}, isRecycle:${field?.isRecycle}")
                mIsFromOtherApp = field?.isFromOtherApp ?: false
                mIsFromSearch = field?.isFromSearch ?: false
                browseSearchWord = field?.browseSearchWord ?: ""
                if (recordId > 0) {
                    setSummaryCallback(recordId)
                    playerController.setDuration(field?.duration ?: 0)
                    playerController.setOCurrentTimeMillis(field?.seekToMill ?: 0)
                    if (isRecycle) {
                        if (!PermissionUtils.hasAllFilePermission()) {
                            val context = BaseApplication.getAppContext()
                            PermissionUtils.sendFilePermissionBroadcast(context)
                            DebugUtil.d(TAG, "playbackActivity no permission.")
                            return
                        }
                        field?.playPath?.let {
                            playerController.setPlayUri(Uri.parse(it), false)
                        }
                    } else {
                        playerController.setPlayUri(MediaDBUtils.genUri(recordId), false)
                    }
                    loadMuteData()
                }
            }
        }
    var autoPlay = true

    var mIsDecodeReady = MutableLiveData<Boolean>()
    var mIsDecodeFinish = MutableLiveData<Boolean>()
    var mSoundFile: SoundFile? = null

    private var mNeedWaveLineCount = 0

    var playName = MutableLiveData<String>()
    var playPath = object : MutableLiveData<String>() {
        override fun setValue(value: String?) {
            super.setValue(value)
            markHelper?.refreshPlayPath(value)
        }
    }
    var needSyncRulerView = false
    var needSyncScrollConvertContentView = false
    var markEnable = MutableLiveData<Boolean>()
    var lastMarkTime = MutableLiveData<Long>()
    var lastMarkAction = CustomMutableLiveData<Int>()
    var addMarkIndex: Int = -1
    var mNeedShowDeleteDialog = MutableLiveData(false)
    var mNeedShowRecycleDeleteDialog = MutableLiveData(false)
    var mNeedShowRecoverDialog = MutableLiveData(false)
    var mNeedShowDetailDialog = MutableLiveData(false)
    var mNeedShowRenameDialog = MutableLiveData(false)
    var mNeedShowSelectPictureDialog = MutableLiveData(false)
    var mRenameEditText = ""

    var mNeedShowMarkRenameDialog = MutableLiveData(false)
    var mRenameMarkEditText = ""
    var mMarkRenameData: MarkDataBean? = null

    var mNeedShowSpeedDialog = MutableLiveData(false)
    var mNeedShowShareDialog = MutableLiveData(false)
    var async: Job? = null
    private var readMarkJob: Job? = null
    var mCurrentTabType = MutableLiveData(TAB_TYPE_CONVERT)
    /*针对几率的所有数据对应的textitem、时间item的 position*/
    var mLastFocusPosition = -1
    var mLastSubFocusPosition = -1
    var mLastFocusTimePosition = -1
    var dataPosition: Int? = null
    var mMimeType = MutableLiveData<String>()
    var markHelper: MarkHelper? = null
    var mIsFromNotification: Boolean = false

    var mNeedTrigCloud: Boolean = false

    var isAddPictureMarkingCallback: (() -> MutableLiveData<Boolean>)? = null

    var markReadReadyCallback: MarkReadReadyCallback? = null
        set(value) {
            //调用set方法时，如果标记已经load成功，回调相关Callback的方法
            DebugUtil.i(TAG, "markReadReadyCallback setted, ${markHelper?.isMarkLoaded()})")
            field = value
            if (markHelper?.isMarkLoaded() == true) {
                field?.onMarkReadReady(markHelper?.getMarkDatas()?.value)
            }
        }

    //控制是否显示沉浸态
    var isImmersiveState: MutableLiveData<Boolean> = MutableLiveData()
    //沉浸态第一段动画下移距离
    var immersiveMoveDownDistance: MutableLiveData<Int> = MutableLiveData()
    //沉浸态切换动画是否正在执行
    var isImmersiveAnimationRunning: Boolean = false
    //列表滚动的累计高度，界面重建也需要使用，所以存放在viewModel中
    var immersiveScrollDistance: Int = 0

    private val iSummaryCallback by lazy {
        object : ISummaryCallback {
            override fun onRecordPreCheckResult(from: String, code: Int) {
                // 点击生成摘要埋点,由于可设置多个callback，增加from判断
                if (from == SummaryStaticUtil.EVENT_FROM_AUDIO || from == SummaryStaticUtil.EVENT_FROM_CONVERT) {
                    SummaryStaticUtil.addClickStartSummaryEvent(from, code)
                }
            }

            override fun onSummaryStart() {
                mSummaryStatus.postValueSafe(RecordSummaryManager.getSummaryState(recordId, summaryNoteId))
            }

            override fun onSummaryProgressEnd(asrErrorCode: Int?, summaryErrorCode: Int?) {
                mSummaryStatus.postValueSafe(RecordSummaryManager.getSummaryState(recordId, summaryNoteId))
            }

            override fun onSummaryEnd() {
                mSummaryStatus.postValueSafe(RecordSummaryManager.getSummaryState(recordId, summaryNoteId))
            }

            override fun onSummaryGenNotedId(noteId: String?, callUuid: String?) {
                // 生成便签id，但是录音摘要db可能还没保存成功
                if (!noteId.isNullOrEmpty()) {
                    summaryNoteId = noteId
                    summaryCallId = callUuid
                    mSummaryStatus.postValueSafe(RecordSummaryManager.getSummaryState(recordId, summaryNoteId))
                }
            }
        }
    }

    var muteDataManager: MuteDataManager? = null
        get() {
            if (field == null) {
                field = MuteDataManager(viewModelScope, hashCode())
            }
            return field
        }

    var mNeedShowMarkDeleteDialog = false
    var mMarkDeleteData: MarkDataBean? = null

    private val mPlayNotificationModel by lazy {
        NotificationModel().also {
            it.playName = playName
            it.playStatus = playerController.playerState.map { playStatus ->
                when (playStatus) {
                    PlayStatus.PLAYER_STATE_PLAYING,
                    PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> NotificationModel.RECORD_STATUS_PLAYING
                    else -> NotificationModel.RECORD_STATUS_PAUSE
                }
            }
            it.curTime = playerController.currentTimeMillis
            it.playDuration = playerController.getDuration()
            //播放和标记按钮不可用
            it.isBtnDisabled = updateNotificationBtnEnable()
            it.isMarkEnabled = markEnable
            it.markPoint = lastMarkTime
            it.canJumpIntent = !mIsFromOtherApp
        }
    }
    private var mNotificationReceiver: BroadcastReceiver? = null

    //旧版分享txt的回调，准备完成之后关闭“请稍后...”dialog，然后显示分享面板
    private var shareListener: IShareListener? = null
    private var mNeedRestoreWaitingDialog: Boolean = false
    private var shareWaitingType: ShareType? = null


    init {
        oneWaveLineTime = WaveViewUtil.getOneWaveLineTime(BaseApplication.getAppContext())
        mPanelShowStatus.value = PanelShowStatus()
        mPanelShowStatus.addSource(hasConvertContent) {
            mPanelShowStatus.value?.mHasConvertContent = it
            mPanelShowStatus.value = mPanelShowStatus.value
        }
        mPanelShowStatus.addSource(mShowSwitch) {
            mPanelShowStatus.value?.mConvertShowSwitch = it
            mPanelShowStatus.value = mPanelShowStatus.value
        }
        mPanelShowStatus.addSource(mShowActivityControlView) {
            mPanelShowStatus.value?.showActivityControlView = it
            mPanelShowStatus.value = mPanelShowStatus.value
        }
        mPanelShowStatus.addSource(mCurrentTabType) {
            mPanelShowStatus.value?.mCurrentTab = it
            mPanelShowStatus.value = mPanelShowStatus.value
        }
        mPanelShowStatus.addSource(mSummaryStatus) {
            mPanelShowStatus.value?.mSummaryStatus = it
            mPanelShowStatus.value = mPanelShowStatus.value
        }
        mPanelShowStatus.addSource(SummarySupportManager.supportRecordSummary) {
            mPanelShowStatus.value?.mHasSummaryFunctions = it
            mPanelShowStatus.value = mPanelShowStatus.value
        }

        markHelper = MarkHelper(playerController, viewModelScope, false)

        observeControlSpeakerArgs()
    }

    fun observeNotificationBtn(isFromRestore: Boolean, isConvertSearch: MutableLiveData<Boolean>?) {
        if (isFromRestore) {
            DebugUtil.d(TAG, "isFromRestore return")
            return
        }
        mNotificationBtnStatus.value = NotificationBtnStatus()
        if (isConvertSearch != null) {
            mNotificationBtnStatus.addSource(isConvertSearch) {
                mNotificationBtnStatus.value?.mIsInConvertSearch = it
                mNotificationBtnStatus.value = mNotificationBtnStatus.value
            }
        }
        val isAddPictureMark = isAddPictureMarking() ?: MutableLiveData(false)
        mNotificationBtnStatus.addSource(isAddPictureMark) {
            mNotificationBtnStatus.value?.mIsAddPictureMarking = it
            mNotificationBtnStatus.value = mNotificationBtnStatus.value
        }
    }

    fun updateNotificationBtnEnable(): LiveData<Boolean> {
        return mNotificationBtnStatus.map {
            if (it.mIsInConvertSearch) {
                true
            } else {
                it.mIsAddPictureMarking
            }
        }
    }

    /**
     * 由于同步中子建立索引需要耗时+搜索列表onresume不会触发新的搜索流程，
     * so 搜索列表数据可能为脏数据，传过来的name、path是不对的；
     * 从数据库重新获取一次，如果跟传过来的不一致则更新
     * 若统一使用从媒体库查询逻辑，则需增加媒体库获取失败的逻辑
     */
    private fun getRecordInfoFromMedia(): Record? {
        val record = if (isRecycle) {
            RecorderDBUtil.getInstance(BaseApplication.getAppContext()).qureyRecycleRecordByPath(mPlayPath)
        } else {
            MediaDBUtils.queryRecordById(recordId)
        }
        if (record == null) {
            DebugUtil.i(TAG, "getRecordInfoFromMedia error")
            return null
        }
        DebugUtil.i(TAG, "getRecordInfoFromMedia success")
        if (record.displayName != playName.value) {
            playName.postValue(record.displayName)
        }
        if (record.data != playPath.value) {
            playPath.postValue(if (isRecycle) record.recycleFilePath else record.data)
            recordType.postValue(record.relativePath.recordType())
        }
        if (record.duration != playerController.getDuration()) {
            playerController.mDuration.postValue(record.duration)
        }
        if (record.mimeType != mMimeType.value) {
            mMimeType.postValue(record.mimeType)
        }
        isDirectOn = record.directOn
        directTime = record.directTime
        return record
    }

    private fun observeControlSpeakerArgs() {
        if (mPanelShowStatus.value != null) {
            mControlSpeakerTip.value = ControlFirstTip(mPanelShowStatus.value!!)
            mControlSpeakerTip.addSource(mPanelShowStatus) {
                mControlSpeakerTip.value?.panelShowStatus = it
                mControlSpeakerTip.value = mControlSpeakerTip.value
            }
            mControlSpeakerTip.addSource(mSpeakerNumber) {
                mControlSpeakerTip.value?.speakerNumber = it
                mControlSpeakerTip.value = mControlSpeakerTip.value
            }
        }
    }

    var hasReadMarkTag: Boolean = false
    private var mReadMarkStartTime = 0L

    fun getMarkList(): MutableLiveData<MutableList<MarkDataBean>>? {
        return markHelper?.getMarkDatas()
    }

    fun readMarkTag() {
        if (hasReadMarkTag) {
            lastMarkAction.setValue(MARK_ACTION_INIT)
            return
        }
        hasReadMarkTag = true
        DebugUtil.i(TAG, "readMarkTag")
        async = viewModelScope.launch(Dispatchers.IO) {

            SummarySupportManager.loadSupportRecordSummary()
            mReadMarkStartTime = System.currentTimeMillis()
            mRecord = getRecordInfoFromMedia()
            getNoteData()
            mRecord?.let {
                val path = if (it.isRecycle == true) {
                    it.recycleFilePath ?: playPath.value
                } else {
                    it.data ?: playPath.value
                }
                DebugUtil.i(TAG, "readMarkTag, mRecord?.isRecycle:${it.isRecycle}, path:$path")
                val playUri = if (it.isRecycle) {
                    path?.let {
                        Uri.parse(it)
                    }
                } else {
                    MediaDBUtils.genUri(recordId)
                }
                amplitudeListUtil = AmplitudeListUtil(BaseApplication.getAppContext(), path, playUri, it.isRecycle)

                var flag = false
                var model: AmpAndMarkModel? = null
                readMarkJob = launch(Dispatchers.IO) {
                    if (it.isRecycle) {
                        ensureRecycleRecordInDb(path)
                    } else {
                        ensureRecordInDbWhenPlaybackActivityIn(path)
                    }
                    model = readAmpAndMark()
                    if (!flag) {
                        flag = true
                        prepareAmplitudeAndMark(path, model, loadDuration())
                    }
                }
                delay(PlaybackAudioFragment.READ_MARK_TIMEOUT.toLong())
                if (!flag) {
                    flag = true
                    prepareAmplitudeAndMark(path, model, loadDuration())
                }
            }
        }
    }

    fun getCurrentTime(): Long {
        return playerController.getCurrentPosition()
    }

    private fun loadDuration(): Long {
        duration = playerController.getDuration()
        DebugUtil.d(TAG, "initView duration:$duration")
        if (duration == 0L) {
            duration = playerController.loadDuration()
        }
        return duration
    }

    private fun readAmpAndMark(): AmpAndMarkModel {
        val model = AmpAndMarkModel()
        try {
            val markString = amplitudeListUtil?.markString
            if (markString != null && !TextUtils.isEmpty(markString)) {
                model.markString = markString
            }
            model.markDataBeans = amplitudeListUtil?.mergeMarkList
            model.ampList = amplitudeListUtil?.ampList
            markHelper?.startIndexWhenReadMarksComplete(model.markDataBeans)
        } catch (e: ExecutionException) {
            DebugUtil.e(TAG, "readAmpAndMark ExecutionException", e)
        } catch (e: InterruptedException) {
            DebugUtil.e(TAG, "readAmpAndMark InterruptedException", e)
        }
        return model
    }

    private fun prepareAmplitudeAndMark(path: String?, model: AmpAndMarkModel?, defaultDuration: Long) {
        amplitudeListUtil?.releaseMp3()
        DebugUtil.i(TAG, "prepareAmplitudeAndMark:${model?.ampList?.size}")
        mKeyId = RecorderDBUtil.getKeyIdByPath(path)
        markHelper?.refreshData(model?.markDataBeans, playerController.getPlayUri(), path)
        //标记刷新之后回调，通知等待的线程可以继续
        markReadReadyCallback?.onMarkReadReady(markHelper?.getMarkDatas()?.value)
        lastMarkAction.postValueSafe(MARK_ACTION_INIT)
        isShowMarkList.postValueSafe(model?.markDataBeans?.isNullOrEmpty() == false)
        if (model?.ampList != null && model.ampList.size > 0) {
            ampList.value?.clear()
            ampList.value?.addAll(model.ampList)
            ampList.postValue(correctAmplitudeList(defaultDuration))
            if ((ampList.value ?: ArrayList()).isNotEmpty()) {
                writeAmpData(path)
            }

            // 这里需要切主线程去播放，在蜻蜓进入A详情，返回再次进入A详情,播放器底部会异常
            Handler(Looper.getMainLooper()).post {
                if (mNotificationBtnStatus.value?.mIsInConvertSearch == true) {
                    /**
                     * 重建逻辑
                     */
                    DebugUtil.i(TAG, "展示转文本搜索页面时，不直接播放录音")
                } else {
                    startPlayForAuto()
                }
                isPrepareAmplitudeAndMark.value = true
            }
        } else {
            prepareAmplitudeBySound(path, defaultDuration)
        }
    }

    private fun startPlayForAuto() {
        DebugUtil.d(TAG, "startPlayForAuto,parse amp time: ${System.currentTimeMillis() - mReadMarkStartTime}")
        if (autoPlay) {
            // start play after prepare 200ms to avoid the wave not start from the start
            playerController.startPlayImmediately(NumberConstant.NUM_200)
        } else {
            /**
             * 更改播放状态为 HALTON
             * 父子级默认不自动开始播放音频，playerState为INIT(-1),MarkHelper.isMarkEnable 默认进入返回true，即使0-1s内已存在标记
             */
            playerController.playerState.postValueSafe(PlayStatus.PLAYER_STATE_HALTON)
        }
    }

    private fun prepareAmplitudeBySound(path: String?, defaultDuration: Long) {
        amplitudeListUtil?.setDecodeReady { _, soundFile ->
            DebugUtil.i(TAG, "amplitudeListUtil decode ready")
            mSoundFile = soundFile
            mIsDecodeReady.postValue(true)
            Handler(Looper.getMainLooper()).post {
                startPlayForAuto()
            }
        }
        amplitudeListUtil?.setDecodeFinish { ampString ->
            DebugUtil.i(TAG, "amplitudeListUtil decode finish,spend time=${System.currentTimeMillis() - mReadMarkStartTime}")
            mIsDecodeFinish.postValue(true)
            ampList.value?.addAll(amplitudeListUtil?.convertStringToInt(ampString) ?: emptyList())
            ampList.postValue(correctAmplitudeList(defaultDuration))
            writeAmpData(path)
        }
        amplitudeListUtil?.getAmpFromSoundFile()
    }

    private fun writeAmpData(path: String?) {
        if (!AmpFileUtil.ampFileIsExists(BaseApplication.getAppContext(), path)) {
            DebugUtil.v(TAG, "begin writeAmplitude.")
            if ((ampList.value ?: ArrayList()).isNotEmpty()) {
                AmplitudeListUtil.writeAmpData(
                    BaseApplication.getAppContext(),
                    path,
                    playerController.getPlayUri(),
                    ampList.value
                )
            }
        }
    }


    private fun correctAmplitudeList(defaultDuration: Long = 0): ArrayList<Int> {
        val amp = ArrayList(ampList.value ?: emptyList())
        var duration = playerController.getDuration()
        if (duration == 0L) {
            duration = defaultDuration
        }
        mNeedWaveLineCount = ceil(duration / oneWaveLineTime.toDouble()).toInt()
        DebugUtil.d(TAG, "correctAmplitudeList duration:$duration mNeedWaveLineCount:$mNeedWaveLineCount")
        if ((ampList.value ?: ArrayList()).isEmpty()) {
            return ArrayList()
        }
        amplitudeListUtil?.releaseSound()
        if (mNeedWaveLineCount > amp.size) {
            var lastAmplitude = 0
            var addAmplitude: Int
            val addSize = mNeedWaveLineCount - amp.size
            try {
                lastAmplitude = amp[amp.size - 1]
            } catch (e: NumberFormatException) {
                DebugUtil.d(TAG, "")
            }
            for (i in 0 until addSize) {
                addAmplitude = (lastAmplitude * Math.random()).toInt()
                amp.add(addAmplitude)
            }
        } else if ((mNeedWaveLineCount > 0) && (mNeedWaveLineCount < (ampList.value
                ?: ArrayList()).size)
        ) {
            val removeSize = amp.size - mNeedWaveLineCount
            for (i in 0 until removeSize) {
                amp.removeAt(0)
            }
        }
        return amp
    }

    fun forwardOrBackWard(isForward: Boolean) {
        val seekByTime = if (isForward) {
            PlaybackContainerFragment.THREE_SECONDS
        } else {
            -1 * PlaybackContainerFragment.THREE_SECONDS
        }

        val seekTime = playerController.getSeekTimeByDelta(seekByTime)
        needSyncRulerView = true
        needSyncScrollConvertContentView = true
        seekTime(seekTime)
    }

    fun seekTime(seekTime: Long) {
        playerController.seekTime(seekTime)
        muteDataManager?.nextMutePosition =
            min(seekTime + MuteConstants.SMOOTH_TIME_BACK_TRACK, playerController.getDuration())
    }

    fun setCurrentTime(currentTime: Long) {
        playerController.setOCurrentTimeMillis(currentTime)
        muteDataManager?.nextMutePosition =
            min(currentTime + MuteConstants.SMOOTH_TIME_BACK_TRACK, playerController.getDuration())
    }

    /**
     * 检测当时时间是否是静音片段需要跳过
     * 1、由于开关打开后，拖动到静音片段又返回正常位置，造成死循环。和交互确认，只有播放状态下才执行检测
     * 2、是静音片段直接跳过，否则执行原有逻辑
     */
    fun checkMuteData(currentTimeMillis: Long): Boolean {
        if (((muteDataManager?.nextMutePosition ?: -1) - currentTimeMillis) in 1..MuteConstants.SMOOTH_TIME_BACK_TRACK) {
            DebugUtil.d(TAG, "checkMuteData: curTime=$currentTimeMillis, nextMutePosition=${muteDataManager?.nextMutePosition}")
            return false
        }
        muteDataManager?.nextMutePosition = -1

        if (muteDataManager?.muteEnable?.value != true) {
            return false
        }

        //假波形单独处理
        if (playerController.playerDuration >= 0 && currentTimeMillis >= playerController.playerDuration) {
            DebugUtil.d(TAG, "checkMuteData: in fake wave, curTime=$currentTimeMillis, playerDuration=${playerController.playerDuration}")
            needSyncRulerView = true
            playerController.setOCurrentTimeMillis(playerController.getDuration())

            return true
        }

        //检测是否属于静音片段
        val newSeekTime = muteDataManager?.getSeekTimeAfterJumpMute(currentTimeMillis) ?: -1
        if (newSeekTime >= 0) {
            needSyncRulerView = true
            playerController.seekTime(newSeekTime)

            return true
        }

        return false
    }

    val mDragListener = WaveRecyclerView.DragListener {
        when (playerController.playerState.value) {
            PlayStatus.PLAYER_STATE_PAUSE,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE,
            -> {
                DebugUtil.i(TAG, "onDraged")
                playerController.waveDragWhenPause = true
            }
        }
        CuttingStaticsUtil.addTrimDragWave()
    }

    val maxAmplitudeSource = object : MaxAmplitudeSource {
        override fun getMaxAmplitude(): Int {
            return NumberConstant.NUM_10000
        }

        override fun getTime(): Long {
            return playerController.getCurrentPosition()
        }

        override fun getRecorderState(): Int {
            return playerController.playerState.value ?: PlayStatus.PLAYER_STATE_HALTON
        }
    }


    /**
     * 播放暂停按钮点击事件
     */
    fun recordControlClick() {
        if (isQuickClick) {
            return
        }
        if (playerController.hasPaused()) {
            correctPlayTimeWhenControlClick(playerController.currentTimeMillis.getValueWithDefault(), targetPlaySegment.value) {
                seekTime(it)
            }
        }
        playerController.playBtnClick()
    }

    val onRenameMarkListener = MarkListAdapter.OnRenameMarkListener { data, newMarkText ->
        val markTime = data.getShowTime()
        val pos = getMarkList()?.value?.indexOf(data) ?: return@OnRenameMarkListener
        if (!TextUtils.isEmpty(newMarkText) && !TextUtils.isEmpty(markTime)) {
            if (markHelper?.renameMark(newMarkText, pos) == true) {
                lastMarkAction.setValue(MARK_ACTION_RENAME)
                mNeedTrigCloud = true
            }
        }
    }

    fun addMark(isFromNotification: Boolean, markMetaData: MarkMetaData): Int {
        DebugUtil.i(TAG, "add mark begin")
        val index = markHelper?.addMark(isFromNotification, markMetaData) ?: -1
        if (index <= -1) {
            if (index == MarkHelper.ADD_RESULT_LIMIT_NUMBER_EXCEED ||
                (index == MarkHelper.ADD_RESULT_DUPLICATE_TIME &&
                        playerController.playerState.value == PlayStatus.PLAYER_STATE_PAUSE)
            ) {

                lastMarkAction.setValue(MARK_ACTION_ICON_UNABLE)
            }
        } else {
            addMarkIndex = index
            lastMarkAction.setValue(MARK_ACTION_ADD)
            lastMarkTime.value = markHelper?.getMark(index)?.correctTime
            mIsFromNotification = isFromNotification
        }
        markEnable.value = isMarkEnabled()
        mNeedTrigCloud = true
        return index
    }

    fun isMarkEnabled(curTime: Long = -1): Boolean {
        return markHelper?.isMarkEnabled(curTime) == true
    }

    fun isAddPictureMarking(): MutableLiveData<Boolean>? {
        return isAddPictureMarkingCallback?.invoke()
    }

    fun removeMark(position: Int): MarkDataBean? {
        val removeItem = markHelper?.removeMark(position)
        if (removeItem != null) {
            if (markHelper?.getMarkDatas()?.value.isNullOrEmpty()) {
                isShowMarkList.value = true
            }
            lastMarkAction.setValue(MARK_ACTION_DELETE)
            markEnable.value = isMarkEnabled()
        }
        mNeedTrigCloud = true
        return removeItem
    }

    /**
     * 校验波形是否解析出来，若未解析，则toast提示用户
     */
    fun checkLoadAmpFinished(): Boolean {
        return if (!loadAmpSuccess()) {
            ToastManager.showShortToast(
                BaseApplication.getAppContext(),
                com.soundrecorder.common.R.string.wait_by_amp_loading
            )
            false
        } else {
            true
        }
    }

    /**
     * 是否已经解析出波形（PS:只是解析出部分波形供UI显示，并非解析完所有波形）
     */
    fun loadAmpSuccess(): Boolean = isPrepareAmplitudeAndMark.value == true || mIsDecodeReady.value == true

    override fun onCleared() {
        super.onCleared()
        DebugUtil.d(TAG, "onCleared")
        hasReadMarkTag = false
        cancelLoadData()
        playerController.releasePlay()
        playerController.onRelease()
        cancelNotification()
        muteDataManager?.release()
        muteDataManager = null
        unregisterNotificationReceiver()
        amplitudeListUtil?.release()
        amplitudeListUtil = null
        mSoundFile = null
        shareListener = null
        mRecord = null
        SummaryAction.unregisterSummaryCallback(iSummaryCallback)
    }

    fun cancelLoadData() {
        readMarkJob?.cancel()
        async?.cancel()
        async = null
        readMarkJob = null
        markReadReadyCallback = null
    }

    private fun registerNotificationReceiver() {
        if (mIsFromNotification) {
            DebugUtil.d(TAG, "isFromNotification = true, no need to register receiver.")
            return
        }

        if (mNotificationReceiver == null) {
            mNotificationReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    val notificationId =
                        intent?.getIntExtra(NotificationUtils.KEY_NOTIFICATION_TYPE, -1) ?: -1
                    val currentId = NotificationAction.getNotificationIdByModeAndPage(
                        getNotificationMode(), NotificationUtils.NOTIFICATION_PAGE_PLAY_BACK
                    )
                    if (notificationId != currentId) {
                        return
                    }

                    when (intent?.action) {
                        NotificationUtils.PLAY_STATUS_CHANGED_ACTION -> {
                            val isPlaying = playerController.isWholePlaying()
                            playerController.playBtnClick()
                            BuryingPoint.addPlayBtnClickFromNotification(
                                NotificationAction.isLockScreen() ?: false,
                                isPlaying,
                                NotificationUtils.NOTIFICATION_PAGE_PLAY_BACK
                            )
                        }
                        NotificationUtils.MARK_CHANGED_ACTION -> {
                            BuryingPoint.addMarkBtnClickFromNotification(
                                    NotificationAction.isLockScreen() ?: false,
                                    NotificationUtils.NOTIFICATION_PAGE_PLAY_BACK)
                            val markMetadata = MarkMetaData("", "", -1, -1, -1)
                            addMark(true, markMetadata)
                        }
                    }
                }
            }

            BaseApplication.getAppContext().registerReceiverCompat(mNotificationReceiver,
                NotificationAction.getIntentFilter(), RECEIVER_NOT_EXPORTED)
        }
    }

    private fun unregisterNotificationReceiver() {
        if (mNotificationReceiver != null) {
            BaseApplication.getAppContext().unregisterReceiver(mNotificationReceiver)
            mNotificationReceiver = null
        }
    }

    fun loadMuteData() {
        muteDataManager?.loadMuteData(recordId)
    }

    data class NotificationBtnStatus(
        var mIsAddPictureMarking: Boolean = false,
        var mIsInConvertSearch: Boolean = false,
    )

    data class PanelShowStatus(
        var mConvertShowSwitch: Boolean = false,
        var mHasConvertContent: Boolean = false,
        var mCurrentTab: String = TAB_TYPE_CONVERT,
        var showActivityControlView: Boolean = false,
        var mSummaryStatus: Int = RecordSummaryManager.SUMMARY_STATE_CLIENT_INIT,
        var mHasSummaryFunctions: Boolean = false,
    ) {

        fun checkOuterMarkGroupShow(): Boolean {
            val result = (showActivityControlView && (mCurrentTab == TAB_TYPE_CONVERT))
            //DebugUtil.i(TAG, "checkOuterMarkGroupShow: ${result}")
            return result
        }

        fun checkOuterConvertGroupShow(): Boolean {
            val result = (showActivityControlView && mHasConvertContent && (mCurrentTab == TAB_TYPE_CONVERT))
            val adjustResult = adjustConvertGroupShow(result)
            //DebugUtil.i(TAG, "checkOuterConvertGroupShow: $result adjust:$adjustResult")
            return result
        }

        fun checkInnerMarkGroupShow(): Boolean {
            return (!showActivityControlView)
        }


        fun checkInnerConvertGroupShow(): Boolean {
            val result = (!showActivityControlView && mHasConvertContent)
            val adjustResult = adjustConvertGroupShow(result)
//            DebugUtil.i(TAG, "checkInnerConvertGroupShow: $result adjust:$adjustResult")
            return adjustResult
        }

        /**
         * 调整转文本整个按钮父布局是否可见
         * 当转文本按钮和内容搜索按钮至少有一个可见时，整个按钮布局才可见，否则不可见
         */
        private fun adjustConvertGroupShow(groupVisible: Boolean): Boolean {
            return groupVisible && (mConvertShowSwitch || FunctionOption.IS_SUPPORT_CONVERT_SEARCH)
        }

        /**
         * 判断转文本讲话人是否显示
         */
        fun checkInnerConvertRoleShow(): Boolean {
            return checkInnerConvertGroupShow() && mConvertShowSwitch && (mCurrentTab == TAB_TYPE_CONVERT)
        }

        /**
         * 判断转文本讲话人是否显示
         */
        fun checkOuterConvertRoleShow(): Boolean {
            return checkOuterConvertGroupShow() && mConvertShowSwitch && (mCurrentTab == TAB_TYPE_CONVERT)
        }

        fun checkHasSummary(): Boolean {
            return mHasSummaryFunctions || RecordSummaryManager.hasSummaryByState(mSummaryStatus)
        }
    }

    data class ControlFirstTip(var panelShowStatus: PanelShowStatus, var speakerNumber: Int = 0) {
        fun checkInerRoleSpliteFirstTipNeedShow(): Boolean {
            val result = (panelShowStatus.checkInnerConvertRoleShow() && (speakerNumber > 1))
            DebugUtil.i(TAG, "SpeakerButtonTipStatus: $result checkInnerConvertGroupShow " +
                    "${panelShowStatus.checkInnerConvertRoleShow()}, speakerNumber: $speakerNumber")
            return result
        }

        fun checkOuterRoleSplitFirstTipNeedShow(): Boolean {
            val result = (panelShowStatus.checkOuterConvertRoleShow() && (speakerNumber > 1))
            DebugUtil.i(TAG, "SpeakerButtonTipStatus: $result checkOuterConvertGroupShow " +
                    "${panelShowStatus.checkOuterConvertRoleShow()}, speakerNumber: $speakerNumber")
            return result
        }
    }

    fun showNotification() {
        DebugUtil.i(TAG, "showNotification: $mIsFromOtherApp")
        if (!NotificationAction.mHasComponent) {
            return
        }

        mPlayNotificationModel.isRecycle = isRecycle
        NotificationAction.showNotification(
            getNotificationMode(),
            NotificationUtils.NOTIFICATION_PAGE_PLAY_BACK,
            mPlayNotificationModel
        )
        registerNotificationReceiver()
    }

    fun cancelNotification() {
        DebugUtil.i(TAG, "cancelNotification")
        if (!NotificationAction.mHasComponent) {
            return
        }

        NotificationAction.cancelNotification(
            getNotificationMode(),
            NotificationUtils.NOTIFICATION_PAGE_PLAY_BACK
        )
    }

    private fun getNotificationMode(): Int? {
        return NotificationAction.getNotificationMode(mIsFromOtherApp)
    }

    fun setShareCallBack(listener: IShareListener?) {
        shareListener = listener
    }

    fun setNeedRestoreWaitingDialog(needShow: Boolean) {
        mNeedRestoreWaitingDialog = needShow
    }

    fun getNeedRestoreWaitingDialog() = mNeedRestoreWaitingDialog

    fun getShareWaitingType(): ShareType? = shareWaitingType

    private fun ensureRecycleRecordInDb(inputPath: String?) {
        if (inputPath.isNullOrEmpty()) {
            DebugUtil.e(TAG, "ensureRecycleRecordInDb inputPath null or Empty, return")
            return
        }
        val record = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).qureyRecycleRecordByPath(inputPath)
         if (record != null) {
             isDirectOn = record.directOn
             directTime = record.directTime

             mRecord?.mD5 = record.mD5
             mRecord?.directOn = record.directOn
             mRecord?.directTime = record.directTime
             DebugUtil.d(TAG, "ensureRecycleRecordInDb,directOn:${record.directOn},directTime:${record.directTime}")
         }
    }

    private fun ensureRecordInDbWhenPlaybackActivityIn(inputPath: String?) {
        if (inputPath.isNullOrEmpty()) {
            DebugUtil.e(TAG, "ensureRecordInDbWhenPlaybackActivityIn inputPath null or Empty, return")
            return
        }
        val context = BaseApplication.getAppContext()
        val record = RecorderDBUtil.getInstance(context).qureyRecordByPath(inputPath)
        if (record == null) {
            insertNewRecord(inputPath, context)
            mNeedTrigCloud = true
        } else {
            val size = record.fileSize
            val fileSize = FileUtils.getRealFileSize(record.relativePath, record.displayName)
            val compareSizeResult = size != fileSize
            val id = RecorderDBUtil.getKeyIdByPath(inputPath)
            // 云同步，云同步处理
            // 私密空间，私密空间处理
            // 此处处理 录音异常结束的录音文件，文件管理覆盖文件相关逻辑
            // 判断是否是录音App自身的异常录音文件
            // 1. 通过媒体库判断是否是录音本身创建的文件
            // 2. 录音异常结束文件RecordId备份在SP中
            val hasSP = RecordAction.removeRecordIdWhenRecordComplete(id, PrefUtil.KEY_SAVE_RECORD_ID_WHEN_RECORDING) ?: false
            if (compareSizeResult) {
                val hasOwner = MediaDBUtils.hasOwnerPackageName(inputPath)
                if (hasOwner || hasSP) {
                    updateNewRecord(inputPath, context)
                } else {
                    val deleteSuc =
                        RecorderDBUtil.getInstance(context).deleteRecordByRelativePathAndDisplayName(record.relativePath, record.displayName)
                    if (deleteSuc) {
                        insertNewRecord(inputPath, context)
                        mNeedTrigCloud = true
                    }
                }
            } else {
                // 是同一个文件，需要将本地的MarkData解析并放到Mark表中
                val recordId = record.id
                val mMarkData = record.markData
                isDirectOn = record.directOn
                directTime = record.directTime
                RecorderDBUtil.getInstance(context).checkAndExactMarkDataToMarkTable(recordId, mMarkData)
                mRecord?.mD5 = record.mD5

                mRecord?.directOn = record.directOn
                mRecord?.directTime = record.directTime
                DebugUtil.d(TAG, "ensureRecordInDb,directOn:${record.directOn},directTime:${record.directTime}")
            }
            // 此音频是OShare目录下且未扫描过转写文本是调用扫描
            if (record.isOShareFile && !record.isScanOshareText) {
                OShareConvertUtil.scanOShareConvertFile(recordId, record, oShareConvertTextPath, true)
            }
        }
    }

    private fun insertNewRecord(inputPath: String, context: Context) {
        val selection = MediaStore.Audio.Media.DATA + " COLLATE NOCASE = ? "
        val selectionArgs = arrayOf(inputPath)
        val mediaRecords = RecordDataSync.getMediaData(context, MediaDBUtils.BASE_URI, selection, selectionArgs, -1, -1)
        if (mediaRecords != null && mediaRecords.size > 0) {
            val mediaRecord = mediaRecords[0]
            val uuIdString = UUID.randomUUID().toString()
            if (mediaRecord.isOShareFile) {
                mediaRecord.groupId = OShareConvertUtil.getGroupId()
                mediaRecord.groupUuid = OShareConvertUtil.getGroupUuid()
            }
            mediaRecord.uuid = uuIdString
            mediaRecord.recordType = RecordModeUtil.getRecordTypeForMediaRecord(mediaRecord)
            mediaRecord.dirty = RecordConstant.RECORD_DIRTY_MEGA_ONLY
            mediaRecord.checkMd5()
            mediaRecord.setSyncUploadStatus(RecordConstant.SYNC_STATUS_BACKUP_START)
            mediaRecord.checkMd5()
            RecorderDBUtil.insertRecordData(context, mediaRecord.convertToContentValues())
            DebugUtil.i(TAG, "writeAmpData: no record found in syncDb, insert media record $mediaRecord")
            mRecord?.mD5 = mediaRecord.mD5

            isDirectOn = mediaRecord.directOn
            directTime = mediaRecord.directTime

            // 此音频是OShare目录下时进行转写文本扫描
            if (mediaRecord.isOShareFile) {
                OShareConvertUtil.scanOShareConvertFile(recordId, mediaRecord, oShareConvertTextPath, true)
            }
        }
    }

    private fun updateNewRecord(inputPath: String, context: Context) {
        val selection = MediaStore.Audio.Media.DATA + " COLLATE NOCASE = ? "
        val selectionArgs = arrayOf(inputPath)
        val mediaRecords = RecordDataSync.getMediaData(context, MediaDBUtils.BASE_URI, selection, selectionArgs, -1, -1)
        if (mediaRecords != null && mediaRecords.size > 0) {
            val mediaRecord = mediaRecords[0]
            val uuIdString = UUID.randomUUID().toString()
            mediaRecord.uuid = uuIdString
            mediaRecord.recordType = RecordModeUtil.getRecordTypeForMediaRecord(mediaRecord)
            mediaRecord.dirty = RecordConstant.RECORD_DIRTY_MEGA_ONLY
            mediaRecord.setSyncUploadStatus(RecordConstant.SYNC_STATUS_BACKUP_START)
            RecorderDBUtil.updateRecordData(context, mediaRecord.convertToContentValues(), selection, selectionArgs)
            DebugUtil.i(TAG, "writeAmpData: no record found in syncDb, insert media record $mediaRecord")

            isDirectOn = mediaRecord.directOn
            directTime = mediaRecord.directTime
        }
    }

    override fun getPlayerName(): MutableLiveData<String> {
        return playName
    }

    override fun getPlayerMimeType(): String? {
        return mMimeType.value
    }

    override fun getKeyId(): String {
        return mKeyId
    }

    override fun onCurTimeChanged(curTime: Long) {
        markEnable.postValueSafe(isMarkEnabled(curTime))
    }

    fun onTimerRefreshTime(timeTickMillis: Long) {
        if (!checkMuteData(timeTickMillis)) {
            needSyncRulerView = false
            playerController.setOCurrentTimeMillis(timeTickMillis)
        }
    }

    companion object {
        private const val TAG = "PlaybackActivityViewModel"

        //第一次加载完标记数据的状态（包括第一次进入或重建）
        const val MARK_ACTION_INIT = 0
        const val MARK_ACTION_DELETE = 1
        const val MARK_ACTION_RENAME = 2
        const val MARK_ACTION_ADD = 3
        const val MARK_ACTION_ICON_UNABLE = 4

        const val TAB_TYPE_CONVERT = "convert"
        const val TAB_TYPE_SUMMARY = "summary"

        const val TAB_INDEX_FIRST = 0
        const val TAB_INDEX_SECOND = 1

        private var sLastClick = SystemClock.elapsedRealtime()
        private const val QUICK_CLICK_INTERVAL = 700

        private val isQuickClick: Boolean
            get() {
                val thisClick = SystemClock.elapsedRealtime()
                return if (thisClick - sLastClick < QUICK_CLICK_INTERVAL) {
                    true
                } else {
                    sLastClick = thisClick
                    false
                }
            }
    }

    //分享文件过大，需要显示waitingDialog
    override fun onShowShareWaitingDialog(mediaId: Long, type: ShareType) {
        DebugUtil.d(TAG, "onShowShareWaitingDialog  type: $type")
        shareWaitingType = type
        shareListener?.onShowShareWaitingDialog(mediaId, type)
    }

    //分享前置流程完成
    override fun onShareSuccess(mediaId: Long, type: ShareType) {
        DebugUtil.d(TAG, "onShareSuccess  type: $type")
        shareWaitingType = null
        mNeedRestoreWaitingDialog = false
        if (type is ShareTypeDoc) {
            playerController.pausePlay()
        }
        shareListener?.onShareSuccess(mediaId, type)
        if (type is ShareTypeLink) {
            showShareLinkPanel.postValue(type.link)
        }
    }

    //分享前置流程失败
    override fun onShareFailed(mediaId: Long, type: ShareType, error: Int, message: String) {
        DebugUtil.d(TAG, "onShareFailed  type: $type")
        shareWaitingType = null
        mNeedRestoreWaitingDialog = false
        shareListener?.onShareFailed(mediaId, type, error, message)
        if (type is ShareTypeLink) {
            if (error == ShareAction.ERROR_CODE_CONTENT_RISK) {
                showShareToast.postValue(com.soundrecorder.common.R.string.content_risk_unable_generate_share_link)
            } else {
                showShareToast.postValue(com.soundrecorder.common.R.string.link_generation_failure_retry)
            }
        }
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        currentLifecycleState = Lifecycle.Event.ON_CREATE
    }

    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        currentLifecycleState = Lifecycle.Event.ON_START
    }

    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        currentLifecycleState = Lifecycle.Event.ON_RESUME
        showShareLinkPanel.value?.let {
            showShareLinkPanel.postValue(it)
        }
        showShareToast.value?.let {
            showShareToast.postValue(it)
        }
    }

    override fun onPause(owner: LifecycleOwner) {
        super.onPause(owner)
        currentLifecycleState = Lifecycle.Event.ON_PAUSE
    }

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        currentLifecycleState = Lifecycle.Event.ON_STOP
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        currentLifecycleState = Lifecycle.Event.ON_DESTROY
    }

    fun initLifecycle(owner: LifecycleOwner) {
        owner.lifecycle.addObserver(this)
    }

    fun clearLifecycle(owner: LifecycleOwner) {
        owner.lifecycle.removeObserver(this)
    }

    /**
     * 修改录音名称后，需更新当前页面的mRecord数据
     */
    fun renameRecord(playPath: String?, displayName: String?) {
        DebugUtil.i(TAG, "playPath: $playPath, playName: $displayName")
        this.playPath.value = playPath
        this.playName.value = displayName
        mRecord?.let {
            it.displayName = displayName
            it.data = playPath
        }
        SeedlingAction.sendRecordInnerRenameEvent(recordId)
    }

    fun startSummary(convertContentData: List<ConvertContentItem>?) {
        val record = mRecord ?: return
        val from = if (mCurrentTabType.value == TAB_TYPE_CONVERT) {
            SummaryStaticUtil.EVENT_FROM_AUDIO
        } else {
            SummaryStaticUtil.EVENT_FROM_CONVERT
        }
        SummaryAction.startSummary(from, record, ConvertUtils.convertConvertContentItemToSentence(convertContentData), getMarkList()?.value)
    }

    fun getNoteData() {
        if (!FeatureOption.supportRecordSummaryFunction() || isRecycle) return
        viewModelScope.launch(Dispatchers.IO) {
            val noteData = NoteDbUtils.queryNoteByMediaId(recordId.toString())
            summaryCallId = noteData?.recordUUID
            summaryNoteId = noteData?.noteId
            mSummaryStatus.postValue(RecordSummaryManager.getSummaryState(recordId, summaryNoteId))
        }
    }

    fun toNotesSummaryActivity(context: Activity, requestCode: Int, funCallBack: ((clearSummary: Boolean, disableDialog: AlertDialog?) -> Unit)) {
        val noteId = summaryNoteId ?: return
        val callId = summaryCallId ?: return
        SummaryAction.toNotesSummaryActivity(context, callId, noteId, requestCode, funCallBack)
        val from = if (mCurrentTabType.value == TAB_TYPE_CONVERT) {
            SummaryStaticUtil.EVENT_FROM_AUDIO
        } else {
            SummaryStaticUtil.EVENT_FROM_CONVERT
        }
        SummaryStaticUtil.addClickViewSummaryEvent(from)
    }

    private fun setSummaryCallback(recordId: Long) {
        if (recordId == -1L) return
        SummaryAction.registerSummaryCallback(recordId, iSummaryCallback)
    }

    fun deleteNoteData() {
        DebugUtil.i(TAG, "deleteNoteData,noteId=$summaryNoteId")
        viewModelScope.launch(Dispatchers.IO) {
            /*callId为null(可能会先走handleClearSummaryCallId将callId置空)，就通过媒体库ID处理*/
            NoteDbUtils.deleteNoteByMediaId(recordId.toString())
        }
    }

    fun handleClearSummaryCallId(noteId: String) {
        if (noteId == summaryNoteId) {
            DebugUtil.i(TAG, "handleClearSummaryCallId,noteId=$noteId")
            mSummaryStatus.postValueSafe(RecordSummaryManager.SUMMARY_STATE_CLIENT_INIT)
            summaryNoteId = null
            summaryCallId = null
        }
    }

    /**
     * 显示 生成摘要 or 查看摘要埋点
     */
    fun addShowSummaryEvent(from: String, supportSummary: Boolean?, hasSummary: Boolean?) {
        if (hasSummary == true) { // 显示查看摘要埋点
            SummaryStaticUtil.addShowViewSummaryEvent(from)
        } else if (supportSummary == true) { //显示生成摘要埋点
            SummaryStaticUtil.addShowStartSummaryEvent(from)
        }
    }

    fun getRecord(): Record? {
        return mRecord
    }

    fun isSupportConvert(): Boolean = convertSupportType != ConvertSupportManager.CONVERT_DISABLE

    /*播放目标时间片*/
    var targetPlaySegment = MutableLiveData<List<Pair<Long, Long>>?>()
    private var curPlaySegment: Pair<Long, Long>? = null
    private var nextPlaySegmentIndex: Int = -1

    /**
     * 暂停状态下，若当前已经在target时间片的最后，则从第一个时间片开始处播放
     */
    private fun correctPlayTimeWhenControlClick(currentTimeMill: Long, targetTimeList: List<Pair<Long, Long>>?, func: ((seekTo: Long) -> Unit)) {
        if (targetTimeList.isNullOrEmpty()) {
            return
        }
        targetTimeList.lastOrNull()?.let { lastItem ->
            if (currentTimeMill >= lastItem.second) {
                targetTimeList.firstOrNull()?.let {
                    func.invoke(it.first)
                    curPlaySegment = it
                    nextPlaySegmentIndex = 1
                }
            }
        }
    }

    fun resetCurrentPlaySegment() {
        curPlaySegment = null
    }

    /**
     * 若有时间片，则只播放时间片段；
     * 若没在时间片段内，矫正播放时间，记录当前播放时间片段、下一个片段index
     */
    fun correctPlayTime(currentTimeMill: Long, targetTimeList: List<Pair<Long, Long>>?, func: ((seekTo: Long, findCurOrNextNull: Boolean) -> Unit)) {
        if (targetTimeList.isNullOrEmpty()) {
            /**1.未选择讲话人*/
//            DebugUtil.i(TAG, "target is empty")
            return
        }
        if (curPlaySegment != null && currentTimeMill in curPlaySegment!!.first..curPlaySegment!!.second) {
            /**2.当前播放mill在当前讲话人片段中*/
//            DebugUtil.i(TAG, "current in target")
            return
        }
        targetTimeList.getOrNull(nextPlaySegmentIndex)?.let {
            if (currentTimeMill in it.first..it.second) {
                /**3.当前播放mill在下一个讲话人片段中*/
                curPlaySegment = it
                nextPlaySegmentIndex += 1
                DebugUtil.i(TAG, "current in target next")
                return
            }
        }

        /**4.找到匹配time的片段，没找到则为距离他最近的下一个片段*/
        val matchOrNext = binarySearch(targetTimeList, currentTimeMill)
        DebugUtil.i(TAG, "find match one $matchOrNext,currentTimeMill=$currentTimeMill")
        if (matchOrNext == null) {
            /**5.没找到，则说明讲话人片段已经播放完毕了，定位到最后一个时间片末尾，要暂停*/
            curPlaySegment = targetTimeList.last()
            nextPlaySegmentIndex = 0
            func.invoke(curPlaySegment!!.second, true)
            return
        }

        /**6.找到对应片段，对其赋值以及跳转到该片段*/
        if (currentTimeMill in matchOrNext.second.first..matchOrNext.second.second) {
            curPlaySegment = matchOrNext.second
        } else {
            curPlaySegment = matchOrNext.second
            func.invoke(matchOrNext.second.first, false)
        }
        nextPlaySegmentIndex = matchOrNext.first + 1
    }



    /**
     * 二分查找，从时间片 [list] 中找到与当前时间 [target] 对应的 [Pair<<Long, Long>>?]
     *
     * @return fist: [list] 的下标若找不到则返回 -1 second: 下标对应的item
     */
    private fun binarySearch(list: List<Pair<Long, Long>>, target: Long): Pair<Int, Pair<Long, Long>>? {
        var low = 0
        var high = list.size - 1
        while (low <= high) {
            val mid = (high - low) / 2 + low
            val value = list[mid]
            //1.当 target 位于 MuteItem 区间内时，返回相应的 MuteItem
            when {
                target in value.first..value.second -> return Pair(mid, value)
                value.first > target -> high = mid - 1
                else -> low = mid + 1
            }

            //2.当 target 位于 MuteItem 区间外；且 target 在 静音片段List<MuteItem> 前面
            val isFirst = (mid == 0) && (value.first > target)
            //3.当 target 位于 MuteItem 区间外时；且 target 在静音片段 List<MuteItem> 之间，返回与其距离最近的下一个 MuteItem
            val isMiddleOfList = (mid > 0) && (value.first > target) && (list[mid - 1].first < target)
            if (isFirst || isMiddleOfList) {
                return Pair(mid, value)
            }
        }
        return null
    }
}