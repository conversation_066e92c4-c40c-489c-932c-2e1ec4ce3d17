/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryViewModel
 * Description:
 * Version: 1.0
 * Date: 2025/6/4
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/6/4      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.playback.aisummary

import android.app.Activity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.SmartSummaryResult
import com.soundrecorder.modulerouter.aisummary.AISummaryAction
import com.soundrecorder.modulerouter.aisummary.IAISummaryCallback
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.common.databean.NoteData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull


/**
 * 摘要数据管理ViewModel
 * 负责管理摘要数据的获取、存储、状态管理等
 *
 * 自动兼容性功能：
 * 1. 自动检测旧摘要系统中的数据
 * 2. 自动将旧系统的摘要数据迁移到新的AISummary数据库中
 * 3. 确保数据格式与新系统兼容
 * 4. 优先显示已迁移的摘要数据，避免重复生成
 * 5. 缓存机制避免重复检查，超时机制确保性能
 * 6. 用户无感知的后台自动迁移，不影响正常使用流程
 */
class AISummaryViewModel : ViewModel() {

    companion object {
        private const val TAG = "SummaryViewModel"
    }

    // 数据仓库
    private val dataManager = AISummaryDataManager.getInstance()

    // 当前录音ID
    private var currentMediaId: Long = -1

    // 摘要状态
    private val _summaryState = MutableLiveData<SummaryState>()
    val summaryState: LiveData<SummaryState> = _summaryState

    // 摘要内容
    private val _summaryContent = MutableLiveData<String?>()
    val summaryContent: MutableLiveData<String?> = _summaryContent

    // 错误信息
    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    // 摘要历史版本列表
    private val _summaryHistory = MutableLiveData<List<AISummaryDataManager.SummaryEntity>>()
    val summaryHistory: LiveData<List<AISummaryDataManager.SummaryEntity>> = _summaryHistory

    // 当前选中的摘要版本索引
    private val _currentSummaryIndex = MutableLiveData<Int>()
    val currentSummaryIndex: LiveData<Int> = _currentSummaryIndex

    // 是否有多个摘要版本
    private val _hasMultipleVersions = MutableLiveData<Boolean>()
    val hasMultipleVersions: LiveData<Boolean> = _hasMultipleVersions

    // 是否已注册回调
    private var isCallbackRegistered = false

    // 兼容性检查缓存 - 记录已检查过的录音ID，避免重复检查
    private val checkedRecordIds = mutableSetOf<Long>()

    // 兼容性检查超时时间（毫秒）
    private val compatibilityCheckTimeout = 3000L

    // AI摘要回调
    private val aiSummaryCallback = object : IAISummaryCallback {

        override fun onAISummaryStart(mediaId: Long, extras: Map<String, Any>?) {
            DebugUtil.d(TAG, "onAISummaryStart: mediaId:$mediaId")
            if (mediaId == currentMediaId) {
                _summaryState.postValue(SummaryState.GENERATING)
            }
        }

        override fun onAISummaryFinished(mediaId: Long, jsonResult: String, extras: Map<String, Any>?) {
            DebugUtil.d(TAG, "onAISummaryFinished: mediaId=$mediaId, result length=${jsonResult.length}")
            if (mediaId == currentMediaId && jsonResult.isNotEmpty()) {
                processSummaryResult(jsonResult)
            }
        }

        override fun onAISummaryError(mediaId: Long, errorCode: Int, errorMsg: String?) {
            DebugUtil.e(TAG, "onAISummaryError: mediaId=$mediaId, errorCode=$errorCode, errorMsg=$errorMsg")
            if (mediaId == currentMediaId) {
                handleSummaryError(errorCode, errorMsg)
            }
        }

        override fun onAISummaryStop(mediaId: Long, extras: Map<String, Any>?) {
            DebugUtil.d(TAG, "onAISummaryStop: mediaId=$mediaId, extras=$extras")
            if (mediaId == currentMediaId) {
                _summaryState.postValue(SummaryState.STOPPED)
            }
        }

        override fun onAISummaryEnd(mediaId: Long) {
            DebugUtil.d(TAG, "onAISummaryEnd: mediaId=$mediaId")
        }
    }

    /**
     * 摘要状态枚举
     */
    enum class SummaryState {
        EMPTY,
        GENERATING,
        COMPLETED,
        ERROR,
        STOPPED
    }

    /**
     * 设置当前录音ID并初始化 - 包含兼容性检查
     */
    fun setRecordId(mediaId: Long) {
        if (mediaId == currentMediaId) {
            DebugUtil.d(TAG, "setRecordId: recordId not changed, skip")
            return
        }
        DebugUtil.d(TAG, "setRecordId: $currentMediaId -> $mediaId")
        // 注销旧的回调
        unregisterCallback()
        // 更新录音ID
        currentMediaId = mediaId
        // 重置状态
        resetState()
        // 注册新的回调
        registerCallback()
        // 检查现有摘要（包含兼容性检查和数据迁移）
        checkExistingSummary()
    }

    /**
     * 生成摘要
     */
    fun generateSummary() {
        if (currentMediaId <= 0) {
            DebugUtil.w(TAG, "generateSummary: invalid recordId=$currentMediaId")
            _errorMessage.postValue("无效的录音ID")
            return
        }

        DebugUtil.d(TAG, "generateSummary: recordId=$currentMediaId")

        viewModelScope.launch {
            try {
                // 检查是否已经在生成中
                val isRunning = withContext(Dispatchers.IO) {
                    AISummaryAction.checkIsTaskRunning(currentMediaId)
                }

                if (isRunning) {
                    DebugUtil.w(TAG, "generateSummary: task already running")
                    _summaryState.postValue(SummaryState.GENERATING)
                    return@launch
                }

                // 更新状态为生成中
                _summaryState.postValue(SummaryState.GENERATING)

                // 直接启动摘要生成（简化调用链）
                val success = withContext(Dispatchers.IO) {
                    AISummaryAction.startAISummary(currentMediaId)
                }

                if (!success) {
                    DebugUtil.e(TAG, "generateSummary: failed to start")
                    _summaryState.postValue(SummaryState.ERROR)
                    _errorMessage.postValue("启动摘要生成失败")
                }
            } catch (e: Exception) {
                DebugUtil.e(TAG, "generateSummary: exception=${e.message}")
                _summaryState.postValue(SummaryState.ERROR)
                _errorMessage.postValue("生成摘要时发生异常: ${e.message}")
            }
        }
    }

    /**
     * 通过AISummaryManagerImpl生成摘要 - 完整流程（包含权限检查、插件下载、文件检查）
     */
    fun generateSummaryWithManager(activity: Activity?, aiSummaryManagerImpl: AISummaryManagerImpl?) {
        if (currentMediaId <= 0) {
            DebugUtil.w(TAG, "generateSummaryWithManager: invalid recordId=$currentMediaId")
            _errorMessage.postValue("无效的录音ID")
            return
        }

        if (activity == null || aiSummaryManagerImpl == null) {
            DebugUtil.w(TAG, "generateSummaryWithManager: activity or manager is null")
            _errorMessage.postValue("参数无效")
            return
        }

        DebugUtil.d(TAG, "generateSummaryWithManager: recordId=$currentMediaId")

        viewModelScope.launch {
            try {
                // 检查是否已经在生成中
                val isRunning = withContext(Dispatchers.IO) {
                    AISummaryAction.checkIsTaskRunning(currentMediaId)
                }

                if (isRunning) {
                    DebugUtil.w(TAG, "generateSummaryWithManager: task already running")
                    _summaryState.postValue(SummaryState.GENERATING)
                    return@launch
                }

                // 更新状态为生成中
                _summaryState.postValue(SummaryState.GENERATING)

                // 使用AISummaryManagerImpl处理完整流程（权限检查、插件下载、文件检查等）
                withContext(Dispatchers.Main) {
                    val mediaIdList = mutableListOf<Long>()
                    mediaIdList.add(currentMediaId)

                    aiSummaryManagerImpl.startAISummaryClickHandle(
                        activity,
                        mediaIdList,
                        1, // PAGE_FROM_PLAYBACK
                    )
                }
            } catch (e: Exception) {
                DebugUtil.e(TAG, "generateSummaryWithManager: exception=${e.message}")
                _summaryState.postValue(SummaryState.ERROR)
                _errorMessage.postValue("生成摘要时发生异常: ${e.message}")
            }
        }
    }

    /**
     * 重新生成摘要
     */
    fun regenerateSummary() {
        if (currentMediaId <= 0) {
            DebugUtil.w(TAG, "regenerateSummary: invalid recordId=$currentMediaId")
            return
        }
        DebugUtil.d(TAG, "regenerateSummary: recordId=$currentMediaId")
        viewModelScope.launch {
            try {
                // 停止当前任务
                withContext(Dispatchers.IO) {
                    if (AISummaryAction.checkIsTaskRunning(currentMediaId)) {
                        AISummaryAction.stopAISummaryTask(currentMediaId)
                    }
                }
                // 清除当前数据
                clearSummaryData()
                // 重新生成
                generateSummary()
            } catch (e: Exception) {
                DebugUtil.e(TAG, "regenerateSummary: exception=${e.message}")
                _summaryState.postValue(SummaryState.ERROR)
                _errorMessage.postValue("重新生成摘要失败: ${e.message}")
            }
        }
    }

    /**
     * 停止摘要生成
     */
    fun stopSummary() {
        if (currentMediaId <= 0) {
            DebugUtil.w(TAG, "stopSummary: invalid recordId=$currentMediaId")
            return
        }

        DebugUtil.d(TAG, "stopSummary: recordId=$currentMediaId")

        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    AISummaryAction.stopAISummaryTask(currentMediaId)
                }
                _summaryState.postValue(SummaryState.STOPPED)
            } catch (e: Exception) {
                DebugUtil.e(TAG, "stopSummary: exception=${e.message}")
            }
        }
    }

    /**
     * 检查现有摘要 - 公共接口
     */
    fun checkExistingSummary() {
        checkExistingSummary(forceCheck = false)
    }

    /**
     * 强制重新检查摘要数据 - 用于Tab切换时刷新
     */
    fun forceRefreshSummary() {
        DebugUtil.d(TAG, "forceRefreshSummary: currentMediaId=$currentMediaId")
        if (currentMediaId <= 0) {
            DebugUtil.w(TAG, "forceRefreshSummary: invalid recordId")
            return
        }

        // 强制重新检查，忽略缓存状态
        checkExistingSummary(forceCheck = true)
    }

    /**
     * 切换到上一个摘要版本
     */
    fun switchToPreviousSummary() {
        val currentIndex = _currentSummaryIndex.value ?: 0
        val historyList = _summaryHistory.value ?: emptyList()

        if (historyList.isNotEmpty() && currentIndex < historyList.size - 1) {
            val newIndex = currentIndex + 1
            switchToSummaryVersion(newIndex)
        }
    }

    /**
     * 切换到下一个摘要版本
     */
    fun switchToNextSummary() {
        val currentIndex = _currentSummaryIndex.value ?: 0

        if (currentIndex > 0) {
            val newIndex = currentIndex - 1
            switchToSummaryVersion(newIndex)
        }
    }

    /**
     * 切换到指定的摘要版本
     */
    fun switchToSummaryVersion(index: Int) {
        val historyList = _summaryHistory.value ?: emptyList()

        if (index < 0 || index >= historyList.size) {
            DebugUtil.w(TAG, "switchToSummaryVersion: invalid index $index")
            return
        }

        DebugUtil.d(TAG, "switchToSummaryVersion: switching to index $index")

        viewModelScope.launch {
            try {
                // 更新数据库中的选中状态
                val updateSuccess = dataManager.setSelectedSummary(currentMediaId, index)
                if (updateSuccess) {
                    // 更新当前索引
                    _currentSummaryIndex.postValue(index)

                    // 加载选中的摘要内容
                    val selectedSummary = historyList[index]
                    loadExistingSummary(selectedSummary.summaryContent)

                    DebugUtil.d(TAG, "switchToSummaryVersion: successfully switched to index $index")
                } else {
                    DebugUtil.e(TAG, "switchToSummaryVersion: failed to update selection in database")
                    _errorMessage.postValue("切换摘要版本失败")
                }
            } catch (e: Exception) {
                DebugUtil.e(TAG, "switchToSummaryVersion: exception=${e.message}")
                _errorMessage.postValue("切换摘要版本时发生错误: ${e.message}")
            }
        }
    }

    /**
     * 切换到最新版本
     */
    fun switchToLatestSummary() {
        switchToSummaryVersion(0)
    }

    /**
     * 检查现有摘要 - 自动集成兼容性检查和数据迁移
     */
    private fun checkExistingSummary(forceCheck: Boolean = false) {
        if (currentMediaId <= 0) {
            return
        }
        viewModelScope.launch {
            try {
                // 检查是否正在生成
                val isRunning = withContext(Dispatchers.IO) {
                    AISummaryAction.checkIsTaskRunning(currentMediaId)
                }
                if (isRunning) {
                    _summaryState.postValue(SummaryState.GENERATING)
                    return@launch
                }

                // 1. 首先检查新系统中是否有摘要数据
                val summaryHistory = dataManager.getSummaryHistory(currentMediaId)
                if (summaryHistory.isNotEmpty()) {
                    DebugUtil.d(TAG, "checkExistingSummary: found ${summaryHistory.size} summaries in new system")
                    loadSummaryHistory(summaryHistory)
                    return@launch
                }

                // 2. 自动触发兼容性检查（仅对未检查过的录音ID，或强制检查时）
                if (!checkedRecordIds.contains(currentMediaId) || forceCheck) {
                    DebugUtil.d(TAG, "checkExistingSummary: triggering compatibility check for recordId=$currentMediaId, forceCheck=$forceCheck")

                    // 标记为已检查，避免重复检查（除非是强制检查）
                    if (!forceCheck) {
                        checkedRecordIds.add(currentMediaId)
                    }

                    // 异步执行兼容性检查，带超时机制
                    val legacySummary = withContext(Dispatchers.IO) {
                        performCompatibilityCheckWithTimeout(currentMediaId)
                    }

                    if (legacySummary != null) {
                        DebugUtil.d(TAG, "checkExistingSummary: found legacy summary")
                        autoMigrateLegacySummary(legacySummary)
                    } else {
                        DebugUtil.d(TAG, "checkExistingSummary: no legacy data found, showing empty state")
                        _summaryState.postValue(SummaryState.EMPTY)
                    }
                } else {
                    DebugUtil.d(TAG, "checkExistingSummary: recordId=$currentMediaId already checked, showing empty state")
                    _summaryState.postValue(SummaryState.EMPTY)
                }
            } catch (e: Exception) {
                DebugUtil.e(TAG, "checkExistingSummary: exception=${e.message}")
                _summaryState.postValue(SummaryState.EMPTY)
            }
        }
    }

    /**
     * 加载摘要历史版本列表
     */
    private fun loadSummaryHistory(summaryHistory: List<AISummaryDataManager.SummaryEntity>) {
        viewModelScope.launch {
            try {
                // 更新摘要历史列表
                _summaryHistory.postValue(summaryHistory)

                // 更新是否有多个版本的状态
                _hasMultipleVersions.postValue(summaryHistory.size > 1)

                // 查找当前选中的摘要，如果没有选中的则默认选择最新的
                val selectedIndex = summaryHistory.indexOfFirst { it.isSelected }
                val currentIndex = if (selectedIndex >= 0) selectedIndex else 0
                _currentSummaryIndex.postValue(currentIndex)

                // 加载当前选中的摘要内容
                val currentSummary = summaryHistory[currentIndex]
                loadExistingSummary(currentSummary.summaryContent)

                DebugUtil.d(TAG, "loadSummaryHistory: loaded ${summaryHistory.size} versions, current index=$currentIndex")
            } catch (e: Exception) {
                DebugUtil.e(TAG, "loadSummaryHistory: exception=${e.message}")
                _summaryState.postValue(SummaryState.ERROR)
                _errorMessage.postValue("加载摘要历史失败: ${e.message}")
            }
        }
    }

    /**
     * 加载已有摘要
     */
    private fun loadExistingSummary(summaryContent: String) {
        viewModelScope.launch {
            try {
                // 保存摘要内容
                _summaryContent.postValue(summaryContent)


                // 更新状态
                _summaryState.postValue(SummaryState.COMPLETED)

            } catch (e: Exception) {
                DebugUtil.e(TAG, "loadExistingSummary: exception=${e.message}")
                _summaryState.postValue(SummaryState.ERROR)
                _errorMessage.postValue("加载摘要失败: ${e.message}")
            }
        }
    }

    /**
     * 处理摘要结果
     */
    internal fun processSummaryResult(jsonResult: String) {
        viewModelScope.launch {
            try {
                val summaryResult = GsonUtil.fromJson(jsonResult, SmartSummaryResult::class.java)
                val summaryContent = summaryResult?.lastSummary

                if (!summaryContent.isNullOrEmpty()) {
                    // 保存摘要内容
                    _summaryContent.postValue(summaryContent)

                    // 更新状态
                    _summaryState.postValue(SummaryState.COMPLETED)

                    // 保存到数据库（使用默认摘要风格）
                    val saved = dataManager.saveSummary(currentMediaId, summaryContent, -1)
                    DebugUtil.d(TAG, "processSummaryResult: saved to database=$saved")

                    // 重新加载摘要历史版本列表
                    if (saved) {
                        val updatedHistory = dataManager.getSummaryHistory(currentMediaId)
                        if (updatedHistory.isNotEmpty()) {
                            loadSummaryHistory(updatedHistory)
                        }
                    }
                } else {
                    DebugUtil.w(TAG, "processSummaryResult: empty content")
                    _summaryState.postValue(SummaryState.ERROR)
                    _errorMessage.postValue("摘要内容为空")
                }
            } catch (e: Exception) {
                DebugUtil.e(TAG, "processSummaryResult: exception=${e.message}")
                _summaryState.postValue(SummaryState.ERROR)
                _errorMessage.postValue("解析摘要结果失败: ${e.message}")
            }
        }
    }

    /**
     * 处理摘要错误
     */
    private fun handleSummaryError(errorCode: Int, errorMsg: String?) {
        val message = when (errorCode) {
            AISummaryAction.NETWORK_ERROR -> "网络连接失败，请检查网络设置"
            AISummaryAction.CONTENT_LESS_ERROR -> "录音内容较少，无法生成摘要"
            AISummaryAction.SERVER_ERROR -> "服务异常，摘要生成失败"
            AISummaryAction.REQUEST_TIMEOUT -> "请求超时，请重试"
            else -> errorMsg ?: "生成摘要失败，错误码：$errorCode"
        }

        _summaryState.postValue(SummaryState.ERROR)
        _errorMessage.postValue(message)
    }

    private fun registerCallback() {
        if (currentMediaId > 0 && !isCallbackRegistered) {
            DebugUtil.d(TAG, "registerCallback: recordId=$currentMediaId")
            AISummaryAction.registerAISummaryCallback(currentMediaId, aiSummaryCallback)
            isCallbackRegistered = true
        }
    }

    private fun unregisterCallback() {
        if (isCallbackRegistered && currentMediaId > 0) {
            DebugUtil.d(TAG, "unregisterCallback: recordId=$currentMediaId")
            AISummaryAction.unRegisterAISummaryCallback(currentMediaId, aiSummaryCallback)
            isCallbackRegistered = false
        }
    }

    /**
     * 重置状态
     */
    private fun resetState() {
        _summaryState.postValue(SummaryState.EMPTY)
        _summaryContent.postValue("")
        _errorMessage.postValue("")
        _summaryHistory.postValue(emptyList())
        _currentSummaryIndex.postValue(0)
        _hasMultipleVersions.postValue(false)
    }

    /**
     * 清除摘要数据
     */
    private fun clearSummaryData() {
        _summaryContent.postValue("")
    }

    /**
     * 带超时机制的兼容性检查
     * @param recordId 录音ID
     * @return 如果找到旧摘要数据则返回NoteData，否则返回null
     */
    private suspend fun performCompatibilityCheckWithTimeout(recordId: Long): NoteData? {
        return try {
            // 使用超时机制避免长时间等待
            withTimeoutOrNull(compatibilityCheckTimeout) {
                checkLegacySummaryData(recordId)
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "performCompatibilityCheckWithTimeout: exception=${e.message}")
            null
        }
    }

    /**
     * 检查旧摘要系统中的数据（兼容性检查）
     * @param recordId 录音ID
     * @return 如果找到旧摘要数据则返回NoteData，否则返回null
     */
    private fun checkLegacySummaryData(recordId: Long): NoteData? {
        return try {
            // 通过mediaId查询旧摘要系统中的数据
            val noteData = NoteDbUtils.queryNoteByMediaId(recordId.toString())

            if (noteData != null && !noteData.noteId.isNullOrEmpty()) {
                DebugUtil.d(TAG, "checkLegacySummaryData: found legacy data, noteId=${noteData.noteId}, recordUUID=${noteData.recordUUID}")

                // 检查是否有有效的摘要内容
                if (!noteData.noteContent.isNullOrEmpty()) {
                    DebugUtil.d(TAG, "checkLegacySummaryData: legacy data has content, length=${noteData.noteContent?.length}")
                    noteData
                } else {
                    DebugUtil.d(TAG, "checkLegacySummaryData: legacy data found but no content")
                    // 即使没有内容，也返回noteData，表示曾经使用过旧系统
                    noteData
                }
            } else {
                DebugUtil.d(TAG, "checkLegacySummaryData: no legacy data found for recordId=$recordId")
                null
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "checkLegacySummaryData: exception=${e.message}")
            null
        }
    }

    /**
     * 自动迁移旧摘要系统的数据到新系统（优化版本）
     * @param legacyData 旧系统的摘要数据
     */
    private suspend fun autoMigrateLegacySummary(legacyData: NoteData) {
        try {
            DebugUtil.d(TAG, "autoMigrateLegacySummary: starting auto-migration for recordId=$currentMediaId")

            val summaryContent = legacyData.noteContent

            if (!summaryContent.isNullOrEmpty()) {
                // 有摘要内容，异步迁移，不阻塞UI
                DebugUtil.d(TAG, "autoMigrateLegacySummary: migrating content, length=${summaryContent.length}")

                // 先更新UI状态，提供即时反馈
                withContext(Dispatchers.Main) {
                    loadExistingSummary(summaryContent)
                }

                // 后台保存到新系统数据库
                val saved = withContext(Dispatchers.IO) {
                    dataManager.saveSummary(currentMediaId, summaryContent, -1)
                }

                if (saved) {
                    DebugUtil.d(TAG, "autoMigrateLegacySummary: successfully migrated to new system")
                } else {
                    DebugUtil.w(TAG, "autoMigrateLegacySummary: failed to save to new system, but UI already updated")
                }
            } else {
                // 没有摘要内容，但曾经使用过旧系统，显示空状态
                DebugUtil.d(TAG, "autoMigrateLegacySummary: legacy data exists but no content, showing empty state")
                withContext(Dispatchers.Main) {
                    _summaryState.postValue(SummaryState.EMPTY)
                }
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "autoMigrateLegacySummary: exception=${e.message}")
            withContext(Dispatchers.Main) {
                _summaryState.postValue(SummaryState.EMPTY)
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        DebugUtil.d(TAG, "onCleared")
        unregisterCallback()

        // 清理兼容性检查缓存
        checkedRecordIds.clear()
        DebugUtil.d(TAG, "onCleared: compatibility check cache cleared")
    }
}