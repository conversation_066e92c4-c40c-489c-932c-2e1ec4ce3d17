/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryViewModel
 * Description:
 * Version: 1.0
 * Date: 2025/6/4
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/6/4      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.playback.aisummary

import android.app.Activity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.SmartSummaryResult
import com.soundrecorder.modulerouter.aisummary.AISummaryAction
import com.soundrecorder.modulerouter.aisummary.IAISummaryCallback
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.common.databean.NoteData
import com.soundrecorder.base.BaseApplication
import android.net.Uri
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull

/**
 * 摘要数据管理ViewModel
 * 负责管理摘要数据的获取、存储、状态管理等
 *
 * 自动兼容性功能：
 * 1. 自动检测旧摘要系统中的数据
 * 2. 自动将旧系统的摘要数据迁移到新的AISummary数据库中
 * 3. 确保数据格式与新系统兼容
 * 4. 优先显示已迁移的摘要数据，避免重复生成
 * 5. 缓存机制避免重复检查，超时机制确保性能
 * 6. 用户无感知的后台自动迁移，不影响正常使用流程
 */
class AISummaryViewModel : ViewModel() {

    companion object {
        private const val TAG = "AISummaryViewModel"
        private const val COMPATIBILITY_CHECK_TIMEOUT = 3000L

        // ContentProvider查询常量
        private const val NOTE_QUERY_URI = "content://com.nearme.note/query_notes_no_limit?local_id_list="
        private const val NOTE_ID = "local_id"
        private const val TEXT = "text"
        private const val EXTRA_INFO = "extra_info"
        private const val AUDIO_INFO = "audio_info"
        private const val MEDIA_ID = "mediaId"
    }

    // 数据管理
    private val dataManager = AISummaryDataRepository.getInstance()
    private var currentMediaId: Long = -1
    private var isCallbackRegistered = false
    private val checkedRecordIds = mutableSetOf<Long>()

    // LiveData - 遵循项目命名规范
    private val _summaryState = MutableLiveData<SummaryState>()
    val summaryState: LiveData<SummaryState> = _summaryState

    private val _summaryContent = MutableLiveData<String?>()
    val summaryContent: MutableLiveData<String?> = _summaryContent

    private val _summaryItems = MutableLiveData<List<SummaryItem>>()
    val summaryItems: LiveData<List<SummaryItem>> = _summaryItems

    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    private val _summaryHistory = MutableLiveData<List<AISummaryDataRepository.SummaryEntity>>()
    val summaryHistory: LiveData<List<AISummaryDataRepository.SummaryEntity>> = _summaryHistory

    private val _currentSummaryIndex = MutableLiveData<Int>()
    val currentSummaryIndex: LiveData<Int> = _currentSummaryIndex

    private val _hasMultipleVersions = MutableLiveData<Boolean>()
    val hasMultipleVersions: LiveData<Boolean> = _hasMultipleVersions

    // AI摘要回调
    private val aiSummaryCallback = object : IAISummaryCallback {
        override fun onAISummaryStart(mediaId: Long, extras: Map<String, Any>?) {
            if (mediaId == currentMediaId) {
                _summaryState.postValue(SummaryState.GENERATING)
            }
        }

        override fun onAISummaryFinished(mediaId: Long, jsonResult: String, extras: Map<String, Any>?) {
            if (mediaId == currentMediaId && jsonResult.isNotEmpty()) {
                processSummaryResult(jsonResult)
            }
        }

        override fun onAISummaryError(mediaId: Long, errorCode: Int, errorMsg: String?) {
            if (mediaId == currentMediaId) {
                handleSummaryError(errorCode, errorMsg)
            }
        }

        override fun onAISummaryStop(mediaId: Long, extras: Map<String, Any>?) {
            if (mediaId == currentMediaId) {
                _summaryState.postValue(SummaryState.STOPPED)
            }
        }

        override fun onAISummaryEnd(mediaId: Long) {
            // 摘要结束，无需特殊处理
        }
    }

    /**
     * 摘要状态枚举
     */
    enum class SummaryState {
        EMPTY,
        GENERATING,
        COMPLETED,
        ERROR,
        STOPPED
    }

    /**
     * 设置当前录音ID并初始化
     */
    fun setRecordId(mediaId: Long) {
        if (mediaId == currentMediaId) return

        unregisterCallback()
        currentMediaId = mediaId
        resetState()
        registerCallback()
        checkExistingSummary()
    }

    /**
     * 生成摘要
     */
    fun generateSummary() {
        if (!isValidMediaId()) return

        viewModelScope.launch {
            val isRunning = withContext(Dispatchers.IO) {
                AISummaryAction.checkIsTaskRunning(currentMediaId)
            }

            if (isRunning) {
                _summaryState.postValue(SummaryState.GENERATING)
                return@launch
            }

            _summaryState.postValue(SummaryState.GENERATING)

            val success = withContext(Dispatchers.IO) {
                AISummaryAction.startAISummary(currentMediaId)
            }

            if (!success) {
                updateErrorState("启动摘要生成失败")
            }
        }
    }

    /**
     * 通过AISummaryManagerImpl生成摘要
     */
    fun generateSummaryWithManager(activity: Activity?, aiSummaryManagerImpl: AISummaryManagerImpl?) {
        if (!isValidMediaId() || activity == null || aiSummaryManagerImpl == null) {
            updateErrorState("参数无效")
            return
        }

        viewModelScope.launch {
            val isRunning = withContext(Dispatchers.IO) {
                AISummaryAction.checkIsTaskRunning(currentMediaId)
            }

            if (isRunning) {
                _summaryState.postValue(SummaryState.GENERATING)
                return@launch
            }

            _summaryState.postValue(SummaryState.GENERATING)

            withContext(Dispatchers.Main) {
                aiSummaryManagerImpl.startAISummaryClickHandle(
                    activity,
                    mutableListOf(currentMediaId),
                    1 // PAGE_FROM_PLAYBACK
                )
            }
        }
    }

    /**
     * 重新生成摘要
     */
    fun regenerateSummary() {
        if (!isValidMediaId()) return

        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                if (AISummaryAction.checkIsTaskRunning(currentMediaId)) {
                    AISummaryAction.stopAISummaryTask(currentMediaId)
                }
            }
            clearSummaryData()
            generateSummary()
        }
    }

    /**
     * 停止摘要生成
     */
    fun stopSummary() {
        if (!isValidMediaId()) return

        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                AISummaryAction.stopAISummaryTask(currentMediaId)
            }
            _summaryState.postValue(SummaryState.STOPPED)
        }
    }

    /**
     * 检查现有摘要
     */
    fun checkExistingSummary() {
        performSummaryCheck(forceCheck = false)
    }

    /**
     * 强制重新检查摘要数据
     */
    fun forceRefreshSummary() {
        if (!isValidMediaId()) return
        performSummaryCheck(forceCheck = true)
    }

    /**
     * 切换到上一个摘要版本
     */
    fun switchToPreviousSummary() {
        val currentIndex = _currentSummaryIndex.value ?: 0
        val historyList = _summaryHistory.value ?: emptyList()

        if (historyList.isNotEmpty() && currentIndex < historyList.size - 1) {
            switchToSummaryVersion(currentIndex + 1)
        }
    }

    /**
     * 切换到下一个摘要版本
     */
    fun switchToNextSummary() {
        val currentIndex = _currentSummaryIndex.value ?: 0
        if (currentIndex > 0) {
            switchToSummaryVersion(currentIndex - 1)
        }
    }

    /**
     * 切换到最新版本
     */
    fun switchToLatestSummary() {
        switchToSummaryVersion(0)
    }

    /**
     * 切换到指定的摘要版本
     */
    fun switchToSummaryVersion(index: Int) {
        val historyList = _summaryHistory.value ?: emptyList()
        if (index < 0 || index >= historyList.size) return

        viewModelScope.launch {
            val updateSuccess = dataManager.setSelectedSummary(currentMediaId, index)
            if (updateSuccess) {
                _currentSummaryIndex.postValue(index)
                val selectedSummary = historyList[index]
                loadExistingSummary(selectedSummary.summaryContent)
            } else {
                updateErrorState("切换摘要版本失败")
            }
        }
    }

    /**
     * 测试数据流
     */
    fun testDataFlow() {
        if (!isValidMediaId()) return

        viewModelScope.launch {
            val testContent = AISummaryDataRepository.createTestSummaryContent()
            val saved = withContext(Dispatchers.IO) {
                dataManager.saveSummary(currentMediaId, testContent, -1)
            }

            if (saved) {
                val summaryHistory = withContext(Dispatchers.IO) {
                    dataManager.getSummaryHistory(currentMediaId)
                }
                if (summaryHistory.isNotEmpty()) {
                    loadSummaryHistory(summaryHistory)
                }
            } else {
                updateErrorState("测试数据保存失败")
            }
        }
    }

    /**
     * 测试兼容性检查功能
     */
    fun testCompatibilityCheck() {
        if (!isValidMediaId()) return

        viewModelScope.launch {
            val legacySummary = withContext(Dispatchers.IO) {
                performCompatibilityCheckWithTimeout(currentMediaId)
            }

            if (legacySummary != null) {
                autoMigrateLegacySummary(legacySummary)
            } else {
                _summaryState.postValue(SummaryState.EMPTY)
            }
        }
    }

    /**
     * 执行摘要检查 - 统一的检查逻辑
     */
    private fun performSummaryCheck(forceCheck: Boolean = false) {
        if (!isValidMediaId()) return

        viewModelScope.launch {
            // 检查是否正在生成
            val isRunning = withContext(Dispatchers.IO) {
                AISummaryAction.checkIsTaskRunning(currentMediaId)
            }
            if (isRunning) {
                _summaryState.postValue(SummaryState.GENERATING)
                return@launch
            }

            // 检查新系统中的摘要数据
            val summaryHistory = dataManager.getSummaryHistory(currentMediaId)
            if (summaryHistory.isNotEmpty()) {
                loadSummaryHistory(summaryHistory)
                return@launch
            }

            // 兼容性检查
            if (!checkedRecordIds.contains(currentMediaId) || forceCheck) {
                if (!forceCheck) {
                    checkedRecordIds.add(currentMediaId)
                }

                val legacySummary = withContext(Dispatchers.IO) {
                    performCompatibilityCheckWithTimeout(currentMediaId)
                }

                if (legacySummary != null) {
                    autoMigrateLegacySummary(legacySummary)
                } else {
                    _summaryState.postValue(SummaryState.EMPTY)
                }
            } else {
                _summaryState.postValue(SummaryState.EMPTY)
            }
        }
    }

    /**
     * 加载摘要历史版本列表
     */
    private fun loadSummaryHistory(summaryHistory: List<AISummaryDataRepository.SummaryEntity>) {
        _summaryHistory.postValue(summaryHistory)
        _hasMultipleVersions.postValue(summaryHistory.size > 1)

        val selectedIndex = summaryHistory.indexOfFirst { it.isSelected }
        val currentIndex = if (selectedIndex >= 0) selectedIndex else 0
        _currentSummaryIndex.postValue(currentIndex)

        val currentSummary = summaryHistory[currentIndex]
        loadExistingSummary(currentSummary.summaryContent)
    }

    /**
     * 加载已有摘要
     */
    private fun loadExistingSummary(summaryContent: String) {
        _summaryContent.postValue(summaryContent)
        val summaryItems = SummaryItem.parseContent(summaryContent)
        _summaryItems.postValue(summaryItems)
        _summaryState.postValue(SummaryState.COMPLETED)
    }

    // ==================== 工具方法 ====================

    /**
     * 验证MediaId是否有效
     */
    private fun isValidMediaId(): Boolean {
        if (currentMediaId <= 0) {
            updateErrorState("无效的录音ID")
            return false
        }
        return true
    }

    /**
     * 更新错误状态
     */
    private fun updateErrorState(message: String) {
        _summaryState.postValue(SummaryState.ERROR)
        _errorMessage.postValue(message)
    }

    /**
     * 处理摘要结果
     */
    internal fun processSummaryResult(jsonResult: String) {
        viewModelScope.launch {
            val summaryResult = GsonUtil.fromJson(jsonResult, SmartSummaryResult::class.java)
            val summaryContent = summaryResult?.lastSummary

            if (!summaryContent.isNullOrEmpty()) {
                _summaryContent.postValue(summaryContent)
                val summaryItems = SummaryItem.parseContent(summaryContent)
                _summaryItems.postValue(summaryItems)
                _summaryState.postValue(SummaryState.COMPLETED)

                val saved = dataManager.saveSummary(currentMediaId, summaryContent, -1)
                if (saved) {
                    val updatedHistory = dataManager.getSummaryHistory(currentMediaId)
                    if (updatedHistory.isNotEmpty()) {
                        loadSummaryHistory(updatedHistory)
                    }
                }
            } else {
                updateErrorState("摘要内容为空")
            }
        }
    }

    /**
     * 处理摘要错误
     */
    private fun handleSummaryError(errorCode: Int, errorMsg: String?) {
        val message = when (errorCode) {
            AISummaryAction.NETWORK_ERROR -> "网络连接失败"
            AISummaryAction.CONTENT_LESS_ERROR -> "录音内容较少，无法生成摘要"
            AISummaryAction.SERVER_ERROR -> "服务异常，摘要生成失败"
            AISummaryAction.REQUEST_TIMEOUT -> "请求超时，请重试"
            else -> errorMsg ?: "生成摘要失败，错误码：$errorCode"
        }
        updateErrorState(message)
    }

    // ==================== 回调和状态管理 ====================

    private fun registerCallback() {
        if (currentMediaId > 0 && !isCallbackRegistered) {
            AISummaryAction.registerAISummaryCallback(currentMediaId, aiSummaryCallback)
            isCallbackRegistered = true
        }
    }

    private fun unregisterCallback() {
        if (isCallbackRegistered && currentMediaId > 0) {
            AISummaryAction.unRegisterAISummaryCallback(currentMediaId, aiSummaryCallback)
            isCallbackRegistered = false
        }
    }

    /**
     * 重置状态
     */
    private fun resetState() {
        _summaryState.postValue(SummaryState.EMPTY)
        _summaryContent.postValue("")
        _summaryItems.postValue(emptyList())
        _errorMessage.postValue("")
        _summaryHistory.postValue(emptyList())
        _currentSummaryIndex.postValue(0)
        _hasMultipleVersions.postValue(false)
    }

    /**
     * 清除摘要数据
     */
    private fun clearSummaryData() {
        _summaryContent.postValue("")
        _summaryItems.postValue(emptyList())
    }

    /**
     * 带超时机制的兼容性检查
     * @param recordId 录音ID
     * @return 如果找到旧摘要数据则返回NoteData，否则返回null
     */
    private suspend fun performCompatibilityCheckWithTimeout(recordId: Long): NoteData? {
        return try {
            // 使用超时机制避免长时间等待
            withTimeoutOrNull(compatibilityCheckTimeout) {
                checkLegacySummaryData(recordId)
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "performCompatibilityCheckWithTimeout: exception=${e.message}")
            null
        }
    }

    /**
     * 检查旧摘要系统中的数据（兼容性检查）- 基于 ContentProvider 查询
     * @param recordId 录音ID
     * @return 如果找到旧摘要数据则返回包含完整内容的NoteData，否则返回null
     */
    private fun checkLegacySummaryData(recordId: Long): NoteData? {
        return try {
            DebugUtil.d(TAG, "checkLegacySummaryData: starting compatibility check for recordId=$recordId")

            // 第一步：查询本地便签数据库，获取与当前 mediaId 关联的 NoteData
            val localNoteDataList = NoteDbUtils.queryAllNotes()
            val relatedNoteData = localNoteDataList.find { noteData ->
                noteData.mediaId == recordId.toString()
            }

            if (relatedNoteData?.noteId.isNullOrEmpty()) {
                DebugUtil.d(TAG, "checkLegacySummaryData: no related noteData found for recordId=$recordId")
                return null
            }

            DebugUtil.d(TAG, "checkLegacySummaryData: found related noteData, noteId=${relatedNoteData?.noteId}")

            // 第二步：使用 ContentProvider 查询系统便签应用获取完整摘要内容
            val fullSummaryContent = queryLegacySummaryFromContentProvider(relatedNoteData!!.noteId!!)

            if (!fullSummaryContent.isNullOrEmpty()) {
                DebugUtil.d(TAG, "checkLegacySummaryData: found full summary content, length=${fullSummaryContent.length}")
                // 创建包含完整内容的 NoteData
                return relatedNoteData.copy(noteContent = fullSummaryContent)
            } else {
                DebugUtil.d(TAG, "checkLegacySummaryData: noteData exists but no summary content found")
                return relatedNoteData
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "checkLegacySummaryData: exception=${e.message}")
            null
        }
    }

    /**
     * 通过 ContentProvider 查询系统便签应用获取完整摘要内容
     * @param noteId 便签ID
     * @return 摘要内容，如果查询失败则返回null
     */
    private fun queryLegacySummaryFromContentProvider(noteId: String): String? {
        return try {
            DebugUtil.d(TAG, "queryLegacySummaryFromContentProvider: querying noteId=$noteId")

            // 构建查询 URI，参考 NoteSearchRepository 的实现
            val noteIdList = arrayListOf<String>()
            noteIdList.add(noteId)
            val uri = Uri.parse("$NOTE_QUERY_URI${Uri.encode(noteIdList.toString())}")

            DebugUtil.d(TAG, "queryLegacySummaryFromContentProvider: query URI=$uri")

            // 执行 ContentProvider 查询
            val cursor = BaseApplication.getAppContext().contentResolver.query(uri, null, null, null, null)
            cursor?.use {
                if (it.count > 0 && it.moveToFirst()) {
                    val textIndex = it.getColumnIndex(TEXT)
                    val extraInfoIndex = it.getColumnIndex(EXTRA_INFO)
                    val noteIdIndex = it.getColumnIndex(NOTE_ID)

                    if (textIndex >= 0) {
                        val summaryText = it.getString(textIndex)
                        val extraInfo = if (extraInfoIndex >= 0) it.getString(extraInfoIndex) else null
                        val queriedNoteId = if (noteIdIndex >= 0) it.getString(noteIdIndex) else null

                        DebugUtil.d(TAG, "queryLegacySummaryFromContentProvider: found data")
                        DebugUtil.d(TAG, "  noteId: $queriedNoteId")
                        DebugUtil.d(TAG, "  summaryText length: ${summaryText?.length ?: 0}")
                        DebugUtil.d(TAG, "  hasExtraInfo: ${!extraInfo.isNullOrEmpty()}")

                        // 验证查询结果的 noteId 是否匹配
                        if (queriedNoteId == noteId && !summaryText.isNullOrEmpty()) {
                            // 去掉换行符，替换为空格，保持与搜索逻辑一致
                            return summaryText.replace("\n", " ")
                        } else {
                            DebugUtil.w(TAG, "queryLegacySummaryFromContentProvider: noteId mismatch or empty content")
                        }
                    } else {
                        DebugUtil.w(TAG, "queryLegacySummaryFromContentProvider: TEXT column not found")
                    }
                } else {
                    DebugUtil.d(TAG, "queryLegacySummaryFromContentProvider: no data found for noteId=$noteId")
                }
            }

            null
        } catch (e: Exception) {
            DebugUtil.e(TAG, "queryLegacySummaryFromContentProvider: exception=${e.message}")
            null
        }
    }

    /**
     * 自动迁移旧摘要系统的数据到新系统（基于 ContentProvider 查询的增强版本）
     * @param legacyData 旧系统的摘要数据（包含从 ContentProvider 查询到的完整内容）
     */
    private suspend fun autoMigrateLegacySummary(legacyData: NoteData) {
        try {
            DebugUtil.d(TAG, "autoMigrateLegacySummary: starting enhanced auto-migration for recordId=$currentMediaId")
            DebugUtil.d(TAG, "autoMigrateLegacySummary: legacyData - noteId=${legacyData.noteId}, mediaId=${legacyData.mediaId}")

            val summaryContent = legacyData.noteContent

            if (!summaryContent.isNullOrEmpty()) {
                DebugUtil.d(TAG, "autoMigrateLegacySummary: migrating content, length=${summaryContent.length}")

                // 构建 SummaryEntity 数据，设置正确的字段
                val createTime = System.currentTimeMillis()
                val summaryStyle = -1 // 默认摘要风格

                DebugUtil.d(TAG, "autoMigrateLegacySummary: preparing migration data")
                DebugUtil.d(TAG, "  recordId: $currentMediaId")
                DebugUtil.d(TAG, "  summaryContent length: ${summaryContent.length}")
                DebugUtil.d(TAG, "  createTime: $createTime")
                DebugUtil.d(TAG, "  summaryStyle: $summaryStyle")

                // 先更新UI状态，提供即时反馈
                withContext(Dispatchers.Main) {
                    loadExistingSummary(summaryContent)
                }

                // 后台保存到新系统数据库
                val saved = withContext(Dispatchers.IO) {
                    dataManager.saveSummary(currentMediaId, summaryContent, summaryStyle)
                }

                if (saved) {
                    DebugUtil.d(TAG, "autoMigrateLegacySummary: successfully migrated to new system")

                    // 重新加载摘要历史，确保UI显示最新的数据
                    val summaryHistory = withContext(Dispatchers.IO) {
                        dataManager.getSummaryHistory(currentMediaId)
                    }

                    if (summaryHistory.isNotEmpty()) {
                        DebugUtil.d(TAG, "autoMigrateLegacySummary: reloading summary history, found ${summaryHistory.size} versions")
                        withContext(Dispatchers.Main) {
                            loadSummaryHistory(summaryHistory)
                        }
                    } else {
                        DebugUtil.w(TAG, "autoMigrateLegacySummary: no summary history found after migration")
                    }
                } else {
                    DebugUtil.w(TAG, "autoMigrateLegacySummary: failed to save to new system, but UI already updated")
                    // 即使保存失败，UI已经显示了内容，用户体验不受影响
                }
            } else {
                // 没有摘要内容，但曾经使用过旧系统，显示空状态
                DebugUtil.d(TAG, "autoMigrateLegacySummary: legacy data exists but no content, showing empty state")
                withContext(Dispatchers.Main) {
                    _summaryState.postValue(SummaryState.EMPTY)
                }
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "autoMigrateLegacySummary: exception=${e.message}")
            withContext(Dispatchers.Main) {
                _summaryState.postValue(SummaryState.ERROR)
                _errorMessage.postValue("数据迁移失败: ${e.message}")
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        DebugUtil.d(TAG, "onCleared")
        unregisterCallback()

        // 清理兼容性检查缓存
        checkedRecordIds.clear()
        DebugUtil.d(TAG, "onCleared: compatibility check cache cleared")
    }
}