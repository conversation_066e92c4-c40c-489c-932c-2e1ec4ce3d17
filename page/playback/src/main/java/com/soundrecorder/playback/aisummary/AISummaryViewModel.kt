/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryViewModel
 * Description:
 * Version: 1.0
 * Date: 2025/6/4
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/6/4      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.playback.aisummary

import android.app.Activity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.SmartSummaryResult
import com.soundrecorder.modulerouter.aisummary.AISummaryAction
import com.soundrecorder.modulerouter.aisummary.IAISummaryCallback
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.common.databean.NoteData
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.playback.aisummary.manager.AISummaryManagerImpl
import android.net.Uri
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull

/**
 * 摘要数据管理ViewModel
 * 负责管理摘要数据的获取、存储、状态管理等
 *
 * 自动兼容性功能：
 * 1. 自动检测旧摘要系统中的数据
 * 2. 自动将旧系统的摘要数据迁移到新的AISummary数据库中
 * 3. 确保数据格式与新系统兼容
 * 4. 优先显示已迁移的摘要数据，避免重复生成
 * 5. 缓存机制避免重复检查，超时机制确保性能
 * 6. 用户无感知的后台自动迁移，不影响正常使用流程
 */
class AISummaryViewModel : ViewModel() {

    companion object {
        private const val TAG = "AISummaryViewModel"
        private const val COMPATIBILITY_CHECK_TIMEOUT = 3000L

        // ContentProvider查询常量
        private const val NOTE_QUERY_URI = "content://com.nearme.note/query_notes_no_limit?local_id_list="
        private const val NOTE_ID = "local_id"
        private const val TEXT = "text"
        private const val EXTRA_INFO = "extra_info"
        private const val AUDIO_INFO = "audio_info"
        private const val MEDIA_ID = "mediaId"
    }

    // 数据管理
    private val dataManager = AISummaryDataRepository.getInstance()
    private var currentMediaId: Long = -1
    private var isCallbackRegistered = false
    private val checkedRecordIds = mutableSetOf<Long>()

    // 摘要状态和内容相关
    var mSummaryState = MutableLiveData<SummaryState>()
    var summaryContent = MutableLiveData<String?>()
    var summaryItems = MutableLiveData<List<SummaryItem>>()
    var mErrorMessage = MutableLiveData<String>()

    // 摘要历史版本相关
    var summaryHistory = MutableLiveData<List<AISummaryDataRepository.SummaryEntity>>()
    var mCurrentSummaryIndex = MutableLiveData<Int>()
    var mHasMultipleVersions = MutableLiveData<Boolean>()

    // AI摘要回调
    private val aiSummaryCallback = object : IAISummaryCallback {
        override fun onAISummaryStart(mediaId: Long, extras: Map<String, Any>?) {
            DebugUtil.d(TAG, "onAISummaryStart: mediaId=$mediaId, currentMediaId=$currentMediaId")
            if (mediaId == currentMediaId) {
                mSummaryState.postValue(SummaryState.GENERATING)
            }
        }

        override fun onAISummaryFinished(mediaId: Long, jsonResult: String, extras: Map<String, Any>?) {
            DebugUtil.d(TAG, "onAISummaryFinished: mediaId=$mediaId, resultLength=${jsonResult.length}")
            if (mediaId == currentMediaId && jsonResult.isNotEmpty()) {
                processSummaryResult(jsonResult)
            }
        }

        override fun onAISummaryError(mediaId: Long, errorCode: Int, errorMsg: String?) {
            DebugUtil.e(TAG, "onAISummaryError: mediaId=$mediaId, errorCode=$errorCode, errorMsg=$errorMsg")
            if (mediaId == currentMediaId) {
                handleSummaryError(errorCode, errorMsg)
            }
        }

        override fun onAISummaryStop(mediaId: Long, extras: Map<String, Any>?) {
            DebugUtil.d(TAG, "onAISummaryStop: mediaId=$mediaId")
            if (mediaId == currentMediaId) {
                mSummaryState.postValue(SummaryState.STOPPED)
            }
        }

        override fun onAISummaryEnd(mediaId: Long) {
            DebugUtil.d(TAG, "onAISummaryEnd: mediaId=$mediaId")
        }
    }

    /**
     * 摘要状态枚举
     */
    enum class SummaryState {
        EMPTY,
        GENERATING,
        COMPLETED,
        ERROR,
        STOPPED
    }

    /**
     * 设置当前录音ID并初始化
     */
    fun setRecordId(mediaId: Long) {
        DebugUtil.d(TAG, "setRecordId: $currentMediaId -> $mediaId")
        if (mediaId == currentMediaId) {
            DebugUtil.d(TAG, "setRecordId: mediaId unchanged, skip")
            return
        }

        unregisterCallback()
        currentMediaId = mediaId
        resetState()
        registerCallback()
        checkExistingSummary()
        DebugUtil.d(TAG, "setRecordId: initialization completed for mediaId=$mediaId")
    }

    /**
     * 生成摘要
     */
    fun generateSummary() {
        DebugUtil.d(TAG, "generateSummary: start for mediaId=$currentMediaId")
        if (!isValidMediaId()) return

        viewModelScope.launch {
            val isRunning = withContext(Dispatchers.IO) {
                AISummaryAction.checkIsTaskRunning(currentMediaId)
            }

            if (isRunning) {
                DebugUtil.d(TAG, "generateSummary: task already running")
                mSummaryState.postValue(SummaryState.GENERATING)
                return@launch
            }

            DebugUtil.d(TAG, "generateSummary: starting AI summary task")
            mSummaryState.postValue(SummaryState.GENERATING)

            val success = withContext(Dispatchers.IO) {
                AISummaryAction.startAISummary(currentMediaId)
            }

            if (!success) {
                DebugUtil.e(TAG, "generateSummary: failed to start AI summary")
                updateErrorState("启动摘要生成失败")
            } else {
                DebugUtil.d(TAG, "generateSummary: AI summary task started successfully")
            }
        }
    }

    /**
     * 通过AISummaryManagerImpl生成摘要
     */
    fun generateSummaryWithManager(activity: Activity?, aiSummaryManagerImpl: AISummaryManagerImpl?) {
        DebugUtil.d(TAG, "generateSummaryWithManager: start for mediaId=$currentMediaId")
        if (!isValidMediaId() || activity == null || aiSummaryManagerImpl == null) {
            DebugUtil.e(TAG, "generateSummaryWithManager: invalid parameters")
            updateErrorState("参数无效")
            return
        }

        viewModelScope.launch {
            val isRunning = withContext(Dispatchers.IO) {
                AISummaryAction.checkIsTaskRunning(currentMediaId)
            }

            if (isRunning) {
                DebugUtil.d(TAG, "generateSummaryWithManager: task already running")
                mSummaryState.postValue(SummaryState.GENERATING)
                return@launch
            }

            DebugUtil.d(TAG, "generateSummaryWithManager: starting with manager")
            mSummaryState.postValue(SummaryState.GENERATING)

            withContext(Dispatchers.Main) {
                aiSummaryManagerImpl.startAISummaryClickHandle(
                    activity,
                    mutableListOf(currentMediaId),
                    1 // PAGE_FROM_PLAYBACK
                )
            }
            DebugUtil.d(TAG, "generateSummaryWithManager: manager started successfully")
        }
    }

    /**
     * 重新生成摘要
     */
    fun regenerateSummary() {
        if (!isValidMediaId()) return

        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                if (AISummaryAction.checkIsTaskRunning(currentMediaId)) {
                    AISummaryAction.stopAISummaryTask(currentMediaId)
                }
            }
            clearSummaryData()
            generateSummary()
        }
    }

    /**
     * 停止摘要生成
     */
    fun stopSummary() {
        if (!isValidMediaId()) return

        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                AISummaryAction.stopAISummaryTask(currentMediaId)
            }
            mSummaryState.postValue(SummaryState.STOPPED)
        }
    }

    /**
     * 检查现有摘要
     */
    fun checkExistingSummary() {
        performSummaryCheck(forceCheck = false)
    }

    /**
     * 强制重新检查摘要数据
     */
    fun forceRefreshSummary() {
        if (!isValidMediaId()) return
        performSummaryCheck(forceCheck = true)
    }

    /**
     * 切换到上一个摘要版本
     */
    fun switchToPreviousSummary() {
        val currentIndex = mCurrentSummaryIndex.value ?: 0
        val historyList = summaryHistory.value ?: emptyList()

        if (historyList.isNotEmpty() && currentIndex < historyList.size - 1) {
            switchToSummaryVersion(currentIndex + 1)
        }
    }

    /**
     * 切换到下一个摘要版本
     */
    fun switchToNextSummary() {
        val currentIndex = mCurrentSummaryIndex.value ?: 0
        if (currentIndex > 0) {
            switchToSummaryVersion(currentIndex - 1)
        }
    }

    /**
     * 切换到最新版本
     */
    fun switchToLatestSummary() {
        switchToSummaryVersion(0)
    }

    /**
     * 切换到指定的摘要版本
     */
    fun switchToSummaryVersion(index: Int) {
        val historyList = summaryHistory.value ?: emptyList()
        if (index < 0 || index >= historyList.size) return

        viewModelScope.launch {
            val updateSuccess = dataManager.setSelectedSummary(currentMediaId, index)
            if (updateSuccess) {
                mCurrentSummaryIndex.postValue(index)
                val selectedSummary = historyList[index]
                loadExistingSummary(selectedSummary.summaryContent)
            } else {
                updateErrorState("切换摘要版本失败")
            }
        }
    }

    /**
     * 测试数据流
     */
    fun testDataFlow() {
        if (!isValidMediaId()) return

        viewModelScope.launch {
            val testContent = AISummaryDataRepository.createTestSummaryContent()
            val saved = withContext(Dispatchers.IO) {
                dataManager.saveSummary(currentMediaId, testContent, -1)
            }

            if (saved) {
                val summaryHistory = withContext(Dispatchers.IO) {
                    dataManager.getSummaryHistory(currentMediaId)
                }
                if (summaryHistory.isNotEmpty()) {
                    loadSummaryHistory(summaryHistory)
                }
            } else {
                updateErrorState("测试数据保存失败")
            }
        }
    }

    /**
     * 测试兼容性检查功能
     */
    fun testCompatibilityCheck() {
        if (!isValidMediaId()) return

        viewModelScope.launch {
            val legacySummary = withContext(Dispatchers.IO) {
                performCompatibilityCheckWithTimeout(currentMediaId)
            }

            if (legacySummary != null) {
                autoMigrateLegacySummary(legacySummary)
            } else {
                mSummaryState.postValue(SummaryState.EMPTY)
            }
        }
    }

    /**
     * 执行摘要检查 - 统一的检查逻辑
     */
    private fun performSummaryCheck(forceCheck: Boolean = false) {
        DebugUtil.d(TAG, "performSummaryCheck: start for mediaId=$currentMediaId, forceCheck=$forceCheck")
        if (!isValidMediaId()) return

        viewModelScope.launch {
            // 检查是否正在生成
            val isRunning = withContext(Dispatchers.IO) {
                AISummaryAction.checkIsTaskRunning(currentMediaId)
            }
            if (isRunning) {
                DebugUtil.d(TAG, "performSummaryCheck: task is running, set state to GENERATING")
                mSummaryState.postValue(SummaryState.GENERATING)
                return@launch
            }

            // 检查新系统中的摘要数据
            val summaryHistory = withContext(Dispatchers.IO) {
                dataManager.getSummaryHistory(currentMediaId)
            }
            if (summaryHistory.isNotEmpty()) {
                DebugUtil.d(TAG, "performSummaryCheck: found ${summaryHistory.size} summaries in new system")
                loadSummaryHistory(summaryHistory)
                return@launch
            }

            // 兼容性检查
            if (!checkedRecordIds.contains(currentMediaId) || forceCheck) {
                DebugUtil.d(TAG, "performSummaryCheck: performing compatibility check")
                if (!forceCheck) {
                    checkedRecordIds.add(currentMediaId)
                }

                val legacySummary = withContext(Dispatchers.IO) {
                    performCompatibilityCheckWithTimeout(currentMediaId)
                }

                if (legacySummary != null) {
                    DebugUtil.d(TAG, "performSummaryCheck: found legacy summary, starting migration")
                    autoMigrateLegacySummary(legacySummary)
                } else {
                    DebugUtil.d(TAG, "performSummaryCheck: no legacy summary found, set state to EMPTY")
                    mSummaryState.postValue(SummaryState.EMPTY)
                }
            } else {
                DebugUtil.d(TAG, "performSummaryCheck: already checked, set state to EMPTY")
                mSummaryState.postValue(SummaryState.EMPTY)
            }
        }
    }

    /**
     * 加载摘要历史版本列表
     */
    private fun loadSummaryHistory(summaryHistoryList: List<AISummaryDataRepository.SummaryEntity>) {
        DebugUtil.d(TAG, "loadSummaryHistory: loading ${summaryHistoryList.size} versions")
        summaryHistory.postValue(summaryHistoryList)
        mHasMultipleVersions.postValue(summaryHistoryList.size > 1)

        val selectedIndex = summaryHistoryList.indexOfFirst { it.isSelected }
        val currentIndex = if (selectedIndex >= 0) selectedIndex else 0
        mCurrentSummaryIndex.postValue(currentIndex)

        val currentSummary = summaryHistoryList[currentIndex]
        DebugUtil.d(TAG, "loadSummaryHistory: selected index=$currentIndex, content length=${currentSummary.summaryContent.length}")
        loadExistingSummary(currentSummary.summaryContent)
    }

    /**
     * 加载已有摘要
     */
    private fun loadExistingSummary(summaryContent: String) {
        this.summaryContent.postValue(summaryContent)
        val summaryItemsList = SummaryItem.parseContent(summaryContent)
        summaryItems.postValue(summaryItemsList)
        mSummaryState.postValue(SummaryState.COMPLETED)
    }

    // ==================== 工具方法 ====================

    /**
     * 验证MediaId是否有效
     */
    private fun isValidMediaId(): Boolean {
        if (currentMediaId <= 0) {
            updateErrorState("无效的录音ID")
            return false
        }
        return true
    }

    /**
     * 更新错误状态
     */
    private fun updateErrorState(message: String) {
        mSummaryState.postValue(SummaryState.ERROR)
        mErrorMessage.postValue(message)
    }

    /**
     * 处理摘要结果
     */
    internal fun processSummaryResult(jsonResult: String) {
        DebugUtil.d(TAG, "processSummaryResult: processing result, length=${jsonResult.length}")
        viewModelScope.launch {
            val summaryResult = GsonUtil.fromJson(jsonResult, SmartSummaryResult::class.java)
            val summaryContent = summaryResult?.lastSummary

            if (!summaryContent.isNullOrEmpty()) {
                DebugUtil.d(TAG, "processSummaryResult: content length=${summaryContent.length}")
                <EMAIL>(summaryContent)
                val summaryItemsList = SummaryItem.parseContent(summaryContent)
                summaryItems.postValue(summaryItemsList)
                mSummaryState.postValue(SummaryState.COMPLETED)

                val saved = withContext(Dispatchers.IO) {
                    dataManager.saveSummary(currentMediaId, summaryContent, -1)
                }
                DebugUtil.d(TAG, "processSummaryResult: saved to database=$saved")

                if (saved) {
                    val updatedHistory = withContext(Dispatchers.IO) {
                        dataManager.getSummaryHistory(currentMediaId)
                    }
                    if (updatedHistory.isNotEmpty()) {
                        loadSummaryHistory(updatedHistory)
                    }
                }
            } else {
                DebugUtil.e(TAG, "processSummaryResult: empty summary content")
                updateErrorState("摘要内容为空")
            }
        }
    }

    /**
     * 处理摘要错误
     */
    private fun handleSummaryError(errorCode: Int, errorMsg: String?) {
        DebugUtil.e(TAG, "handleSummaryError: errorCode=$errorCode, errorMsg=$errorMsg")
        val message = when (errorCode) {
            AISummaryAction.NETWORK_ERROR -> "网络连接失败"
            AISummaryAction.CONTENT_LESS_ERROR -> "录音内容较少，无法生成摘要"
            AISummaryAction.SERVER_ERROR -> "服务异常，摘要生成失败"
            AISummaryAction.REQUEST_TIMEOUT -> "请求超时，请重试"
            else -> errorMsg ?: "生成摘要失败，错误码：$errorCode"
        }
        updateErrorState(message)
    }

    // ==================== 回调和状态管理 ====================

    private fun registerCallback() {
        if (currentMediaId > 0 && !isCallbackRegistered) {
            DebugUtil.d(TAG, "registerCallback: registering for mediaId=$currentMediaId")
            AISummaryAction.registerAISummaryCallback(currentMediaId, aiSummaryCallback)
            isCallbackRegistered = true
        }
    }

    private fun unregisterCallback() {
        if (isCallbackRegistered && currentMediaId > 0) {
            DebugUtil.d(TAG, "unregisterCallback: unregistering for mediaId=$currentMediaId")
            AISummaryAction.unRegisterAISummaryCallback(currentMediaId, aiSummaryCallback)
            isCallbackRegistered = false
        }
    }

    private fun resetState() {
        mSummaryState.postValue(SummaryState.EMPTY)
        summaryContent.postValue("")
        summaryItems.postValue(emptyList())
        mErrorMessage.postValue("")
        summaryHistory.postValue(emptyList())
        mCurrentSummaryIndex.postValue(0)
        mHasMultipleVersions.postValue(false)
    }

    private fun clearSummaryData() {
        summaryContent.postValue("")
        summaryItems.postValue(emptyList())
    }

    // ==================== 兼容性检查和数据迁移 ====================

    /**
     * 带超时机制的兼容性检查
     */
    private suspend fun performCompatibilityCheckWithTimeout(recordId: Long): NoteData? {
        return withTimeoutOrNull(COMPATIBILITY_CHECK_TIMEOUT) {
            checkLegacySummaryData(recordId)
        }
    }

    /**
     * 检查旧摘要系统中的数据
     */
    private fun checkLegacySummaryData(recordId: Long): NoteData? {
        DebugUtil.d(TAG, "checkLegacySummaryData: checking for recordId=$recordId")
        val localNoteDataList = NoteDbUtils.queryAllNotes()
        val relatedNoteData = localNoteDataList.find { noteData ->
            noteData.mediaId == recordId.toString()
        }

        if (relatedNoteData == null) {
            DebugUtil.d(TAG, "checkLegacySummaryData: no related noteData found")
            return null
        }

        if (relatedNoteData.noteId.isNullOrEmpty()) {
            DebugUtil.d(TAG, "checkLegacySummaryData: noteId is empty")
            return null
        }

        DebugUtil.d(TAG, "checkLegacySummaryData: found noteData with noteId=${relatedNoteData.noteId}")
        val fullSummaryContent = queryLegacySummaryFromContentProvider(relatedNoteData.noteId!!)
        return if (!fullSummaryContent.isNullOrEmpty()) {
            DebugUtil.d(TAG, "checkLegacySummaryData: found content from ContentProvider, length=${fullSummaryContent.length}")
            relatedNoteData.copy(noteContent = fullSummaryContent)
        } else {
            DebugUtil.d(TAG, "checkLegacySummaryData: no content from ContentProvider")
            relatedNoteData
        }
    }

    /**
     * 通过 ContentProvider 查询系统便签应用获取完整摘要内容
     */
    private fun queryLegacySummaryFromContentProvider(noteId: String): String? {
        DebugUtil.d(TAG, "queryLegacySummaryFromContentProvider: querying noteId=$noteId")
        return try {
            val noteIdList = arrayListOf<String>()
            noteIdList.add(noteId)
            val uri = Uri.parse("$NOTE_QUERY_URI${Uri.encode(noteIdList.toString())}")
            DebugUtil.d(TAG, "queryLegacySummaryFromContentProvider: query URI=$uri")

            val cursor = BaseApplication.getAppContext().contentResolver.query(uri, null, null, null, null)
            cursor?.use {
                if (it.count > 0 && it.moveToFirst()) {
                    val textIndex = it.getColumnIndex(TEXT)
                    val noteIdIndex = it.getColumnIndex(NOTE_ID)

                    if (textIndex >= 0) {
                        val summaryText = it.getString(textIndex)
                        val queriedNoteId = if (noteIdIndex >= 0) it.getString(noteIdIndex) else null

                        DebugUtil.d(TAG, "queryLegacySummaryFromContentProvider: found data, noteId=$queriedNoteId, textLength=${summaryText?.length ?: 0}")
                        if (queriedNoteId == noteId && !summaryText.isNullOrEmpty()) {
                            return summaryText.replace("\n", " ")
                        }
                    } else {
                        DebugUtil.w(TAG, "queryLegacySummaryFromContentProvider: TEXT column not found")
                    }
                } else {
                    DebugUtil.d(TAG, "queryLegacySummaryFromContentProvider: no data found")
                }
            }
            null
        } catch (e: SecurityException) {
            DebugUtil.e(TAG, "queryLegacySummaryFromContentProvider: SecurityException=${e.message}")
            null
        } catch (e: IllegalStateException) {
            DebugUtil.e(TAG, "queryLegacySummaryFromContentProvider: IllegalStateException=${e.message}")
            null
        }
    }

    /**
     * 自动迁移旧摘要系统的数据到新系统
     */
    private suspend fun autoMigrateLegacySummary(legacyData: NoteData) {
        DebugUtil.d(TAG, "autoMigrateLegacySummary: starting migration for noteId=${legacyData.noteId}")
        val summaryContent = legacyData.noteContent

        if (!summaryContent.isNullOrEmpty()) {
            DebugUtil.d(TAG, "autoMigrateLegacySummary: content length=${summaryContent.length}")
            // 先更新UI状态，提供即时反馈
            withContext(Dispatchers.Main) {
                loadExistingSummary(summaryContent)
            }

            // 后台保存到新系统数据库
            val saved = withContext(Dispatchers.IO) {
                dataManager.saveSummary(currentMediaId, summaryContent, -1)
            }
            DebugUtil.d(TAG, "autoMigrateLegacySummary: saved to new system=$saved")

            if (saved) {
                // 重新加载摘要历史
                val summaryHistory = withContext(Dispatchers.IO) {
                    dataManager.getSummaryHistory(currentMediaId)
                }

                if (summaryHistory.isNotEmpty()) {
                    DebugUtil.d(TAG, "autoMigrateLegacySummary: reloading ${summaryHistory.size} versions")
                    withContext(Dispatchers.Main) {
                        loadSummaryHistory(summaryHistory)
                    }
                }
            }
        } else {
            DebugUtil.d(TAG, "autoMigrateLegacySummary: no content to migrate")
            withContext(Dispatchers.Main) {
                mSummaryState.postValue(SummaryState.EMPTY)
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        unregisterCallback()
        checkedRecordIds.clear()
    }
}