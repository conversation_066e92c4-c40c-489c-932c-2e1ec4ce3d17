<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.soundrecorder.playback">

    <uses-sdk tools:overrideLibrary="com.oplus.sdk.addon.sdk"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />

    <application>
        <activity
            android:name="com.soundrecorder.playback.PlaybackActivity"
            android:configChanges="keyboardHidden|layoutDirection|navigation"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:label="@string/app_name_main"
            android:launchMode="singleTop"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:screenOrientation="behind"
            android:theme="@style/AppBaseTheme.NoActionBar.ActionMode.LocalDirection"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustNothing" />

        <activity
            android:name="com.soundrecorder.playback.newconvert.BridgeActivity"
            android:configChanges="locale|orientation|keyboardHidden|mcc|mnc|density"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:label="@string/app_name_main"
            android:launchMode="singleTop"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:resizeableActivity="false"
            android:screenOrientation="behind"
            android:theme="@style/TransparentActivityTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />

                <data
                    android:host="openbridgebctivity"
                    android:scheme="privacy" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.soundrecorder.playback.newconvert.exportconvert.txt.ShareWithTxtActivity"
            android:configChanges="locale|keyboardHidden|screenSize|screenLayout|smallestScreenSize|navigation|fontScale"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:label="@string/app_name_main"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:theme="@style/AppBaseTheme.NoActionBar.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustNothing">
            <intent-filter>
                <action android:name="com.oplus.soundrecorder.SHARE_WITH_TXT_PREVIEW"/>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
    </application>
</manifest>