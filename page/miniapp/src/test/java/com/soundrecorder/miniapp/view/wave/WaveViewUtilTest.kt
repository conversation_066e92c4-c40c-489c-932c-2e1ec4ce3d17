/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: WaveViewUtilTest
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: W9013333(v-z<PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/29 1.0 create
 */

package com.soundrecorder.miniapp.view.wave

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.miniapp.shadow.ShadowFeatureOption
import com.soundrecorder.miniapp.view.wave.WaveViewUtil.DURATION_267
import com.soundrecorder.miniapp.view.wave.WaveViewUtil.DURATION_283
import com.soundrecorder.miniapp.view.wave.WaveViewUtil.getEnterHeightByTime
import com.soundrecorder.miniapp.view.wave.WaveViewUtil.getLineScale
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class WaveViewUtilTest {
    @Test
    fun getLineScaleTest() {
        Assert.assertTrue(getLineScale(0f) >= 0)
    }

    @Test
    fun getEnterHeightByTimeTest() {
        Assert.assertTrue(getEnterHeightByTime(100L, 8f, 10f) > 0f)
        Assert.assertTrue(getEnterHeightByTime(DURATION_267 + 100L, 8f, 10f) > 0f)
        Assert.assertTrue(getEnterHeightByTime(DURATION_267 + DURATION_283 + 1L, 8f, 10f) > 0f)
    }
}