/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: WaveRecyclerViewTest
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: W9013333(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/29 1.0 create
 */

package com.soundrecorder.miniapp.view.wave

import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.miniapp.shadow.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito.*
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class WaveRecyclerViewTest {

    @Test
    fun runDoEnterAnimatorTest() {
        val waveRecyclerView = spy(WaveRecyclerView(ApplicationProvider.getApplicationContext()))
        waveRecyclerView.doEnterAnimator()
        Assert.assertTrue(Whitebox.getInternalState<Long>(waveRecyclerView, "doEnterAnimationTime") > 0L)
    }

    @Test
    fun setSelectTimeTest() {
        val waveRecyclerView = spy(WaveRecyclerView(ApplicationProvider.getApplicationContext()))
        waveRecyclerView.setSelectTime(0)
        Assert.assertTrue(Whitebox.getInternalState<Int>(waveRecyclerView, "preTime") == 0)
        waveRecyclerView.setSelectTime(1)
        Assert.assertTrue(Whitebox.getInternalState<Int>(waveRecyclerView, "preTime") > 0)
    }

    @Test
    fun refreshColorTest() {
        val waveRecyclerView = spy(WaveRecyclerView(ApplicationProvider.getApplicationContext()))
        waveRecyclerView.refreshColor(1, 2, 3)
        Assert.assertTrue(Whitebox.getInternalState<Int>(waveRecyclerView, "cardWaveColor") == 1)
        Assert.assertTrue(Whitebox.getInternalState<Int>(waveRecyclerView, "cardDashWaveColor") == 2)
        Assert.assertTrue(Whitebox.getInternalState<Int>(waveRecyclerView, "markLineColor") == 3)
    }

    @Suppress("SENSELESS_COMPARISON")
    @Test
    fun createNewItemViewTest() {
        val waveRecyclerView = spy(WaveRecyclerView(ApplicationProvider.getApplicationContext()))
        val waveItemView = waveRecyclerView.createNewItemView(waveRecyclerView)
        Assert.assertTrue(waveItemView != null)
    }

    @Test
    fun onBindItemViewTest() {
        val waveRecyclerView = spy(WaveRecyclerView(ApplicationProvider.getApplicationContext()))
        waveRecyclerView.onBindItemView(waveRecyclerView.createNewItemView(waveRecyclerView), 1)
    }

    @Test
    fun halfWidthTest() {
        val waveRecyclerView = spy(WaveRecyclerView(ApplicationProvider.getApplicationContext()))
        doReturn(200).doCallRealMethod().`when`(waveRecyclerView).width
        Assert.assertTrue(waveRecyclerView.halfWidth() > 0)
    }

    @Test
    fun eventTest() {
        val waveRecyclerView = spy(WaveRecyclerView(ApplicationProvider.getApplicationContext()))
        Assert.assertTrue(waveRecyclerView.dispatchTouchEvent(any()))
        Assert.assertTrue(waveRecyclerView.onTouchEvent(any()))
    }

    @Test
    fun stopRecorderMove() {
        val waveRecyclerView = spy(WaveRecyclerView(ApplicationProvider.getApplicationContext()))
        waveRecyclerView.stopRecorderMove(1, listOf(200))
        Assert.assertTrue(!Whitebox.getInternalState<Boolean>(waveRecyclerView, "hasRecording"))
        Assert.assertTrue(Whitebox.getInternalState<Int>(waveRecyclerView, "ampsSize") == 1)
    }

    @Test
    fun recorderIntervalUpdateTest() {
        val waveRecyclerView = spy(WaveRecyclerView(ApplicationProvider.getApplicationContext()))
        waveRecyclerView.recorderIntervalUpdate(2, listOf(100, 200), true)
        waveRecyclerView.recorderIntervalUpdate(2, listOf(100, 200), false)
        Assert.assertTrue(Whitebox.getInternalState(waveRecyclerView, "hasRecording"))
        Assert.assertTrue(Whitebox.getInternalState<Int>(waveRecyclerView, "ampsSize") == 2)
        waveRecyclerView.recorderIntervalUpdate(4, listOf(100, 200, 300, 400), true)
        waveRecyclerView.recorderIntervalUpdate(4, listOf(100, 200, 300, 400), false)
        Assert.assertTrue(Whitebox.getInternalState(waveRecyclerView, "hasRecording"))
        Assert.assertTrue(Whitebox.getInternalState<Int>(waveRecyclerView, "ampsSize") == 4)
    }
}