/*********************************************************************************
 ** Copyright (C), 2008-2020, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** Oplus Coding Static Checking Skip
 ** File: - public class COUIButton extends AppCompatButton {.java
 ** Description:
 **     This widget is used for ButtonUI for COUI.
 **
 ** Version: 1.0
 ** Date: 2019/11/7
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>             <data>           <version>         <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><EMAIL>      2019/11/7           1.0       Annotate this module
 ********************************************************************************/

package com.soundrecorder.miniapp.view.button;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.appcompat.widget.AppCompatImageView;

import com.soundrecorder.miniapp.R;

public class AppCardButton extends AppCompatImageView {
    private static final int MAX_COLOR_VALUE = 255;
    private final Paint mForegroundPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private final AppCardPressFeedbackHelper mFeedbackUtils;
    //某些场景下，想置灰按钮，但是又想响应点击事件，即可使用这个值，表示假的置灰
    private boolean mFakeDisable;
    private final int mPressColor;
    private final float mDisabledPressAlpha;
    private final float mEnablePressAlpha;

    public AppCardButton(Context context) {
        this(context, null);
    }

    public AppCardButton(Context context, AttributeSet attrs) {
        this(context, attrs, androidx.appcompat.R.attr.buttonStyle);
    }

    public AppCardButton(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setScaleType(ScaleType.CENTER);
        final TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.MiniAppCardButton, defStyleAttr, 0);
        mPressColor = a.getColor(R.styleable.MiniAppCardButton_pressForeGroundColor, 0);
        mDisabledPressAlpha = a.getFloat(R.styleable.MiniAppCardButton_disabledPressAlpha, 0);
        mEnablePressAlpha = a.getFloat(R.styleable.MiniAppCardButton_enablePressAlpha, 0);
        a.recycle();
        mFeedbackUtils = new AppCardPressFeedbackHelper(this);
        mForegroundPaint.setStyle(Paint.Style.FILL);
        mForegroundPaint.setColor(mPressColor);
    }

    @Override
    public void onDrawForeground(Canvas canvas) {
        super.onDrawForeground(canvas);
        canvas.save();
        canvas.translate(getScrollX(), getScrollY());
        float halfWidth = getWidth() / 2f;
        float halfHeight = getHeight() / 2f;
        float r = Math.min(halfWidth, halfHeight);
        float alphaRadio = mFeedbackUtils.getAlpha();
        if (isEnabled() && !mFakeDisable) {
            mForegroundPaint.setAlpha((int) (alphaRadio * mEnablePressAlpha * MAX_COLOR_VALUE));
        } else {
            mForegroundPaint.setAlpha((int) (alphaRadio * mDisabledPressAlpha * MAX_COLOR_VALUE));
        }
        canvas.drawCircle(halfWidth, halfHeight, r, mForegroundPaint);
        canvas.restore();
    }

    public synchronized void setFakeDisable(boolean fakeDisable) {
        if (this.mFakeDisable == fakeDisable) return;
        this.mFakeDisable = fakeDisable;
        invalidate();
    }

    public synchronized boolean getFakeDisable() {
        return mFakeDisable;
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        try {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    if (isEnabled() && !mFakeDisable) {
                        mFeedbackUtils.executeFeedbackAnimator(true);
                    }
                    break;
                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    if (isEnabled() && !mFakeDisable) {
                        mFeedbackUtils.executeFeedbackAnimator(false);
                    } else {
                        mFeedbackUtils.reset();
                    }
                    break;
                default:
            }
        } catch (Exception ignore) {
        }
        return super.onTouchEvent(event);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mFeedbackUtils.reset();
    }
}
