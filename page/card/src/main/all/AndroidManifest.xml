<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.soundrecorder.common.card">

    <queries>
        <package android:name="com.oplus.smartengine" />
    </queries>
    <application>
        <meta-data
            android:name="com.soundRecorder.dragonFlyCard.versionCode"
            android:value="2" />

        <!--蜻蜓副屏卡片-->
        <provider
            android:name="com.soundrecorder.common.card.DragonFlyAppCardWidgetProvider"
            android:authorities="com.soundrecorder.dragonfly.AppCardWidgetProvider"
            android:enabled="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT">
            <intent-filter>
                <action android:name="android.appcard.action.APPCARD_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.card.provider"
                android:resource="@xml/dragon_fly_card_app_widget" />
        </provider>

        <!--泛在状态栏胶囊-->
        <provider
            android:name="com.soundrecorder.common.card.RecordSeedlingCardWidgetProvider"
            android:authorities="com.oplus.soundRecorder.RecordSeedlingCardWidgetProvider"
            android:enabled="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT">
            <intent-filter>
                <action android:name="com.oplus.seedling.action.SEEDLING_CARD" />
            </intent-filter>
            <meta-data
                android:name="oplus.seedling.provider"
                android:value="RecordSeedling.upk;RecordSeeding2.upk;RecordSeeding3.upk" />
        </provider>

        <!--authorities固定配置，不可设置为其他-->
        <provider
            android:name="com.soundrecorder.common.card.RecordCardEventProvider"
            android:authorities="${applicationId}.card.event.provider"
            android:enabled="true"
            android:exported="true"
            android:grantUriPermissions="true"
            android:permission="com.oplus.permission.safe.ASSISTANT"/>
        <!--魔方按键 接入交互service-->
        <service
            android:name="com.soundrecorder.common.card.KeyMagicMessengerService"
            android:enabled="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT">
            <intent-filter>
                <action android:name="com.oplus.soundrecorder.keyMagicMessengerService" />
            </intent-filter>
        </service>

        <service
            android:name=".zoom.fanzaiai.channel.CustomChannelService"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT">
            <intent-filter>
                <action android:name="org.hapjs.features.channel.action.BIND" />
            </intent-filter>
        </service>

        <service android:name="org.hapjs.features.channel.ChannelService"
            android:enabled="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT"
            tools:node="remove">
            <intent-filter>
                <action android:name="org.hapjs.features.channel.action.BIND"/>
            </intent-filter>
        </service>
    </application>
</manifest>
