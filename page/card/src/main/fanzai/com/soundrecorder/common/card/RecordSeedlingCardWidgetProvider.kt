/*
 Copyright (C), 2008-2023 OPLUS Mobile Comm Corp., Ltd.
 File: RecordSeedlingCardWidgetProvider
 Description:
 Version: 1.0
 Date: 2023/3/6
 Author: ********(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2023/3/6 1.0 create
 */

package com.soundrecorder.common.card

import android.content.Context
import android.content.pm.ProviderInfo
import android.os.Bundle
import com.oplus.pantanal.seedling.SeedlingCardWidgetProvider
import com.oplus.pantanal.seedling.bean.SeedlingCard
import com.oplus.pantanal.seedling.bean.SeedlingHostEnum
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.card.api.SeedlingApi
import com.soundrecorder.common.card.api.SeedlingSdkApi
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import org.json.JSONObject

class RecordSeedlingCardWidgetProvider : SeedlingCardWidgetProvider() {
    companion object {
        private const val TAG = "RecordSeedlingProvider"

        /**
         * 泛在状态栏胶囊SeedlingCard对象，用于更新胶囊录制时长
         */
        @Volatile
        private var statusBarCardMap: MutableMap<SeedlingHostEnum, SeedlingCard> = mutableMapOf()

        @JvmStatic
        fun refreshSeedlingData(
            seedlingCard: SeedlingCard? = getSeedlingCard(),
            jsonData: JSONObject? = RecorderViewModelAction.getSeedlingData(),
            updateAll: Boolean = true
        ) {
            val card = seedlingCard ?: return
            if (jsonData != null && jsonData.length() > 0) {
                SeedlingApi.sendUpdateCardData(card, jsonData, updateAll)
            } else {
                DebugUtil.w(TAG, "refreshSeedlingData: jsonData is null or empty")
            }
        }

        private fun getSeedlingCard(): SeedlingCard? {
            val iterator = statusBarCardMap.entries.iterator()
            while (iterator.hasNext()) {
                return iterator.next().value
            }
            return null
        }
    }

    override fun attachInfo(context: Context?, info: ProviderInfo?) {
        super.attachInfo(context, info)
        if (context != null) {
            SeedlingApi.sendHideSeedlingStatusBar()
        }
    }

    override fun onCardCreate(context: Context, card: SeedlingCard) {
        DebugUtil.i(TAG, "onCardCreate card = $card")
        if (card.host == SeedlingHostEnum.StatusBar || card.host == SeedlingHostEnum.Notification) {
            statusBarCardMap[card.host] = card
        }
    }

    override fun onCardObserve(context: Context, clientName: String, cards: List<SeedlingCard>) {}
    override fun onDestroy(context: Context, card: SeedlingCard) {
        DebugUtil.i(TAG, "onDestroy card = $card")
        if (card.host == SeedlingHostEnum.StatusBar || card.host == SeedlingHostEnum.Notification) {
            statusBarCardMap.remove(card.host)
        }
    }

    override fun onHide(context: Context, card: SeedlingCard) {
        DebugUtil.i(TAG, "onHide card = $card")
        RecorderViewModelAction.onSeedlingCardStateChanged(false)
    }

    override fun onShow(context: Context, card: SeedlingCard) {
        DebugUtil.i(TAG, "onShow card = $card")
        RecorderViewModelAction.onSeedlingCardStateChanged(true)
    }

    override fun onSubscribed(context: Context, card: SeedlingCard) {
        DebugUtil.i(TAG, "onSubscribed card = $card")
    }

    override fun onUnSubscribed(context: Context, card: SeedlingCard) {
        DebugUtil.i(TAG, "onUnSubscribed card = $card")
    }

    override fun onSizeChanged(context: Context, card: SeedlingCard, oldSize: Int, newSize: Int) {
        DebugUtil.i(TAG, "onSizeChanged card = $card oldSize = $oldSize  newSize = $newSize")
        SeedlingSdkApi.osVersion.onSeedlingCardSizeChanged(newSize)
    }

    override fun onUpdateData(context: Context, card: SeedlingCard, data: Bundle) {
        DebugUtil.i(TAG, "onUpdateData card = $card")
        /**
         * onUpdateData 必须强制刷新，不然可能导致卡片显示不出来
         */
        if (card.host == SeedlingHostEnum.StatusBar || card.host == SeedlingHostEnum.Notification) {
            refreshSeedlingData(seedlingCard = card, updateAll = false)
            DebugUtil.d(TAG, "onUpdateData, card id = ${card.serviceId}", true)
        }
    }
}