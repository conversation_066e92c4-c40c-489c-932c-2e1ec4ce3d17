/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SeedlingCardProcessTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/12/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card

import android.os.Build
import android.os.Handler
import android.os.HandlerThread
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oplus.pantanal.seedling.bean.SeedlingCard
import com.soundrecorder.common.card.shadows.ShadowFeatureOption
import org.json.JSONObject
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class SeedlingCardProcessTest {

    @Test
    fun should_success_when_initHandlerThread() {
        val process = SeedingCardProcess()
        var handlerThread = Whitebox.getInternalState<HandlerThread>(process, "mHandlerThread")
        Assert.assertNull(handlerThread)

        process.initHandlerThread()
        handlerThread = Whitebox.getInternalState<HandlerThread>(process, "mHandlerThread")
        Assert.assertNotNull(handlerThread)
    }

    @Test
    fun should_equals_when_sendShowSeedlingStatusBar() {
        val process = SeedingCardProcess()
        process.initHandlerThread()
        process.sendShowSeedlingStatusBar(null)

        val handler: Handler? = Whitebox.getInternalState<Handler>(process, "mHandler")
        Assert.assertNotNull(handler)
    }

    @Test
    fun should_equals_when_sendHideSeedlingStatusBar() {
        val process = SeedingCardProcess()
        process.initHandlerThread()
        process.sendHideSeedlingStatusBar(null)

        val handler: Handler? = Whitebox.getInternalState<Handler>(process, "mHandler")
        Assert.assertNotNull(handler)
    }

    @Test
    fun should_equals_when_sendUpdateCardData() {
        val process = SeedingCardProcess()
        process.initHandlerThread()
        val handler: Handler? = Whitebox.getInternalState<Handler>(process, "mHandler")
        val seedlingCard = Mockito.mock(SeedlingCard::class.java)
        process.sendUpdateCardData(seedlingCard, JSONObject(), false)
        Assert.assertNotNull(handler)
    }

    @Test
    fun should_equals_when_doActionInThread() {
        val process = SeedingCardProcess()
        process.initHandlerThread()
        process.doActionInThread {
            //do nothing
        }

        val handler: Handler? = Whitebox.getInternalState<Handler>(process, "mHandler")
        Assert.assertNotNull(handler)
    }

    @Test
    fun should_equals_when_unRegisterResultCallBack() {
        val process = SeedingCardProcess()
        process.initHandlerThread()
        val handler: Handler? = Whitebox.getInternalState<Handler>(process, "mHandler")
        process.unRegisterResultCallBack()
        Assert.assertNotNull(handler)
    }

    @Test
    fun should_equals_when_release() {
        val process = SeedingCardProcess()
        process.initHandlerThread()
        var handler = Whitebox.getInternalState<Handler>(process, "mHandler")
        Assert.assertNotNull(handler)

        process.release()
        handler = Whitebox.getInternalState<Handler>(process, "mHandler")
        Assert.assertNull(handler)
    }
}