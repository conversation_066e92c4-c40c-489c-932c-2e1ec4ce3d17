/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: AmplitudeViewTest
 * Description:
 * Version: 1.0
 * Date: 2023/5/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/5/18 1.0 create
 */

package com.soundrecorder.editrecord.views.preview

import android.content.Context
import android.graphics.Canvas
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.editrecord.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class AmplitudeViewTest {
    private var mContext: Context? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
    }

    @Test
    fun onDraw() {
        var ampView = AmplitudeView(mContext)
        ampView.onDraw(Canvas())

        val preViewBar = GloblePreViewBar(mContext)
        ampView = Whitebox.getInternalState(preViewBar, "mAmplitudeView")
        Assert.assertNotNull(ampView)
        ampView.onDraw(Canvas())

        preViewBar.amplitudes = mutableListOf<Int>().apply { add(0) }
        ampView.onDraw(Canvas())

        preViewBar.totalTime = 188 * 70 + 10
        val list = mutableListOf<Int>()
        for (i in 0..1000) {
            list.add(i)
        }
        preViewBar.amplitudes = list
        ampView.onDraw(Canvas())
    }
}