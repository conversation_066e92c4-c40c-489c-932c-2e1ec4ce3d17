/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: EditViewUtilsTest
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.utils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;

import android.app.Dialog;
import android.content.Context;
import android.os.Build;

import androidx.appcompat.app.AlertDialog;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.common.constant.RecordModeConstant;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.db.RecorderDBUtil;
import com.soundrecorder.common.utils.MarkSerializUtil;
import com.soundrecorder.common.utils.RecordModeUtil;
import com.soundrecorder.editrecord.EditRecordActivity;
import com.soundrecorder.editrecord.shadows.ShadowCOUIVersionUtil;
import com.soundrecorder.editrecord.shadows.ShadowFeatureOption;
import com.soundrecorder.editrecord.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.editrecord.ui.DialogDismissListener;
import com.soundrecorder.editrecord.ui.EditPlayerController;
import com.soundrecorder.editrecord.ui.EditRecordFragment;
import com.soundrecorder.editrecord.ui.EditViewModel;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowDialog;
import org.robolectric.shadows.ShadowLog;

import java.util.ArrayList;
import java.util.List;

import kotlin.Pair;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class,
        ShadowFeatureOption.class, ShadowCOUIVersionUtil.class})
public class EditViewUtilsTest {

    private Context context;
    private ActivityController<EditRecordActivity> controller;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        context = ApplicationProvider.getApplicationContext();
        controller = Robolectric.buildActivity(EditRecordActivity.class);
    }

    @After
    public void tearDown() {
        controller = null;
        context = null;
    }

    @Test
    public void should_create_dialog_when_createCancelClipDialog_different_inputs() {
        EditViewUtils.INSTANCE.createCancelClipDialog(true, controller.get(), null, null, null);
        Dialog dialog = ShadowDialog.getLatestDialog();
        assertNull(dialog);
        EditViewUtils.INSTANCE.createCancelClipDialog(false, controller.get(), "", new ArrayList<>(), mock(EditViewModel.class));
        dialog = ShadowDialog.getLatestDialog();
        assertNull(dialog);
        EditViewUtils.INSTANCE.createCancelClipDialog(true, controller.get(), "", new ArrayList<>(), mock(EditViewModel.class));
        dialog = ShadowDialog.getLatestDialog();
        assertNotNull(dialog);
    }

    @Test
    public void should_create_dialog_when_createSaveClipDialog_different_inputs() {
        EditViewUtils.INSTANCE.createSaveClipDialog(false, controller.get(), null, "", null, null);
        Dialog dialog = ShadowDialog.getLatestDialog();
        assertNull(dialog);
        EditViewModel mockedViewModel = mock(EditViewModel.class);
        EditViewUtils.INSTANCE.createSaveClipDialog(false, controller.get(), "", "", new ArrayList<>(), mockedViewModel);
        dialog = ShadowDialog.getLatestDialog();
        assertNull(dialog);
    }

    @Test
    public void should_create_dialog_when_createProgressDialog() {
        EditRecordFragment editRecordFragment = mock(EditRecordFragment.class);
        doReturn(context).when(editRecordFragment).getContext();
        Pair<AlertDialog, DialogDismissListener> result = null;
        result = EditViewUtils.INSTANCE.createProgressDialog(null, null);
        assertNull(result);
        result = EditViewUtils.INSTANCE.createProgressDialog(editRecordFragment, null);
        assertNotNull(result);
    }

    @Test
    public void should_correct_when_clipLastTime() {
        EditViewModel editViewModel = mock(EditViewModel.class);
        EditPlayerController playerController = mock(EditPlayerController.class);
        doReturn(playerController).when(editViewModel).getPlayerController();
        doReturn(10000L).when(playerController).getDuration();
        Long result = EditViewUtils.INSTANCE.clipLastTime(5000L, true, editViewModel, null);
        assertEquals(0, result.compareTo(5000L));
        result = EditViewUtils.INSTANCE.clipLastTime(-5000L, true, editViewModel, null);
        assertEquals(0, result.compareTo(-5000L));
        result = EditViewUtils.INSTANCE.clipLastTime(5000L, false, editViewModel, null);
        assertEquals(0, result.compareTo(10000L));
        result = EditViewUtils.INSTANCE.clipLastTime(-5000L, false, editViewModel, null);
        assertEquals(0, result.compareTo(0L));
    }

    @Test
    public void should_correct_when_getTitle_different_input() {
        EditViewUtils.INSTANCE.getTitle(0);
        BaseApplication.sIsRTLanguage = true;
        EditViewUtils.INSTANCE.getTitle(89);
    }

    @Test
    public void should_async_when_asyncSaveCutRecords() throws Exception {
        Record record = new Record();
        record.setId(1L);
        List<Integer> ampList = new ArrayList<>();
        ampList.add(1);
        ampList.add(2);
        List<MarkDataBean> markDataBeanList = new ArrayList<>();
        markDataBeanList.add(new MarkDataBean(1000L, MarkSerializUtil.VERSION_NEW));
        MockedStatic<RecordModeUtil> recordModeUtilMockedStatic = mockStatic(RecordModeUtil.class);
        recordModeUtilMockedStatic.when(() -> RecordModeUtil.getRecordTypeForMediaRecord(any())).thenReturn(RecordModeConstant.RECORD_TYPE_STANDARD);
        MockedStatic<BaseUtil> baseUtilMockedStatic = mockStatic(BaseUtil.class);
        baseUtilMockedStatic.when(() -> BaseUtil.isAndroidQOrLater()).thenReturn(true, false);
        RecorderDBUtil instance = mock(RecorderDBUtil.class);
        MockedStatic<RecorderDBUtil> recorderDBUtilMockedStatic = mockStatic(RecorderDBUtil.class);
        recorderDBUtilMockedStatic.when(() -> RecorderDBUtil.getInstance(context)).thenReturn(instance);
        Whitebox.invokeMethod(EditViewUtils.INSTANCE, "asyncSaveCutRecords", record, "", "", markDataBeanList, ampList);

        baseUtilMockedStatic.close();
        recorderDBUtilMockedStatic.close();
    }
}
