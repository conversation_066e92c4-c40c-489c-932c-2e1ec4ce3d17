/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: DragBar
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.views.preview

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.dp2px
import com.soundrecorder.editrecord.EditConstant
import com.soundrecorder.editrecord.R
import com.soundrecorder.imageload.utils.BitmapUtil
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.math.abs
import kotlin.math.ceil

class DragBar @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : View(context, attrs, defStyleAttr) {

    private var mHandleStartBitmap: Bitmap
    private var mHandleEndBitmap: Bitmap
    private val mPaint = Paint()
    private val mZonePaint = Paint()
    private val mLinePaint = Paint()
    private var mBarWidth = 0

    private var mListener: CutTimeListener? = null

    //DragBar 的边缘
    private var mStartEdge = 0f
    private var mEndEdge = 0f

    //把手的内边缘
    private var mStart = 0f
    private var mEnd = 0f
    private var mLastX = 0f

    private var direction = DRAG_NONE
    private val mLineHeight = context.dp2px(2)
    private var mStartTime = 0L
    private var mEndTime = 0L

    //每个像素代表的时长
    private var mPxDuration = 0f

    //最小裁切长度
    private var minClip = 0f

    //总时长
    var mDuration = 0L

    companion object {
        private const val TAG = "DragBar"
        const val DRAG_NONE = 0
        const val DRAG_START = 1
        const val DRAG_END = 2
        private const val PRECISION = 9
    }

    init {
        mPaint.apply {
            isAntiAlias = true
            isDither = true
        }
        mZonePaint.apply {
            isAntiAlias = true
            isDither = true
            color = context.resources.getColor(R.color.edit_previewbar_cutzone_color, context.theme)
        }
        mLinePaint.apply {
            isAntiAlias = true
            isDither = true
            color = context.resources.getColor(com.support.appcompat.R.color.coui_color_container_theme_blue, context.theme)
        }
        mHandleStartBitmap = BitmapUtil.getBitmapFromDrawable(context, R.drawable.ic_handle_start)
        mHandleEndBitmap = BitmapUtil.getBitmapFromDrawable(context, R.drawable.ic_handle_end)
        mBarWidth = ceil(context.resources.getDimension(R.dimen.edit_left_drag_bar_width)).toInt()
    }

    private fun getPreViewBar(): GloblePreViewBar? {
        return parent as? GloblePreViewBar
    }

    fun getDragBarStart(): Float {
        return mStart - mBarWidth
    }

    fun getDragBarEnd(): Float {
        return mEnd
    }

    fun invalidateByStart(time: Long) {
        mStartTime = time
        mStart = findChildXByTime(mStartTime)
        postInvalidateOnAnimation()
    }

    fun getStartTime(): Long {
        return mStartTime
    }

    fun invalidateByEnd(time: Long) {
        mEndTime = time
        mEnd = findChildXByTime(mEndTime)
        postInvalidateOnAnimation()
    }

    fun getEndTime(): Long {
        return mEndTime
    }

    private fun findChildXByTime(time: Long): Float {
        val preView = getPreViewBar()
        var x = 0f
        if (preView != null) {
            x = if (isRtl()) {
                width - preView.findChildXByTime(time)
            } else {
                preView.findChildXByTime(time)
            }
        }
        return x
    }

    /**
     * 裁切文件后，会回调 onLayout，重置参数
     */
    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        val preView = getPreViewBar() ?: return
        if (mStartTime == mEndTime) {
            DebugUtil.e(TAG, "mStartTime equals mEndTime return")
            return
        }
        mStartEdge = preView.findChildXByTime(0) - mBarWidth
        mEndEdge = preView.findChildXByTime(mDuration) + mBarWidth
        mStart = preView.findChildXByTime(mStartTime)
        mEnd = preView.findChildXByTime(mEndTime)
        if (isRtl()) {
            mStartEdge = width - mStartEdge
            mEndEdge = width - mEndEdge
            mStart = width - mStart
            mEnd = width - mEnd
        }
        val bigDuration = BigDecimal(mDuration)
        val length = abs(mEnd - mStart)
        val bigLength = BigDecimal(length.toLong())
        if (bigLength == BigDecimal.ZERO || length.isNaN()) {
            DebugUtil.i(TAG, "bigLength is 0 or NaN and return")
            return
        }
        val bigPxDuration = bigDuration.divide(bigLength, PRECISION, RoundingMode.DOWN)
        if (bigPxDuration == BigDecimal.ZERO) {
            DebugUtil.i(TAG, "bigPxDuration is 0 and return")
            return
        }
        mPxDuration = bigPxDuration.toFloat()
        minClip = if (mDuration >= EditConstant.MIN_CLIP_TIME) {
            BigDecimal(EditConstant.MIN_CLIP_TIME).divide(bigPxDuration, PRECISION, RoundingMode.HALF_UP).toFloat()
        } else {
            bigLength.toFloat()
        }
        DebugUtil.i(TAG, "onLayout mStart=$mStart mEnd=$mEnd mStartEdge=$mStartEdge mEndEdge=$mEndEdge mPxDuration=$mPxDuration minClip=$minClip")
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        canvas?.let { drawDragBar(it) }
    }

    private fun drawDragBar(canvas: Canvas) {
        val preView = parent as? GloblePreViewBar
            ?: return
        val amplitudes = preView.amplitudes
        if (amplitudes == null || amplitudes.isEmpty()) {
            return
        }
        canvas.apply {
            //画上下边框
            drawRect(mStart, 0f, mEnd, mLineHeight, mLinePaint)
            drawRect(mStart, height.toFloat() - mLineHeight, mEnd, height.toFloat(), mLinePaint)
            drawRect(mStart, 0f, mEnd, height.toFloat(), mZonePaint)
            if (isRtl()) {
                //绘制起始把手，RTL下使用 mHandleEndBitmap
                drawBitmap(mHandleEndBitmap, mStart, 0f, mPaint)
                //绘制结束把手
                drawBitmap(mHandleStartBitmap, mEnd - mBarWidth, 0f, mPaint)
            } else {
                drawBitmap(mHandleStartBitmap, mStart - mBarWidth, 0f, mPaint)
                drawBitmap(mHandleEndBitmap, mEnd, 0f, mPaint)
                DebugUtil.d(TAG, "mStart == $mStart,mBarWidth == $mBarWidth,left == ${mStart - mBarWidth},mEnd == $mEnd")
            }
        }
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (event == null) {
            return false
        }
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                mLastX = event.x
                val x = event.x
                val y = event.y
                val startRect = if (isRtl()) {
                    createRect(mStart, mStart + mBarWidth)
                } else {
                    createRect(mStart - mBarWidth, mStart)
                }

                val endRect = if (isRtl()) {
                    createRect(mEnd - mBarWidth, mEnd)
                } else {
                    createRect(mEnd, mEnd + mBarWidth)
                }

                direction = when {
                    startRect.contains(x, y) -> {
                        DRAG_START
                    }

                    endRect.contains(x, y) -> {
                        DRAG_END
                    }

                    else -> {
                        DRAG_NONE
                    }
                }
                if (direction == DRAG_START) {
                    mListener?.onTouchDownOnBar(direction, getStartTime())
                } else if (direction == DRAG_END) {
                    mListener?.onTouchDownOnBar(direction, getEndTime())
                }
                DebugUtil.i(TAG, "ACTION_DOWN direction=$direction")
                if (direction == DRAG_NONE) {
                    return false
                }
            }

            MotionEvent.ACTION_MOVE -> {
                DebugUtil.i(TAG, "ACTION_MOVE direction=$direction")
                val downX = event.x
                val offset = downX - mLastX
                if (abs(offset) > 0) {
                    dealOffset(offset)
                    mLastX = downX
                    postInvalidateOnAnimation()
                }
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                val downX = event.x
                val offset = downX - mLastX
                if (abs(offset) > 0) {
                    dealOffset(offset)
                    mLastX = downX
                    postInvalidateOnAnimation()
                }
                if (direction == DRAG_START) {
                    mListener?.onTouchUpOnBar(direction, getStartTime())
                } else if (direction == DRAG_END) {
                    mListener?.onTouchUpOnBar(direction, getEndTime())
                }
                direction = DRAG_NONE
                DebugUtil.i(TAG, "ACTION_UP direction=$direction")
            }
        }
        return true
    }

    private fun dealOffset(offset: Float) {
        when (direction) {
            DRAG_START -> {
                val newX = mStart + offset
                if (isRtl()) {
                    when {
                        newX > mStartEdge - mBarWidth -> {
                            mStart = mStartEdge - mBarWidth
                        }

                        newX < mEnd + minClip -> {
                            mStart = mEnd + minClip
                        }

                        else -> {
                            mStart += offset
                        }
                    }
                } else {
                    when {
                        newX < mStartEdge + mBarWidth -> {
                            mStart = mStartEdge + mBarWidth
                        }

                        newX > mEnd - minClip -> {
                            mStart = mEnd - minClip
                        }

                        else -> {
                            mStart += offset
                        }
                    }
                }
                cutStart(mStart)
            }

            DRAG_END -> {
                val newX = mEnd + offset
                if (isRtl()) {
                    when {
                        newX < mEndEdge + mBarWidth -> {
                            mEnd = mEndEdge + mBarWidth
                        }

                        newX > mStart - minClip -> {
                            mEnd = mStart - minClip
                        }

                        else -> {
                            mEnd += offset
                        }
                    }
                } else {
                    when {
                        newX > mEndEdge - mBarWidth -> {
                            mEnd = mEndEdge - mBarWidth
                        }

                        newX < mStart + minClip -> {
                            mEnd = mStart + minClip
                        }

                        else -> {
                            mEnd += offset
                        }
                    }
                }
                cutEnd(mEnd)
            }
        }
    }

    private fun isRtl(): Boolean {
        return getPreViewBar()?.reverseLayout ?: false
    }

    private fun createRect(start: Float, end: Float): RectF {
        return RectF(start, 0f, end, height.toFloat())
    }

    private fun cutStart(x: Float) {
        val newX = if (isRtl()) width - x else x
        var time = getPreViewBar()?.getTimeByX(newX, true) ?: 0
        DebugUtil.i(TAG, "cutStart x=$x mStartTime=$time mEndTime=$mEndTime width-x=${width - x}")
        if (time > mEndTime - EditConstant.MIN_CLIP_TIME) {
            time = mEndTime - EditConstant.MIN_CLIP_TIME
        }
        if (time < 0) {
            time = 0
        }
        mStartTime = time
        mListener?.onMoveOnDragBar(DRAG_START, time)
    }

    private fun cutEnd(x: Float) {
        val newX = if (isRtl()) width - x else x
        var time = getPreViewBar()?.getTimeByX(newX, true) ?: 0
        DebugUtil.i(TAG, "cutEnd x:$x mEndTime=$time mStartTime=$mStartTime width-x=${width - x}")
        if (time < mStartTime + EditConstant.MIN_CLIP_TIME) {
            time = mStartTime + EditConstant.MIN_CLIP_TIME
        }
        if (time > mDuration) {
            time = mDuration
        }
        mEndTime = time
        mListener?.onMoveOnDragBar(DRAG_END, time)
    }

    fun setCutTimeListener(listener: CutTimeListener) {
        mListener = listener
    }

    interface CutTimeListener {
        fun onTouchDownOnBar(direction: Int, time: Long)
        fun onMoveOnDragBar(direction: Int, time: Long)
        fun onTouchUpOnBar(direction: Int, time: Long)
    }
}