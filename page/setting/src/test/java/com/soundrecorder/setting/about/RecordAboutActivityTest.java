/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RecordAboutActivityTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.setting.about;

import static android.content.Intent.ACTION_USER_BACKGROUND;

import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.view.View;

import androidx.lifecycle.Lifecycle;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.setting.databinding.ActivityRecordAboutBinding;
import com.soundrecorder.setting.shadows.ShadowFeatureOption;
import com.soundrecorder.setting.shadows.ShadowRecorderLogger;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowApplication;
import org.robolectric.shadows.ShadowLooper;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class, ShadowRecorderLogger.class})
public class RecordAboutActivityTest {
    private ActivityController<RecordAboutActivity> mController;
    private ShadowApplication mShadowApplication;
    private Context mContext;

    @Before
    public void setUp() {
        mController = Robolectric.buildActivity(RecordAboutActivity.class);
        mContext = ApplicationProvider.getApplicationContext();
        mShadowApplication = ShadowApplication.getInstance();
    }

    @Test
    @Ignore
    public void should_startActivityAndRegisteredReceivers_when_onCreate() {
        RecordAboutActivity recordAboutActivity = mController.create().get();
        ShadowLooper.runUiThreadTasksIncludingDelayedTasks();
        Assert.assertNotNull(recordAboutActivity);
        Assert.assertEquals(Lifecycle.State.CREATED, recordAboutActivity.getLifecycle().getCurrentState());
        Assert.assertTrue(mShadowApplication.hasReceiverForIntent(new Intent(ACTION_USER_BACKGROUND)));
    }

    @Test
    @Ignore
    public void should_destoryActivity_when_onDestroy() {
        RecordAboutActivity recordAboutActivity = mController.create().destroy().get();
        Assert.assertEquals(Lifecycle.State.DESTROYED, recordAboutActivity.getLifecycle().getCurrentState());
        Assert.assertFalse(mShadowApplication.hasReceiverForIntent(new Intent(Intent.ACTION_USER_BACKGROUND)));
    }

    @Test
    public void should_correct_when_onApplyInsets() {
        RecordAboutActivity recordAboutActivity = mController.create().destroy().get();
        ActivityRecordAboutBinding binding = Whitebox.getInternalState(recordAboutActivity, "viewBinding");
        Assert.assertTrue(binding.tvIcpInfo.getVisibility() == View.VISIBLE);
        Assert.assertTrue(binding.tvIcpInfo.hasOnClickListeners());
        recordAboutActivity.onApplyInsets(0);
        binding.tvIcpInfo.performClick();
    }

    @Test
    public void should_correct_when_icpPerformLongClick() {
        RecordAboutActivity recordAboutActivity = mController.create().get();
        ActivityRecordAboutBinding binding = Whitebox.getInternalState(recordAboutActivity, "viewBinding");
        Assert.assertTrue(binding.tvIcpInfo.hasOnLongClickListeners());
        binding.tvIcpInfo.performLongClick();
        Assert.assertNotNull(Whitebox.getInternalState(recordAboutActivity, "popupWindow"));

        recordAboutActivity.onDestroy();
        Assert.assertNull(Whitebox.getInternalState(recordAboutActivity, "popupWindow"));
    }

    @Test
    public void should_correct_when_setTextToClipboard() throws Exception {
        RecordAboutActivity recordAboutActivity = mController.create().get();
        Whitebox.invokeMethod(recordAboutActivity, "setTextToClipboard", "");
    }

    @After
    public void tearDown() {
        mController = null;
    }
}
