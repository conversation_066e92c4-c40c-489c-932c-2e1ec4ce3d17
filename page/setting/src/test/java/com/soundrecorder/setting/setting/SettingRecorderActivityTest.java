/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SettingRecorderActivityTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.setting.setting;

import static org.junit.Assert.*;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.robolectric.Shadows.shadowOf;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.view.MenuItem;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.setting.shadows.ShadowFeatureOption;
import com.soundrecorder.setting.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.Robolectric;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowApplication;

import java.util.ArrayList;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = { ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class SettingRecorderActivityTest {

    private ActivityController<SettingRecorderActivity> mActivityController;
    private SettingRecorderActivity mActivity;
    private Context mContext;

    @Before
    public void setUp() throws Exception {
        mContext = ApplicationProvider.getApplicationContext();
        mActivityController = Robolectric.buildActivity(SettingRecorderActivity.class);
    }

    @After
    public void tearDown() throws Exception {
        mActivityController = null;
        mContext = null;
    }

    @Test
    public void should_initView_and_registerReceiver_when_onCreate() {
        mActivity = mActivityController.create().get();
        List<ShadowApplication.Wrapper> receivers = shadowOf(RuntimeEnvironment.application).getRegisteredReceivers();
        List<String> receiverActions = new ArrayList<>();
        for (ShadowApplication.Wrapper receiver : receivers) {
            IntentFilter intentFilter = receiver.getIntentFilter();
            for (int i = 0; i < intentFilter.countActions(); i++) {
                receiverActions.add(intentFilter.getAction(i));
            }
        }
        assertFalse(receiverActions.contains(Intent.ACTION_USER_BACKGROUND));
    }

    @Test
    public void should_finish_when_onActivityResult_exitApp() {
        mActivity = mActivityController.create().get();
        assertFalse(mActivity.isFinishing());
    }

    @Test
    public void should_finish_when_onOptionsItemSelected() {
        mActivity = mActivityController.create().get();
        MenuItem item = mock(MenuItem.class);
        doReturn(android.R.id.home).when(item).getItemId();
        mActivity.onOptionsItemSelected(item);
        assertTrue(mActivity.isFinishing());
    }

    @Test
    public void should_unregisterReceivers_when_onDestroy() {
        mActivity = mActivityController.create().get();
        List<ShadowApplication.Wrapper> receivers = shadowOf(RuntimeEnvironment.application).getRegisteredReceivers();
        List<String> receiverActions = new ArrayList<>();
        for (ShadowApplication.Wrapper receiver : receivers) {
            IntentFilter intentFilter = receiver.getIntentFilter();
            for (int i = 0; i < intentFilter.countActions(); i++) {
                receiverActions.add(intentFilter.getAction(i));
            }
        }
        assertFalse(receiverActions.contains(Intent.ACTION_USER_BACKGROUND));
    }
}