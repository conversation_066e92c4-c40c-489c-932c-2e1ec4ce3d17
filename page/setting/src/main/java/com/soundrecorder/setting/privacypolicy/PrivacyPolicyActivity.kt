/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PrivacyPolicyActivity
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.setting.privacypolicy

import android.os.Bundle
import android.view.MenuItem
import androidx.lifecycle.lifecycleScope
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.base.IFragmentCallback
import com.soundrecorder.common.base.PrivacyPolicyBaseActivity
import com.soundrecorder.common.db.CloudSyncRecorderDbUtil
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState
import com.soundrecorder.modulerouter.cloudkit.CloudSyncAction
import com.soundrecorder.modulerouter.cloudkit.utils.CloudPermissionUtilsAction
import com.soundrecorder.modulerouter.playback.PlaybackAction
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyAction
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.setting.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class PrivacyPolicyActivity : PrivacyPolicyBaseActivity() {

    companion object {
        /*用于识别,统计页面 勿改*/
        private const val FUNCTION_NAME = "PrivacyPolicy"
    }

    private val fragmentType by lazy {
        intent.getIntExtra(PrivacyPolicyConstant.FRAGMENT_CLASS_NAME_TYPE, com.soundrecorder.common.R.string.privacy_policy_settings_key)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_privacy_policy)
        val fragment = when (fragmentType) {
            com.soundrecorder.common.R.string.privacy_policy_settings_share_key -> {
                //三方信息共享清单
                PrivacyPolicyAction.newPrivacyPolicyInfoFragment(
                    PrivacyPolicyConstant.TYPE_THIRD_PARTY_INFORMATION_SHARING_CHECKLIST
                )
            }
            com.soundrecorder.common.R.string.privacy_policy_settings_collection_key -> {
                //个人信息明示清单
                PrivacyPolicyAction.newCollectionInfoFragment(
                    PrivacyPolicyConstant.TYPE_COLLECTION_OF_PERSONAL_INFORMATION_EXPRESS_CHECKLIST
                )
            }
            com.soundrecorder.common.R.string.settings_personal_collection_content_key -> {
                //个人信息明示清单 详情页
                val title = intent.getStringExtra(PrivacyPolicyConstant.FRAGMENT_CLASS_NAME_TITLE) ?: ""
                val collectionType = intent.getIntExtra(PrivacyPolicyConstant.FRAGMENT_CLASS_COLLECTION_TYPE, 0)
                DebugUtil.d(FUNCTION_NAME, "collection_content_key, title:$title")
                PrivacyPolicyAction.newCollectionInfoContentFragment(
                    title,
                    PrivacyPolicyConstant.TYPE_COLLECTION_OF_PERSONAL_INFORMATION_EXPRESS_CHECKLIST,
                    collectionType
                )
            }
            com.soundrecorder.common.R.string.privacy_policy_settings_policy_key -> {
                PrivacyPolicyAction.newPrivacyPolicyInfoFragment(
                    PrivacyPolicyConstant.TYPE_PERSONAL_INFORMATION_PROTECTION_POLICY
                )
            }
            else -> PrivacyPolicyAction.newPrivacyPolicyFragment()
        }

        if (fragment != null) {
            supportFragmentManager.beginTransaction().replace(R.id.root_layout, fragment, getFragmentTag()).commit()
        } else {
            finish()
        }
    }

    private fun getFragmentTag(): String = fragmentType.toString()

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> onBackPressed()
            else -> {}
        }
        return super.onOptionsItemSelected(item)
    }

    override fun getPrivacyPolicyDelegate(): IPrivacyPolicyDelegate? {
        if (mIPrivacyPolicyDelegate == null) {
            mIPrivacyPolicyDelegate = PrivacyPolicyAction.newPrivacyPolicyDelegate(context = this, resultListener = this)
        }

        return mIPrivacyPolicyDelegate
    }

    override fun checkAndShowPrivacyPolicyDialog() {
        if (getPrivacyPolicyDelegate()?.canShowWithdrawnPermissionConvert() == true) {
            getPrivacyPolicyDelegate()?.resumeShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_WITHDRAWN)
        }
    }

    override fun onPrivacyPolicyFail(type: Int, pageFrom: Int?) {
        if (type == PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_WITHDRAWN) {
            clearPermissionGrantedStatus()
        }
    }

    private fun isRegionAndSupportCloud(): Boolean = CloudSyncAction.isSupportCloudArea() && CloudSyncAction.isSupportSwitch()

    private fun clearPermissionGrantedStatus() {
        PermissionUtils.clearConvertGrantedStatus()
        PermissionUtils.setNetWorkGrantedStatus(this, false)
        /*
         * 取消全部转文本任务
         */
        PlaybackAction.cancelAllConvertTask()
        //关闭云服务
        if (isRegionAndSupportCloud()) {
            lifecycleScope.launch(Dispatchers.IO) {
                val isSuccess = CloudSyncAction.setSyncSwitch(CloudSwitchState.CLOSE)
                if (isSuccess) {
                    CloudPermissionUtilsAction.clearCloudGrantedStatus()
                    CloudSyncRecorderDbUtil.clearLocalSyncStatusForLogingout(BaseApplication.getAppContext())
                }
                lifecycleScope.launch(Dispatchers.Main) {
                    onFragmentRefresh()
                }
            }
        } else {
            onFragmentRefresh()
        }
    }

    private fun onFragmentRefresh() {
        (supportFragmentManager.findFragmentByTag(getFragmentTag()) as? IFragmentCallback)?.onFragmentRefresh()
    }
}