/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  SmartNamePreference
 * * Description: SmartNamePreference
 * * Version: 1.0
 * * Date : 2025/5/10
 * * Author: ********
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  ********    2025/5/10   1.0    build this module
 ****************************************************************/
package com.soundrecorder.setting.widget

import android.content.Context
import android.util.AttributeSet
import android.view.View.OnClickListener
import android.widget.TextView
import androidx.preference.PreferenceViewHolder
import com.coui.appcompat.cardlist.COUICardListSelectedItemLayout
import com.coui.appcompat.couiswitch.COUISwitch
import com.coui.appcompat.preference.COUISwitchPreference

class SmartNamePreference(context: Context?, attrs: AttributeSet?) :
    COUISwitchPreference(context, attrs) {

    private var listener: OnClickListener? = null
    //是否有智能命名任务正在进行中
    private var isTaskRunning: Boolean = false

    override fun onBindViewHolder(holder: PreferenceViewHolder?) {
        super.onBindViewHolder(holder)
        holder?.apply {
            itemView.setOnClickListener {
                listener?.onClick(it)
            }
            val title = holder.findViewById(android.R.id.title) as? TextView
            val summary = holder.findViewById(android.R.id.summary) as? TextView
            val couiSwitch = holder.findViewById(android.R.id.switch_widget) as? COUISwitch
            //有智能命名任务正在进行时，Preference中控件设置为不可点击
            (itemView as? COUICardListSelectedItemLayout)?.setBackgroundAnimationEnabled(!isTaskRunning)
            title?.isEnabled = !isTaskRunning
            summary?.isEnabled = !isTaskRunning
            couiSwitch?.isEnabled = !isTaskRunning
        }
    }

    fun setTaskRunning(isTaskRunning: Boolean) {
        this.isTaskRunning = isTaskRunning
        notifyChanged()
    }

    fun setOnClick(listener: OnClickListener) {
        this.listener = listener
    }
}