<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/couiColorBackground"
    android:orientation="vertical">

    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/common_margin"
        android:layout_marginEnd="@dimen/common_margin"
        android:nestedScrollingEnabled="true"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="@dimen/dp50">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/common_margin"
                android:textSize="18sp"
                app:layout_constraintBottom_toTopOf="@id/tv_licence" />

            <TextView
                android:id="@+id/tv_licence"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/common_margin"
                android:layout_marginBottom="90dp"
                android:textSize="12sp" />
        </LinearLayout>
    </ScrollView>

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/couiColorBackground"
        app:elevation="0dp"
        app:layout_behavior="com.soundrecorder.setting.opensource.SettingRecordBehavior">

        <com.coui.appcompat.toolbar.COUIToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp50"
            android:minHeight="@dimen/dp50" />

        <View
            android:id="@+id/divider_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/divider_background_height"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="@dimen/common_margin"
            android:layout_marginRight="@dimen/common_margin"
            android:alpha="0"
            android:background="?attr/couiColorDivider"
            android:forceDarkAllowed="false"
            tools:ignore="UnusedAttribute" />

    </com.google.android.material.appbar.AppBarLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>