/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordImagesPermissionTips.java
 Description:
 Version: 1.0
 Date: 2023-05-23
 Author: wangyu
 -----------Revision History-----------
 <author> <date> <version> <desc>
 wangyu 2023-05-23 create
 */
package com.soundrecorder.record

import android.content.Context
import android.view.View
import com.coui.appcompat.snackbar.COUISnackBar
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.R

object RecordImagesPermissionTips {

    /**
     * 权限提示重置开关 true需要显示，false不需要显示
     */
    var mShowReadImagePermissionTips = true

    @JvmStatic
    fun showReadVisualUserSelectedPermissionSnackBar(
        context: Context,
        rootView: View,
        listener: View.OnClickListener
    ) {
        COUISnackBar.make(
            rootView,
            context.getString(R.string.permission_open_read_image_desc_snackbar_v2),
            TimeUtils.TIME_MS_10000
        ).apply {
            setOnAction(context.getString(R.string.app_name_settings)) {
                listener.onClick(rootView)
                dismiss()
            }
            show()
        }
    }
}