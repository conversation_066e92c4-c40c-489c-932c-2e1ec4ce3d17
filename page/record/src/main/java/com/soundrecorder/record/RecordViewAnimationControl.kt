/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecordViewAnimationControl
 * Description:
 * Version: 1.0
 * Date: 2023/7/12
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/7/12 1.0 create
 */

package com.soundrecorder.record

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.view.View
import android.view.ViewGroup
import android.widget.Space
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.addListener
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.core.view.marginTop
import androidx.core.view.updateLayoutParams
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.splitwindow.FoldingWindowObserver
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.getFloatValue
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.common.utils.ViewUtils.updateConstraintHeight
import kotlin.math.roundToInt

class RecordViewAnimationControl(rootView: ViewGroup) {
    private val logTag = "RecorderViewAnimation"
    private var timerLayout: ViewGroup = rootView.findViewById(R.id.recorder_top)
    private var markLayout: ViewGroup = rootView.findViewById(R.id.mark_layout)
    private var markListView: ViewGroup = rootView.findViewById(R.id.mark_listview)
    private var waveGradientView: ViewGroup = rootView.findViewById(R.id.wave_gradient_view)
    private var toobarLayout: View = rootView.findViewById(R.id.abl)
    private var middleControl: ViewGroup = rootView.findViewById(R.id.middle_control)
    private var directRecordingView: ViewGroup = rootView.findViewById(R.id.view_direct_view)
    private var viewCenterArea: Space = rootView.findViewById(R.id.view_center_divider)
    private val pathInterpolator = COUIMoveEaseInterpolator()

    var foldWindowType: Int = FoldingWindowObserver.SCREEN_VERTICAL_EXPAND
    private val wavePercentHover: Float = BaseApplication.getAppContext()
        .getFloatValue(R.dimen.record_wave_view_height_percent_hover)
    private val wavePercentShowMark: Float = BaseApplication.getAppContext()
        .getFloatValue(R.dimen.record_wave_view_height_percent_show_mark)
    private val wavePercentDefault: Float = BaseApplication.getAppContext()
        .getFloatValue(R.dimen.record_wave_view_height_percent)

    private var showMarkAnimation: AnimatorSet? = null
    private var hideMarkAnimation: AnimatorSet? = null
    private var maxTimeLayoutHeight: Int = 0

    init {
        val resource = rootView.resources
        maxTimeLayoutHeight = resource.getDimensionPixelSize(com.soundrecorder.common.R.dimen.common_max_time_layout_height)
    }

    /**
     * 显示标记列表的动效
     * 1. 添加标记
     * 2.有标记的前提下进入、退出悬停模式
     */
    fun doShowMarkListViewAnimation(forceRun: Boolean) {
        if (!forceRun && markListView.isVisible) {
            DebugUtil.i(logTag, "doShowMarkListViewAnimation return by markListView.isVisible")
            return
        }
        cancelAllAnimation()
        DebugUtil.i(logTag, "doShowMarkListViewAnimation start,forceRun=$forceRun")

        showMarkAnimation = AnimatorSet()
        val animList = mutableListOf<Animator>()
        addShowMarkAnimation(animList)
        if (animList.isNotEmpty()) {
            showMarkAnimation?.playTogether(animList)
            showMarkAnimation?.start()
        }
    }

    /**
     * 隐藏标记列表的动效
     * 1. 删除所有标记
     * 2.无标记的前提下，进入、退出悬停模式
     */
    fun doHideMarkListViewAnimation(forceRun: Boolean) {
        if (!forceRun && !markListView.isVisible) {
            DebugUtil.i(logTag, "doHideMarkListViewAnimation return by markListView.notVisible")
            return
        }
        cancelAllAnimation()
        DebugUtil.i(logTag, "doHideMarkListViewAnimation start,forceRun=$forceRun")
        hideMarkAnimation = AnimatorSet()
        val animList = mutableListOf<Animator>()
        addHideMarkAnimation(animList)
        if (animList.isNotEmpty()) {
            hideMarkAnimation?.playTogether(animList)
            hideMarkAnimation?.start()
        }
    }

    private fun addShowMarkAnimation(animList: MutableList<Animator>): MutableList<Animator> {
        val waveDuration = NumberConstant.NUMBER_L400
        // 波形高度变化
        val endWaveHeight = getWaveHeight(true)
        genHeightAnimator(
            view = waveGradientView,
            startValue = waveGradientView.height, endValue = endWaveHeight,
            durationMill = waveDuration
        )?.run {
            animList.add(this)
        }
        val startTimeLayoutMarginTop = timerLayout.marginTop
        val endTimeLayoutMarginTop = calTimeLayoutMarginTop(true, endWaveHeight)
        // 时间区域的marginTop
        genTopMarginAnimator(
            view = timerLayout,
            startValue = startTimeLayoutMarginTop, endValue = endTimeLayoutMarginTop,
            durationMill = waveDuration
        )?.run {
            animList.add(this)
        }
        // 标记的的marginTOp
        genTopMarginAnimator(
            view = markLayout,
            startValue = markLayout.marginTop,
            endValue = calMarkLayoutMarginTop(endTimeLayoutMarginTop),
            durationMill = waveDuration
        )?.run {
            animList.add(this)
        }
        if (!markListView.isVisible || markListView.alpha != NumberConstant.NUM_F1_0) {
            markListView.alpha = NumberConstant.NUM_F0_0
            markListView.isVisible = true
            // 标记alpha动效
            genAlphaAnimator(
                view = markListView,
                startValue = markListView.alpha,
                endValue = NumberConstant.NUM_F1_0,
                delayMill = NumberConstant.NUM_200.toLong(),
                durationMill = NumberConstant.NUM_500.toLong()
            )?.run {
                animList.add(this)
            }
        }
        return animList
    }

    private fun addHideMarkAnimation(animList: MutableList<Animator>) {
        val waveDelay = NumberConstant.NUM_50.toLong()
        val waveDuration = NumberConstant.NUM_500.toLong()
        // 波形高度变化
        val endWaveHeight = getWaveHeight(false)
        if (waveGradientView.measuredHeight != endWaveHeight) {
            genHeightAnimator(
                view = waveGradientView,
                startValue = waveGradientView.height, endValue = endWaveHeight,
                delayMill = waveDelay, durationMill = waveDuration
            )?.run {
                animList.add(this)
            }
        }

        // 时间区域marginTop
        val timeLayoutMarginTop = calTimeLayoutMarginTop(false, endWaveHeight)
        genTopMarginAnimator(
            view = timerLayout,
            startValue = timerLayout.marginTop, endValue = timeLayoutMarginTop,
            delayMill = waveDelay, durationMill = waveDuration
        )?.run {
            animList.add(this)
        }
        genAlphaAnimator(
            view = markListView,
            startValue = markListView.alpha, endValue = NumberConstant.NUM_F0_0,
            durationMill = NumberConstant.NUM_200.toLong()
        )?.run {
            addListener(onEnd = {
                markListView.isVisible = false
            })
            animList.add(this)
        }
    }

    private fun genHeightAnimator(
        view: View,
        startValue: Int,
        endValue: Int,
        delayMill: Long = 0,
        durationMill: Long
    ): ValueAnimator? {
        if (startValue == endValue) {
            return null
        }
        return ValueAnimator.ofInt(startValue, endValue).apply {
            startDelay = delayMill
            duration = durationMill
            interpolator = pathInterpolator
            addUpdateListener {
                view.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    height = it.animatedValue as Int
                }
            }
        }
    }

    private fun genTopMarginAnimator(
        view: View,
        startValue: Int,
        endValue: Int,
        delayMill: Long = 0,
        durationMill: Long
    ): ValueAnimator? {
        if (startValue == endValue) {
            return null
        }
        return ValueAnimator.ofInt(startValue, endValue).apply {
            startDelay = delayMill
            duration = durationMill
            interpolator = pathInterpolator
            addUpdateListener {
                view.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    topMargin = it.animatedValue as Int
                }
            }
        }
    }

    private fun genScaleAnimator(
        view: View,
        startValue: Float,
        endValue: Float,
        delayMill: Long = 0,
        durationMill: Long
    ): ValueAnimator? {
        if (startValue == endValue) {
            return null
        }
        return ValueAnimator.ofFloat(startValue, endValue).apply {
            startDelay = delayMill
            duration = durationMill
            interpolator = pathInterpolator
            addUpdateListener {
                view.scaleX = it.animatedValue as Float
                view.scaleY = it.animatedValue as Float
            }
        }
    }

    private fun genAlphaAnimator(
        view: View,
        startValue: Float,
        endValue: Float,
        delayMill: Long = 0,
        durationMill: Long
    ): ValueAnimator? {
        if (startValue == endValue) {
            return null
        }
        return ValueAnimator.ofFloat(startValue, endValue).apply {
            startDelay = delayMill
            duration = durationMill
            interpolator = pathInterpolator
            addUpdateListener {
                view.alpha = it.animatedValue as Float
            }
        }
    }

    private fun genTransitionYAnimator(
        view: View,
        startValue: Float,
        endValue: Float,
        delayMill: Long = 0,
        durationMill: Long
    ): ValueAnimator? {
        if (startValue == endValue) {
            return null
        }
        return ValueAnimator.ofFloat(startValue, endValue).apply {
            startDelay = delayMill
            duration = durationMill
            interpolator = pathInterpolator
            addUpdateListener {
                view.translationY = it.animatedValue as Float
            }
        }
    }

    /**
     * 录制时间动画结束后应该显示的偏移位置。
     *
     * @param showCenter 是否显示在正中间（原始位置就是居中显示）
     */
    fun showRecordTopCenterOrTop(showCenter: Boolean) {
        DebugUtil.i(logTag, "showRecordTopCenterOrTop showCenter=$showCenter")
        if (showCenter) {
            if (hideMarkAnimation?.isRunning == true) {
                DebugUtil.i(logTag, "showRecordTopCenterOrTop doHideAnimationTime is running")
                return
            }
            showViewLocationCenter()
        } else {
            if (showMarkAnimation?.isRunning == true) {
                DebugUtil.i(logTag, "showRecordTopCenterOrTop doShowAnimationTime is running")
                return
            }
            showViewLocationTop()
        }
    }

    private fun showViewLocationCenter() {
        DebugUtil.i(logTag, "showViewLocationCenter")
        markListView.isInvisible = true
        val waveHeight = getWaveHeight(false)
        waveGradientView.updateConstraintHeight(waveHeight)
        val timeLayoutMarginTop = calTimeLayoutMarginTop(false, waveHeight)
        timerLayout.updateLayoutParams<ConstraintLayout.LayoutParams> {
            topMargin = timeLayoutMarginTop
        }
        markLayout.updateLayoutParams<ConstraintLayout.LayoutParams> {
            topMargin = calMarkLayoutMarginTop(timeLayoutMarginTop)
        }
    }

    /**
     * 有标记置顶显示
     */
    private fun showViewLocationTop() {
        DebugUtil.i(logTag, "showViewLocationTop")
        markListView.isVisible = true
        val waveHeight = getWaveHeight(true)
        waveGradientView.updateConstraintHeight(waveHeight)
        val timeLayoutMarginTop = calTimeLayoutMarginTop(true, waveHeight)
        timerLayout.updateLayoutParams<ConstraintLayout.LayoutParams> {
            topMargin = timeLayoutMarginTop
        }
    }

    /**
     * 计算时间区域marginTop
     */
    private fun calTimeLayoutMarginTop(
        showMark: Boolean,
        waveHeight: Int = getWaveHeight(showMark)
    ): Int {
        val waveBottom = if (waveGradientView.isVisible) {
            waveGradientView.top + waveHeight
        } else {
            // 不可见，要减去标题栏状态栏高度
            toobarLayout.top + toobarLayout.height
        }

        return if (isHoverState()) {
            // 悬停下：（中线top-波形.bottom-时间区域高度）/2
            (viewCenterArea.top - waveBottom - maxTimeLayoutHeight) / NumberConstant.NUM_2
        } else if (!showMark) {
            // 非悬停无标记：（底部录制按钮.top-波形.bottom-时间区域高度）/2
            val view = if (directRecordingView.isVisible) directRecordingView else middleControl
            (view.top - waveBottom - maxTimeLayoutHeight) / NumberConstant.NUM_2
        } else {
            0
        }
    }

    /**
     * 计算标记区域marginTop
     */
    private fun calMarkLayoutMarginTop(timeMarginBottom: Int): Int {
        return if (isHoverState()) {
            // 标记marginTop = 时间区域marginBottom+中线高度+标记距离中线的margin
            (timeMarginBottom + viewCenterArea.height +
                    markLayout.context.resources.getDimensionPixelSize(R.dimen.mark_layout_margin_top_hover_state))
        } else {
            0
        }
    }


    /**
     * 获取波形高度
     */
    fun getWaveHeight(showMark: Boolean): Int {
        val percent = getWavePercent(showMark)
        val screenHeight = ScreenUtil.screenHeight
        val result =  (screenHeight * percent).roundToInt()
        DebugUtil.i(logTag, "getWaveHeight showMark=$showMark,percent =$percent,screenHeight=$screenHeight,result=$result")
        return result
    }

    /**
     * 获取当前波形区域占屏幕的百分占比
     */
    fun getWavePercent(showMark: Boolean): Float {
        return if (isHoverState()) {
            wavePercentHover
        } else if (showMark) {
            wavePercentShowMark
        } else {
            wavePercentDefault
        }
    }

    /**
     * 是否处于悬停状态
     */
    fun isHoverState(): Boolean =
        foldWindowType == FoldingWindowObserver.SCREEN_HORIZONTAL_HOVER

    fun cancelAllAnimation() {
        showMarkAnimation?.cancel()
        hideMarkAnimation?.cancel()
    }

    fun release() {
        cancelAllAnimation()
        hideMarkAnimation = null
        showMarkAnimation = null
    }
}