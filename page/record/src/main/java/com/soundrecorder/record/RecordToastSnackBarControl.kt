/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecordToastSnackBarControl
 * Description:
 * Version: 1.0
 * Date: 2023/8/31
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/8/31 1.0 create
 */

package com.soundrecorder.record

import android.os.Build
import android.widget.Toast
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.recorderservice.api.RecorderViewModelApi
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class RecordToastSnackBarControl(private var recordActivity: RecorderActivity?) {
    private val logTag = "RecordToastSnackBarControl"

    /**
     * 非静音提示Toast是否正在show
     */
    var muteToastShowing = false
        set(value) {
            field = value
            if (!value) {
                doCheckShowNextSnack()
            }
        }
    /**
     * 通知权限引导snackBar是否正在show
     */
    var notificationSnackShowing = false
        set(value) {
            field = value
            if (!value) {
                doCheckShowNextSnack()
            }
        }

    /**
     * 是否需要显示通知权限引导snackBar
     */
    var needShowNotificationSnack = false
    /**
     * 是否需要显示读取图片权限引导snackBar
     */
    var needShowReadImageSnack = false

    private var muteToast: Toast? = null
    private var toastCallBack: Toast.Callback? = null

    /**
     * 满意度
     * 在录制开始时
     * 判断当前是铃声变为静音状态
     * 则先提示用户“录音期间来电和通知不响铃”
     * 提示结束之后再去检测是否要显示图片标记新手提示
     *
     *
     * 如果录制前就处于静音状态
     * 则直接检测是否要显示图片标记新手提示
     */
    fun checkNeedRecordingMuteToast() {
        if (BaseApplication.sNeedToNormalRingMode) {
            val context = BaseApplication.getAppContext()
            val msg: String = context.resources.getString(com.soundrecorder.common.R.string.record_mute_tips)
            muteToast = Toast.makeText(context, msg, Toast.LENGTH_SHORT)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                toastCallBack = object : Toast.Callback() {
                    override fun onToastShown() {
                        DebugUtil.d("RecordToastSnackBarControl", "Toast onToastShown")
                        muteToastShowing = true
                    }

                    override fun onToastHidden() {
                        DebugUtil.d("RecordToastSnackBarControl", "Toast onToastHidden")
                        muteToastShowing = false
                    }
                }
                muteToast?.addCallback(toastCallBack!!)
            } else {
                CoroutineScope(Dispatchers.IO).launch {
                    delay(NumberConstant.NUM_L2000)
                    muteToastShowing = false
                }
            }
            muteToast?.show()
        } else {
            doCheckShowNextSnack()
        }
    }

    private fun doCheckShowNextSnack() {
        DebugUtil.i(logTag, "doCheckShowNextSnack, needShowNotificationSnack=$needShowNotificationSnack," +
                "needShowReadImageSnack=$needShowReadImageSnack")
        if (needShowNotificationSnack) {
            recordActivity?.showNotificationPermissionSnackBar(false)
            needShowNotificationSnack = false
        } else if (needShowReadImageSnack) {
            recordActivity?.mPopViewController?.checkReadImagePermissionTips()
            needShowReadImageSnack = false
        } else {
            recordActivity?.showPictureMarkTips()
        }
    }

    /**
     * check是否可以弹通知权限引导snackBar
     */
    fun checkCanShowNotificationSnack(): Boolean {
        // toast在权限回调后执行，所以增加录制状态一起来判断可以显示通知snackBar
        return !muteToastShowing && RecorderViewModelApi.isAlreadyRecording()
    }

    /**
     * check 是否可以弹图片权限snackBar
     */
    fun checkCanShowReadImageSnack(): Boolean {
        return !muteToastShowing && !notificationSnackShowing
    }

    /**
     * check 是否可以新手引导
     */
    fun checkShowPictureMarkTip(): Boolean {
        return !muteToastShowing && !notificationSnackShowing
    }

    fun release() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            toastCallBack?.let {
                muteToast?.removeCallback(it)
            }
            toastCallBack = null
        }
        muteToast = null
        recordActivity = null
    }
}