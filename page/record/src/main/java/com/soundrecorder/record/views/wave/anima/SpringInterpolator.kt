/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SpringInterpolator
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.record.views.wave.anima

import android.view.animation.Interpolator
import java.lang.Math.*
import kotlin.math.pow

class SpringInterpolator : Interpolator {

    override fun getInterpolation(input: Float): Float {
        return (2.0.pow((-10 * input).toDouble()) * kotlin.math.sin((input - 0.7 / 3.3) * (1.5 * PI) / 0.7) + 1).toFloat()
    }
}