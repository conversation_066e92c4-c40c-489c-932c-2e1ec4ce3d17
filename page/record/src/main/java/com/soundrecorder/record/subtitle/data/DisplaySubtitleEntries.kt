/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - DisplaySubtitleEntries.kt
 * Description:
 *     Define some data entries for display subtitle.
 *
 * Version: 1.0
 * Date: 2025-05-29
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2025-05-29   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.record.subtitle.data

import com.soundrecorder.common.databean.ConvertContentItem

/**
 * 上屏展示的字幕文本缓存单元
 */
data class DisplaySubtitleEntry(
    // 原始的字幕文本段Asr内容(含时间戳及文本内容)
    var originContent: ConvertContentItem,
    // 上屏展示的字幕文本段内容(已插入标记的旗标字符)
    var displayContent: String,
    // 已插入的标记记录，用于后续处理新标记插入
    val insertedMarks: MutableList<DisplayMark>
)

/**
 * 上屏展示的标记插入信息
 */
data class DisplayMark(
    // 标记的在录音时间轴上的时间点位置
    val timeOffset: Long,
    // 标记在对应的字幕文本段中的插入位置(基于原始的字幕文本计算)
    var insertPosition: Int
)