/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - SubtitleMarkInsertHelper.kt
 * Description:
 *     The helper class for inserting marks into subtitle text.
 *
 * Version: 1.0
 * Date: 2025-05-29
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2025-05-29   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.record.subtitle

import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.record.subtitle.data.DisplayMark
import com.soundrecorder.record.subtitle.data.DisplaySubtitleEntry
import kotlin.math.max
import kotlin.math.min

/**
 * 采用Augment生成，生成时的Prompt如下：
 * ----------------------------------------------------------------
 * 后续相关解答及生成的代码的注释均用中文。
 *
 * 已知：
 * 1. SubtitleMarkInsertHelper中已实现了根据单个标记时间点计算该标记在原始字幕文本中插入位置的方法calculateMarkFlagInsertPosition；
 * 2. 已定义了DisplayMark和DisplaySubtitleEntry作为单条上屏字幕的数据实体类；
 * 3. ConvertContentItem的startTime到endTime的时间段区间不会重叠，textContent为字幕的原始文本；
 *
 * 按以下要求在SubtitleMarkInsertHelper中生成代码：
 * 1. 添加多条上屏字幕的DisplaySubtitleEntry列表及其对外获取方法；
 * 2. 添加传入ConvertContentItem新增DisplaySubtitleEntry至列表的方法，
 *    如果ConvertContentItem的startTime相同则对已有DisplaySubtitleEntry的originContent进行更新而非创建新的；
 * 3. 添加传入标记时间点位置的列表来新增标记的方法，添加标记的逻辑为：
 *   1) 如果标记的时间点在ConvertContentItem的startTime到endTime之间，
 *      则添加到对应的DisplaySubtitleEntry的insertedMarks中，
 *      并通过calculateMarkFlagInsertPosition计算insertPosition；
 *   2) 如果标记的时间点在第一个ConvertContentItem的startTime之前，
 *      则添加到第一个ConvertContentItem对应的DisplaySubtitleEntry的insertedMarks中，且insertPosition为0；
 *   3) 如果标记的时间点不在任何一个ConvertContentItem的startTime到endTime之间，
 *      也不在第一个ConvertContentItem的startTime之前，
 *      则加入标记的时间点之前的endTime最接近的ConvertContentItem对应的DisplaySubtitleEntry的insertedMarks中，
 *      且insertPosition为ConvertContentItem的textContent的末尾位置；
 *   4) 时间点相同的标记视为同一个标记不重复进行处理；
 * 4. 按第3条要求添加标记后，要刷新被修改的DisplaySubtitleEntry的displayContent，刷新逻辑如下；
 *   1) 按照insertedMarks中每个标记的insertPosition，将标记字符(已定义常量ICON_MARK_FLAG)插入到textContent中的对应位置；
 *   2) 有多个insertPosition相同的标记，则在textContent中的对应位置插入多个标记字符；
 *   3) 对textContent字符串内容的修改均赋值给displayContent，不要修改textContent的原始值；
 * 5. 新增的方法需要添加对应的注释；
 *
 * 生成代码后，对SubtitleMarkInsertHelper中新增的方法及已有的calculateMarkFlagInsertPosition方法，
 * 在SubtitleMarkInsertHelperTest中生成对应的单元测试用例，具体要求如下：
 * 1. 单元测试框架为JUnit4，需要mock处理时使用mockk；
 * 2. 按照Given-When-Then的格式生成用例代码，并用行注释标记用例输入(Given)，用例执行(When)和结果校验(Then)的单元测试代码起始位置；
 * 3. 单元测试用例的方法名按照
 *    `should <用例的期待结果> when <被测试的方法> <用例的输入内容或if条件等>`的格式命名，
 *    "<>"位置按照"<>"内的提示根据用例内容动态替换，方法名本身按照英文命名处理；
 * ----------------------------------------------------------------
 *
 * 补充修正的Prompt：
 * ----------------------------------------------------------------
 * addOrUpdateDisplaySubtitleEntry也需要对更新的DisplaySubtitleEntry触发刷新refreshDisplayContent，
 * 补充修改，相关单元测试用例也一并更新
 * ----------------------------------------------------------------
 * 更新originContent后，刷新displayContent前，
 * 已插入标记的insertPosition需要重新按照新的originContent进行计算，补充修改
 * ----------------------------------------------------------------
 */
class SubtitleMarkInsertHelper {

    private companion object {
        /**
         * 标记字符，用于插入到字幕文本当中
         */
        private const val ICON_MARK_FLAG = "\u2691"
    }

    /**
     * 上屏字幕条目列表
     */
    private val displaySubtitleEntries = mutableListOf<DisplaySubtitleEntry>()

    /**
     * 获取上屏字幕条目列表
     *
     * @return 上屏字幕条目列表的只读副本
     */
    fun getDisplaySubtitleEntries(): List<DisplaySubtitleEntry> {
        return displaySubtitleEntries.toList()
    }

    /**
     * 添加或更新字幕条目
     *
     * 如果传入的ConvertContentItem的startTime与已有条目相同，则更新已有条目的originContent；
     * 否则创建新的DisplaySubtitleEntry并添加到列表中。
     *
     * @param convertContentItem 要添加的字幕内容项
     */
    fun addOrUpdateDisplaySubtitleEntry(convertContentItem: ConvertContentItem) {
        // 查找是否已存在相同startTime的条目
        val existingEntry = displaySubtitleEntries.find {
            it.originContent.startTime == convertContentItem.startTime
        }

        if (existingEntry != null) {
            // 更新已有条目的originContent
            existingEntry.originContent = convertContentItem
            // 如果已有标记，需要重新计算标记的插入位置，然后刷新displayContent；否则直接使用新的textContent
            if (existingEntry.insertedMarks.isNotEmpty()) {
                recalculateMarkInsertPositions(existingEntry)
                refreshDisplayContent(existingEntry)
            } else {
                existingEntry.displayContent = convertContentItem.textContent
            }
        } else {
            // 创建新的DisplaySubtitleEntry
            val newEntry = DisplaySubtitleEntry(
                originContent = convertContentItem,
                displayContent = convertContentItem.textContent,
                insertedMarks = mutableListOf()
            )
            displaySubtitleEntries.add(newEntry)

            // 按startTime排序保持列表有序
            displaySubtitleEntries.sortBy { it.originContent.startTime }
        }
    }

    /**
     * 批量添加标记到字幕条目中
     *
     * 根据标记的时间点，将标记添加到对应的DisplaySubtitleEntry中，并刷新displayContent。
     * 时间点相同的标记视为同一个标记，不重复处理。
     *
     * @param markTimeOffsets 标记时间点列表（毫秒）
     */
    fun addMarksToSubtitleEntries(markTimeOffsets: List<Long>) {
        if (displaySubtitleEntries.isEmpty()) {
            return
        }

        // 去重处理，时间点相同的标记视为同一个标记
        val uniqueMarkTimeOffsets = markTimeOffsets.distinct()
        val modifiedEntries = mutableSetOf<DisplaySubtitleEntry>()

        for (markTimeOffset in uniqueMarkTimeOffsets) {
            val targetEntry = findTargetEntryForMark(markTimeOffset)
            if (targetEntry != null) {
                val insertPosition = calculateInsertPositionForEntry(targetEntry, markTimeOffset)

                // 检查是否已存在相同时间点的标记
                val existingMark = targetEntry.insertedMarks.find {
                    it.timeOffset == markTimeOffset
                }

                if (existingMark == null) {
                    // 添加新标记
                    val newMark = DisplayMark(markTimeOffset, insertPosition)
                    targetEntry.insertedMarks.add(newMark)
                    modifiedEntries.add(targetEntry)
                }
            }
        }

        // 刷新被修改的条目的displayContent
        modifiedEntries.forEach { entry ->
            refreshDisplayContent(entry)
        }
    }

    /**
     * 查找标记应该插入的目标字幕条目
     *
     * @param markTimeOffset 标记时间点
     * @return 目标字幕条目，如果找不到则返回null
     */
    private fun findTargetEntryForMark(markTimeOffset: Long): DisplaySubtitleEntry? {
        if (displaySubtitleEntries.isEmpty()) {
            return null
        }

        // 1. 如果标记时间点在某个ConvertContentItem的startTime到endTime之间
        for (entry in displaySubtitleEntries) {
            if (markTimeOffset >= entry.originContent.startTime &&
                markTimeOffset <= entry.originContent.endTime) {
                return entry
            }
        }

        // 2. 如果标记时间点在第一个ConvertContentItem的startTime之前
        val firstEntry = displaySubtitleEntries.first()
        if (markTimeOffset < firstEntry.originContent.startTime) {
            return firstEntry
        }

        /*
         * 3. 如果标记时间点不在任何一个ConvertContentItem的时间范围内，
         *    找到标记时间点之前的endTime最接近的ConvertContentItem
         */
        var targetEntry: DisplaySubtitleEntry? = null
        var closestEndTime = Long.MIN_VALUE

        for (entry in displaySubtitleEntries) {
            if (entry.originContent.endTime <= markTimeOffset &&
                entry.originContent.endTime > closestEndTime) {
                closestEndTime = entry.originContent.endTime
                targetEntry = entry
            }
        }

        return targetEntry
    }

    /**
     * 计算标记在指定字幕条目中的插入位置
     *
     * @param entry 目标字幕条目
     * @param markTimeOffset 标记时间点
     * @return 插入位置
     */
    private fun calculateInsertPositionForEntry(
        entry: DisplaySubtitleEntry,
        markTimeOffset: Long
    ): Int {
        val originContent = entry.originContent

        // 如果标记时间点在第一个ConvertContentItem的startTime之前
        if (markTimeOffset < originContent.startTime) {
            return 0
        }

        // 如果标记时间点在ConvertContentItem的endTime之后
        if (markTimeOffset > originContent.endTime) {
            return originContent.textContent.length
        }

        // 使用已有的计算方法
        return calculateMarkFlagInsertPosition(
            originContent.textContent,
            originContent.startTime,
            originContent.endTime,
            markTimeOffset
        )
    }

    /**
     * 重新计算字幕条目中所有标记的插入位置
     *
     * 当originContent更新后，需要根据新的文本内容重新计算所有已插入标记的insertPosition。
     *
     * @param entry 需要重新计算标记位置的字幕条目
     */
    private fun recalculateMarkInsertPositions(entry: DisplaySubtitleEntry) {
        val originContent = entry.originContent

        // 创建新的标记列表，包含重新计算后的插入位置
        val updatedMarks = entry.insertedMarks.map { mark ->
            val newInsertPosition = calculateMarkFlagInsertPosition(
                originContent.textContent,
                originContent.startTime,
                originContent.endTime,
                mark.timeOffset
            )
            DisplayMark(mark.timeOffset, newInsertPosition)
        }

        // 清空原有标记并添加更新后的标记
        entry.insertedMarks.clear()
        entry.insertedMarks.addAll(updatedMarks)
    }

    /**
     * 刷新字幕条目的displayContent
     *
     * 根据insertedMarks中的标记，将标记字符插入到textContent的对应位置，
     * 生成新的displayContent。
     *
     * @param entry 要刷新的字幕条目
     */
    private fun refreshDisplayContent(entry: DisplaySubtitleEntry) {
        val originalText = entry.originContent.textContent
        val marks = entry.insertedMarks.sortedBy { it.insertPosition }

        if (marks.isEmpty()) {
            entry.displayContent = originalText
            return
        }

        val result = StringBuilder()
        var currentPosition = 0

        // 按插入位置分组，处理相同位置的多个标记
        val marksByPosition = marks.groupBy { it.insertPosition }

        for (position in marksByPosition.keys.sorted()) {
            // 添加当前位置之前的文本
            if (position > currentPosition) {
                result.append(originalText.substring(currentPosition, position))
            }

            // 添加该位置的所有标记字符
            val marksAtPosition = marksByPosition[position] ?: emptyList()
            repeat(marksAtPosition.size) {
                result.append(ICON_MARK_FLAG)
            }

            currentPosition = position
        }

        // 添加剩余的文本
        if (currentPosition < originalText.length) {
            result.append(originalText.substring(currentPosition))
        }

        entry.displayContent = result.toString()
    }

    /**
     * 计算标记插入位置
     *
     * 根据字幕内容、字幕的开始和结束时间偏移量以及标记的时间偏移量，计算标记在字幕中的插入位置。
     *
     * @param asrContent 字幕内容
     * @param asrStartTimeOffset 字幕的开始时间偏移量（毫秒）
     * @param asrEndTimeOffset 字幕的结束时间偏移量（毫秒）
     * @param markTimeOffset 标记的时间偏移量（毫秒）
     * @return 标记在字幕中的插入位置（字符索引），如果无法计算返回-1
     */
    fun calculateMarkFlagInsertPosition(
        asrContent: String,
        asrStartTimeOffset: Long,
        asrEndTimeOffset: Long,
        markTimeOffset: Long
    ): Int {
        if (asrEndTimeOffset <= asrStartTimeOffset) {
            return -1
        }
        if (markTimeOffset <= asrStartTimeOffset) {
            return 0
        }
        if (markTimeOffset >= asrEndTimeOffset) {
            return asrContent.length
        }
        val asrDuration = asrEndTimeOffset - asrStartTimeOffset
        val markTimePos = markTimeOffset - asrStartTimeOffset
        val pos = (markTimePos / asrDuration.toFloat() * asrContent.length).toInt()
        return max(0, min(pos, asrContent.length))
    }
}