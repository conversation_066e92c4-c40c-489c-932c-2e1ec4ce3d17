/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: TransparentActivity
 Description:
 Version: 1.0
 Date: 2022/8/30
 Author: ********(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/30 1.0 create
 */

package oplus.multimedia.soundrecorder.card.dragonFly

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.soundrecorder.base.ext.getStringExtraSecure
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.utils.AppCardUtils.addContinueRequestPermissionFlag
import com.soundrecorder.common.task.ExcludeActivityTask
import oplus.multimedia.soundrecorder.card.dragonFly.RecorderAppCardDialogUtils.showSaveFileSuccessDialog
import oplus.multimedia.soundrecorder.card.dragonFly.RecorderAppCardManager.CARD_TAG

class RecorderAppCardActivity : AppCompatActivity(), ExcludeActivityTask {
    companion object {
        const val KEY_DO_ACTION = "do_action"
        const val KEY_FILE_NAME = "file_name"
        const val ACTION_SHOW_NO_PERMISSION = "action_show_no_permission"
        const val ACTION_SHOW_SAVE_FILE_SUCCESS = "action_show_save_file_success"
    }

    private var dialog: COUIBottomSheetDialog? = null
    override fun hasExclude(): Boolean {
        return true
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val doAction = intent.getStringExtraSecure(KEY_DO_ACTION)
        DebugUtil.i(CARD_TAG, "DoAction = $doAction")
        if (doAction.isNullOrEmpty()) {
            finish()
            return
        }
        when (doAction) {
            ACTION_SHOW_NO_PERMISSION -> continueMainTaskWhenNoPermission()
            ACTION_SHOW_SAVE_FILE_SUCCESS -> {
                val fileName = intent.getStringExtra(KEY_FILE_NAME)
                if (fileName.isNullOrEmpty()) {
                    finish()
                } else {
                    dialog = showSaveFileSuccessDialog(fileName) {
                        BuryingPoint.addClickBtnForLook()
                    }?.apply {
                        setOnDismissListener {
                            finish()
                        }
                    }
                }
            }
        }
    }

    override fun onStop() {
        super.onStop()
        if (!isFinishing) {
            finish()
        }
    }

    override fun onDestroy() {
        dialog?.let {
            if (it.isShowing) {
                it.dismiss(false)
            }
        }
        super.onDestroy()
    }

    /**
     * 副屏卡片无权限，展开续接录音录制页面
     */
    private fun continueMainTaskWhenNoPermission() {
        startActivity(Intent("com.soundrecorder.dragonfly.startRecordActivity").apply {
            setPackage(packageName)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
            addContinueRequestPermissionFlag()
        })
        finish()
    }
}
