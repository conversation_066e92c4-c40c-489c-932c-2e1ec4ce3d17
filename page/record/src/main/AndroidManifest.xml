<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.soundrecorder.record">

    <uses-sdk tools:overrideLibrary="com.oplus.sdk.addon.sdk" />

    <application>
        <activity
            android:name="com.soundrecorder.record.RecorderActivity"
            android:configChanges="locale|orientation|keyboardHidden|screenSize|screenLayout|layoutDirection|uiMode|smallestScreenSize|navigation|fontScale|keyboard|density"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:label="@string/app_name_main"
            android:launchMode="singleTop"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:screenOrientation="behind"
            android:theme="@style/RecorderActivityTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="com.oppo.soundrecorder.open_recorder" />
                <action android:name="com.oplus.soundrecorder.open_recorder" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <!--泛在状态栏胶囊点击跳转录制页面逻辑-->
            <intent-filter>
                <action android:name="com.coloros.soundrecorder.seedling.startRecord" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data
                android:name="color.support.UI_OPTIONS"
                android:value="splitActionBarWhenNarrow" />
        </activity>

        <activity
            android:name="com.soundrecorder.record.picturemark.view.PictureSelectActivity"
            android:exported="false"
            android:label="@string/app_name_main"
            android:screenOrientation="behind"
            android:theme="@style/NormalAndTransparentWindow" />

        <activity
            android:name="com.soundrecorder.record.picturemark.PopViewLoadingActivity"
            android:exported="false"
            android:screenOrientation="behind"
            android:theme="@style/PopViewLoading" />

        <activity
            android:name="oplus.multimedia.soundrecorder.card.dragonFly.RecorderAppCardActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT"
            android:showWhenLocked="true"
            android:launchMode="singleTop"
            android:taskAffinity="${applicationId}.OtherDisplay"
            android:theme="@style/AppCardTheme">
            <intent-filter>
                <action android:name="com.soundrecorder.dragonfly.RecorderAppCardActivity" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <!--蜻蜓副屏-负一屏-->

        <service
            android:name="oplus.multimedia.soundrecorder.RecordStateService"
            android:enabled="true"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE">

            <intent-filter>
                <action android:name="com.oplus.soundrecorder.RecordStateService" />
            </intent-filter>
        </service>

        <provider
            android:name="oplus.multimedia.soundrecorder.RecorderStateProvider"
            android:authorities="com.multimedia.record.state.provider"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE" />
    </application>
</manifest>