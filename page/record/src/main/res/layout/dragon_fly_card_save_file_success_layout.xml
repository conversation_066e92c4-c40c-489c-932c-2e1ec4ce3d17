<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tvAppInfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="52dp"
        android:drawablePadding="@dimen/dp4"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:includeFontPadding="false"
        android:lines="1"
        android:padding="0dp"
        android:text="@string/app_name_main"
        android:textColor="#D9FFFFFF"
        android:textSize="@dimen/dp12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvSaveSuccess"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp8"
        android:layout_marginTop="142dp"
        android:layout_marginEnd="@dimen/dp8"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:includeFontPadding="false"
        android:lines="1"
        android:padding="0dp"
        android:text="@string/dragon_fly_save_record_success"
        android:textColor="#FFFFFFFF"
        android:textSize="@dimen/dp24"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tvRecordTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp8"
        android:layout_marginTop="178dp"
        android:layout_marginEnd="@dimen/dp8"
        android:ellipsize="end"
        android:gravity="center"
        android:lines="1"
        android:textColor="#8CFFFFFF"
        android:textSize="@dimen/dp12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="标准录音 3" />

    <com.coui.appcompat.button.COUIButton
        android:id="@+id/btnView"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp44"
        android:layout_gravity="center_horizontal"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginBottom="@dimen/dp24"
        android:fontFamily="sans-serif-medium"
        android:text="@string/cloud_recordings_reason"
        android:textColor="@color/coui_color_white"
        android:textSize="@dimen/dp14"
        app:animEnable="true"
        app:drawableColor="#4DFFFFFF"
        app:expandOffset="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>