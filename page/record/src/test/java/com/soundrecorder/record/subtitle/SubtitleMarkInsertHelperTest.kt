/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - SubtitleMarkInsertHelperTest.kt
 * Description:
 *     Test cases for SubtitleMarkInsertHelper
 *
 * Version: 1.0
 * Date: 2025-05-29
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2025-05-29   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.record.subtitle

import com.soundrecorder.common.databean.ConvertContentItem
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class SubtitleMarkInsertHelperTest : Assert() {

    private lateinit var helper: SubtitleMarkInsertHelper

    @Before
    fun setUp() {
        helper = SubtitleMarkInsertHelper()
    }

    // ========== calculateMarkFlagInsertPosition方法的测试用例 ==========

    @Test
    fun should_return_negative_one_when_calculateMarkFlagInsertPosition_with_invalid_time_range() {
        // Given: 无效的时间范围（结束时间小于等于开始时间）
        val asrContent = "测试内容"
        val asrStartTimeOffset = 1000L
        val asrEndTimeOffset = 1000L // 等于开始时间
        val markTimeOffset = 1500L

        // When: 调用计算方法
        val result = helper.calculateMarkFlagInsertPosition(
            asrContent, asrStartTimeOffset, asrEndTimeOffset, markTimeOffset
        )

        // Then: 返回-1
        assertEquals(-1, result)
    }

    @Test
    fun should_return_zero_when_calculateMarkFlagInsertPosition_with_mark_before_start_time() {
        // Given: 标记时间点在开始时间之前
        val asrContent = "测试内容"
        val asrStartTimeOffset = 1000L
        val asrEndTimeOffset = 2000L
        val markTimeOffset = 500L // 在开始时间之前

        // When: 调用计算方法
        val result = helper.calculateMarkFlagInsertPosition(
            asrContent, asrStartTimeOffset, asrEndTimeOffset, markTimeOffset
        )

        // Then: 返回0
        assertEquals(0, result)
    }

    @Test
    fun should_return_content_length_when_calculateMarkFlagInsertPosition_with_mark_after_end_time() {
        // Given: 标记时间点在结束时间之后
        val asrContent = "测试内容"
        val asrStartTimeOffset = 1000L
        val asrEndTimeOffset = 2000L
        val markTimeOffset = 2500L // 在结束时间之后

        // When: 调用计算方法
        val result = helper.calculateMarkFlagInsertPosition(
            asrContent, asrStartTimeOffset, asrEndTimeOffset, markTimeOffset
        )

        // Then: 返回内容长度
        assertEquals(asrContent.length, result)
    }

    @Test
    fun should_return_middle_position_when_calculateMarkFlagInsertPosition_with_mark_in_middle() {
        // Given: 标记时间点在中间位置
        val asrContent = "测试内容" // 4个字符
        val asrStartTimeOffset = 1000L
        val asrEndTimeOffset = 2000L
        val markTimeOffset = 1500L // 正好在中间

        // When: 调用计算方法
        val result = helper.calculateMarkFlagInsertPosition(
            asrContent, asrStartTimeOffset, asrEndTimeOffset, markTimeOffset
        )

        // Then: 返回中间位置（2）
        assertEquals(2, result)
    }

    // ========== getDisplaySubtitleEntries方法的测试用例 ==========

    @Test
    fun should_return_empty_list_when_getDisplaySubtitleEntries_with_no_entries() {
        // Given: 没有添加任何条目

        // When: 获取条目列表
        val result = helper.getDisplaySubtitleEntries()

        // Then: 返回空列表
        assertTrue(result.isEmpty())
    }

    @Test
    fun should_return_copy_of_entries_when_getDisplaySubtitleEntries_with_existing_entries() {
        // Given: 已添加一些条目
        val convertContentItem = ConvertContentItem(
            startTime = 1000L,
            endTime = 2000L,
            textContent = "测试内容"
        )
        helper.addOrUpdateDisplaySubtitleEntry(convertContentItem)

        // When: 获取条目列表
        val result = helper.getDisplaySubtitleEntries()

        // Then: 返回包含条目的列表副本
        assertEquals(1, result.size)
        assertEquals("测试内容", result[0].displayContent)
    }

    // ========== addOrUpdateDisplaySubtitleEntry方法的测试用例 ==========

    @Test
    fun should_add_new_entry_when_addOrUpdateDisplaySubtitleEntry_with_new_start_time() {
        // Given: 新的ConvertContentItem
        val convertContentItem = ConvertContentItem(
            startTime = 1000L,
            endTime = 2000L,
            textContent = "新内容"
        )

        // When: 添加条目
        helper.addOrUpdateDisplaySubtitleEntry(convertContentItem)

        // Then: 列表中应该有一个新条目
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(1, entries.size)
        assertEquals("新内容", entries[0].displayContent)
        assertEquals(1000L, entries[0].originContent.startTime)
    }

    @Test
    fun should_update_existing_entry_when_addOrUpdateDisplaySubtitleEntry_with_same_start_time() {
        // Given: 已存在一个条目
        val originalItem = ConvertContentItem(
            startTime = 1000L,
            endTime = 2000L,
            textContent = "原始内容"
        )
        helper.addOrUpdateDisplaySubtitleEntry(originalItem)

        val updatedItem = ConvertContentItem(
            startTime = 1000L, // 相同的开始时间
            endTime = 2500L,
            textContent = "更新内容"
        )

        // When: 添加相同开始时间的条目
        helper.addOrUpdateDisplaySubtitleEntry(updatedItem)

        // Then: 应该更新现有条目而不是创建新条目
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(1, entries.size)
        assertEquals("更新内容", entries[0].displayContent)
        assertEquals(2500L, entries[0].originContent.endTime)
    }

    @Test
    fun should_refresh_display_content_when_addOrUpdateDisplaySubtitleEntry_with_existing_marks() {
        // Given: 已存在一个带有标记的条目
        val originalItem = ConvertContentItem(
            startTime = 1000L,
            endTime = 2000L,
            textContent = "原始内容"
        )
        helper.addOrUpdateDisplaySubtitleEntry(originalItem)
        // 添加标记
        helper.addMarksToSubtitleEntries(listOf(1500L))

        // 验证标记已添加
        val entriesBeforeUpdate = helper.getDisplaySubtitleEntries()
        assertEquals(1, entriesBeforeUpdate.size)
        assertTrue(entriesBeforeUpdate[0].displayContent.contains("\u2691"))
        assertEquals(1, entriesBeforeUpdate[0].insertedMarks.size)

        val updatedItem = ConvertContentItem(
            startTime = 1000L, // 相同的开始时间
            endTime = 2000L,
            textContent = "更新后的内容" // 更长的内容
        )

        // When: 更新已有标记的条目
        helper.addOrUpdateDisplaySubtitleEntry(updatedItem)

        // Then: 应该重新刷新displayContent，标记应该重新计算位置并正确显示
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(1, entries.size)
        val entry = entries[0]
        assertEquals("更新后的内容", entry.originContent.textContent)
        assertEquals(1, entry.insertedMarks.size)
        assertEquals(1500L, entry.insertedMarks[0].timeOffset)
        assertEquals("更新后\u2691的内容", entry.displayContent) // 新内容应该存在
    }

    @Test
    fun should_recalculate_mark_insert_positions_when_addOrUpdateDisplaySubtitleEntry_with_different_content_length() {
        // Given: 已存在一个带有标记的条目
        val originalItem = ConvertContentItem(
            startTime = 1000L,
            endTime = 2000L,
            textContent = "短内容" // 3个字符
        )
        helper.addOrUpdateDisplaySubtitleEntry(originalItem)
        // 添加标记在中间位置
        helper.addMarksToSubtitleEntries(listOf(1500L))

        // 验证原始标记位置
        val entriesBeforeUpdate = helper.getDisplaySubtitleEntries()
        val originalInsertPosition = entriesBeforeUpdate[0].insertedMarks[0].insertPosition
        assertEquals(1, originalInsertPosition) // 在"短内容"中间位置

        val updatedItem = ConvertContentItem(
            startTime = 1000L, // 相同的开始时间
            endTime = 2000L,
            textContent = "这是一段很长的更新后的内容，用来测试标记位置重新计算" // 更长的内容
        )

        // When: 更新为更长的内容
        helper.addOrUpdateDisplaySubtitleEntry(updatedItem)

        // Then: 标记的插入位置应该根据新内容重新计算
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(1, entries.size)
        val entry = entries[0]
        assertEquals("这是一段很长的更新后的内容，用来测试标记位置重新计算", entry.originContent.textContent)
        assertEquals(1, entry.insertedMarks.size)
        assertEquals(1500L, entry.insertedMarks[0].timeOffset)

        // 新的插入位置应该根据新内容长度重新计算（中间位置）
        val newInsertPosition = entry.insertedMarks[0].insertPosition
        val expectedPosition = helper.calculateMarkFlagInsertPosition(
            "这是一段很长的更新后的内容，用来测试标记位置重新计算",
            1000L,
            2000L,
            1500L
        )
        assertEquals(expectedPosition, newInsertPosition)
        assertTrue(newInsertPosition > originalInsertPosition) // 新位置应该更靠后

        // displayContent应该在正确位置包含标记
        assertTrue(entry.displayContent.contains("\u2691"))
        val markIndex = entry.displayContent.indexOf('\u2691')
        assertEquals(newInsertPosition, markIndex)
    }

    @Test
    fun should_recalculate_multiple_mark_positions_when_addOrUpdateDisplaySubtitleEntry_with_multiple_marks() {
        // Given: 已存在一个带有多个标记的条目
        val originalItem = ConvertContentItem(
            startTime = 1000L,
            endTime = 3000L,
            textContent = "原始内容" // 4个字符
        )
        helper.addOrUpdateDisplaySubtitleEntry(originalItem)
        // 添加多个标记
        helper.addMarksToSubtitleEntries(listOf(1500L, 2000L, 2500L))

        // 验证原始标记位置
        val entriesBeforeUpdate = helper.getDisplaySubtitleEntries()
        val originalPositions = entriesBeforeUpdate[0].insertedMarks.map { it.insertPosition }.sorted()

        val updatedItem = ConvertContentItem(
            startTime = 1000L, // 相同的开始时间
            endTime = 3000L,
            textContent = "这是一段更新后的很长的内容，用来测试多个标记位置的重新计算功能" // 更长的内容
        )

        // When: 更新为更长的内容
        helper.addOrUpdateDisplaySubtitleEntry(updatedItem)

        // Then: 所有标记的插入位置都应该根据新内容重新计算
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(1, entries.size)
        val entry = entries[0]
        assertEquals(3, entry.insertedMarks.size)

        // 验证所有标记的时间点保持不变
        val timeOffsets = entry.insertedMarks.map { it.timeOffset }.sorted()
        assertEquals(listOf(1500L, 2000L, 2500L), timeOffsets)

        // 验证所有标记的位置都重新计算了
        val newPositions = entry.insertedMarks.map { it.insertPosition }.sorted()
        // 新位置应该都比原始位置大（因为内容变长了）
        for (i in newPositions.indices) {
            assertTrue("新位置 ${newPositions[i]} 应该大于原始位置 ${originalPositions[i]}",
                newPositions[i] > originalPositions[i])
        }

        // 验证displayContent包含所有标记
        val markCount = entry.displayContent.count { it == '\u2691' }
        assertEquals(3, markCount)
    }

    @Test
    fun should_not_refresh_display_content_when_addOrUpdateDisplaySubtitleEntry_without_existing_marks() {
        // Given: 已存在一个没有标记的条目
        val originalItem = ConvertContentItem(
            startTime = 1000L,
            endTime = 2000L,
            textContent = "原始内容"
        )
        helper.addOrUpdateDisplaySubtitleEntry(originalItem)

        val updatedItem = ConvertContentItem(
            startTime = 1000L, // 相同的开始时间
            endTime = 2000L,
            textContent = "更新内容"
        )

        // When: 更新没有标记的条目
        helper.addOrUpdateDisplaySubtitleEntry(updatedItem)

        // Then: 应该直接使用新的textContent作为displayContent
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(1, entries.size)
        val entry = entries[0]
        assertEquals("更新内容", entry.originContent.textContent)
        assertEquals("更新内容", entry.displayContent)
        assertEquals(0, entry.insertedMarks.size)
        assertFalse(entry.displayContent.contains("\u2691")) // 不应该有标记
    }

    @Test
    fun should_sort_entries_by_start_time_when_addOrUpdateDisplaySubtitleEntry_with_multiple_entries() {
        // Given: 多个不同开始时间的条目（按非顺序添加）
        val item3 = ConvertContentItem(startTime = 3000L, endTime = 4000L, textContent = "第三个")
        val item1 = ConvertContentItem(startTime = 1000L, endTime = 2000L, textContent = "第一个")
        val item2 = ConvertContentItem(startTime = 2000L, endTime = 3000L, textContent = "第二个")

        // When: 按非顺序添加条目
        helper.addOrUpdateDisplaySubtitleEntry(item3)
        helper.addOrUpdateDisplaySubtitleEntry(item1)
        helper.addOrUpdateDisplaySubtitleEntry(item2)

        // Then: 条目应该按开始时间排序
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(3, entries.size)
        assertEquals("第一个", entries[0].displayContent)
        assertEquals("第二个", entries[1].displayContent)
        assertEquals("第三个", entries[2].displayContent)
    }

    // ========== addMarksToSubtitleEntries方法的测试用例 ==========

    @Test
    fun should_do_nothing_when_addMarksToSubtitleEntries_with_empty_entries() {
        // Given: 没有字幕条目
        val markTimeOffsets = listOf(1500L, 2500L)

        // When: 添加标记
        helper.addMarksToSubtitleEntries(markTimeOffsets)

        // Then: 不应该有任何变化
        val entries = helper.getDisplaySubtitleEntries()
        assertTrue(entries.isEmpty())
    }

    @Test
    fun should_add_mark_to_correct_entry_when_addMarksToSubtitleEntries_with_mark_in_time_range() {
        // Given: 一个字幕条目和在其时间范围内的标记
        val convertContentItem = ConvertContentItem(
            startTime = 1000L,
            endTime = 2000L,
            textContent = "测试内容"
        )
        helper.addOrUpdateDisplaySubtitleEntry(convertContentItem)
        val markTimeOffsets = listOf(1500L) // 在时间范围内

        // When: 添加标记
        helper.addMarksToSubtitleEntries(markTimeOffsets)

        // Then: 标记应该被添加到条目中，并更新displayContent
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(1, entries.size)
        val entry = entries[0]
        assertEquals(1, entry.insertedMarks.size)
        assertEquals(1500L, entry.insertedMarks[0].timeOffset)
        assertTrue(entry.displayContent.contains("\u2691")) // 包含标记字符
    }

    @Test
    fun should_add_mark_to_first_entry_when_addMarksToSubtitleEntries_with_mark_before_first_start_time() {
        // Given: 一个字幕条目和在第一个条目开始时间之前的标记
        val convertContentItem = ConvertContentItem(
            startTime = 1000L,
            endTime = 2000L,
            textContent = "测试内容"
        )
        helper.addOrUpdateDisplaySubtitleEntry(convertContentItem)
        val markTimeOffsets = listOf(500L) // 在第一个条目开始时间之前

        // When: 添加标记
        helper.addMarksToSubtitleEntries(markTimeOffsets)

        // Then: 标记应该被添加到第一个条目中，插入位置为0
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(1, entries.size)
        val entry = entries[0]
        assertEquals(1, entry.insertedMarks.size)
        assertEquals(500L, entry.insertedMarks[0].timeOffset)
        assertEquals(0, entry.insertedMarks[0].insertPosition)
        assertTrue(entry.displayContent.startsWith("\u2691")) // 标记在开头
    }

    @Test
    fun should_add_mark_to_closest_entry_when_addMarksToSubtitleEntries_with_mark_after_all_end_times() {
        // Given: 两个字幕条目和在所有条目结束时间之后的标记
        val item1 = ConvertContentItem(startTime = 1000L, endTime = 2000L, textContent = "第一个")
        val item2 = ConvertContentItem(startTime = 2500L, endTime = 3500L, textContent = "第二个")
        helper.addOrUpdateDisplaySubtitleEntry(item1)
        helper.addOrUpdateDisplaySubtitleEntry(item2)
        val markTimeOffsets = listOf(4000L) // 在所有条目结束时间之后

        // When: 添加标记
        helper.addMarksToSubtitleEntries(markTimeOffsets)

        // Then: 标记应该被添加到endTime最接近的条目（第二个）中，插入位置为末尾
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)
        val secondEntry = entries[1]
        assertEquals(1, secondEntry.insertedMarks.size)
        assertEquals(4000L, secondEntry.insertedMarks[0].timeOffset)
        assertEquals("第二个".length, secondEntry.insertedMarks[0].insertPosition)
        assertTrue(secondEntry.displayContent.endsWith("\u2691")) // 标记在末尾
    }

    @Test
    fun should_deduplicate_marks_when_addMarksToSubtitleEntries_with_duplicate_time_offsets() {
        // Given: 一个字幕条目和包含重复时间点的标记列表
        val convertContentItem = ConvertContentItem(
            startTime = 1000L,
            endTime = 2000L,
            textContent = "测试内容"
        )
        helper.addOrUpdateDisplaySubtitleEntry(convertContentItem)
        val markTimeOffsets = listOf(1500L, 1500L, 1600L, 1500L) // 包含重复的1500L

        // When: 添加标记
        helper.addMarksToSubtitleEntries(markTimeOffsets)

        // Then: 相同时间点的标记应该去重，只添加一次
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(1, entries.size)
        val entry = entries[0]
        assertEquals(2, entry.insertedMarks.size) // 只有1500L和1600L两个不同的时间点
        val timeOffsets = entry.insertedMarks.map { it.timeOffset }.sorted()
        assertEquals(listOf(1500L, 1600L), timeOffsets)
    }

    @Test
    fun should_not_add_duplicate_mark_when_addMarksToSubtitleEntries_with_existing_mark() {
        // Given: 一个字幕条目已经有一个标记
        val convertContentItem = ConvertContentItem(
            startTime = 1000L,
            endTime = 2000L,
            textContent = "测试内容"
        )
        helper.addOrUpdateDisplaySubtitleEntry(convertContentItem)
        helper.addMarksToSubtitleEntries(listOf(1500L)) // 先添加一个标记

        // When: 再次添加相同时间点的标记
        helper.addMarksToSubtitleEntries(listOf(1500L, 1600L))

        // Then: 相同时间点的标记不应该重复添加
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(1, entries.size)
        val entry = entries[0]
        assertEquals(2, entry.insertedMarks.size) // 1500L不重复，1600L是新的
        val timeOffsets = entry.insertedMarks.map { it.timeOffset }.sorted()
        assertEquals(listOf(1500L, 1600L), timeOffsets)
    }

    @Test
    fun should_insert_multiple_marks_at_same_position_when_addMarksToSubtitleEntries_with_same_insert_position() {
        // Given: 一个字幕条目和会产生相同插入位置的多个标记
        val convertContentItem = ConvertContentItem(
            startTime = 1000L,
            endTime = 2000L,
            textContent = "测试内容" // 4个字符
        )
        helper.addOrUpdateDisplaySubtitleEntry(convertContentItem)
        // 1500L和1501L都会计算到相同的插入位置
        val markTimeOffsets = listOf(1500L, 1501L)

        // When: 添加标记
        helper.addMarksToSubtitleEntries(markTimeOffsets)

        // Then: 相同位置应该插入多个标记字符
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(1, entries.size)
        val entry = entries[0]
        assertEquals(2, entry.insertedMarks.size)
        // displayContent应该在相同位置包含两个标记字符
        val markCount = entry.displayContent.count { it == '\u2691' }
        assertEquals(2, markCount)
    }

    /**
     * 非AI生成的人工集成测试用例，整体校验AI生成代码结果的准确性
     */
    @Test
    fun `should as expected when addMarksToSubtitleEntries with preset use cases`() {
        // Given
        val content1 = ConvertContentItem(
            startTime = 1000L,
            endTime = 10 * 1000L,
            textContent = "这是第一段测试内容，填充测试文本至20字以上。"
        )
        helper.addOrUpdateDisplaySubtitleEntry(content1)
        val content2 = ConvertContentItem(
            startTime = 13 * 1000L,
            endTime = 30 * 1000L,
            textContent = "这是第二段测试内容，填充测试文本至40字以上，以满足正常说话的语速，模拟实际情况。"
        )
        helper.addOrUpdateDisplaySubtitleEntry(content2)
        val content3 = ConvertContentItem(
            startTime = 38 * 1000L,
            endTime = 60 * 1000L,
            textContent = "这是第三段测试内容，填充测试文本至40字以上，以满足每分钟平均120字至150字的中文语速。"
        )
        helper.addOrUpdateDisplaySubtitleEntry(content3)
        val markPoints = listOf(
            538L,
            1234L,
            5578L,
            11674L,
            22000L,
            22300L,
            22600L,
            23000L,
            34000L,
            43000L,
            53000L,
            53001L,
            67000L,
            68000L
        )

        // When
        helper.addMarksToSubtitleEntries(markPoints)

        // Then
        val results = helper.getDisplaySubtitleEntries()
        assertEquals(3, results.size)
        assertEquals(
            "\u2691\u2691这是第一段测试内容，填\u2691充测试文本至20字以上。\u2691",
            results[0].displayContent
        )
        assertEquals(
            "这是第二段测试内容，填充测试文本至40字以\u2691上\u2691，\u2691以\u2691满足正常说话的语速，模拟实际情况。\u2691",
            results[1].displayContent
        )
        assertEquals(
            "这是第三段测试内容，\u2691填充测试文本至40字以上，以满足每分钟平均\u2691\u2691120字至150字的中文语速。\u2691\u2691",
            results[2].displayContent
        )
    }
}