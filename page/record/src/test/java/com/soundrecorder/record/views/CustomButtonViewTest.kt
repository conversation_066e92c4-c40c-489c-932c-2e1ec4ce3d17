/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CustomButtonViewTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/7/28
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.record.views

import android.content.Context
import android.content.res.Resources
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.record.shadows.ShadowFeatureOption
import com.soundrecorder.record.shadows.ShadowOS12FeatureUtil
import io.mockk.MockKAnnotations
import io.mockk.impl.annotations.MockK
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@PrepareForTest(CustomButtonViewTest::class, Build.VERSION::class)
@Config(
        sdk = [Build.VERSION_CODES.S],
        shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class CustomButtonViewTest {

    var context: Context? = null
    var mCustomButtonView: CustomButtonView? = null

    @MockK
    lateinit var resources: Resources

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = ApplicationProvider.getApplicationContext()
        context?.let {
            mCustomButtonView = CustomButtonView(it)
            Whitebox.invokeMethod<Void>(mCustomButtonView, "initView")
        }
    }

    @After
    fun release() {
        context = null
        unmockkAll()
    }

    @Test
    fun `assert value when setCustomEnable`() {
        mCustomButtonView?.setCustomEnable(false)
        Assert.assertEquals(Whitebox.getInternalState(mCustomButtonView, "mEnable"), false)
        mCustomButtonView?.setCustomEnable(true)
        Assert.assertEquals(Whitebox.getInternalState(mCustomButtonView, "mEnable"), true)
    }
}