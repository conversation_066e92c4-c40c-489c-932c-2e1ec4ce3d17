/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RecorderWaveItemViewTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.record.views.wave

import android.content.Context
import android.graphics.Canvas
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.record.RecorderActivity
import com.soundrecorder.record.shadows.ShadowFeatureOption
import com.soundrecorder.record.views.wave.anima.StartRecordPathInterpolator
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class RecorderWaveItemViewTest {

    private var mContext: Context? = null
    private var mController: ActivityController<RecorderActivity>? = null

    @Before
    fun setUp() {
        mContext = BaseApplication.getAppContext()
        mController = Robolectric.buildActivity(RecorderActivity::class.java)
    }

    @After
    fun tearDown() {
        mController = null
        mContext = null
    }

    @Test
    fun should_notNull_when_initBasicInfo() {
        mController?.get()?.let {
            val itemView = RecorderWaveItemView(it)
            Whitebox.invokeMethod<Any>(itemView, "initBasicInfo", it)
            Assert.assertNotNull(
                Whitebox.getInternalState<StartRecordPathInterpolator>(
                    itemView,
                    "recordPathInterpolator"
                )
            )
        }
    }

    @Test
    fun should_notNull_when_initPaint() {
        mController?.get()?.let {
            val itemView = RecorderWaveItemView(it)
            Whitebox.invokeMethod<Any>(itemView, "initPaint", it)
            Assert.assertNotNull(Whitebox.getInternalState<Int>(itemView, "enterAnimationPaint"))
        }
    }

    @Test
    fun should_equals_when_getXByTime() {
        mController?.get()?.let {
            val itemView = RecorderWaveItemView(it)
            itemView.updatePaintColor()
            Assert.assertTrue(Whitebox.getInternalState<Int>(itemView, "mWaveEnterAnimColor") != 0)
        }
    }

    @Test
    fun should_equals_when_checkEnterAnimation() {
        mController?.get()?.let {
            val itemView = RecorderWaveItemView(it)
            itemView.mIsDrawStartAnimation = false
            var result = Whitebox.invokeMethod<Boolean>(itemView, "checkEnterAnimation", Canvas())
            Assert.assertFalse(result)


            itemView.mIsDrawStartAnimation = true
            result = Whitebox.invokeMethod(itemView, "checkEnterAnimation", Canvas())
            Assert.assertTrue(result)
        }
    }
}