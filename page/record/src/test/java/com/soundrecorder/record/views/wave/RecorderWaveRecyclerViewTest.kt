/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RecorderWaveRecyclerViewTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.record.views.wave

import android.content.Context
import android.os.Build
import android.view.MotionEvent
import android.widget.LinearLayout
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.record.RecorderActivity
import com.soundrecorder.record.shadows.ShadowFeatureOption
import com.soundrecorder.wavemark.wave.view.MaxAmplitudeSource
import com.soundrecorder.wavemark.wave.view.WaveAdapter
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class RecorderWaveRecyclerViewTest {

    private var mContext: Context? = null
    private var mController: ActivityController<RecorderActivity>? = null

    @Before
    fun setUp() {
        mContext = BaseApplication.getAppContext()
        mController = Robolectric.buildActivity(RecorderActivity::class.java)
    }

    @After
    fun tearDown() {
        mController = null
        mContext = null
    }

    @Test
    fun should_return_true_when_onInterceptTouchEvent() {
        mController?.get()?.let {
            val recyclerView = RecorderWaveRecyclerView(it, null)
            recyclerView.setIsCanScrollTimeRuler(false)
            val event =
                MotionEvent.obtain(
                    System.currentTimeMillis(),
                    200,
                    MotionEvent.ACTION_DOWN,
                    200f,
                    200f,
                    0
                )
            val result = recyclerView.onInterceptTouchEvent(event)
            Assert.assertTrue(result)
        }
    }

    @Test
    fun should_returnFalse_when_onTouchEvent() {
        mController?.get()?.let {
            val recyclerView = RecorderWaveRecyclerView(it, null)
            val motionEvent =
                MotionEvent.obtain(
                    System.currentTimeMillis(),
                    200,
                    MotionEvent.ACTION_DOWN,
                    200f,
                    200f,
                    0
                )
            Whitebox.setInternalState(recyclerView, "mParent", LinearLayout(it))

            //模拟mCanScrollHorizontally = true, ACTION_DOWN
            recyclerView.setIsCanScrollTimeRuler(true)
            recyclerView.onTouchEvent(motionEvent)
            Assert.assertEquals(200, Whitebox.getInternalState(recyclerView, "mTouchDownX"))

            //mCanScrollHorizontally = false, ACTION_DOWN
            recyclerView.setIsCanScrollTimeRuler(false)
            recyclerView.onTouchEvent(motionEvent)
            Assert.assertTrue(recyclerView.onTouchEvent(motionEvent))
        }
    }

    @Test
    fun should_equals_when_createNewItemView() {
        mController?.get()?.let {
            val recyclerView = RecorderWaveRecyclerView(it, null)
            val view = recyclerView.createNewItemView(it, LinearLayout(it))
            Assert.assertTrue(view is RecorderWaveItemView)
        }
    }

    @Test
    fun should_equals_when_fixItemCount() {
        mController?.get()?.let {
            val recyclerView = RecorderWaveRecyclerView(it, null)
            recyclerView.mIsRecording = false
            val totalCount = 2
            var fixItemCount = recyclerView.fixItemCount(totalCount)
            Assert.assertEquals(totalCount, fixItemCount)

            recyclerView.mIsRecording = true
            fixItemCount = recyclerView.fixItemCount(totalCount)
            Assert.assertEquals(WaveAdapter.MAX_COUNT, fixItemCount)
        }
    }

    @Test
    fun should_equals_when_onBindItemView() {
        mController?.get()?.let {
            val recyclerView = RecorderWaveRecyclerView(it, null)
            recyclerView.mIsRecording = true
            val rulerView = RecorderWaveItemView(it)
            recyclerView.onBindItemView(rulerView, 1)
            Assert.assertTrue(rulerView.mIsRecording)
        }
    }

    @Test
    fun should_equals_when_startRecordEnterAnimation() {
        mController?.get()?.let {
            val recyclerView = RecorderWaveRecyclerView(it, null)
            val time = 1000L
            recyclerView.startRecordEnterAnimation(time)
            Assert.assertEquals(
                time,
                Whitebox.getInternalState(recyclerView, "mStartEnterAnimationTimeMillis")
            )
        }
    }

    @Test
    fun should_equals_when_stopEnterWaveAnimation() {
        mController?.get()?.let {
            val recyclerView = RecorderWaveRecyclerView(it, null)
            recyclerView.stopEnterWaveAnimation()
            Assert.assertFalse(Whitebox.getInternalState(recyclerView, "mIsDrawStartAnimation"))
        }
    }

    @Test
    fun should_equals_when_startRecordMove() {
        mController?.get()?.let {
            val recyclerView = RecorderWaveRecyclerView(it, null)
            recyclerView.startRecordMove()
            Assert.assertTrue(recyclerView.mIsRecording)
        }
    }

    @Test
    fun should_equals_when_stopRecordMove() {
        mController?.get()?.let {
            val recyclerView = RecorderWaveRecyclerView(it, null)
            recyclerView.mIsRecording = true
            recyclerView.stopRecorderMove()
            Assert.assertFalse(recyclerView.mIsRecording)
        }
    }

    @Test
    fun should_equals_when_recorderIntervalUpdate() {
        mController?.get()?.let {
            val recyclerView = RecorderWaveRecyclerView(it, null)
            recyclerView.setMaxAmplitudeSource(object :
                MaxAmplitudeSource {
                override fun getMaxAmplitude(): Int {
                    return 0
                }

                override fun getTime(): Long {
                    return 0
                }

                override fun getRecorderState(): Int {
                    return 0
                }
            })
            recyclerView.recorderIntervalUpdate()
            Assert.assertEquals(
                0,
                Whitebox.getInternalState<List<Int>>(recyclerView, "mAmplitudeValue").size
            )
        }
    }
}