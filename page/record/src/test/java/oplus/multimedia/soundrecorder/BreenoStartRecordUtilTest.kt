/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: BreenoStartRecordUtilTest
 * Description:
 * Version: 1.0
 * Date: 2024/1/8
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/1/8 1.0 create
 */

package oplus.multimedia.soundrecorder

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.modulerouter.recorder.RecorderViewModelAction
import com.soundrecorder.record.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.record.shadows.ShadowFeatureOption
import com.soundrecorder.record.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.recorderservice.ERROR_CODE_INCALLSTATE
import com.soundrecorder.recorderservice.ERROR_CODE_NOBOTHPERMISSION
import com.soundrecorder.recorderservice.ERROR_CODE_NORECORDPERMISSION
import com.soundrecorder.recorderservice.ERROR_CODE_NOSTORAGEPERMISSION
import com.soundrecorder.recorderservice.ERROR_CODE_NO_SPACE
import com.soundrecorder.recorderservice.OTHER_STATE_DEFAULT
import com.soundrecorder.recorderservice.OTHER_STATE_EDITRECORD
import com.soundrecorder.recorderservice.OTHER_STATE_OTHERACTVITY
import com.soundrecorder.recorderservice.OTHER_STATE_PLAYBACK
import com.soundrecorder.recorderservice.RECORD_STATE_NOTRECORD
import com.soundrecorder.recorderservice.RECORD_STATE_PAUSED
import com.soundrecorder.recorderservice.RECORD_STATE_RECORDING
import com.soundrecorder.recorderservice.RecordStatus
import com.soundrecorder.recorderservice.STATUS_ERROR_CODE_DEFAULT
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowCOUIVersionUtil::class]
)
class BreenoStartRecordUtilTest {
    private var mockStaticRecorderViewModelAction: MockedStatic<RecorderViewModelAction>? = null
    private var mockStaticPermissionUtils: MockedStatic<PermissionUtils>? = null

    @Before
    fun setup() {
        mockStaticRecorderViewModelAction = Mockito.mockStatic(RecorderViewModelAction::class.java)
        mockStaticPermissionUtils = Mockito.mockStatic(PermissionUtils::class.java)
    }

    @After
    fun release() {
        mockStaticRecorderViewModelAction?.close()
        mockStaticPermissionUtils?.close()
        mockStaticRecorderViewModelAction = null
        mockStaticPermissionUtils = null
    }

    @Test
    fun should_return_correct_when_doCheckExceptionWhenStartRecordFromBreeno() {
        // check storage fail
        mockStaticRecorderViewModelAction?.`when`<Boolean> { RecorderViewModelAction.checkDistBeforeStartRecord() }
            ?.thenReturn(false, true)
        var result = BreenoStartRecordUtil.doCheckExceptionWhenStartRecordFromBreeno()
        Assert.assertFalse(result)

        // no read、record audio permission
        mockStaticPermissionUtils?.`when`<Boolean> { PermissionUtils.hasReadAudioPermission() }
            ?.thenReturn(false, true)
        mockStaticPermissionUtils?.`when`<Boolean> { PermissionUtils.hasRecordAudioPermission() }
            ?.thenReturn(false, true)
        result = BreenoStartRecordUtil.doCheckExceptionWhenStartRecordFromBreeno()
        Assert.assertFalse(result)

        result = BreenoStartRecordUtil.doCheckExceptionWhenStartRecordFromBreeno()
        Assert.assertFalse(result)

        // already recording
        mockStaticRecorderViewModelAction?.`when`<Boolean> { RecorderViewModelAction.isAlreadyRecording() }
            ?.thenReturn(true, false)
        result = BreenoStartRecordUtil.doCheckExceptionWhenStartRecordFromBreeno()
        Assert.assertFalse(result)

        // pass the all check
        result = BreenoStartRecordUtil.doCheckExceptionWhenStartRecordFromBreeno()
        Assert.assertTrue(result)
    }

    @Test
    fun should_return_correct_when_getErrorStatus() {
        val recordStatus = RecordStatus()
        // other scene
        recordStatus.otherState = OTHER_STATE_PLAYBACK
        BreenoStartRecordUtil.getErrorStatus(recordStatus)
        Assert.assertEquals(STATUS_ERROR_CODE_DEFAULT, recordStatus.errorCode)

        recordStatus.otherState = OTHER_STATE_EDITRECORD
        BreenoStartRecordUtil.getErrorStatus(recordStatus)
        Assert.assertEquals(STATUS_ERROR_CODE_DEFAULT, recordStatus.errorCode)

        recordStatus.otherState = OTHER_STATE_OTHERACTVITY
        BreenoStartRecordUtil.getErrorStatus(recordStatus)
        Assert.assertEquals(STATUS_ERROR_CODE_DEFAULT, recordStatus.errorCode)

        // record scene
        mockStaticPermissionUtils?.`when`<Boolean> { PermissionUtils.hasReadAudioPermission() }
            ?.thenReturn(false, false, false, true, true, true)
        mockStaticPermissionUtils?.`when`<Boolean> { PermissionUtils.hasRecordAudioPermission() }
            ?.thenReturn(false, true, false, false, true)
        recordStatus.otherState = OTHER_STATE_DEFAULT
        BreenoStartRecordUtil.getErrorStatus(recordStatus)
        Assert.assertEquals(ERROR_CODE_NOBOTHPERMISSION, recordStatus.errorCode)

        BreenoStartRecordUtil.getErrorStatus(recordStatus)
        Assert.assertEquals(ERROR_CODE_NOSTORAGEPERMISSION, recordStatus.errorCode)

        BreenoStartRecordUtil.getErrorStatus(recordStatus)
        Assert.assertEquals(ERROR_CODE_NORECORDPERMISSION, recordStatus.errorCode)

        mockStaticPermissionUtils?.`when`<Boolean> { PermissionUtils.hasReadAudioPermission() }
            ?.thenReturn(true)
        mockStaticPermissionUtils?.`when`<Boolean> { PermissionUtils.hasRecordAudioPermission() }
            ?.thenReturn(true)
        mockStaticRecorderViewModelAction?.`when`<Boolean> { RecorderViewModelAction.checkDistBeforeStartRecord() }
            ?.thenReturn(false, true, true, true)
        BreenoStartRecordUtil.getErrorStatus(recordStatus)
        Assert.assertEquals(ERROR_CODE_NO_SPACE, recordStatus.errorCode)

        mockStaticRecorderViewModelAction?.`when`<Boolean> {
            RecorderViewModelAction.checkModeCanRecord(false)
        }?.thenReturn(false, true, true)
        BreenoStartRecordUtil.getErrorStatus(recordStatus)
        Assert.assertEquals(ERROR_CODE_INCALLSTATE, recordStatus.errorCode)

        BreenoStartRecordUtil.getErrorStatus(recordStatus)
        Assert.assertEquals(STATUS_ERROR_CODE_DEFAULT, recordStatus.errorCode)
    }

    @Test
    fun should_return_correct_when_getCurrentRecordStatus() {
        mockStaticRecorderViewModelAction?.`when`<Int> { RecorderViewModelAction.getCurrentStatus() }
            ?.thenReturn(
                RecorderViewModelAction.INIT,
                RecorderViewModelAction.RECORDING,
                RecorderViewModelAction.PAUSED
            )

        val recordStatus = RecordStatus()
        BreenoStartRecordUtil.getCurrentRecordStatus(recordStatus)
        Assert.assertEquals(RECORD_STATE_NOTRECORD, recordStatus.currentRecordState)

        BreenoStartRecordUtil.getCurrentRecordStatus(recordStatus)
        Assert.assertEquals(RECORD_STATE_RECORDING, recordStatus.currentRecordState)

        BreenoStartRecordUtil.getCurrentRecordStatus(recordStatus)
        Assert.assertEquals(RECORD_STATE_PAUSED, recordStatus.currentRecordState)
    }

    @Test
    fun should_return_correct_when_isOtherCantRecordState() {
        Assert.assertFalse(BreenoStartRecordUtil.isOtherCantRecordState(OTHER_STATE_DEFAULT))

        Assert.assertTrue(BreenoStartRecordUtil.isOtherCantRecordState(OTHER_STATE_PLAYBACK))
        Assert.assertTrue(BreenoStartRecordUtil.isOtherCantRecordState(OTHER_STATE_EDITRECORD))
        Assert.assertTrue(BreenoStartRecordUtil.isOtherCantRecordState(OTHER_STATE_OTHERACTVITY))
    }
}