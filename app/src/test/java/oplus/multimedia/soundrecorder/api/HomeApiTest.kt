/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  HomeApiTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/12/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package oplus.multimedia.soundrecorder.api

import android.app.Activity
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.BrowseFile
import oplus.multimedia.soundrecorder.shadows.ShadowFeatureOption
import oplus.multimedia.soundrecorder.slidebar.TransparentActivity
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowFeatureOption::class]
)
class HomeApiTest {

    @Test
    fun should_equals_when_isTransparentActivity() {
        var activity: Activity = BrowseFile()
        Assert.assertFalse(HomeApi.isTransparentActivity(activity))

        activity = TransparentActivity()
        Assert.assertTrue(HomeApi.isTransparentActivity(activity))
    }

    @Test
    fun should_success_when_getTransParentActivityClass() {
        val classRef = HomeApi.getTransParentActivityClass()
        Assert.assertTrue(classRef.simpleName == TransparentActivity::class.java.simpleName)
    }
}